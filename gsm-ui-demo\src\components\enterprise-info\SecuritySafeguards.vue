<template>
  <div class="space-y-6">
    <!-- 安全防控措施概览 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Shield class="h-5 w-5" />
            <span>数据安全防控措施概览</span>
          </div>
          <Badge variant="outline">全方位安全保障</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ securityMeasures.length }}</div>
            <div class="text-sm text-green-600">安全措施总数</div>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ activeMeasures }}</div>
            <div class="text-sm text-blue-600">已实施措施</div>
          </div>
          <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-2xl font-bold text-yellow-600">{{ measuresCoverage }}%</div>
            <div class="text-sm text-yellow-600">覆盖率</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ lastReviewDaysAgo }}</div>
            <div class="text-sm text-purple-600">距上次审查(天)</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 安全措施分类管理 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Lock class="h-5 w-5" />
          <span>安全防控措施详情</span>
        </CardTitle>
        <CardDescription> 企业数据安全防控措施的全面管理与维护 </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs v-model="activeCategory" class="w-full">
          <TabsList class="grid w-full grid-cols-6">
            <TabsTrigger value="network">网络安全</TabsTrigger>
            <TabsTrigger value="access">访问控制</TabsTrigger>
            <TabsTrigger value="encryption">数据加密</TabsTrigger>
            <TabsTrigger value="audit">审计监控</TabsTrigger>
            <TabsTrigger value="backup">备份恢复</TabsTrigger>
            <TabsTrigger value="physical">物理安全</TabsTrigger>
          </TabsList>

          <!-- 网络安全措施 -->
          <TabsContent value="network" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Globe class="w-5 h-5" />
                  网络安全措施
                </CardTitle>
                <CardDescription>网络隔离、防火墙配置等网络层面的安全措施</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- 网络隔离措施 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">网络隔离措施</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        v-for="measure in networkIsolationMeasures"
                        :key="measure.id"
                        class="p-4 border rounded-lg space-y-3"
                      >
                        <div class="flex items-center justify-between">
                          <h5 class="font-medium">{{ measure.measureType }}</h5>
                          <Badge :variant="measure.status === 'active' ? 'default' : 'secondary'">
                            {{ measure.status === 'active' ? '已实施' : '未实施' }}
                          </Badge>
                        </div>
                        <p class="text-sm text-muted-foreground">{{ measure.description }}</p>
                        <div class="space-y-2">
                          <div class="text-xs text-muted-foreground">实施细节:</div>
                          <div class="text-sm bg-muted p-2 rounded">
                            {{ measure.implementationDetails }}
                          </div>
                        </div>
                        <div class="flex justify-between text-xs text-muted-foreground">
                          <span>上次审查: {{ formatDate(measure.lastReviewDate) }}</span>
                          <span>创建时间: {{ formatDate(measure.createTime) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 防火墙配置 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">防火墙配置</h4>
                    <div class="table-elevated rounded-md border">
                      <Table class="data-table">
                        <TableHeader>
                          <TableRow class="data-table-header">
                            <TableHead class="data-table-cell">防火墙名称</TableHead>
                            <TableHead class="data-table-cell">类型</TableHead>
                            <TableHead class="data-table-cell">状态</TableHead>
                            <TableHead class="data-table-cell">规则数量</TableHead>
                            <TableHead class="data-table-cell">最后更新</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow
                            v-for="firewall in firewallConfigs"
                            :key="firewall.id"
                            class="data-table-row"
                          >
                            <TableCell class="data-table-cell">{{ firewall.name }}</TableCell>
                            <TableCell class="data-table-cell">{{ firewall.type }}</TableCell>
                            <TableCell class="data-table-cell">
                              <Badge
                                :variant="firewall.status === 'active' ? 'default' : 'destructive'"
                              >
                                {{ firewall.status === 'active' ? '运行中' : '已停止' }}
                              </Badge>
                            </TableCell>
                            <TableCell class="data-table-cell">{{ firewall.ruleCount }}</TableCell>
                            <TableCell class="data-table-cell">{{
                              formatDate(firewall.lastUpdated)
                            }}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 访问控制措施 -->
          <TabsContent value="access" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Key class="w-5 h-5" />
                  访问控制措施
                </CardTitle>
                <CardDescription>身份认证、权限管理等访问控制安全措施</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- 身份认证措施 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">身份认证措施</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        v-for="measure in accessControlMeasures"
                        :key="measure.id"
                        class="p-4 border rounded-lg space-y-3"
                      >
                        <div class="flex items-center justify-between">
                          <h5 class="font-medium">{{ measure.measureType }}</h5>
                          <Badge :variant="measure.status === 'active' ? 'default' : 'secondary'">
                            {{ measure.status === 'active' ? '已启用' : '未启用' }}
                          </Badge>
                        </div>
                        <p class="text-sm text-muted-foreground">{{ measure.description }}</p>
                        <div class="space-y-2">
                          <div class="text-xs text-muted-foreground">配置详情:</div>
                          <div class="text-sm bg-muted p-2 rounded">
                            {{ measure.implementationDetails }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 权限管理统计 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">权限管理统计</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div class="text-center p-3 bg-blue-50 rounded">
                        <div class="text-lg font-bold text-blue-600">
                          {{ permissionStats.totalUsers }}
                        </div>
                        <div class="text-xs text-blue-600">用户总数</div>
                      </div>
                      <div class="text-center p-3 bg-green-50 rounded">
                        <div class="text-lg font-bold text-green-600">
                          {{ permissionStats.totalRoles }}
                        </div>
                        <div class="text-xs text-green-600">角色总数</div>
                      </div>
                      <div class="text-center p-3 bg-yellow-50 rounded">
                        <div class="text-lg font-bold text-yellow-600">
                          {{ permissionStats.totalPermissions }}
                        </div>
                        <div class="text-xs text-yellow-600">权限总数</div>
                      </div>
                      <div class="text-center p-3 bg-purple-50 rounded">
                        <div class="text-lg font-bold text-purple-600">
                          {{ permissionStats.activeUsers }}
                        </div>
                        <div class="text-xs text-purple-600">活跃用户</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 数据加密措施 -->
          <TabsContent value="encryption" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <FileText class="w-5 h-5" />
                  数据加密措施
                </CardTitle>
                <CardDescription>数据传输加密、存储加密等数据保护措施</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- 加密算法配置 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">加密算法配置</h4>
                    <div class="table-elevated rounded-md border">
                      <Table class="data-table">
                        <TableHeader>
                          <TableRow class="data-table-header">
                            <TableHead class="data-table-cell">加密类型</TableHead>
                            <TableHead class="data-table-cell">算法</TableHead>
                            <TableHead class="data-table-cell">密钥长度</TableHead>
                            <TableHead class="data-table-cell">状态</TableHead>
                            <TableHead class="data-table-cell">应用范围</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow
                            v-for="encryption in encryptionMeasures"
                            :key="encryption.id"
                            class="data-table-row"
                          >
                            <TableCell class="data-table-cell">{{ encryption.type }}</TableCell>
                            <TableCell class="data-table-cell">{{
                              encryption.algorithm
                            }}</TableCell>
                            <TableCell class="data-table-cell">{{
                              encryption.keyLength
                            }}</TableCell>
                            <TableCell class="data-table-cell">
                              <Badge
                                :variant="encryption.status === 'active' ? 'default' : 'secondary'"
                              >
                                {{ encryption.status === 'active' ? '已启用' : '未启用' }}
                              </Badge>
                            </TableCell>
                            <TableCell class="data-table-cell">{{ encryption.scope }}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </div>

                  <!-- 密钥管理 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">密钥管理</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div class="p-4 border rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600">12</div>
                        <div class="text-sm text-green-600">活跃密钥</div>
                      </div>
                      <div class="p-4 border rounded-lg text-center">
                        <div class="text-2xl font-bold text-yellow-600">3</div>
                        <div class="text-sm text-yellow-600">即将过期</div>
                      </div>
                      <div class="p-4 border rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600">45天</div>
                        <div class="text-sm text-blue-600">轮换周期</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 审计监控措施 -->
          <TabsContent value="audit" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Eye class="w-5 h-5" />
                  审计监控措施
                </CardTitle>
                <CardDescription>日志审计、实时监控等安全监控措施</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- 审计配置 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">审计配置</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        v-for="measure in auditMeasures"
                        :key="measure.id"
                        class="p-4 border rounded-lg space-y-3"
                      >
                        <div class="flex items-center justify-between">
                          <h5 class="font-medium">{{ measure.measureType }}</h5>
                          <Badge :variant="measure.status === 'active' ? 'default' : 'secondary'">
                            {{ measure.status === 'active' ? '运行中' : '已停止' }}
                          </Badge>
                        </div>
                        <p class="text-sm text-muted-foreground">{{ measure.description }}</p>
                        <div class="space-y-2">
                          <div class="text-xs text-muted-foreground">监控范围:</div>
                          <div class="text-sm bg-muted p-2 rounded">
                            {{ measure.implementationDetails }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 监控统计 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">今日监控统计</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div class="text-center p-3 bg-blue-50 rounded">
                        <div class="text-lg font-bold text-blue-600">1,245</div>
                        <div class="text-xs text-blue-600">访问日志</div>
                      </div>
                      <div class="text-center p-3 bg-green-50 rounded">
                        <div class="text-lg font-bold text-green-600">89</div>
                        <div class="text-xs text-green-600">操作日志</div>
                      </div>
                      <div class="text-center p-3 bg-yellow-50 rounded">
                        <div class="text-lg font-bold text-yellow-600">5</div>
                        <div class="text-xs text-yellow-600">异常事件</div>
                      </div>
                      <div class="text-center p-3 bg-red-50 rounded">
                        <div class="text-lg font-bold text-red-600">1</div>
                        <div class="text-xs text-red-600">安全告警</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 备份恢复措施 -->
          <TabsContent value="backup" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Archive class="w-5 h-5" />
                  备份恢复措施
                </CardTitle>
                <CardDescription>数据备份策略、灾难恢复等数据保护措施</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- 备份策略 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">备份策略配置</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        v-for="measure in backupMeasures"
                        :key="measure.id"
                        class="p-4 border rounded-lg space-y-3"
                      >
                        <div class="flex items-center justify-between">
                          <h5 class="font-medium">{{ measure.measureType }}</h5>
                          <Badge :variant="measure.status === 'active' ? 'default' : 'secondary'">
                            {{ measure.status === 'active' ? '正常运行' : '已暂停' }}
                          </Badge>
                        </div>
                        <p class="text-sm text-muted-foreground">{{ measure.description }}</p>
                        <div class="space-y-2">
                          <div class="text-xs text-muted-foreground">备份详情:</div>
                          <div class="text-sm bg-muted p-2 rounded">
                            {{ measure.implementationDetails }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 备份统计 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">备份状态统计</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div class="text-center p-3 bg-blue-50 rounded">
                        <div class="text-lg font-bold text-blue-600">15.6TB</div>
                        <div class="text-xs text-blue-600">备份总量</div>
                      </div>
                      <div class="text-center p-3 bg-green-50 rounded">
                        <div class="text-lg font-bold text-green-600">3</div>
                        <div class="text-xs text-green-600">备份副本</div>
                      </div>
                      <div class="text-center p-3 bg-yellow-50 rounded">
                        <div class="text-lg font-bold text-yellow-600">2小时</div>
                        <div class="text-xs text-yellow-600">备份周期</div>
                      </div>
                      <div class="text-center p-3 bg-purple-50 rounded">
                        <div class="text-lg font-bold text-purple-600">99.9%</div>
                        <div class="text-xs text-purple-600">成功率</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 物理安全措施 -->
          <TabsContent value="physical" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Building class="w-5 h-5" />
                  物理安全措施
                </CardTitle>
                <CardDescription>机房安全、设备安全等物理层面的安全措施</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- 物理安全设施 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">物理安全设施</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        v-for="measure in physicalMeasures"
                        :key="measure.id"
                        class="p-4 border rounded-lg space-y-3"
                      >
                        <div class="flex items-center justify-between">
                          <h5 class="font-medium">{{ measure.measureType }}</h5>
                          <Badge :variant="measure.status === 'active' ? 'default' : 'destructive'">
                            {{ measure.status === 'active' ? '正常' : '异常' }}
                          </Badge>
                        </div>
                        <p class="text-sm text-muted-foreground">{{ measure.description }}</p>
                        <div class="space-y-2">
                          <div class="text-xs text-muted-foreground">设施详情:</div>
                          <div class="text-sm bg-muted p-2 rounded">
                            {{ measure.implementationDetails }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 环境监控 -->
                  <div class="space-y-4">
                    <h4 class="font-medium text-sm">机房环境监控</h4>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div class="text-center p-3 bg-blue-50 rounded">
                        <div class="text-lg font-bold text-blue-600">22°C</div>
                        <div class="text-xs text-blue-600">当前温度</div>
                      </div>
                      <div class="text-center p-3 bg-green-50 rounded">
                        <div class="text-lg font-bold text-green-600">45%</div>
                        <div class="text-xs text-green-600">相对湿度</div>
                      </div>
                      <div class="text-center p-3 bg-yellow-50 rounded">
                        <div class="text-lg font-bold text-yellow-600">正常</div>
                        <div class="text-xs text-yellow-600">烟雾检测</div>
                      </div>
                      <div class="text-center p-3 bg-purple-50 rounded">
                        <div class="text-lg font-bold text-purple-600">在线</div>
                        <div class="text-xs text-purple-600">UPS状态</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>

    <!-- 安全措施审查记录 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Clock class="h-5 w-5" />
          <span>安全措施审查记录</span>
        </CardTitle>
        <CardDescription>安全措施的定期审查和更新记录</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="table-elevated rounded-md border">
          <Table class="data-table">
            <TableHeader>
              <TableRow class="data-table-header">
                <TableHead class="data-table-cell">审查日期</TableHead>
                <TableHead class="data-table-cell">措施类型</TableHead>
                <TableHead class="data-table-cell">审查结果</TableHead>
                <TableHead class="data-table-cell">发现问题</TableHead>
                <TableHead class="data-table-cell">整改措施</TableHead>
                <TableHead class="data-table-cell">审查人员</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="record in reviewRecords" :key="record.id" class="data-table-row">
                <TableCell class="data-table-cell">{{ formatDate(record.reviewDate) }}</TableCell>
                <TableCell class="data-table-cell">{{ record.measureType }}</TableCell>
                <TableCell class="data-table-cell">
                  <Badge :variant="record.result === '通过' ? 'default' : 'destructive'">
                    {{ record.result }}
                  </Badge>
                </TableCell>
                <TableCell class="data-table-cell">{{ record.issues || '无' }}</TableCell>
                <TableCell class="data-table-cell">{{ record.actions || '无需整改' }}</TableCell>
                <TableCell class="data-table-cell">{{ record.reviewer }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Shield, Lock, Globe, Key, FileText, Eye, Archive, Building, Clock } from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from '@/components/ui/table'

interface Props {
  enterpriseId: string
}

const props = defineProps<Props>()

// 当前选中的安全措施分类
const activeCategory = ref('network')

// 企业安全措施数据管理
const securityMeasures = ref([
  { id: 1, measureType: '网络隔离', status: 'active' },
  { id: 2, measureType: '访问控制', status: 'active' },
  { id: 3, measureType: '数据加密', status: 'active' },
  { id: 4, measureType: '审计监控', status: 'active' },
  { id: 5, measureType: '备份恢复', status: 'active' },
  { id: 6, measureType: '物理安全', status: 'active' },
])

// 计算统计数据
const activeMeasures = computed(
  () => securityMeasures.value.filter((m) => m.status === 'active').length,
)
const measuresCoverage = computed(() =>
  Math.round((activeMeasures.value / securityMeasures.value.length) * 100),
)
const lastReviewDaysAgo = computed(() => 15)

// 网络隔离措施详情
const networkIsolationMeasures = ref([
  {
    id: 1,
    measureType: '生产网络隔离',
    description: '将生产环境与测试环境进行物理隔离，确保数据安全',
    implementationDetails: '采用VLAN技术实现网络隔离，配置独立的网段和路由策略，禁止跨网段直接访问',
    lastReviewDate: '2024-01-15',
    createTime: '2023-06-20',
    status: 'active',
  },
  {
    id: 2,
    measureType: '数据传输隔离',
    description: '对敏感数据传输通道进行专线隔离',
    implementationDetails: '建立专用数据传输通道，采用VPN技术加密传输，设置访问白名单',
    lastReviewDate: '2024-01-10',
    createTime: '2023-08-15',
    status: 'active',
  },
])

// 访问控制措施详情
const accessControlMeasures = ref([
  {
    id: 1,
    measureType: '多因素身份认证',
    description: '实施基于用户名密码+短信验证码的双因素认证',
    implementationDetails: '集成LDAP认证系统，支持短信、邮箱等多种验证方式，设置登录失败锁定机制',
    status: 'active',
  },
  {
    id: 2,
    measureType: '基于角色的权限控制',
    description: '根据用户角色分配不同的系统访问权限',
    implementationDetails: '建立分级权限体系，设置数据访问、操作权限矩阵，定期进行权限审查',
    status: 'active',
  },
])

// 权限统计数据
const permissionStats = ref({
  totalUsers: 45,
  totalRoles: 8,
  totalPermissions: 32,
  activeUsers: 38,
})

// 防火墙配置
const firewallConfigs = ref([
  {
    id: 1,
    name: '边界防火墙',
    type: '硬件防火墙',
    status: 'active',
    ruleCount: 156,
    lastUpdated: '2024-01-18',
  },
  {
    id: 2,
    name: '内网防火墙',
    type: '软件防火墙',
    status: 'active',
    ruleCount: 89,
    lastUpdated: '2024-01-16',
  },
  {
    id: 3,
    name: '应用防火墙',
    type: 'Web应用防火墙',
    status: 'active',
    ruleCount: 72,
    lastUpdated: '2024-01-19',
  },
])

// 加密措施详情
const encryptionMeasures = ref([
  {
    id: 1,
    type: '传输加密',
    algorithm: 'TLS 1.3',
    keyLength: '256位',
    status: 'active',
    scope: '全部数据传输',
  },
  {
    id: 2,
    type: '存储加密',
    algorithm: 'AES-256',
    keyLength: '256位',
    status: 'active',
    scope: '敏感数据存储',
  },
  {
    id: 3,
    type: '备份加密',
    algorithm: 'SM4',
    keyLength: '128位',
    status: 'active',
    scope: '备份数据',
  },
  {
    id: 4,
    type: '通信加密',
    algorithm: 'SM2',
    keyLength: '256位',
    status: 'active',
    scope: '内部通信',
  },
])

// 审计措施详情
const auditMeasures = ref([
  {
    id: 1,
    measureType: '访问日志审计',
    description: '记录所有用户访问行为，包括登录、操作、数据访问等',
    implementationDetails: '部署日志收集系统，实时记录用户行为，设置异常行为告警机制',
    status: 'active',
  },
  {
    id: 2,
    measureType: '系统操作审计',
    description: '监控系统级别的操作和配置变更',
    implementationDetails: '监控文件系统变更、配置修改、权限变更等系统操作，生成详细审计报告',
    status: 'active',
  },
])

// 备份措施详情
const backupMeasures = ref([
  {
    id: 1,
    measureType: '增量备份策略',
    description: '每日进行增量备份，每周进行全量备份',
    implementationDetails: '配置自动备份任务，备份数据存储在独立存储设备，保留30天历史版本',
    status: 'active',
  },
  {
    id: 2,
    measureType: '异地备份策略',
    description: '重要数据进行异地备份存储',
    implementationDetails: '关键业务数据同步至异地机房，采用加密传输和存储，实现灾难恢复能力',
    status: 'active',
  },
])

// 物理安全措施详情
const physicalMeasures = ref([
  {
    id: 1,
    measureType: '门禁控制系统',
    description: '机房采用刷卡+指纹双重验证门禁',
    implementationDetails:
      '安装多层门禁系统，支持刷卡、指纹、面部识别等多种认证方式，记录所有进出记录',
    status: 'active',
  },
  {
    id: 2,
    measureType: '视频监控系统',
    description: '7x24小时视频监控覆盖',
    implementationDetails: '部署高清摄像头全方位监控，视频存储30天，支持远程监控和告警',
    status: 'active',
  },
])

// 审查记录
const reviewRecords = ref([
  {
    id: 1,
    reviewDate: '2024-01-15',
    measureType: '网络安全措施',
    result: '通过',
    issues: null,
    actions: null,
    reviewer: '张安全工程师',
  },
  {
    id: 2,
    reviewDate: '2024-01-10',
    measureType: '访问控制措施',
    result: '通过',
    issues: '部分用户权限过高',
    actions: '调整用户权限级别',
    reviewer: '李系统管理员',
  },
  {
    id: 3,
    reviewDate: '2024-01-05',
    measureType: '数据加密措施',
    result: '不通过',
    issues: '密钥轮换周期过长',
    actions: '缩短密钥轮换周期至30天',
    reviewer: '王安全专家',
  },
])

// 工具方法
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}
</script>
