@import './ocean-depths-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* 增强卡片层次系统 */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --card-elevated: 0 0% 98%;
    --card-elevated-foreground: 222.2 84% 4.9%;
    --card-surface: 210 20% 96%;
    --card-surface-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    /* 增强次要色层次 */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-hover: 210 40% 93%;
    --secondary-active: 210 40% 90%;

    /* 改进静音色层次 */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --muted-hover: 210 40% 93%;
    --muted-darker: 210 40% 91%;

    /* 增强强调色层次 */
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --accent-hover: 210 40% 93%;
    --accent-subtle: 210 20% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* 增强边框层次 */
    --border: 214.3 31.8% 91.4%;
    --border-light: 214.3 31.8% 94%;
    --border-medium: 214.3 31.8% 88%;
    --border-strong: 214.3 31.8% 82%;
    
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    
    /* 图表色彩优化 */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    
    /* 新增：侧边栏增强颜色变量 */
    --sidebar-header-bg: 0 0% 96%;
    --sidebar-header-foreground: 222.2 84% 4.9%;
    --sidebar-footer-bg: 210 20% 98%;
    --sidebar-footer-foreground: 215.4 16.3% 46.9%;
    --sidebar-group-bg: 0 0% 99%;
    --sidebar-group-border: 214.3 31.8% 93%;
    
    /* 侧边栏优化 */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* 新增：表格专用颜色变量 */
    --table-header-bg: 210 20% 96%;
    --table-header-foreground: 222.2 47.4% 11.2%;
    --table-row-hover: 210 40% 98%;
    --table-border: 214.3 31.8% 91.4%;

    /* 新增：状态色增强 */
    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 32 95% 44%;
    --warning-foreground: 210 40% 98%;
    --info: 221.2 83.2% 53.3%;
    --info-foreground: 210 40% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    /* 增强暗色模式卡片层次 */
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --card-elevated: 222.2 84% 6.9%;
    --card-elevated-foreground: 210 40% 98%;
    --card-surface: 217.2 32.6% 12%;
    --card-surface-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    /* 增强暗色次要色层次 */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-hover: 217.2 32.6% 20%;
    --secondary-active: 217.2 32.6% 23%;

    /* 改进暗色静音层次 */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --muted-hover: 217.2 32.6% 20%;
    --muted-darker: 217.2 32.6% 14%;

    /* 增强暗色强调层次 */
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --accent-hover: 217.2 32.6% 20%;
    --accent-subtle: 217.2 32.6% 12%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* 增强暗色边框层次 */
    --border: 217.2 32.6% 17.5%;
    --border-light: 217.2 32.6% 20%;
    --border-medium: 217.2 32.6% 15%;
    --border-strong: 217.2 32.6% 25%;
    
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    
    /* 暗色图表优化 */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    /* 暗色侧边栏增强颜色 */
    --sidebar-header-bg: 222.2 84% 6%;
    --sidebar-header-foreground: 240 4.8% 95.9%;
    --sidebar-footer-bg: 217.2 32.6% 12%;
    --sidebar-footer-foreground: 215 20.2% 65.1%;
    --sidebar-group-bg: 222.2 84% 3%;
    --sidebar-group-border: 217.2 32.6% 15%;
    
    /* 暗色侧边栏优化 */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* 暗色表格专用 */
    --table-header-bg: 217.2 32.6% 12%;
    --table-header-foreground: 210 40% 98%;
    --table-row-hover: 217.2 32.6% 15%;
    --table-border: 217.2 32.6% 17.5%;

    /* 暗色状态色 */
    --success: 142 84% 47%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 32 95% 55%;
    --warning-foreground: 222.2 84% 4.9%;
    --info: 217.2 91.2% 59.8%;
    --info-foreground: 222.2 84% 4.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* 统一的卡片层次系统 */
  .card-elevated {
    @apply bg-[hsl(var(--card-elevated))] text-[hsl(var(--card-elevated-foreground))] border-[hsl(var(--border-light))];
  }
  
  .card-surface {
    @apply bg-[hsl(var(--card-surface))] text-[hsl(var(--card-surface-foreground))] border-[hsl(var(--border-medium))];
  }

  /* 统一的表格样式 */
  .table-elevated {
    @apply bg-[hsl(var(--card-elevated))];
  }
  
  .table-header {
    @apply bg-[hsl(var(--table-header-bg))] text-[hsl(var(--table-header-foreground))];
  }
  
  .table-row-hover {
    @apply hover:bg-[hsl(var(--table-row-hover))];
  }

  /* 统一的状态样式 */
  .status-success {
    @apply bg-[hsl(var(--success))] text-[hsl(var(--success-foreground))];
  }
  
  .status-warning {
    @apply bg-[hsl(var(--warning))] text-[hsl(var(--warning-foreground))];
  }
  
  .status-info {
    @apply bg-[hsl(var(--info))] text-[hsl(var(--info-foreground))];
  }

  /* 统一的交互状态 */
  .interactive-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-[hsl(var(--secondary-hover))] active:bg-[hsl(var(--secondary-active))];
  }

  .interactive-muted {
    @apply bg-muted text-muted-foreground hover:bg-[hsl(var(--muted-hover))];
  }

  .interactive-accent {
    @apply bg-accent text-accent-foreground hover:bg-[hsl(var(--accent-hover))];
  }
}

/* 数据表格专用样式类 */
@layer components {
  .pagination-size-control {
    @apply flex items-center gap-2 text-sm text-muted-foreground whitespace-nowrap;
  }
  .data-table {
    @apply w-full border-collapse text-sm;
  }

  .data-table-header {
    @apply table-header font-medium text-left border-b border-[hsl(var(--table-border))];
  }

  .data-table-cell {
    @apply p-3 border-b border-[hsl(var(--table-border))] align-middle;
  }

  .data-table-row {
    @apply table-row-hover transition-colors duration-150;
  }

  /* 卡片容器层次 */
  .card-container {
    @apply space-y-6;
  }

  .card-primary {
    background: linear-gradient(135deg, var(--ocean-primary-500) 0%, var(--ocean-primary-600) 100%);
    color: var(--ocean-neutral-50);
    border: 1px solid var(--ocean-primary-400);
    box-shadow: 0 4px 6px -1px rgba(15, 76, 117, 0.1), 0 2px 4px -1px rgba(15, 76, 117, 0.06);
  }

  .card-secondary {
    background: linear-gradient(135deg, var(--ocean-primary-100) 0%, var(--ocean-primary-200) 100%);
    color: var(--ocean-primary-800);
    border: 1px solid var(--ocean-primary-300);
    box-shadow: 0 4px 6px -1px rgba(70, 130, 180, 0.1), 0 2px 4px -1px rgba(70, 130, 180, 0.06);
  }

  .card-subtle {
    @apply card-surface shadow-none;
  }
}
