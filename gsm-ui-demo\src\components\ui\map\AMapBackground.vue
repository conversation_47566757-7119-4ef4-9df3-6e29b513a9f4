<template>
  <div class="amap-background-container">
    <!-- 地图容器 -->
    <div ref="mapContainer" class="amap-container"></div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载高德地图...</p>
    </div>

    <!-- 地图控制面板 -->
    <div class="map-controls-panel">
      <div class="control-group">
        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <button @click="zoomIn" class="control-btn zoom-btn" title="放大">
            <Plus class="w-4 h-4" />
          </button>
          <button @click="zoomOut" class="control-btn zoom-btn" title="缩小">
            <Minus class="w-4 h-4" />
          </button>
        </div>

        <!-- 重置视图 -->
        <button @click="resetView" class="control-btn reset-btn" title="重置视图">
          <RotateCcw class="w-4 h-4" />
        </button>

        <!-- 地图类型切换 -->
        <select v-model="mapType" @change="changeMapType" class="map-type-select">
          <option value="satellite">卫星</option>
          <option value="roadnet">路网</option>
          <option value="normal">标准</option>
        </select>
      </div>
    </div>

    <!-- Loca 可视化图层控制 -->
    <div class="loca-controls-panel">
      <div class="control-group">
        <div class="loca-controls">
          <button
            @click="toggleHeatmap"
            :class="['control-btn', { active: showHeatmap }]"
            title="热力图"
          >
            <Flame class="w-4 h-4" />
          </button>
          <button
            @click="toggleScatter"
            :class="['control-btn', { active: showScatter }]"
            title="散点图"
          >
            <MapPin class="w-4 h-4" />
          </button>
          <button
            @click="toggleHexagon"
            :class="['control-btn', { active: showHexagon }]"
            title="蜂窝图"
          >
            <Hexagon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { Plus, Minus, RotateCcw, Flame, MapPin, Hexagon } from 'lucide-vue-next'

// 声明全局类型
declare global {
  interface Window {
    AMap: any
    Loca: any
  }
}

interface Props {
  center?: [number, number]
  zoom?: number
  isDark?: boolean
  showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  center: () => [39.084158, 117.200983], // 天津市中心
  zoom: 10,
  isDark: false,
  showControls: true,
})

const emit = defineEmits<{
  mapReady: [map: any]
  locaReady: [loca: any]
}>()

// 组件状态
const mapContainer = ref<HTMLDivElement>()
const loading = ref(true)
const mapInstance = ref<any>(null)
const locaInstance = ref<any>(null)
const heatmapLayer = ref<any>(null)
const scatterLayer = ref<any>(null)
const hexagonLayer = ref<any>(null)

// 地图配置
const mapType = ref('normal')
const showHeatmap = ref(false)
const showScatter = ref(false)
const showHexagon = ref(false)

// 风险点和围栏数据
const riskMarkers = ref<any[]>([])
const infoWindow = ref<any>(null)
const districtLayer = ref<any>(null)
const tianjinBoundaryPolys = ref<any[]>([])
const sensitiveAreas = ref<any[]>([])
const heatMap = ref<any>(null)

// 高德地图配置
const amapConfig = {
  key: import.meta.env.VITE_AMAP_API_KEY || 'a139182760d79f73aa3b86826382da6f',
  version: '2.0',
  plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.ControlBar', 'AMap.MapType'],
  // 移除Loca配置，使用动态加载
}

// 调试日志：检查环境变量
console.log('🔍 [AMap Debug] 环境变量检查:', {
  VITE_AMAP_API_KEY: import.meta.env.VITE_AMAP_API_KEY,
  VITE_AMAP_SECURITY_KEY: import.meta.env.VITE_AMAP_SECURITY_KEY,
  origin: window.location.origin,
  protocol: window.location.protocol,
  hostname: window.location.hostname,
  port: window.location.port,
})

// 带重试机制的 AMap 加载
const loadAMapWithRetry = async (maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 [AMap Debug] 尝试加载高德地图 JS API (第 ${attempt} 次)`)
      console.log(`🔍 [AMap Debug] API Key: ${amapConfig.key}`)
      console.log(`🔍 [AMap Debug] 版本: ${amapConfig.version}`)
      console.log(`🔍 [AMap Debug] 插件:`, amapConfig.plugins)

      await AMapLoader.load({
        key: amapConfig.key,
        version: amapConfig.version,
        plugins: amapConfig.plugins,
      })
      console.log('✅ [AMap Debug] 高德地图 JS API 加载成功')
      return
    } catch (error) {
      console.warn(`⚠️ [AMap Debug] 高德地图 JS API 加载失败 (第 ${attempt} 次):`, error)

      if (attempt === maxRetries) {
        console.error('❌ [AMap Debug] 高德地图 JS API 加载失败，已达到最大重试次数')
        throw new Error(`高德地图 JS API 加载失败，已重试 ${maxRetries} 次`)
      }

      // 等待更长时间后重试
      const delay = 3000 * attempt
      console.log(`⏳ [AMap Debug] ${delay}ms 后重试...`)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }
}

// 动态加载 Loca 库（仅接受官方 AMap Loca，避免与同名第三方库冲突）
const loadLoca = async () => {
  return new Promise((resolve) => {
    console.log('🔍 [AMap Debug] 开始加载 Loca 库')

    // 若已存在且具备 Container 构造，直接使用
    if (window.Loca && typeof window.Loca.Container === 'function') {
      console.log('✅ [AMap Debug] Loca 库已存在且有效')
      resolve(window.Loca)
      return
    }

    // 直接加载官方远程 Loca 库
    const locaScript = document.createElement('script')
    locaScript.src = `https://webapi.amap.com/loca?v=2.0.0&key=${amapConfig.key}`
    locaScript.async = true
    console.log('🔍 [AMap Debug] 加载官方 Loca 库:', locaScript.src)

    locaScript.onload = () => {
      let tries = 0
      const maxTries = 150
      const check = () => {
        tries++
        const ok = !!(window.Loca && typeof window.Loca.Container === 'function')
        console.log(`🔍 [AMap Debug] 检查 Loca 就绪 (${tries}/${maxTries}):`, {
          ok,
          Loca: !!window.Loca,
          Container: !!(window.Loca && window.Loca.Container),
          HeatmapLayer: !!(window.Loca && window.Loca.HeatmapLayer),
          ScatterLayer: !!(window.Loca && window.Loca.ScatterLayer),
          HexagonLayer: !!(window.Loca && window.Loca.HexagonLayer),
        })
        if (ok) return resolve(window.Loca)
        if (tries < maxTries) return setTimeout(check, 100)
        console.error('❌ [AMap Debug] Loca 库初始化超时')
        resolve(null)
      }
      check()
    }
    locaScript.onerror = (err) => {
      console.error('❌ [AMap Debug] Loca 脚本加载失败:', err)
      resolve(null)
    }
    document.head.appendChild(locaScript)
  })
}

// 初始化地图
const initMap = async () => {
  const startTime = performance.now()

  try {
    // 加载高德地图 JS API，添加重试机制
    await loadAMapWithRetry()

    if (!mapContainer.value) {
      console.warn('地图容器不存在，跳过初始化')
      return
    }

    // 创建地图实例
    mapInstance.value = new window.AMap.Map(mapContainer.value, {
      center: props.center,
      zoom: props.zoom,
      mapStyle: props.isDark ? 'amap://styles/dark' : 'amap://styles/normal',
      viewMode: '3D',
      pitch: 45,
      rotation: 0,
      features: ['bg', 'road', 'building', 'point'],
      // 性能优化配置
      zooms: [3, 20], // 缩放级别范围
      resizeEnable: true,
      dragEnable: true,
      zoomEnable: true,
      doubleClickZoom: true,
      keyboardEnable: false, // 禁用键盘，避免冲突
    })

    // 使用模板样式 'darkblue'
    mapInstance.value.setMapStyle('amap://styles/darkblue')

    // 监听地图事件
    mapInstance.value.on('complete', async () => {
      const loadTime = performance.now() - startTime
      console.log(`✅ [AMap Debug] 高德地图加载完成，耗时: ${loadTime.toFixed(2)}ms`)
      loading.value = false
      emit('mapReady', mapInstance.value)

      // 增强缩放控制
      enhanceZoomControls()

      // 初始化高级功能
      try {
        await initAdvancedFeatures()
      } catch (advancedError) {
        console.warn('⚠️ [AMap Debug] 高级功能初始化失败:', advancedError)
      }

      // 异步加载 Loca 并初始化可视化图层
      try {
        const loca = await loadLoca()
        if (loca) {
          // 创建 Loca 实例
          locaInstance.value = new window.Loca.Container({
            map: mapInstance.value,
          })
          // 初始化可视化图层
          initVisualizationLayers()
          emit('locaReady', locaInstance.value)
        } else {
          console.warn('⚠️ [AMap Debug] Loca 库加载失败，使用基础地图模式')
        }
      } catch (locaError) {
        console.warn('⚠️ [AMap Debug] Loca 库加载失败，使用基础地图模式:', locaError)
        // Loca加载失败时，地图仍然可以正常工作，只是没有可视化图层
      }
    })

    // 错误处理
    mapInstance.value.on('error', (error: any) => {
      console.error('❌ [AMap Debug] 地图加载错误:', error)
      loading.value = false

      // 尝试重新初始化地图
      if (error && error.type === 'network') {
        console.log('🔄 [AMap Debug] 检测到网络错误，尝试重新初始化...')
        setTimeout(() => {
          if (!loading.value) {
            initMap()
          }
        }, 5000)
      }
    })
  } catch (error) {
    const loadTime = performance.now() - startTime
    console.error(`高德地图初始化失败，耗时: ${loadTime.toFixed(2)}ms`, error)
    loading.value = false

    // 可以在这里添加错误重试逻辑
    // retryInitMap()
  }
}

// 初始化可视化图层
const initVisualizationLayers = () => {
  console.log('🔍 [AMap Debug] 开始初始化可视化图层')
  console.log('🔍 [AMap Debug] locaInstance:', locaInstance.value)
  console.log('🔍 [AMap Debug] window.Loca:', window.Loca)

  if (!locaInstance.value || !window.Loca) {
    console.warn('❌ [AMap Debug] Loca 未加载，跳过可视化图层初始化')
    return
  }

  try {
    // 检查必要的Loca类是否存在
    const availableClasses = []
    const requiredClasses = ['HeatmapLayer', 'ScatterLayer', 'HexagonLayer']

    requiredClasses.forEach((cls) => {
      if (window.Loca && window.Loca[cls]) {
        availableClasses.push(cls)
      }
    })

    console.log('🔍 [AMap Debug] 检查 Loca 类:', {
      HeatmapLayer: !!window.Loca.HeatmapLayer,
      ScatterLayer: !!window.Loca.ScatterLayer,
      HexagonLayer: !!window.Loca.HexagonLayer,
      availableClasses: availableClasses,
      totalAvailable: availableClasses.length,
    })

    if (availableClasses.length === 0) {
      console.warn('❌ [AMap Debug] 没有可用的 Loca 可视化类，跳过可视化图层初始化')
      console.log('🔍 [AMap Debug] 可用 Loca 类列表:', Object.keys(window.Loca || {}))
      return
    }

    if (availableClasses.length < requiredClasses.length) {
      console.warn(
        `⚠️ [AMap Debug] 部分 Loca 可视化类缺失 (${availableClasses.length}/${requiredClasses.length})，将只初始化可用的图层`,
      )
    }

    // 条件性创建可视化图层
    try {
      // 热力图层
      if (window.Loca.HeatmapLayer) {
        heatmapLayer.value = new window.Loca.HeatmapLayer({
          map: mapInstance.value,
          loca: locaInstance.value,
          opacity: 0.8,
          visible: false,
        })

        // 配置热力图样式
        if (heatmapLayer.value) {
          heatmapLayer.value.setOptions({
            style: {
              radius: 20,
              opacity: [0, 1],
              color: {
                0.5: '#2c7bb6',
                0.65: '#abd9e9',
                0.7: '#ffffbf',
                0.9: '#fdae61',
                1.0: '#d7191c',
              },
            },
          })
          console.log('✅ [AMap Debug] 热力图层创建成功')
        }
      } else {
        console.warn('⚠️ [AMap Debug] HeatmapLayer 类不可用，跳过热力图层创建')
      }

      // 散点图层
      if (window.Loca.ScatterLayer) {
        scatterLayer.value = new window.Loca.ScatterLayer({
          map: mapInstance.value,
          loca: locaInstance.value,
          opacity: 0.8,
          visible: false,
        })

        // 配置散点图样式
        if (scatterLayer.value) {
          scatterLayer.value.setOptions({
            style: {
              radius: 8,
              color: function (data: any) {
                return data.value > 70 ? '#ff4444' : data.value > 50 ? '#ffaa00' : '#44ff44'
              },
              borderWidth: 2,
              borderColor: '#ffffff',
            },
          })
          console.log('✅ [AMap Debug] 散点图层创建成功')
        }
      } else {
        console.warn('⚠️ [AMap Debug] ScatterLayer 类不可用，跳过散点图层创建')
      }

      // 蜂窝图层
      if (window.Loca.HexagonLayer) {
        hexagonLayer.value = new window.Loca.HexagonLayer({
          map: mapInstance.value,
          loca: locaInstance.value,
          opacity: 0.8,
          visible: false,
        })

        // 配置蜂窝图样式
        if (hexagonLayer.value) {
          hexagonLayer.value.setOptions({
            style: {
              radius: 1000, // 蜂窝大小（米）
              color: {
                0.5: 'rgba(44, 123, 182, 0.6)',
                0.65: 'rgba(171, 217, 233, 0.6)',
                0.7: 'rgba(255, 255, 191, 0.6)',
                0.9: 'rgba(253, 174, 97, 0.6)',
                1.0: 'rgba(215, 25, 28, 0.6)',
              },
              borderWidth: 1,
              borderColor: '#ffffff',
            },
          })
          console.log('✅ [AMap Debug] 蜂窝图层创建成功')
        }
      } else {
        console.warn('⚠️ [AMap Debug] HexagonLayer 类不可用，跳过蜂窝图层创建')
      }
    } catch (layerError) {
      console.error('❌ [AMap Debug] 创建可视化图层时出错:', layerError)
    }

    console.log('可视化图层初始化完成')

    // 添加模拟数据
    addMockData()
  } catch (error) {
    console.error('可视化图层初始化失败:', error)
    // 可视化图层失败不影响基础地图功能
  }
}

// 初始化高级功能
const initAdvancedFeatures = async () => {
  if (!mapInstance.value) return

  try {
    console.log('开始初始化高级地图功能...')

    // 1. 添加天津区域面标注
    await addTianjinDistrictLayer()

    // 2. 添加风险点标记
    addRiskMarkers()

    // 3. 添加敏感区域地理围栏
    addSensitiveAreas()

    // 4. 初始化信息框
    initInfoWindow()

    // 5. 初始化风险热力图
    initRiskHeatmap()

    console.log('高级地图功能初始化完成')
  } catch (error) {
    console.error('高级功能初始化失败:', error)
  }
}

// 添加天津区域面标注
const addTianjinDistrictLayer = async () => {
  if (!mapInstance.value || !window.AMap) return

  try {
    // 加载行政区插件
    await AMapLoader.load({
      key: amapConfig.key,
      version: amapConfig.version,
      plugins: ['AMap.DistrictLayer', 'AMap.DistrictSearch'],
    })

    // 创建行政区图层
    districtLayer.value = new window.AMap.DistrictLayer.Country({
      zIndex: 10,
      adcode: ['120000'], // 天津市行政区划代码
      depth: 2, // 显示到区县级别
      styles: {
        fill: function (properties: any) {
          // 根据行政区级别设置不同颜色
          const level = properties.level
          if (level === 'province') {
            return '#2c7bb6' // 省级 - 蓝色
          } else if (level === 'city') {
            return '#abd9e9' // 市级 - 浅蓝色
          } else {
            return '#ffffbf' // 区县级 - 浅黄色
          }
        },
        'province-stroke': '#ffffff',
        'city-stroke': '#ffffff',
        'county-stroke': '#ffffff',
        'stroke-width': 2,
        'stroke-opacity': 0.8,
      },
    })

    // 添加到地图
    mapInstance.value.add(districtLayer.value)
    console.log('天津区域面标注已添加')

    // 使用行政区查询获取天津市边界并高亮显示
    const ds = new window.AMap.DistrictSearch({
      level: 'city',
      extensions: 'all',
      subdistrict: 0,
    })

    // 若已存在边界多边形，先清理
    if (tianjinBoundaryPolys.value.length) {
      mapInstance.value.remove(tianjinBoundaryPolys.value)
      tianjinBoundaryPolys.value = []
    }

    ds.search('天津市', (status: string, result: any) => {
      if (status !== 'complete' || !result?.districts?.length) return
      const d = result.districts[0]
      const boundaries: any[] = d.boundaries || []
      const polys: any[] = []

      boundaries.forEach((path: any) => {
        const poly = new window.AMap.Polygon({
          path,
          strokeColor: '#93c5fd',
          strokeWeight: 3,
          strokeOpacity: 0.95,
          fillColor: 'rgba(147,197,253,0.20)',
          fillOpacity: 0.4,
          zIndex: 20,
        })
        polys.push(poly)
      })

      if (polys.length) {
        mapInstance.value.add(polys)
        tianjinBoundaryPolys.value = polys
        // 视图自适应到天津市范围
        mapInstance.value.setFitView(polys, false, [10, 10, 10, 10])
      }
    })
  } catch (error) {
    console.error('添加天津区域面标注失败:', error)
  }
}

// 添加风险点标记
const addRiskMarkers = () => {
  if (!mapInstance.value || !window.AMap) return

  // 风险点数据
  const riskPoints = [
    {
      position: [117.200983, 39.084158],
      title: '天津市中心风险点',
      content: '高风险区域 - 数据泄露风险',
      level: 'high',
      type: 'data-leak',
    },
    {
      position: [117.220983, 39.124158],
      title: '和平区风险点',
      content: '中风险区域 - 访问异常',
      level: 'medium',
      type: 'access-anomaly',
    },
    {
      position: [117.240983, 39.104158],
      title: '河东区风险点',
      content: '低风险区域 - 系统故障',
      level: 'low',
      type: 'system-failure',
    },
    {
      position: [117.160983, 39.104158],
      title: '南开区风险点',
      content: '高风险区域 - 传输异常',
      level: 'high',
      type: 'transmission-error',
    },
  ]

  riskPoints.forEach((point, index) => {
    // 创建标记
    const marker = new window.AMap.Marker({
      position: point.position,
      title: point.title,
      icon: getRiskIcon(point.level),
      offset: new window.AMap.Pixel(-16, -32),
      zIndex: 100 + index,
    })

    // 添加点击事件
    marker.on('click', () => {
      showInfoWindow(point, marker)
    })

    // 添加到地图
    mapInstance.value.add(marker)
    riskMarkers.value.push(marker)
  })

  console.log('风险点标记已添加:', riskMarkers.value.length, '个')
}

// 获取风险等级对应的图标
const getRiskIcon = (level: string) => {
  try {
    // 使用安全的 base64 编码函数
    const safeBtoa = (str: string) => {
      try {
        return btoa(unescape(encodeURIComponent(str)))
      } catch (e) {
        console.warn('btoa 编码失败，使用备用方案:', e)
        // 备用方案：移除特殊字符
        return btoa(str.replace(/[^\x00-\x7F]/g, ''))
      }
    }

    const iconUrl =
      level === 'high'
        ? 'data:image/svg+xml;base64,' +
          safeBtoa(
            `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="14" fill="#ff4444" stroke="#ffffff" stroke-width="2"/><text x="16" y="20" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">!</text></svg>`,
          )
        : level === 'medium'
          ? 'data:image/svg+xml;base64,' +
            safeBtoa(
              `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="14" fill="#ffaa00" stroke="#ffffff" stroke-width="2"/><text x="16" y="20" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">!</text></svg>`,
            )
          : 'data:image/svg+xml;base64,' +
            safeBtoa(
              `<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><circle cx="16" cy="16" r="14" fill="#44ff44" stroke="#ffffff" stroke-width="2"/><text x="16" y="20" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">✓</text></svg>`,
            )

    return new window.AMap.Icon({
      image: iconUrl,
      size: new window.AMap.Size(32, 32),
      imageSize: new window.AMap.Size(32, 32),
    })
  } catch (error) {
    console.error('创建风险图标失败:', error)
    // 返回默认图标
    return new window.AMap.Icon({
      image:
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNCIgZmlsbD0iI2NjYyIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4=',
      size: new window.AMap.Size(32, 32),
      imageSize: new window.AMap.Size(32, 32),
    })
  }
}

// 初始化信息框
const initInfoWindow = () => {
  if (!mapInstance.value || !window.AMap) return

  infoWindow.value = new window.AMap.InfoWindow({
    isCustom: true,
    content: '',
    offset: new window.AMap.Pixel(0, -32),
    closeWhenClickMap: true,
  })

  console.log('信息框已初始化')
}

// 显示信息框
const showInfoWindow = (point: any, marker: any) => {
  if (!infoWindow.value || !mapInstance.value) return

  const content = `
    <div class="risk-info-window">
      <div class="info-header">
        <h4>${point.title}</h4>
        <span class="risk-level ${point.level}">${getRiskLevelText(point.level)}</span>
      </div>
      <div class="info-content">
        <p>${point.content}</p>
        <div class="info-details">
          <div class="detail-item">
            <span class="label">类型:</span>
            <span class="value">${getRiskTypeText(point.type)}</span>
          </div>
          <div class="detail-item">
            <span class="label">坐标:</span>
            <span class="value">${point.position[0].toFixed(6)}, ${point.position[1].toFixed(6)}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态:</span>
            <span class="value status-active">活跃</span>
          </div>
        </div>
      </div>
    </div>
  `

  infoWindow.value.setContent(content)
  infoWindow.value.open(mapInstance.value, marker.getPosition())
}

// 获取风险等级文本
const getRiskLevelText = (level: string) => {
  const levels = {
    high: '高风险',
    medium: '中风险',
    low: '低风险',
  }
  return levels[level as keyof typeof levels] || level
}

// 获取风险类型文本
const getRiskTypeText = (type: string) => {
  const types = {
    'data-leak': '数据泄露',
    'access-anomaly': '访问异常',
    'system-failure': '系统故障',
    'transmission-error': '传输异常',
  }
  return types[type as keyof typeof types] || type
}

// 添加敏感区域地理围栏
const addSensitiveAreas = () => {
  if (!mapInstance.value || !window.AMap) return

  // 敏感区域数据
  const sensitiveZones = [
    {
      name: '天津市中心敏感区',
      path: [
        [117.190983, 39.074158],
        [117.210983, 39.074158],
        [117.210983, 39.094158],
        [117.190983, 39.094158],
      ],
      level: 'high',
    },
    {
      name: '和平区重点监管区',
      path: [
        [117.210983, 39.114158],
        [117.230983, 39.114158],
        [117.230983, 39.134158],
        [117.210983, 39.134158],
      ],
      level: 'medium',
    },
  ]

  sensitiveZones.forEach((zone, index) => {
    // 创建多边形
    const polygon = new window.AMap.Polygon({
      path: zone.path,
      strokeColor: getZoneColor(zone.level),
      strokeWeight: 3,
      strokeOpacity: 0.8,
      fillColor: getZoneFillColor(zone.level),
      fillOpacity: 0.3,
      zIndex: 50 + index,
    })

    // 添加点击事件
    polygon.on('click', () => {
      showZoneInfoWindow(zone, polygon)
    })

    // 添加到地图
    mapInstance.value.add(polygon)
    sensitiveAreas.value.push(polygon)
  })

  console.log('敏感区域地理围栏已添加:', sensitiveAreas.value.length, '个')
}

// 获取围栏颜色
const getZoneColor = (level: string) => {
  const colors = {
    high: '#ff4444',
    medium: '#ffaa00',
    low: '#44ff44',
  }
  return colors[level as keyof typeof colors] || '#666666'
}

// 获取围栏填充颜色
const getZoneFillColor = (level: string) => {
  const colors = {
    high: '#ff4444',
    medium: '#ffaa00',
    low: '#44ff44',
  }
  return colors[level as keyof typeof colors] || '#666666'
}

// 显示区域信息框
const showZoneInfoWindow = (zone: any, polygon: any) => {
  if (!infoWindow.value || !mapInstance.value) return

  const bounds = polygon.getBounds()
  const center = bounds.getCenter()

  const content = `
    <div class="zone-info-window">
      <div class="info-header">
        <h4>${zone.name}</h4>
        <span class="zone-level ${zone.level}">${getRiskLevelText(zone.level)}</span>
      </div>
      <div class="info-content">
        <p>地理围栏区域 - 重点监管范围</p>
        <div class="info-details">
          <div class="detail-item">
            <span class="label">监管等级:</span>
            <span class="value">${getRiskLevelText(zone.level)}</span>
          </div>
          <div class="detail-item">
            <span class="label">覆盖面积:</span>
            <span class="value">约 ${calculateZoneArea(zone.path)} 平方公里</span>
          </div>
          <div class="detail-item">
            <span class="label">监控状态:</span>
            <span class="value status-active">正常监控中</span>
          </div>
        </div>
      </div>
    </div>
  `

  infoWindow.value.setContent(content)
  infoWindow.value.open(mapInstance.value, center)
}

// 计算区域面积（简化计算）
const calculateZoneArea = (path: number[][]) => {
  if (path.length < 3) return 0

  // 简化的面积计算（实际应该使用更精确的地理计算）
  const bounds = {
    minLng: Math.min(...path.map((p) => p[0])),
    maxLng: Math.max(...path.map((p) => p[0])),
    minLat: Math.min(...path.map((p) => p[1])),
    maxLat: Math.max(...path.map((p) => p[1])),
  }

  const width = (bounds.maxLng - bounds.minLng) * 111 // 经度每度约111km
  const height = (bounds.maxLat - bounds.minLat) * 111 // 纬度每度约111km

  return Math.round(width * height * 100) / 100
}

// 初始化风险热力图
const initRiskHeatmap = () => {
  if (!mapInstance.value || !window.AMap) return

  try {
    // 加载热力图插件
    AMapLoader.load({
      key: amapConfig.key,
      version: amapConfig.version,
      plugins: ['AMap.HeatMap'],
    })
      .then(() => {
        if (!window.AMap.HeatMap) {
          console.warn('AMap.HeatMap 插件加载失败')
          return
        }

        // 创建热力图
        heatMap.value = new window.AMap.HeatMap(mapInstance.value, {
          radius: 25, // 热力点半径
          opacity: [0, 0.8], // 透明度
          gradient: {
            0.2: '#2c7bb6',
            0.4: '#abd9e9',
            0.6: '#ffffbf',
            0.8: '#fdae61',
            1.0: '#d7191c',
          },
        })

        // 设置热力图数据
        const heatmapData = [
          { lng: 117.200983, lat: 39.084158, count: 100 },
          { lng: 117.220983, lat: 39.124158, count: 80 },
          { lng: 117.240983, lat: 39.104158, count: 90 },
          { lng: 117.160983, lat: 39.104158, count: 70 },
          // 添加更多随机数据点
          ...Array.from({ length: 100 }, () => ({
            lng: 117.2 + (Math.random() - 0.5) * 0.3,
            lat: 39.08 + (Math.random() - 0.5) * 0.3,
            count: Math.floor(Math.random() * 50) + 10,
          })),
        ]

        heatMap.value.setDataSet({
          data: heatmapData,
          max: 100,
        })

        console.log('风险热力图已初始化')
      })
      .catch((error) => {
        console.error('热力图插件加载失败:', error)
      })
  } catch (error) {
    console.error('初始化风险热力图失败:', error)
  }
}

// 添加模拟数据
const addMockData = () => {
  if (!locaInstance.value) return

  // 模拟热力图数据 - 天津市区域热点
  const heatmapData = [
    // 天津市中心热点
    { lng: 117.200983, lat: 39.084158, value: 100 },
    { lng: 117.190983, lat: 39.094158, value: 80 },
    { lng: 117.210983, lat: 39.074158, value: 90 },

    // 和平区热点
    { lng: 117.220983, lat: 39.124158, value: 70 },
    { lng: 117.230983, lat: 39.114158, value: 60 },

    // 河东区热点
    { lng: 117.240983, lat: 39.104158, value: 75 },
    { lng: 117.250983, lat: 39.094158, value: 65 },

    // 南开区热点
    { lng: 117.160983, lat: 39.104158, value: 55 },
    { lng: 117.170983, lat: 39.094158, value: 50 },

    // 随机分布的中小热点
    ...Array.from({ length: 50 }, () => ({
      lng: 117.2 + (Math.random() - 0.5) * 0.2,
      lat: 39.08 + (Math.random() - 0.5) * 0.2,
      value: Math.random() * 40 + 10,
    })),
  ]

  // 模拟散点数据 - 车辆/设备位置
  const scatterData = [
    // 车端节点
    { lng: 117.200983, lat: 39.084158, value: 85, type: 'vehicle' },
    { lng: 117.205983, lat: 39.089158, value: 75, type: 'vehicle' },
    { lng: 117.195983, lat: 39.079158, value: 90, type: 'vehicle' },

    // 云端节点
    { lng: 117.215983, lat: 39.099158, value: 70, type: 'cloud' },
    { lng: 117.185983, lat: 39.069158, value: 65, type: 'cloud' },

    // 随机分布的节点
    ...Array.from({ length: 30 }, () => ({
      lng: 117.2 + (Math.random() - 0.5) * 0.15,
      lat: 39.08 + (Math.random() - 0.5) * 0.15,
      value: Math.random() * 50 + 20,
      type: Math.random() > 0.5 ? 'vehicle' : 'cloud',
    })),
  ]

  // 设置数据到可视化图层
  if (heatmapLayer.value) {
    heatmapLayer.value.setData(heatmapData, {
      lnglat: function (data) {
        return [data.lng, data.lat]
      },
      value: 'value',
    })
    console.log('热力图数据已设置:', heatmapData.length, '个点')
  }

  if (scatterLayer.value) {
    scatterLayer.value.setData(scatterData, {
      lnglat: function (data) {
        return [data.lng, data.lat]
      },
      value: 'value',
    })
    console.log('散点图数据已设置:', scatterData.length, '个点')
  }

  if (hexagonLayer.value) {
    hexagonLayer.value.setData(scatterData, {
      lnglat: function (data) {
        return [data.lng, data.lat]
      },
      value: 'value',
    })
    console.log('蜂窝图数据已设置:', scatterData.length, '个点')
  }

  // 渲染可视化图层
  if (locaInstance.value) {
    locaInstance.value.animate.start()
  }
}

// 地图控制方法
const zoomIn = () => {
  if (mapInstance.value) {
    const currentZoom = mapInstance.value.getZoom()
    mapInstance.value.setZoom(Math.min(20, currentZoom + 1))
  }
}

const zoomOut = () => {
  if (mapInstance.value) {
    const currentZoom = mapInstance.value.getZoom()
    mapInstance.value.setZoom(Math.max(3, currentZoom - 1))
  }
}

const resetView = () => {
  if (mapInstance.value) {
    mapInstance.value.setZoomAndCenter(props.zoom, props.center)
    // 重置地图旋转和俯仰
    mapInstance.value.setRotation(0)
    mapInstance.value.setPitch(45)
  }
}

// 添加键盘快捷键支持
const handleKeydown = (event: KeyboardEvent) => {
  if (!mapInstance.value) return

  switch (event.key) {
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      resetView()
      break
  }
}

// 添加鼠标滚轮缩放优化
const enhanceZoomControls = () => {
  if (!mapInstance.value) return

  // 启用鼠标滚轮缩放
  mapInstance.value.setStatus({ zoomEnable: true })

  // 添加缩放变化监听
  mapInstance.value.on('zoomchange', () => {
    console.log('地图缩放级别:', mapInstance.value?.getZoom())
  })

  // 添加移动监听
  mapInstance.value.on('moveend', () => {
    const center = mapInstance.value?.getCenter()
    console.log('地图中心点:', center)
  })
}

const changeMapType = () => {
  if (!mapInstance.value) return

  let style = 'amap://styles/normal'
  switch (mapType.value) {
    case 'satellite':
      style = 'amap://styles/satellite'
      break
    case 'roadnet':
      style = 'amap://styles/roadnet'
      break
  }

  mapInstance.value.setMapStyle(style)
}

// 可视化图层控制
const toggleHeatmap = () => {
  showHeatmap.value = !showHeatmap.value
  if (heatmapLayer.value) {
    heatmapLayer.value.show()
    if (!showHeatmap.value) {
      heatmapLayer.value.hide()
    }
  }
}

const toggleScatter = () => {
  showScatter.value = !showScatter.value
  if (scatterLayer.value) {
    scatterLayer.value.show()
    if (!showScatter.value) {
      scatterLayer.value.hide()
    }
  }
}

const toggleHexagon = () => {
  showHexagon.value = !showHexagon.value
  if (hexagonLayer.value) {
    hexagonLayer.value.show()
    if (!showHexagon.value) {
      hexagonLayer.value.hide()
    }
  }
}

// 监听主题变化
watch(
  () => props.isDark,
  (newValue) => {
    if (mapInstance.value) {
      const style = newValue ? 'amap://styles/darkblue' : 'amap://styles/normal'
      mapInstance.value.setMapStyle(style)
    }
  },
)

// 监听中心点变化
watch(
  () => props.center,
  (newCenter) => {
    if (mapInstance.value && newCenter) {
      mapInstance.value.setCenter(newCenter)
    }
  },
)

// 监听缩放级别变化
watch(
  () => props.zoom,
  (newZoom) => {
    if (mapInstance.value && newZoom) {
      mapInstance.value.setZoom(newZoom)
    }
  },
)

// 生命周期
onMounted(() => {
  initMap()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)

  if (mapInstance.value) {
    mapInstance.value.destroy()
  }
  if (locaInstance.value) {
    locaInstance.value.destroy()
  }
})
</script>

<style scoped>
.amap-background-container {
  @apply relative w-full h-full overflow-hidden;
}

.amap-container {
  @apply w-full h-full;
}

/* 加载状态 */
.loading-overlay {
  @apply absolute inset-0 bg-black/50 backdrop-blur-sm flex flex-col items-center justify-center z-50;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(0, 237, 255, 0.1);
  border-top-color: #00edff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-overlay p {
  @apply mt-4 text-white text-lg;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 控制面板 */
.map-controls-panel,
.loca-controls-panel {
  @apply absolute z-10;
}

/* 将控制面板移动到中间底部区域，避免被图表遮挡 */
.map-controls-panel {
  @apply bottom-8 left-1/2 transform -translate-x-1/2;
}

.loca-controls-panel {
  @apply bottom-8 right-8;
}

.loca-controls {
  @apply flex gap-2;
}

.control-group {
  @apply bg-black/20 backdrop-blur-md rounded-lg p-3 mb-2 flex items-center gap-3;
}

.zoom-controls {
  @apply flex flex-col gap-1;
}

.zoom-btn {
  @apply p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-md transition-colors duration-200 text-white;
}

.reset-btn {
  @apply p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-md transition-colors duration-200 text-white;
}

.control-btn.active {
  @apply bg-blue-500/80 hover:bg-blue-600/80;
}

.map-type-select {
  @apply bg-white/10 border border-white/20 rounded-md px-3 py-2 text-white text-sm;
  background: transparent;
}

.map-type-select option {
  @apply bg-gray-800 text-white;
}

/* 深色主题适配 */
[data-theme='dark'] .control-btn {
  @apply text-gray-200;
}

[data-theme='dark'] .map-type-select {
  @apply text-gray-200;
}

/* 信息框样式 */
.risk-info-window,
.zone-info-window {
  min-width: 280px;
  max-width: 320px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.risk-info-window .info-header,
.zone-info-window .info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, rgba(26, 211, 255, 0.1), rgba(0, 237, 255, 0.05));
}

.risk-info-window .info-header h4,
.zone-info-window .info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a365d;
}

.risk-level,
.zone-level {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.risk-level.high,
.zone-level.high {
  background: #fee2e2;
  color: #dc2626;
}

.risk-level.medium,
.zone-level.medium {
  background: #fef3c7;
  color: #d97706;
}

.risk-level.low,
.zone-level.low {
  background: #d1fae5;
  color: #059669;
}

.risk-info-window .info-content,
.zone-info-window .info-content {
  padding: 12px 16px;
}

.risk-info-window .info-content p,
.zone-info-window .info-content p {
  margin: 0 0 12px 0;
  color: #4a5568;
  font-size: 14px;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-item .label {
  font-weight: 500;
  color: #718096;
  font-size: 13px;
}

.detail-item .value {
  font-weight: 600;
  color: #2d3748;
  font-size: 13px;
}

.status-active {
  color: #059669 !important;
}

.status-inactive {
  color: #dc2626 !important;
}

/* 深色主题信息框适配 */
[data-theme='dark'] .risk-info-window,
[data-theme='dark'] .zone-info-window {
  background: rgba(26, 32, 44, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .risk-info-window .info-header,
[data-theme='dark'] .zone-info-window .info-header {
  background: linear-gradient(135deg, rgba(26, 211, 255, 0.15), rgba(0, 237, 255, 0.08));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .risk-info-window .info-header h4,
[data-theme='dark'] .zone-info-window .info-header h4 {
  color: #e2e8f0;
}

[data-theme='dark'] .risk-info-window .info-content p,
[data-theme='dark'] .zone-info-window .info-content p {
  color: #a0aec0;
}

[data-theme='dark'] .detail-item .label {
  color: #718096;
}

[data-theme='dark'] .detail-item .value {
  color: #e2e8f0;
}
</style>
