<template>
  <ChartContainer
    :option="chartOption"
    :height="height"
    :color-scheme="colorScheme"
    :class="class"
    @click="handleClick"
    @mouseover="handleMouseover"
    @mouseout="handleMouseout"
  />
</template>

<script setup lang="ts" generic="<PERSON><PERSON> extends Record<string, unknown>">
import { computed } from 'vue'
import ChartContainer from '@/components/charts/ChartContainer.vue'
import { CHART_COLOR_SCHEMES } from '@/lib/chart-themes'

interface Props {
  data: Datum[]
  angleAccessor?: (d: Datum) => number
  colorAccessor?: (d: Datum, i: number) => string
  centralLabel?: string
  centralSubLabel?: string
  showBackground?: boolean
  radius?: number
  arcWidth?: number
  cornerRadius?: number
  padAngle?: number
  sortFunction?: (a: Datum, b: Datum) => number
  height?: number
  class?: string
  colorScheme?: 'primary' | 'risk' | 'enterprise' | 'stages'
  // Event handlers
  onClick?: (params: any) => void
  onMouseover?: (params: any) => void
  onMouseout?: (params: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  showBackground: true,
  height: 280,
  colorScheme: 'primary',
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
}>()

// Convert data to ECharts format
const chartData = computed(() => {
  if (!props.data || !Array.isArray(props.data)) {
    return []
  }

  return props.data.map((item, index) => ({
    name: typeof item === 'object' && 'name' in item ? String(item.name) : `Item ${index + 1}`,
    value: props.angleAccessor
      ? props.angleAccessor(item)
      : typeof item === 'object' && 'value' in item
        ? Number(item.value)
        : 1,
    itemStyle: {
      color: props.colorAccessor ? props.colorAccessor(item, index) : undefined,
    },
  }))
})

// ECharts pie chart configuration
const chartOption = computed(() => {
  const colors = getColorScheme(props.colorScheme)
  const radius = props.radius ? [props.radius * 0.6, props.radius] : ['45%', '70%']

  const option: any = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      textStyle: {
        fontSize: 12,
      },
    },
    series: [
      {
        name: '数据',
        type: 'pie',
        radius: radius,
        center: ['50%', '45%'],
        data: chartData.value,
        avoidLabelOverlap: true,
        padAngle: props.padAngle || 0,
        itemStyle: {
          borderRadius: props.cornerRadius || 4,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          formatter: '{b}: {d}%',
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 8,
        },
        emphasis: {
          scale: true,
          scaleSize: 5,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  // Add central label if provided
  if (props.centralLabel || props.centralSubLabel) {
    option.graphic = {
      type: 'text',
      left: 'center',
      top: 'center',
      style: {
        text: props.centralLabel || '',
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
      },
    }
  }

  return option
})

// Get color scheme
function getColorScheme(scheme: Props['colorScheme']) {
  switch (scheme) {
    case 'risk':
      return CHART_COLOR_SCHEMES.risk
    case 'enterprise':
      return CHART_COLOR_SCHEMES.enterprise
    case 'stages':
      return CHART_COLOR_SCHEMES.stages
    default:
      return CHART_COLOR_SCHEMES.primary
  }
}

// Event handlers
const handleClick = (params: any) => {
  emit('click', params)
  props.onClick?.(params)
}

const handleMouseover = (params: any) => {
  emit('mouseover', params)
  props.onMouseover?.(params)
}

const handleMouseout = (params: any) => {
  emit('mouseout', params)
  props.onMouseout?.(params)
}
</script>
