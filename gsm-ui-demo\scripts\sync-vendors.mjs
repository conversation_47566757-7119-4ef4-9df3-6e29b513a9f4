#!/usr/bin/env node
import { mkdir, copyFile, access } from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

async function main() {
  const __dirname = path.dirname(fileURLToPath(import.meta.url))
  const projectRoot = path.resolve(__dirname, '..')
  const src = path.join(projectRoot, 'node_modules', 'echarts', 'dist', 'echarts.min.js')
  const outDir = path.join(projectRoot, 'public', 'iframe', 'vendor')
  const out = path.join(outDir, 'echarts.min.js')
  // 新增：Lucide UMD 路径与输出位置
  const lucideSrc = path.join(projectRoot, 'node_modules', 'lucide', 'dist', 'umd', 'lucide.min.js')
  const lucideOutDir = path.join(projectRoot, 'public', 'assets', 'icons')
  const lucideOut = path.join(lucideOutDir, 'lucide.min.js')

  try {
    await access(src)
  } catch {
    console.error('[sync-vendors] Cannot find ECharts UMD at:', src)
    console.error('Please ensure `echarts` is installed in dependencies.')
    process.exit(1)
  }

  await mkdir(outDir, { recursive: true })
  await copyFile(src, out)
  console.log(`[sync-vendors] Copied ${path.relative(projectRoot, src)} -> ${path.relative(projectRoot, out)}`)

  // 新增：尝试复制 Lucide UMD（不存在则仅警告，避免中断构建）
  try {
    await access(lucideSrc)
    await mkdir(lucideOutDir, { recursive: true })
    await copyFile(lucideSrc, lucideOut)
    console.log(`[sync-vendors] Copied ${path.relative(projectRoot, lucideSrc)} -> ${path.relative(projectRoot, lucideOut)}`)
  } catch {
    console.warn('[sync-vendors] Lucide UMD not found in node_modules. If icons fail to load, CDN fallback will be used.')
  }
}

main().catch((err) => {
  console.error('[sync-vendors] Failed:', err)
  process.exit(1)
})

