#!/usr/bin/env node
import { mkdir, copyFile, access } from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

async function main() {
  const __dirname = path.dirname(fileURLToPath(import.meta.url))
  const projectRoot = path.resolve(__dirname, '..')
  const src = path.join(projectRoot, 'node_modules', 'echarts', 'dist', 'echarts.min.js')
  const outDir = path.join(projectRoot, 'public', 'iframe', 'vendor')
  const out = path.join(outDir, 'echarts.min.js')

  try {
    await access(src)
  } catch {
    console.error('[sync-vendors] Cannot find ECharts UMD at:', src)
    console.error('Please ensure `echarts` is installed in dependencies.')
    process.exit(1)
  }

  await mkdir(outDir, { recursive: true })
  await copyFile(src, out)
  console.log(`[sync-vendors] Copied ${path.relative(projectRoot, src)} -> ${path.relative(projectRoot, out)}`)
}

main().catch((err) => {
  console.error('[sync-vendors] Failed:', err)
  process.exit(1)
})

