/**
 * 高德地图 JS API Loader 类型声明
 * 为 @amap/amap-jsapi-loader 包提供 TypeScript 类型支持
 */

declare module '@amap/amap-jsapi-loader' {
  /**
   * 高德地图 JS API 加载配置选项
   */
  interface LoadOptions {
    /** API Key，必填 */
    key: string
    /** API 版本，默认为 '1.4.15' */
    version?: string
    /** 需要加载的插件列表 */
    plugins?: string[]
    /** AMapUI 组件库版本 */
    AMapUI?: {
      version?: string
      plugins?: string[]
    }
    /** Loca 数据可视化库版本 */
    Loca?: {
      version?: string
    }
    /** 是否加载 AMapUI，默认为 false */
    uiVersion?: string
    /** 安全密钥，用于安全验证 */
    securityJsCode?: string
    /** 服务域名，默认为 'webapi.amap.com' */
    serviceHost?: string
  }

  /**
   * 高德地图 JS API Loader 主类
   */
  class AMapLoader {
    /**
     * 加载高德地图 JS API
     * @param options 加载配置选项
     * @returns Promise，加载完成后 resolve
     */
    static load(options: LoadOptions): Promise<void>

    /**
     * 重置加载状态，允许重新加载
     */
    static reset(): void
  }

  export default AMapLoader
}

/**
 * 高德地图全局对象类型声明
 * 扩展 Window 接口以包含高德地图相关的全局变量
 */
declare global {
  interface Window {
    /** 高德地图主对象 */
    AMap: typeof AMap
    /** Loca 数据可视化库 */
    Loca: typeof Loca
    /** AMapUI 组件库 */
    AMapUI: any
  }

  /**
   * 高德地图主命名空间
   */
  namespace AMap {
    /**
     * 地图构造函数选项
     */
    interface MapOptions {
      /** 地图中心点坐标 */
      center?: [number, number] | LngLat
      /** 地图缩放级别 */
      zoom?: number
      /** 地图样式 */
      mapStyle?: string
      /** 视图模式：'2D' | '3D' */
      viewMode?: '2D' | '3D'
      /** 俯仰角度（3D模式下有效） */
      pitch?: number
      /** 旋转角度 */
      rotation?: number
      /** 地图显示的图层 */
      features?: string[]
      /** 缩放级别范围 */
      zooms?: [number, number]
      /** 是否允许拖拽 */
      dragEnable?: boolean
      /** 是否允许缩放 */
      zoomEnable?: boolean
      /** 是否允许双击缩放 */
      doubleClickZoom?: boolean
      /** 是否允许键盘操作 */
      keyboardEnable?: boolean
      /** 是否允许调整大小 */
      resizeEnable?: boolean
    }

    /**
     * 地图类
     */
    class Map {
      constructor(container: string | HTMLElement, options?: MapOptions)

      /** 设置地图中心点 */
      setCenter(center: [number, number] | LngLat): void
      /** 获取地图中心点 */
      getCenter(): LngLat
      /** 设置地图缩放级别 */
      setZoom(zoom: number): void
      /** 获取地图缩放级别 */
      getZoom(): number
      /** 设置地图样式 */
      setMapStyle(style: string): void
      /** 添加事件监听 */
      on(event: string, callback: (e: any) => void): void
      /** 移除事件监听 */
      off(event: string, callback: (e: any) => void): void
      /** 销毁地图 */
      destroy(): void
      /** 添加图层 */
      add(layer: any): void
      /** 移除图层 */
      remove(layer: any): void
    }

    /**
     * 经纬度坐标类
     */
    class LngLat {
      constructor(lng: number, lat: number)
      lng: number
      lat: number
    }

    /**
     * 热力图插件
     */
    class HeatMap {
      constructor(map: Map, options?: any)
      setDataSet(data: any): void
      show(): void
      hide(): void
    }

    /**
     * 行政区图层
     */
    namespace DistrictLayer {
      class Country {
        constructor(options?: any)
        setMap(map: Map): void
      }
    }

    /**
     * 行政区搜索
     */
    class DistrictSearch {
      constructor(options?: any)
      search(keyword: string, callback: (status: string, result: any) => void): void
    }

    /**
     * 比例尺控件
     */
    class Scale {
      constructor(options?: any)
    }

    /**
     * 工具条控件
     */
    class ToolBar {
      constructor(options?: any)
    }

    /**
     * 控制条控件
     */
    class ControlBar {
      constructor(options?: any)
    }

    /**
     * 地图类型控件
     */
    class MapType {
      constructor(options?: any)
    }
  }

  /**
   * Loca 数据可视化库命名空间
   */
  namespace Loca {
    /**
     * Loca 容器
     */
    class Container {
      constructor(options: { map: AMap.Map })
      add(layer: any): void
      remove(layer: any): void
      render(): void
    }

    /**
     * 热力图图层
     */
    class HeatmapLayer {
      constructor(options?: any)
      setData(data: any[]): void
      setOptions(options: any): void
      show(): void
      hide(): void
    }

    /**
     * 散点图图层
     */
    class ScatterLayer {
      constructor(options?: any)
      setData(data: any[]): void
      setOptions(options: any): void
      show(): void
      hide(): void
    }

    /**
     * 蜂窝图图层
     */
    class HexagonLayer {
      constructor(options?: any)
      setData(data: any[]): void
      setOptions(options: any): void
      show(): void
      hide(): void
    }
  }
}
