<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          查看和管理企业备案信息记录，支持备案状态跟踪、审批结果查看和资料下载
        </p>
      </div>
      <Button @click="handleCreateFiling" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新建备案
      </Button>
    </div>

    <!-- 备案列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>备案记录列表</span>
          <Badge variant="outline"> 共 {{ filteredFilings.length }} 条记录 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <!-- 筛选条件 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>备案编号</TableHead>
                <TableHead>备案类型</TableHead>
                <TableHead>审批状态</TableHead>
                <TableHead>提交时间</TableHead>
                <TableHead>审批时间</TableHead>
                <TableHead>审批机构</TableHead>
                <TableHead>备注信息</TableHead>
                <TableHead class="w-[120px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedFilings.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无备案记录
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedFilings"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <span class="font-mono text-sm">{{ item.filingNumber }}</span>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{{ item.filingType }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ item.submitAt }}</TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{
                  item.approvalAt || '-'
                }}</TableCell>
                <TableCell class="text-sm">{{ item.approvalOrg }}</TableCell>
                <TableCell class="max-w-[200px]">
                  <div v-if="item.remark" class="text-sm line-clamp-2" :title="item.remark">
                    {{ item.remark }}
                  </div>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleView(item.id)">
                        <Eye class="w-4 h-4 mr-2" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '待完善' || item.status === '审核不通过'"
                        @click="handleEdit(item.id)"
                      >
                        <Edit class="w-4 h-4 mr-2" />
                        重新填报
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleDownload(item.id)">
                        <Download class="w-4 h-4 mr-2" />
                        下载资料
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="item.status === '审核不通过'"
                        @click="handleAppeal(item.id)"
                        class="text-orange-600"
                      >
                        <AlertCircle class="w-4 h-4 mr-2" />
                        申诉审核
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '待提交'"
                        @click="handleDelete(item.id)"
                        class="text-red-600"
                      >
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredFilings.length) }} 条， 共
            {{ filteredFilings.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>备案详情</DialogTitle>
          <DialogDescription> 备案编号：{{ selectedFiling?.filingNumber }} </DialogDescription>
        </DialogHeader>

        <div v-if="selectedFiling" class="space-y-4">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div class="text-xs text-muted-foreground">备案类型</div>
              <div class="text-base font-semibold">{{ selectedFiling.filingType }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">审批状态</div>
              <div class="text-base font-semibold">{{ selectedFiling.status }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">提交时间</div>
              <div class="text-base font-semibold">{{ selectedFiling.submitAt }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">审批时间</div>
              <div class="text-base font-semibold">{{ selectedFiling.approvalAt || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">审批机构</div>
              <div class="text-base font-semibold">{{ selectedFiling.approvalOrg }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">审批人员</div>
              <div class="text-base font-semibold">{{ selectedFiling.approvalPerson || '-' }}</div>
            </div>
          </div>

          <!-- 备注信息 -->
          <div v-if="selectedFiling.remark" class="space-y-2">
            <div class="text-xs text-muted-foreground">审批意见</div>
            <div class="text-sm p-3 bg-muted rounded-lg">{{ selectedFiling.remark }}</div>
          </div>

          <!-- 备案进度 -->
          <div class="space-y-3">
            <div class="text-xs text-muted-foreground">备案进度</div>
            <div class="space-y-2">
              <div
                v-for="(step, index) in getFilingSteps(selectedFiling)"
                :key="index"
                class="flex items-center gap-3 p-2 rounded"
                :class="{
                  'bg-primary/10': step.isActive,
                  'bg-muted/30': !step.isActive && step.isCompleted,
                }"
              >
                <div
                  class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium"
                  :class="{
                    'bg-primary text-primary-foreground': step.isActive,
                    'bg-green-500 text-white': step.isCompleted && !step.isActive,
                    'bg-muted text-muted-foreground': !step.isCompleted && !step.isActive,
                  }"
                >
                  {{ index + 1 }}
                </div>
                <div class="flex-1">
                  <div class="text-base font-semibold">{{ step.title }}</div>
                  <div v-if="step.time" class="text-xs text-muted-foreground">{{ step.time }}</div>
                </div>
                <div v-if="step.isActive" class="text-xs text-primary font-medium">进行中</div>
                <div v-else-if="step.isCompleted" class="text-xs text-green-600 font-medium">
                  已完成
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-2">
            <Button variant="outline" @click="detailOpen = false">关闭</Button>
            <Button @click="handleDownload(selectedFiling.id)">
              <Download class="w-4 h-4 mr-2" />
              下载资料
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { AlertCircle, Download, Edit, Eye, MoreHorizontal, Plus, Trash2 } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

const router = useRouter()

type FilingType =
  | '企业基本信息'
  | '测绘资质信息'
  | '防控制度措施'
  | '防控技术措施'
  | '车辆备案信息'
  | '数据处理活动'
type FilingStatus = '待提交' | '待完善' | '审核中' | '审核通过' | '审核不通过'

interface FilingItem {
  id: string
  filingNumber: string
  filingType: FilingType
  status: FilingStatus
  submitAt: string
  approvalAt?: string
  approvalOrg: string
  approvalPerson?: string
  remark?: string
  createdAt: string
  updatedAt: string
}

interface FilingStep {
  title: string
  time?: string
  isCompleted: boolean
  isActive: boolean
}

// 筛选条件
const filters = ref({
  filingNumber: '',
  filingType: 'ALL' as 'ALL' | FilingType,
  status: 'ALL' as 'ALL' | FilingStatus,
  timeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'filingNumber',
    label: '备案编号',
    type: 'input',
    placeholder: '请输入备案编号',
  },
  {
    key: 'filingType',
    label: '备案类型',
    type: 'select',
    placeholder: '请选择备案类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '企业基本信息', value: '企业基本信息' },
      { label: '测绘资质信息', value: '测绘资质信息' },
      { label: '防控制度措施', value: '防控制度措施' },
      { label: '防控技术措施', value: '防控技术措施' },
      { label: '车辆备案信息', value: '车辆备案信息' },
      { label: '数据处理活动', value: '数据处理活动' },
    ],
  },
  {
    key: 'status',
    label: '审批状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '待提交', value: '待提交' },
      { label: '待完善', value: '待完善' },
      { label: '审核中', value: '审核中' },
      { label: '审核通过', value: '审核通过' },
      { label: '审核不通过', value: '审核不通过' },
    ],
  },
  {
    key: 'timeRange',
    label: '提交时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 弹窗状态
const detailOpen = ref(false)
const selectedFiling = ref<FilingItem | null>(null)

// Mock数据
const filings = ref<FilingItem[]>([
  {
    id: 'F-2025-001',
    filingNumber: 'BA202501001',
    filingType: '企业基本信息',
    status: '审核通过',
    submitAt: '2025-08-15 10:30',
    approvalAt: '2025-08-18 14:20',
    approvalOrg: '国家测绘地理信息局',
    approvalPerson: '李审核员',
    remark: '企业基本信息完整，符合备案要求。',
    createdAt: '2025-08-15 10:30',
    updatedAt: '2025-08-18 14:20',
  },
  {
    id: 'F-2025-002',
    filingNumber: 'BA202501002',
    filingType: '测绘资质信息',
    status: '审核中',
    submitAt: '2025-08-20 16:45',
    approvalOrg: '国家测绘地理信息局',
    createdAt: '2025-08-20 16:45',
    updatedAt: '2025-08-20 16:45',
  },
  {
    id: 'F-2025-003',
    filingNumber: 'BA202501003',
    filingType: '防控制度措施',
    status: '待完善',
    submitAt: '2025-08-19 09:15',
    approvalOrg: '国家测绘地理信息局',
    approvalPerson: '王审核员',
    remark: '防控制度不够完善，请补充数据安全管理制度和应急预案。',
    createdAt: '2025-08-19 09:15',
    updatedAt: '2025-08-21 11:30',
  },
  {
    id: 'F-2025-004',
    filingNumber: 'BA202501004',
    filingType: '车辆备案信息',
    status: '审核不通过',
    submitAt: '2025-08-17 14:20',
    approvalAt: '2025-08-19 10:30',
    approvalOrg: '国家测绘地理信息局',
    approvalPerson: '张审核员',
    remark: '车辆信息不完整，缺少车载设备技术参数和数据采集范围说明。',
    createdAt: '2025-08-17 14:20',
    updatedAt: '2025-08-19 10:30',
  },
  {
    id: 'F-2025-005',
    filingNumber: 'BA202501005',
    filingType: '数据处理活动',
    status: '待提交',
    submitAt: '2025-08-21 11:45',
    approvalOrg: '国家测绘地理信息局',
    createdAt: '2025-08-21 11:45',
    updatedAt: '2025-08-21 11:45',
  },
])

// 过滤后的数据
const filteredFilings = computed(() => {
  return filings.value.filter((filing) => {
    if (filters.value.filingNumber && !filing.filingNumber.includes(filters.value.filingNumber)) {
      return false
    }
    if (filters.value.filingType !== 'ALL' && filing.filingType !== filters.value.filingType) {
      return false
    }
    if (filters.value.status !== 'ALL' && filing.status !== filters.value.status) {
      return false
    }
    if (filters.value.timeRange && filters.value.timeRange[0] && filters.value.timeRange[1]) {
      const startDate = new Date(
        filters.value.timeRange[0].getFullYear(),
        filters.value.timeRange[0].getMonth(),
        filters.value.timeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.value.timeRange[1].getFullYear(),
        filters.value.timeRange[1].getMonth(),
        filters.value.timeRange[1].getDate(),
        23,
        59,
        59,
      )
      const submitDate = new Date(filing.submitAt.replace(/-/g, '/'))
      if (submitDate < startDate || submitDate > endDate) return false
    }
    return true
  })
})

// 分页数据
const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredFilings.value.length / pageSize.value)),
)
const pagedFilings = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredFilings.value.slice(start, start + pageSize.value)
})

// 获取备案流程步骤
const getFilingSteps = (filing: FilingItem): FilingStep[] => {
  const steps: FilingStep[] = [
    {
      title: '提交申请',
      time: filing.submitAt,
      isCompleted: true,
      isActive: false,
    },
    {
      title: '资料审核',
      time: filing.status === '审核中' ? '进行中' : filing.approvalAt,
      isCompleted: ['审核通过', '审核不通过', '待完善'].includes(filing.status),
      isActive: filing.status === '审核中',
    },
    {
      title: '审核结果',
      time: filing.approvalAt,
      isCompleted: ['审核通过', '审核不通过'].includes(filing.status),
      isActive: filing.status === '待完善',
    },
  ]

  if (filing.status === '审核通过') {
    steps.push({
      title: '备案完成',
      time: filing.approvalAt,
      isCompleted: true,
      isActive: false,
    })
  }

  return steps
}

// 搜索处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
  console.log('搜索条件:', filters.value)
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    filingNumber: '',
    filingType: 'ALL',
    status: 'ALL',
    timeRange: null,
  }
  currentPage.value = 1
}

// 导出数据
const exportData = () => {
  const headers = [
    '序号',
    '备案编号',
    '备案类型',
    '审批状态',
    '提交时间',
    '审批时间',
    '审批机构',
    '备注信息',
  ]
  const rows = filteredFilings.value.map((filing, index) => [
    (index + 1).toString(),
    filing.filingNumber,
    filing.filingType,
    filing.status,
    filing.submitAt,
    filing.approvalAt || '',
    filing.approvalOrg,
    filing.remark || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `备案管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

// 操作处理函数
const handleCreateFiling = () => {
  router.push('/corp/filing/form/basic-info')
}

const handleView = (id: string) => {
  const filing = filings.value.find((f) => f.id === id)
  if (filing) {
    selectedFiling.value = filing
    detailOpen.value = true
  }
}

const handleEdit = (id: string) => {
  const filing = filings.value.find((f) => f.id === id)
  if (filing) {
    // 根据备案类型跳转到对应的表单页面
    const routeMap = {
      企业基本信息: '/corp/filing/form/basic-info',
      测绘资质信息: '/corp/filing/form/qualification',
      防控制度措施: '/corp/filing/form/security-policy',
      防控技术措施: '/corp/filing/form/security-tech',
      车辆备案信息: '/corp/filing/form/vehicle-info',
      数据处理活动: '/corp/filing/form/data-activity',
    }
    const route = routeMap[filing.filingType]
    if (route) {
      router.push(`${route}?edit=${id}`)
    }
  }
}

const handleDownload = (id: string) => {
  console.log('下载资料:', id)
}

const handleAppeal = (id: string) => {
  console.log('申诉审核:', id)
}

const handleDelete = (id: string) => {
  const index = filings.value.findIndex((f) => f.id === id)
  if (index > -1) {
    filings.value.splice(index, 1)
  }
  console.log('删除备案:', id)
}

// Badge样式
const getStatusVariant = (status: FilingStatus) => {
  switch (status) {
    case '待提交':
      return 'outline'
    case '待完善':
      return 'secondary'
    case '审核中':
      return 'default'
    case '审核通过':
      return 'default'
    case '审核不通过':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
