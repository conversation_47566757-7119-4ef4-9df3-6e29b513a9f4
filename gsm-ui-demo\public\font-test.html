<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>阿里妈妈字体测试</title>
    <link rel="stylesheet" href="/assets/fonts/inter/inter.css" />
    <style>
      body {
        margin: 0;
        padding: 20px;
        background: #f5f5f5;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .font-test {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
      }

      .alimama-font {
        font-family:
          'PingFang SC',
          'Hiragino Sans GB',
          'Microsoft YaHei',
          'WenQuanYi Micro Hei',
          'Helvetica Neue',
          -apple-system,
          BlinkMacSystemFont,
          'Segoe UI',
          Roboto,
          Arial,
          sans-serif;
      }

      .inter-font {
        font-family:
          'Inter',
          -apple-system,
          BlinkMacSystemFont,
          'Segoe UI',
          Roboto,
          sans-serif;
      }

      .system-font {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }
      h2 {
        font-size: 1.8rem;
        margin-bottom: 10px;
        color: #333;
      }
      h3 {
        font-size: 1.2rem;
        margin-bottom: 10px;
        color: #666;
      }
      p {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 10px;
      }

      .weight-test {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 15px;
      }

      .weight-item {
        flex: 1;
        min-width: 150px;
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
      }

      .font-100 {
        font-weight: 100;
      }
      .font-200 {
        font-weight: 200;
      }
      .font-300 {
        font-weight: 300;
      }
      .font-400 {
        font-weight: 400;
      }
      .font-500 {
        font-weight: 500;
      }
      .font-600 {
        font-weight: 600;
      }
      .font-700 {
        font-weight: 700;
      }
      .font-800 {
        font-weight: 800;
      }
      .font-900 {
        font-weight: 900;
      }

      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: 500;
      }

      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>字体加载测试页面</h1>

      <div id="font-status"></div>

      <div class="font-test">
        <h2 class="alimama-font">优化中文字体测试</h2>
        <h3>品牌标题：地理信息安全监测平台</h3>
        <p class="alimama-font">贯彻落实国家地理信息安全防控要求</p>
        <p class="alimama-font">构建地理信息安全风险防控新体系</p>
        <p class="alimama-font">多级联动 · 智慧监管 · 合规护航</p>

        <div class="weight-test">
          <div class="weight-item">
            <div class="alimama-font font-100">细体 100</div>
            <small>font-weight: 100</small>
          </div>
          <div class="weight-item">
            <div class="alimama-font font-300">轻体 300</div>
            <small>font-weight: 300</small>
          </div>
          <div class="weight-item">
            <div class="alimama-font font-400">常规 400</div>
            <small>font-weight: 400</small>
          </div>
          <div class="weight-item">
            <div class="alimama-font font-500">中等 500</div>
            <small>font-weight: 500</small>
          </div>
          <div class="weight-item">
            <div class="alimama-font font-700">粗体 700</div>
            <small>font-weight: 700</small>
          </div>
          <div class="weight-item">
            <div class="alimama-font font-900">特粗 900</div>
            <small>font-weight: 900</small>
          </div>
        </div>
      </div>

      <div class="font-test">
        <h2 class="inter-font">Inter 字体测试</h2>
        <p class="inter-font">Geospatial Security Monitoring Platform</p>
        <p class="inter-font">Enterprise Access · Policy Interpretation · Compliance Navigation</p>
      </div>

      <div class="font-test">
        <h2 class="system-font">系统字体对比</h2>
        <p class="system-font">这是系统默认字体显示效果</p>
        <p class="system-font">用于对比字体加载是否成功</p>
      </div>
    </div>

    <script>
      // 检测字体是否加载成功
      function checkFontLoading() {
        const statusDiv = document.getElementById('font-status')

        // 检查阿里妈妈字体
        if (document.fonts) {
          document.fonts.ready.then(() => {
            const alimamaLoaded =
              document.fonts.check('16px "PingFang SC"') ||
              document.fonts.check('16px "Microsoft YaHei"') ||
              document.fonts.check('16px "Hiragino Sans GB"')
            const interLoaded = document.fonts.check('16px Inter')

            let status = ''
            if (alimamaLoaded) {
              status += '<div class="status success">✅ 优化中文字体加载成功</div>'
            } else {
              status += '<div class="status error">❌ 中文字体加载失败</div>'
            }

            if (interLoaded) {
              status += '<div class="status success">✅ Inter 字体加载成功</div>'
            } else {
              status += '<div class="status error">❌ Inter 字体加载失败</div>'
            }

            statusDiv.innerHTML = status
          })
        } else {
          statusDiv.innerHTML = '<div class="status error">⚠️ 浏览器不支持字体检测API</div>'
        }
      }

      // 页面加载完成后检查字体
      window.addEventListener('load', checkFontLoading)
    </script>
  </body>
</html>
