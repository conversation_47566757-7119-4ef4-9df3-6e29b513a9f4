<template>
  <div class="space-y-6">
    <!-- 安全防护概览 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Shield class="h-5 w-5" />
            <span>安全防护措施概览</span>
          </div>
          <Badge
            :variant="
              securityLevel === '高' ? 'default' : securityLevel === '中' ? 'secondary' : 'outline'
            "
          >
            {{ securityLevel }}安全等级
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ securityStats.policies }}</div>
            <div class="text-sm text-green-600">制度体系</div>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ securityStats.technologies }}</div>
            <div class="text-sm text-blue-600">技术措施</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ securityStats.certifications }}</div>
            <div class="text-sm text-purple-600">资质认证</div>
          </div>
          <div class="text-center p-4 bg-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-orange-600">{{ securityStats.assessments }}</div>
            <div class="text-sm text-orange-600">安全评估</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 组织架构 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Users class="h-5 w-5" />
          <span>数据安全组织架构</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div class="space-y-3">
              <Label class="text-sm font-medium">数据安全负责人</Label>
              <div class="p-3 border rounded-lg">
                <div class="flex items-center space-x-3">
                  <div
                    class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center"
                  >
                    <User class="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <div class="font-medium">{{ organizationStructure.securityOfficer.name }}</div>
                    <div class="text-sm text-muted-foreground">
                      {{ organizationStructure.securityOfficer.position }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      {{ organizationStructure.securityOfficer.contact }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-3">
              <Label class="text-sm font-medium">负责部门</Label>
              <div class="p-3 border rounded-lg">
                <div class="font-medium">{{ organizationStructure.department.name }}</div>
                <div class="text-sm text-muted-foreground mt-1">
                  {{ organizationStructure.department.description }}
                </div>
                <div class="text-xs text-muted-foreground mt-2">
                  成员数量：{{ organizationStructure.department.memberCount }} 人
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div class="space-y-3">
              <Label class="text-sm font-medium">组织架构文件</Label>
              <div class="space-y-2">
                <div
                  v-for="doc in organizationStructure.documents"
                  :key="doc.name"
                  class="flex items-center justify-between p-2 border rounded"
                >
                  <div class="flex items-center space-x-2">
                    <FileText class="w-4 h-4 text-blue-500" />
                    <div>
                      <div class="text-sm">{{ doc.name }}</div>
                      <div class="text-xs text-muted-foreground">{{ doc.updateDate }}</div>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost" @click="viewDocument(doc)">
                    <Eye class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 制度体系 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <BookOpen class="h-5 w-5" />
          <span>数据安全制度体系</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <!-- 数据分类分级管理 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2">
              <h4 class="font-semibold">数据分类分级管理</h4>
              <Badge variant="outline">已建立</Badge>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label class="text-sm font-medium">管理制度</Label>
                <div class="flex items-center justify-between p-2 border rounded">
                  <div class="flex items-center space-x-2">
                    <FileText class="w-4 h-4 text-blue-500" />
                    <span class="text-sm">数据分类分级管理制度.pdf</span>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    @click="viewDocument({ name: '数据分类分级管理制度.pdf' })"
                  >
                    <Eye class="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div class="space-y-2">
                <Label class="text-sm font-medium">核心数据目录</Label>
                <div class="flex items-center justify-between p-2 border rounded">
                  <div class="flex items-center space-x-2">
                    <FileText class="w-4 h-4 text-green-500" />
                    <span class="text-sm">重要核心数据目录.xlsx</span>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    @click="viewDocument({ name: '重要核心数据目录.xlsx' })"
                  >
                    <Eye class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
            <div class="p-3 bg-blue-50 rounded-lg">
              <div class="text-sm text-blue-800">
                <strong>法规提示：</strong>
                根据《数据安全法》第二十一条规定，国家建立数据分类分级保护制度，对数据实行分类分级保护。
              </div>
            </div>
          </div>

          <!-- 数据处理阶段制度 -->
          <div class="space-y-4">
            <h4 class="font-semibold">数据处理阶段制度</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="stage in processingStages" :key="stage.name" class="space-y-3">
                <div class="flex items-center justify-between">
                  <Label class="text-sm font-medium">{{ stage.name }}</Label>
                  <Badge :variant="stage.status === '已建立' ? 'default' : 'secondary'">
                    {{ stage.status }}
                  </Badge>
                </div>
                <div class="space-y-2">
                  <div
                    v-for="doc in stage.documents"
                    :key="doc"
                    class="flex items-center justify-between p-2 border rounded text-sm"
                  >
                    <div class="flex items-center space-x-2">
                      <FileText class="w-4 h-4 text-blue-500" />
                      <span>{{ doc }}</span>
                    </div>
                    <Button size="sm" variant="ghost" @click="viewDocument({ name: doc })">
                      <Eye class="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 日志管理制度 -->
          <div class="space-y-4">
            <div class="flex items-center space-x-2">
              <h4 class="font-semibold">日志管理制度</h4>
              <Badge variant="outline">已建立</Badge>
            </div>
            <div class="flex items-center justify-between p-3 border rounded-lg">
              <div class="flex items-center space-x-2">
                <FileText class="w-5 h-5 text-blue-500" />
                <div>
                  <div class="font-medium">数据处理日志管理制度.pdf</div>
                  <div class="text-sm text-muted-foreground">
                    包含日志记录、存储、审计等管理要求
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                @click="viewDocument({ name: '数据处理日志管理制度.pdf' })"
              >
                查看详情
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 安全风险评估 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <AlertTriangle class="h-5 w-5" />
          <span>安全风险评估</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="space-y-3">
                <Label class="text-sm font-medium">评估类型</Label>
                <div class="p-3 border rounded-lg">
                  <div class="flex items-center space-x-2">
                    <Badge variant="outline">{{ riskAssessment.type }}</Badge>
                    <span class="text-sm">{{ riskAssessment.description }}</span>
                  </div>
                </div>
              </div>

              <div v-if="riskAssessment.type === '第三方评估'" class="space-y-3">
                <Label class="text-sm font-medium">评估机构信息</Label>
                <div class="p-3 border rounded-lg space-y-2">
                  <div class="flex justify-between text-sm">
                    <span>机构名称：</span>
                    <span>{{ riskAssessment.agency.name }}</span>
                  </div>
                  <div class="flex justify-between text-sm">
                    <span>信用代码：</span>
                    <span class="font-mono">{{ riskAssessment.agency.creditCode }}</span>
                  </div>
                  <div class="flex justify-between text-sm">
                    <span>资质等级：</span>
                    <Badge variant="outline">{{ riskAssessment.agency.qualification }}</Badge>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div class="space-y-3">
                <Label class="text-sm font-medium">评估文件</Label>
                <div class="space-y-2">
                  <div
                    v-for="doc in riskAssessment.documents"
                    :key="doc.name"
                    class="flex items-center justify-between p-2 border rounded"
                  >
                    <div class="flex items-center space-x-2">
                      <FileText class="w-4 h-4 text-blue-500" />
                      <div>
                        <div class="text-sm">{{ doc.name }}</div>
                        <div class="text-xs text-muted-foreground">{{ doc.updateDate }}</div>
                      </div>
                    </div>
                    <Button size="sm" variant="ghost" @click="viewDocument(doc)">
                      <Eye class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 监测预警与应急管理 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Bell class="h-5 w-5" />
          <span>监测预警与应急管理</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div class="space-y-3">
              <Label class="text-sm font-medium">监测预警机制</Label>
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <Check class="w-4 h-4 text-green-500" />
                  <span class="text-sm">7×24小时实时监测</span>
                </div>
                <div class="flex items-center space-x-2">
                  <Check class="w-4 h-4 text-green-500" />
                  <span class="text-sm">多级预警阈值设置</span>
                </div>
                <div class="flex items-center space-x-2">
                  <Check class="w-4 h-4 text-green-500" />
                  <span class="text-sm">自动预警通知机制</span>
                </div>
                <div class="flex items-center space-x-2">
                  <Check class="w-4 h-4 text-green-500" />
                  <span class="text-sm">预警记录追溯管理</span>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div class="space-y-3">
              <Label class="text-sm font-medium">应急管理文件</Label>
              <div class="space-y-2">
                <div
                  v-for="doc in emergencyManagement.documents"
                  :key="doc.name"
                  class="flex items-center justify-between p-2 border rounded"
                >
                  <div class="flex items-center space-x-2">
                    <FileText class="w-4 h-4 text-red-500" />
                    <div>
                      <div class="text-sm">{{ doc.name }}</div>
                      <div class="text-xs text-muted-foreground">{{ doc.updateDate }}</div>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost" @click="viewDocument(doc)">
                    <Eye class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Shield,
  Users,
  User,
  FileText,
  BookOpen,
  AlertTriangle,
  Bell,
  Eye,
  Check,
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

interface Enterprise {
  id: string
  name: string
  type: string
  creditCode: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  contactPerson: string
  contactPhone: string
  address: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

interface Props {
  enterprise: Enterprise
}

const props = defineProps<Props>()

// 安全等级评估
const securityLevel = computed(() => {
  const riskLevel = props.enterprise.riskLevel
  if (riskLevel === '低') return '高'
  if (riskLevel === '中') return '中'
  return '基础'
})

// 安全防护统计
const securityStats = computed(() => ({
  policies: 8, // 制度体系数量
  technologies: 12, // 技术措施数量
  certifications: 3, // 资质认证数量
  assessments: 2, // 安全评估数量
}))

// 组织架构信息
const organizationStructure = computed(() => ({
  securityOfficer: {
    name: '王安全',
    position: '首席信息安全官(CISO)',
    contact: '<EMAIL>',
  },
  department: {
    name: '数据安全管理部',
    description: '负责公司数据安全策略制定、风险评估、合规管理等工作',
    memberCount: 15,
  },
  documents: [
    { name: '数据安全组织架构图.pdf', updateDate: '2024-01-15' },
    { name: '数据安全管理制度.pdf', updateDate: '2024-01-10' },
    { name: '数据安全岗位职责说明.docx', updateDate: '2024-01-08' },
  ],
}))

// 数据处理阶段制度
const processingStages = computed(() => [
  {
    name: '数据收集阶段',
    status: '已建立',
    documents: ['数据收集管理制度.pdf', '数据收集授权机制.docx'],
  },
  {
    name: '数据存储阶段',
    status: '已建立',
    documents: ['数据存储安全制度.pdf', '数据备份管理规范.docx'],
  },
  {
    name: '数据加工阶段',
    status: '已建立',
    documents: ['数据加工处理制度.pdf', '数据脱敏处理规范.docx'],
  },
  {
    name: '数据提供阶段',
    status: '已建立',
    documents: ['数据提供管理制度.pdf', '数据共享协议模板.docx'],
  },
  {
    name: '数据公开阶段',
    status: '已建立',
    documents: ['数据公开管理制度.pdf'],
  },
  {
    name: '数据销毁阶段',
    status: '已建立',
    documents: ['数据销毁管理制度.pdf', '数据销毁记录表.xlsx'],
  },
])

// 安全风险评估信息
const riskAssessment = computed(() => ({
  type: '第三方评估',
  description: '委托专业第三方机构进行安全风险评估',
  agency: {
    name: '北京网络安全技术评估中心',
    creditCode: '91110000123456789A',
    qualification: '国家级安全评估资质',
  },
  documents: [
    { name: '安全风险评估制度.pdf', updateDate: '2024-01-15' },
    { name: '安全风险评估报告.pdf', updateDate: '2024-01-20' },
    { name: '风险处置方案.docx', updateDate: '2024-01-22' },
  ],
}))

// 应急管理信息
const emergencyManagement = computed(() => ({
  documents: [
    { name: '数据安全应急预案.pdf', updateDate: '2024-01-15' },
    { name: '数据泄露应急处置流程.pdf', updateDate: '2024-01-12' },
    { name: '应急联系人名单.xlsx', updateDate: '2024-01-10' },
    { name: '应急演练记录.docx', updateDate: '2024-01-08' },
  ],
}))

// 方法
const viewDocument = (doc: any) => {
  console.log('查看文档:', doc.name)
  // 这里实现查看文档的逻辑
}
</script>
