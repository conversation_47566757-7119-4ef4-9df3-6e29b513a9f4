﻿,,,,,,,,
表名: log_veh_transmit_detail,,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment),
1,id,主键ID,BIGINT,,○,●,自增主键,
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id""",
3,target_enterprise_code,传输目的地(信用代码),VARCHAR,18,○,,接收方企业的18位统一社会信用代码,
4,target_ip,传输目的地IP,VARCHAR,45,○,,IPv6格式地址 (IPv4使用映射格式),
5,covered_mileage,传输覆盖里程,BIGINT,,○,,"""(DWORD)。覆盖里程", 0.1km精度 (定点数)。"
6,coordinate_processing_flag,坐标处理标志,SMALLINT,,○,,"""(BYTE) 0x01:未传输真实坐标; 0x02:传输真实坐标""",
7,coordinate_processing_method,坐标处理手段,SMALLINT,,○,,"""(BYTE) 0x01:坐标偏转插件; 0x02:坐标偏转算法; 0x03:其他""",
8,transmission_area_type,传输区域类型,SMALLINT,,○,,"""(BYTE) 0x01:境内; 0x02:境外""",
9,network_type,通信网络类型,SMALLINT,,○,,"""(BYTE) 0x01:公共网络; 0x02:专用网络; 0x03:国家认定网络; 0x04:其他""",
10,security_protocol,安全传输协议,SMALLINT,,○,,"""(BYTE) 0x01:HTTP; 0x02:HTTPS; 0x03:国密通道; 0x04:其他""",
11,external_transmission_capability,车外传输功能,SMALLINT,,○,,"""(BYTE) 0x01:具备; 0x02:不具备""",
