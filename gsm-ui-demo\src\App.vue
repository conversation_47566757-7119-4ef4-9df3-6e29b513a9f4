<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { Toaster } from '@/components/ui/sonner'
import AppSidebar from '@/components/AppSidebar.vue'
import GlobalHeader from '@/components/layout/GlobalHeader.vue'
import { resolveSectionFromPath } from '@/config/sidebar-menu'

const route = useRoute()

// 用 ref 来追踪当前是否为公开页面，避免计算属性的延迟
const isPublicPage = ref(true) // 默认为 true，避免闪烁

// 定义公开页面路径
const publicPaths = [
  '/',
  '/login',
  '/policy',
  '/policies',
  '/compliance',
  '/news',
  '/content',
  '/external',
  '/auth',
  '/404',
]

// 检查路径是否为公开页面
const checkIsPublicPage = (path: string) => {
  if (!path || path === '/') {
    return true
  }

  return publicPaths.some((publicPath) => {
    if (publicPath === '/') {
      return path === '/'
    }
    return path.startsWith(publicPath + '/') || path === publicPath
  })
}

// 立即检查当前路径
isPublicPage.value = checkIsPublicPage(route.path)

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    isPublicPage.value = checkIsPublicPage(newPath)
  },
  { immediate: true },
)

// 根据当前路径的 SectionKey 判断是否展示 Sidebar08（仅非 overview 显示）
const showSidebar = computed(() => {
  if (isPublicPage.value) return false
  const section = resolveSectionFromPath(route.path)
  return section !== 'overview'
})
</script>

<template>
  <div class="app-container">
    <!-- 公开页面：独立单页面模式 -->
    <div v-if="isPublicPage" class="public-page-wrapper">
      <router-view />
    </div>

    <!-- 内部页面：完整框架模式 -->
    <div v-else>
      <GlobalHeader />
      <div class="internal-page-wrapper">
        <template v-if="showSidebar">
          <SidebarProvider>
            <AppSidebar />
            <SidebarInset>
              <div class="flex flex-1 flex-col gap-4 p-4">
                <router-view />
              </div>
            </SidebarInset>
          </SidebarProvider>
        </template>
        <template v-else>
          <div class="flex flex-1 flex-col gap-4 p-4">
            <router-view />
          </div>
        </template>
      </div>
    </div>
  </div>
  <Toaster />
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  width: 100vw;
}

/* 公开页面独立模式样式 */
.public-page-wrapper {
  min-height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  position: relative;
  margin: 0;
  padding: 0;
  border: none;
  background: transparent;
}

/* 内部页面框架样式 */
.internal-page-wrapper {
  min-height: 100vh;
  width: 100vw;
}
</style>
