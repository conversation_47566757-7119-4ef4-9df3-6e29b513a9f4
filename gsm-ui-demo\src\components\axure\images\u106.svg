﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="116px" height="52px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="79px" y="113px" width="116px" height="52px" filterUnits="userSpaceOnUse" id="filter452">
      <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="4" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.462745098039216  0 0 0 0 0.556862745098039  0 0 0 0 0.807843137254902  0 0 0 0.996078431372549 0  " in="shadowComposite" />
    </filter>
    <g id="widget453">
      <path d="M 87 117  L 187 117  L 187 153  L 87 153  L 87 117  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -79 -113 )">
    <use xlink:href="#widget453" filter="url(#filter452)" />
    <use xlink:href="#widget453" />
  </g>
</svg>