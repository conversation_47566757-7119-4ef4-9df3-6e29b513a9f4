<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          请填写企业数据安全防控技术措施，包含车端技术和云端技术的具体实施情况
        </p>
      </div>
      <Badge variant="outline" class="text-sm"> 步骤 4 / 5 </Badge>
    </div>

    <!-- 进度条 -->
    <div class="w-full bg-muted rounded-full h-2">
      <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 80%"></div>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Settings class="w-5 h-5" />
          数据安全防控措施 - 技术
        </CardTitle>
        <CardDescription>
          企业数据安全防控的技术措施实施情况，包含车端和云端的技术防护手段
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-8">
          <!-- 车端技术 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <Car class="w-5 h-5" />
                车端技术
              </CardTitle>
              <CardDescription>车端数据处理各阶段的技术防护措施</CardDescription>
            </CardHeader>
            <CardContent class="space-y-6">
              <!-- 收集阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">收集阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-collection-desensitization"
                          v-model:checked="formData.vehicle.collection.desensitization"
                        />
                        <Label for="vehicle-collection-desensitization" class="text-sm"
                          >属性脱敏</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-collection-geofencing"
                          v-model:checked="formData.vehicle.collection.geofencing"
                        />
                        <Label for="vehicle-collection-geofencing" class="text-sm">地理围栏</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-collection-other"
                          v-model:checked="formData.vehicle.collection.other"
                        />
                        <Label for="vehicle-collection-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="vehicle-collection-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="vehicle-collection-notes"
                      v-model="formData.vehicle.collection.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              <!-- 存储阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">存储阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-storage-confidentiality"
                          v-model:checked="formData.vehicle.storage.confidentiality"
                        />
                        <Label for="vehicle-storage-confidentiality" class="text-sm"
                          >保密处理</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-storage-mileage"
                          v-model:checked="formData.vehicle.storage.mileageLimit"
                        />
                        <Label for="vehicle-storage-mileage" class="text-sm">里程限制</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-storage-desensitization"
                          v-model:checked="formData.vehicle.storage.desensitization"
                        />
                        <Label for="vehicle-storage-desensitization" class="text-sm"
                          >属性脱敏</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-storage-other"
                          v-model:checked="formData.vehicle.storage.other"
                        />
                        <Label for="vehicle-storage-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="vehicle-storage-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="vehicle-storage-notes"
                      v-model="formData.vehicle.storage.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              <!-- 传输阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">传输阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-transmission-confidentiality"
                          v-model:checked="formData.vehicle.transmission.confidentiality"
                        />
                        <Label for="vehicle-transmission-confidentiality" class="text-sm"
                          >保密处理</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-transmission-desensitization"
                          v-model:checked="formData.vehicle.transmission.desensitization"
                        />
                        <Label for="vehicle-transmission-desensitization" class="text-sm"
                          >属性脱敏</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-transmission-mileage"
                          v-model:checked="formData.vehicle.transmission.mileageLimit"
                        />
                        <Label for="vehicle-transmission-mileage" class="text-sm">里程限制</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-transmission-vms"
                          v-model:checked="formData.vehicle.transmission.vmsFusion"
                        />
                        <Label for="vehicle-transmission-vms" class="text-sm">VMS数据融合</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="vehicle-transmission-other"
                          v-model:checked="formData.vehicle.transmission.other"
                        />
                        <Label for="vehicle-transmission-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="vehicle-transmission-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="vehicle-transmission-notes"
                      v-model="formData.vehicle.transmission.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 云端技术 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <Cloud class="w-5 h-5" />
                云端技术
              </CardTitle>
              <CardDescription>云端数据处理各阶段的技术防护措施</CardDescription>
            </CardHeader>
            <CardContent class="space-y-6">
              <!-- 存储阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">存储阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-storage-network"
                          v-model:checked="formData.cloud.storage.networkSecurity"
                        />
                        <Label for="cloud-storage-network" class="text-sm"
                          >网络与通信安全（三级等保）</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-storage-device"
                          v-model:checked="formData.cloud.storage.deviceSecurity"
                        />
                        <Label for="cloud-storage-device" class="text-sm">设备与计算安全</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-storage-application"
                          v-model:checked="formData.cloud.storage.applicationSecurity"
                        />
                        <Label for="cloud-storage-application" class="text-sm"
                          >应用与数据安全</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-storage-other"
                          v-model:checked="formData.cloud.storage.other"
                        />
                        <Label for="cloud-storage-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="cloud-storage-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="cloud-storage-notes"
                      v-model="formData.cloud.storage.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              <!-- 加工阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">加工阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-desensitization"
                          v-model:checked="formData.cloud.processing.desensitization"
                        />
                        <Label for="cloud-processing-desensitization" class="text-sm"
                          >属性脱敏</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-vms"
                          v-model:checked="formData.cloud.processing.vmsFusion"
                        />
                        <Label for="cloud-processing-vms" class="text-sm">VMS融合</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-access"
                          v-model:checked="formData.cloud.processing.accessControl"
                        />
                        <Label for="cloud-processing-access" class="text-sm">访问控制</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-monitoring"
                          v-model:checked="formData.cloud.processing.riskMonitoring"
                        />
                        <Label for="cloud-processing-monitoring" class="text-sm">风险监测</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-traceback"
                          v-model:checked="formData.cloud.processing.emergencyTraceback"
                        />
                        <Label for="cloud-processing-traceback" class="text-sm">应急溯源</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-response"
                          v-model:checked="formData.cloud.processing.emergencyResponse"
                        />
                        <Label for="cloud-processing-response" class="text-sm">应急处置</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-processing-other"
                          v-model:checked="formData.cloud.processing.other"
                        />
                        <Label for="cloud-processing-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="cloud-processing-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="cloud-processing-notes"
                      v-model="formData.cloud.processing.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              <!-- 传输阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">传输阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-transmission-verification"
                          v-model:checked="formData.cloud.transmission.verificationTech"
                        />
                        <Label for="cloud-transmission-verification" class="text-sm"
                          >校验技术</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-transmission-cryptography"
                          v-model:checked="formData.cloud.transmission.cryptographyTech"
                        />
                        <Label for="cloud-transmission-cryptography" class="text-sm"
                          >密码技术</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-transmission-secure-channel"
                          v-model:checked="formData.cloud.transmission.secureChannel"
                        />
                        <Label for="cloud-transmission-secure-channel" class="text-sm"
                          >安全传输通道/协议</Label
                        >
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-transmission-other"
                          v-model:checked="formData.cloud.transmission.other"
                        />
                        <Label for="cloud-transmission-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="cloud-transmission-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="cloud-transmission-notes"
                      v-model="formData.cloud.transmission.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              <!-- 审计阶段 -->
              <div class="space-y-4">
                <h4 class="font-medium text-base">审计阶段</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">技术措施（多选）</Label>
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-audit-monitoring"
                          v-model:checked="formData.cloud.audit.securityMonitoring"
                        />
                        <Label for="cloud-audit-monitoring" class="text-sm">安全监测</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-audit-log"
                          v-model:checked="formData.cloud.audit.logAudit"
                        />
                        <Label for="cloud-audit-log" class="text-sm">日志审计</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-audit-integrity"
                          v-model:checked="formData.cloud.audit.logIntegrity"
                        />
                        <Label for="cloud-audit-integrity" class="text-sm">日志完整性</Label>
                      </div>
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          id="cloud-audit-other"
                          v-model:checked="formData.cloud.audit.other"
                        />
                        <Label for="cloud-audit-other" class="text-sm">其他</Label>
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label for="cloud-audit-notes" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      id="cloud-audit-notes"
                      v-model="formData.cloud.audit.notes"
                      placeholder="如有特殊情况或其他技术措施，请详细说明"
                      rows="3"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 访问控制策略与数据脱敏规则（保留原功能） -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg">访问控制策略与数据脱敏规则</CardTitle>
              <CardDescription>补充说明访问控制和数据脱敏的具体实施规则</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-2">
                <Label for="accessControlPolicy" class="text-base font-semibold">
                  访问控制策略
                </Label>
                <Textarea
                  id="accessControlPolicy"
                  v-model="formData.additional.accessControlPolicy"
                  placeholder="请详细描述访问控制策略，包括用户权限管理、数据访问分级等"
                  rows="4"
                />
              </div>

              <div class="space-y-2">
                <Label for="dataDesensitizationRules" class="text-base font-semibold">
                  数据脱敏规则
                </Label>
                <Textarea
                  id="dataDesensitizationRules"
                  v-model="formData.additional.dataDesensitizationRules"
                  placeholder="请详细描述数据脱敏规则，包括脱敏字段、脱敏算法、脱敏程度等"
                  rows="4"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" @click="handlePrevious">
              <ChevronLeft class="w-4 h-4 mr-2" />
              上一步
            </Button>
            <div class="flex gap-2">
              <Button type="button" variant="outline" @click="handleSave">
                <Save class="w-4 h-4 mr-2" />
                保存（暂存）
              </Button>
              <Button type="submit" @click="handleNext">
                下一步
                <ChevronRight class="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 保存提示弹窗 -->
    <Dialog v-model:open="saveDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>保存成功</DialogTitle>
          <DialogDescription>
            您的数据安全防控措施技术信息已保存，可稍后继续填报。
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end">
          <Button @click="saveDialogOpen = false">确定</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Car, ChevronLeft, ChevronRight, Cloud, Save, Settings } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const router = useRouter()

// 表单数据
const formData = reactive({
  vehicle: {
    collection: {
      desensitization: false,
      geofencing: false,
      other: false,
      notes: '',
    },
    storage: {
      confidentiality: false,
      mileageLimit: false,
      desensitization: false,
      other: false,
      notes: '',
    },
    transmission: {
      confidentiality: false,
      desensitization: false,
      mileageLimit: false,
      vmsFusion: false,
      other: false,
      notes: '',
    },
  },
  cloud: {
    storage: {
      networkSecurity: false,
      deviceSecurity: false,
      applicationSecurity: false,
      other: false,
      notes: '',
    },
    processing: {
      desensitization: false,
      vmsFusion: false,
      accessControl: false,
      riskMonitoring: false,
      emergencyTraceback: false,
      emergencyResponse: false,
      other: false,
      notes: '',
    },
    transmission: {
      verificationTech: false,
      cryptographyTech: false,
      secureChannel: false,
      other: false,
      notes: '',
    },
    audit: {
      securityMonitoring: false,
      logAudit: false,
      logIntegrity: false,
      other: false,
      notes: '',
    },
  },
  additional: {
    accessControlPolicy: '',
    dataDesensitizationRules: '',
  },
})

// 表单验证错误
const errors = reactive<Record<string, string>>({})

// 其他状态
const saveDialogOpen = ref(false)

// 表单验证（原型演示版本 - 校验已禁用）
const validateForm = () => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => {
    delete errors[key]
  })

  // 原型演示模式：直接返回true，跳过所有校验
  console.log('原型演示模式：数据安全防控措施技术表单校验已禁用，直接通过')
  return true

  /* 原始校验逻辑（原型完成后可恢复）
  let isValid = true

  // 车端技术验证 - 至少选择一项技术措施
  const vehicleStages = ['collection', 'storage', 'transmission']
  for (const stage of vehicleStages) {
    const stageData = formData.vehicle[stage as keyof typeof formData.vehicle]
    const hasSelection = Object.entries(stageData).some(
      ([key, value]) => key !== 'notes' && value === true,
    )

    if (!hasSelection) {
      errors[`vehicle_${stage}`] =
        `请至少选择一项车端${stage === 'collection' ? '收集' : stage === 'storage' ? '存储' : '传输'}阶段的技术措施`
      isValid = false
    }
  }

  // 云端技术验证 - 至少选择一项技术措施
  const cloudStages = ['storage', 'processing', 'transmission', 'audit']
  for (const stage of cloudStages) {
    const stageData = formData.cloud[stage as keyof typeof formData.cloud]
    const hasSelection = Object.entries(stageData).some(
      ([key, value]) => key !== 'notes' && value === true,
    )

    if (!hasSelection) {
      const stageNames: Record<string, string> = {
        storage: '存储',
        processing: '加工',
        transmission: '传输',
        audit: '审计',
      }
      errors[`cloud_${stage}`] = `请至少选择一项云端${stageNames[stage]}阶段的技术措施`
      isValid = false
    }
  }

  return isValid
  */
}

// 上一步
const handlePrevious = () => {
  router.push('/corp/filing/form/security-policy')
}

// 保存（暂存）
const handleSave = () => {
  console.log('保存数据安全防控措施技术信息:', formData)

  // 保存到本地存储
  localStorage.setItem(
    'enterprise_security_tech',
    JSON.stringify({
      formData,
    }),
  )

  saveDialogOpen.value = true
}

// 下一步
const handleNext = () => {
  if (validateForm()) {
    handleSave()
    // 跳转到下一步（数据处理活动信息）
    router.push('/corp/filing/form/data-activity')
  }
}

// 表单提交
const handleSubmit = () => {
  // 阻止默认提交行为，由handleNext处理
}

// 页面加载时尝试恢复数据
const loadSavedData = () => {
  const saved = localStorage.getItem('enterprise_security_tech')
  if (saved) {
    try {
      const data = JSON.parse(saved)
      if (data.formData) {
        Object.assign(formData, data.formData)
      }
    } catch (e) {
      console.warn('Failed to load saved data:', e)
    }
  }
}

// 组件挂载时加载保存的数据
onMounted(() => {
  loadSavedData()
})
</script>

<style scoped>
/* 自定义样式 */
.border-red-500 {
  border-color: rgb(239 68 68);
}
</style>
