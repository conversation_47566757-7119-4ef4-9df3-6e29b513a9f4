name: Deploy to CloudBase (Unified)

on:
  push:
    branches: [ main ]
  workflow_dispatch:

concurrency:
  group: cloudbase-deploy-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    defaults:
      run:
        working-directory: gsm-ui-demo

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'gsm-ui-demo/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Type Check (non-blocking)
        run: npm run type-check
        continue-on-error: true

      # 跳过因 TS 警告导致的失败，保持构建成功
      - name: Build (skip type errors)
        run: npm run build-only

      - name: Optimize iframe bundle
        run: node scripts/optimize-iframe.mjs

      - name: Upload dist artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: gsm-ui-demo/dist
          if-no-files-found: error
          retention-days: 7

      - name: Install CloudBase CLI
        run: npm i -g @cloudbase/cli

      - name: Deploy to CloudBase
        env:
          CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
          CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
        run: |
          tcb login --key "$CLOUDBASE_SECRET_ID" --secret "$CLOUDBASE_SECRET_KEY"
          tcb framework deploy

