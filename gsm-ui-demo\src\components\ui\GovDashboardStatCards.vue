<template>
  <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
    <!-- 车端统计卡片 -->
    <Card class="p-6">
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">车辆类型/品牌占比</h3>
        <PieChart :data="vehicleTypeBrandData" :height="220" :color-scheme="'primary'" />
      </div>
    </Card>
    <Card class="p-6">
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">处理活动统计</h3>
        <LineChart :series="vehicleActivitySeries" :height="220" />
      </div>
    </Card>
    <Card class="p-6">
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">风险事件统计</h3>
        <div class="flex gap-2">
          <BarChart :data="vehicleRiskLevelData" :color-scheme="'risk'" :height="220" />
          <PieChart :data="vehicleEventTypeData" :height="220" :color-scheme="'primary'" />
        </div>
      </div>
    </Card>
    <!-- 云端统计卡片 -->
    <Card class="p-6">
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">企业类型占比</h3>
        <PieChart :data="cloudCompanyTypeData" :height="220" :color-scheme="'enterprise'" />
      </div>
    </Card>
    <Card class="p-6">
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">处理活动统计</h3>
        <LineChart :series="cloudActivitySeries" :height="220" />
      </div>
    </Card>
    <Card class="p-6">
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">风险事件统计</h3>
        <div class="flex gap-2">
          <BarChart :data="cloudRiskStageData" :height="220" :color-scheme="'stages'" />
          <PieChart :data="cloudEventTypeData" :height="220" />
        </div>
      </div>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card } from '@/components/ui/card'
import PieChart from '@/components/charts/PieChart.vue'
import LineChart from '@/components/charts/LineChart.vue'
import BarChart from '@/components/charts/BarChart.vue'

const vehicleTypeBrandData = [
  { name: '轿车', value: 120 },
  { name: 'SUV', value: 80 },
  { name: '新能源', value: 60 },
  { name: '其他', value: 40 },
]
const vehicleActivityData = {
  day: [120, 80, 60],
  week: [700, 500, 400],
  month: [3000, 2000, 1500],
}
const activityXAxis = ['收集', '存储', '传输']
const vehicleRiskLevelData = [
  { name: '高', value: 10 },
  { name: '中', value: 30 },
  { name: '低', value: 60 },
]
const vehicleEventTypeData = [
  { name: '数据泄露', value: 20 },
  { name: '非法访问', value: 15 },
  { name: '其他', value: 5 },
]

const cloudCompanyTypeData = [
  { name: '平台运营商', value: 50 },
  { name: '智驾方案商', value: 30 },
  { name: '地图服务商', value: 20 },
]
const cloudActivityData = {
  day: [50, 40, 30, 20, 10, 5, 2],
  week: [300, 250, 200, 150, 100, 50, 20],
  month: [1200, 1000, 800, 600, 400, 200, 80],
}
const cloudActivityXAxis = ['收集', '存储', '传输', '加工', '提供', '公开', '销毁']
const cloudRiskStageData = [
  { name: '收集', value: 5 },
  { name: '存储', value: 8 },
  { name: '传输', value: 6 },
  { name: '加工', value: 4 },
  { name: '提供', value: 3 },
  { name: '公开', value: 2 },
  { name: '销毁', value: 1 },
]
const cloudEventTypeData = [
  { name: '数据泄露', value: 8 },
  { name: '非法访问', value: 6 },
  { name: '其他', value: 2 },
]

// 将旧的数据结构映射为 LineChart 需要的 series 结构
const vehicleActivitySeries = computed(() => {
  const x = activityXAxis
  const map = vehicleActivityData as Record<string, number[]>
  return Object.keys(map).map((key) => ({
    name: key,
    data: (map[key] || []).map((v, i) => ({ name: x[i] || String(i), value: v })),
    smooth: true
  }))
})

const cloudActivitySeries = computed(() => {
  const x = cloudActivityXAxis
  const map = cloudActivityData as Record<string, number[]>
  return Object.keys(map).map((key) => ({
    name: key,
    data: (map[key] || []).map((v, i) => ({ name: x[i] || String(i), value: v })),
    smooth: true
  }))
})
</script>

<style scoped>
.grid {
  margin-top: 1rem;
}
</style>
