import pluginVue from 'eslint-plugin-vue'

export default [
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'dist-ssr/**',
      'coverage/**',
      'd3-visualization/**',
      '*.min.js',
      'public/axure/**',
      'public/iframe/**',
      'test-*.js',
      'test-*.ps1',
      'test-*.html',
      '.claude/**',
      '.env*',
      'cloudbaserc.json',
      'components.json',
      '*.config.js',
      '*.config.ts',
      'deploy.*',
      '*.md',
      '*.json',
      '*.css',
      '*.html',
      '*.bat',
      '*.ps1',
      '*.pid',
    ],
  },
  ...pluginVue.configs['flat/essential'],
  {
    files: ['src/**/*.{js,ts,vue}'],
    rules: {
      // 基本规则
      'no-console': 'warn',
      'no-debugger': 'warn',
    },
  },
]
