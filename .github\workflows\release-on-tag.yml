name: Release on Tag

on:
  push:
    tags:
      - "v*"
  release:
    types: [published]
  workflow_dispatch:

concurrency:
  group: release-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: write

jobs:
  build-and-release:
    runs-on: ubuntu-latest
    timeout-minutes: 25
    env:
      TAG_NAME: ${{ github.event_name == 'release' && github.event.release.tag_name || github.ref_name }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'gsm-ui-demo/package-lock.json'

      - name: Debug workspace layout
        run: |
          echo "EVENT: ${{ github.event_name }}"
          echo "REF: ${{ github.ref }}"
          echo "REF_NAME: ${{ github.ref_name }}"
          echo "TAG_NAME: ${TAG_NAME}"
          echo "WORKSPACE: $(pwd)"
          echo "Listing root:" && ls -la
          echo "Listing gsm-ui-demo:" && ls -la gsm-ui-demo || true
          node -v
          npm -v

      - name: Install dependencies
        run: npm ci
        working-directory: gsm-ui-demo

      - name: Type Check (non-blocking)
        run: npm run type-check
        working-directory: gsm-ui-demo
        continue-on-error: true

      - name: Build (skip type errors)
        run: npm run build-only
        working-directory: gsm-ui-demo

      - name: Optimize iframe bundle
        run: node scripts/optimize-iframe.mjs
        working-directory: gsm-ui-demo

      - name: Package artifacts
        run: |
          rm -f dist.zip iframe-bundle.zip
          cd dist
          zip -r ../dist.zip .
          zip -r ../iframe-bundle.zip iframe axure
          cd ..
        working-directory: gsm-ui-demo

      - name: Create or update GitHub Release
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ env.TAG_NAME }}
          files: |
            gsm-ui-demo/dist.zip
            gsm-ui-demo/iframe-bundle.zip
          draft: false
          prerelease: ${{ contains(env.TAG_NAME, '-') }}
          generate_release_notes: true
          make_latest: ${{ !contains(env.TAG_NAME, '-') }}