﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="164px" height="52px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="79px" y="720px" width="164px" height="52px" filterUnits="userSpaceOnUse" id="filter456">
      <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="4" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.462745098039216  0 0 0 0 0.556862745098039  0 0 0 0 0.807843137254902  0 0 0 0.996078431372549 0  " in="shadowComposite" />
    </filter>
    <g id="widget457">
      <path d="M 87 724  L 235 724  L 235 760  L 87 760  L 87 724  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -79 -720 )">
    <use xlink:href="#widget457" filter="url(#filter456)" />
    <use xlink:href="#widget457" />
  </g>
</svg>