# 地理信息安全监测平台通信协议－技术评审版

# 版本： V4.1 (20250717－合并修订版）

# 1. 范围

本文档规定了企业平台（客户端）与政府监测平台（服务端）之间的通信协议，包括通信连接、数据包结构、数据单元格式等。本文档适用于所有接入中国汽车时空数据安全监测平台的企业平台。

# 2. 术语和定义

 客户端平台： 指代进行数据处理活动的企业平台、地图服务商平台等。  
 服务端平台： 指政府侧的地理信息安全监测平台。  
 定点数： 一种使用整型数据表示浮点数的方法，通过乘以一个固定的缩放因子来实现。

# 3. 数据交换流程

客户端平台与服务端平台的整体通信流程如下图所示。

# 4. 通信连接

# 4.1 通信协议

a） 通信协议结构以 TCP/IP 网络控制协议作为底层通信承载协议。如图所示。

<table><tr><td>本部分所规定的协议</td><td></td><td>本部分所规定的协议</td></tr><tr><td>TCP</td><td rowspan="3"></td><td>TCP</td></tr><tr><td>IP</td><td>IP</td></tr><tr><td>底层承载</td><td>底层承载</td></tr></table>

# 图2 自动驾驶地理信息数据安全监测系统通信协议栈

b） 平台间的通信连接与数据传输应满足本文档的相关要求。  
c） 终端到企业平台间的通信连接与数据传输宜参照本文要求执行。

# 4.2 连接建立

a） 客户端平台向服务端平台发起通信连接请求，当通信链路连接建立后，客户端平台应自动向服务端平台发送登入信息进行身份识别，服务端平台应对接收到的数据进行校验：校验正确时，服务端平台应返回成功应答；校验错误时，服务端平台应存储错误数据记录并通知客户端平台。

b） 客户端平台应在接收到服务端平台的应答指令后完成本次登入传输；客户端平台在规定时间内未收到应答指令，应每间隔1min 重新进行登入；若连续重复3次登入无应答，应间隔 30min 后，继续重新连接。链接成功并登入后，应将链接中断期间本地存储的未成功发送的数据，通过补发机制进行上报。重复登入间隔时间可以设置。

# 4.3 连接维持

连接建立和校验成功后，在没有正常数据包传输的情况下，客户端平台应周期性向服务端平台发送心跳消息，服务端平台收到后向客户端平台发送平台通用应答消息，发送周期默认为 60 秒。

# 4.4 数据传输

客户端平台登入成功后，应向服务端平台周期性上报与地理信息数据处理活动相关的信息数据。当客户端平台发生预定义的客观系统状态事件时，应立即上报事件数据。

表 1 事件类型标志定义  

<table><tr><td>事件分类标识</td><td>事件类型</td><td>记录时间</td><td>记录数据</td></tr><tr><td>0x01</td><td>车端数据泄露/丢失</td><td rowspan="2">检测到事件的时刻</td><td>***章节</td></tr><tr><td>0x02</td><td>云端数据泄露/丢失</td><td>***章节</td></tr></table>

服务端平台根据“数据处理活动信息”和“事件数据”，通过其内置的规则引擎、统计算法及机器学习模型，对潜在的数据安全风险进行识别、研判和告警。风险的识别能力与责任主体为服务端平台，客户端平台仅负责忠实、完整地按本协议上报其活动

与状态。当客户端平台向服务端平台上报信息时，服务端平台应对接收到的数据进行校验。当校验正确时，服务端平台做正确应答；当校验错误时，服务端平台做错误应答并丢弃该数据。

# 4.5 控制与查询

服务端平台根据需要向客户端平台发送控制指令（命令标识 $0 \times 8 3 ^ { \cdot }$ ），如数据查询、参数配置等。客户端平台应在规定时间内进行响应。

# 4.5.1 数据查询的 HTTPS 上报实现

对于数据查询类指令（控制类型为 $0 \times 0 1$ : 数据查询请求），客户端平台应通过独立的HTTPS通道向服务端平台上报所查询的历史数据，具体流程如下：

1. 指令下发： 服务端平台通过本协议定义的 TCP 长连接，向客户端平台下发控制指令（命令标识 $0 \times 8 3$ ），其中“控制类型”为 $0 \times 0 1$ : 数据查询请求。指令中应包含查询任务的唯一 ID、时间范围、数据类型等参数。

2. 指令确认： 客户端平台收到查询指令后，应立即通过 TCP 连接返回“平台通用应答”（命令标识 0x81），确认已接收该指令。

3. 数据准备与上报： 客户端平台根据指令要求，在本地数据库中异步查询并打包历史数据。完成后，通过 HTTP/S POST 请求，将数据提交至服务端平台指定的HTTPS 数据接收地址。该地址由平台在备案阶段提供。

# 4. 接口规范：

¢ 认证机制： HTTPS 请求的 Header 中必须包含有效的认证令牌（如 APIKey/Secret 或动态生成的 JWT），用于服务端进行身份鉴权。  
¢ 数据格式： POST 请求的 Body 应为 application/json 格式，数据内容遵循本协议第 6.4 节定义的实时信息上报格式，可包含一个或多个信息体。  
¢ 上报回执： 服务端 HTTPS 接口在成功接收并校验数据后，应返回 HTTP 200OK 状态码。若数据或请求格式错误，则返回 4xx 系列状态码及错误说明。客户端平台需记录上报结果。

# 4.6 平台通用应答

平台通用应答（命令标识 0x81）是服务端对客户端大部分上行消息的统一应答机制。除平台登入、链路检测等有专门应答消息的指令外，服务端在接收到客户端的实时信息上报、事件数据上报、平台登出、补发数据消息后，均应返回通用应答。通用应答消息体中需包含应答流水号（对应上行消息的流水号）、应答 ID（对应上行消息的命令标识）和应答结果（成功/失败/消息有误），以便客户端对消息发送状态进行确认。

# 4.7 补发机制

为确保通信中断期间的数据完整性，客户端平台必须实现可靠的数据补发机制。

# 4.7.1 补发触发条件

当客户端平台无法将数据成功发送至服务端平台时（包括但不限于TCP连接中断、发送后未在规定时间内收到通用应答），应立即启动补发机制，将该数据转入本地补发队列。

# 4.7.2 本地缓存要求

a） 缓存队列： 客户端平台应建立一个或多个先进先出（FIFO）的本地缓存队列，用于存储待补发的数据。关键事件数据和实时业务数据可分队列存储，高优先级数据优先补发。  
b） 缓存能力： 本地缓存至少能容纳连续 72 小时的平均数据量。当缓存空间使用率超过 $9 0 \%$ 时，应记录一条“系统异常”事件。

# 4.7.3 补发流程与策略

a） 连接恢复： 与服务端平台重连并成功登入后，客户端平台应立即检查本地补发队列。b）补发操作： 若队列非空，则开始数据补发流程。补发数据的数据单元格式与实时上报（6.4 节）完全相同，但必须使用命令标识 $0 \times 0 4$ 进行上报。c） 流量控制： 补发数据时，应采用平滑的发送策略， 避免瞬间流量冲击。建议补发速率不超过正常实时上报速率的 2 倍，且可由服务端通过参数配置指令进行调整。

d）补发超时与重试： 单条补发数据的发送同样需要等待服务端通用应答。若超时未收到应答，应按 1, 2, 5, 10... 分钟的退避策略进行重试，最多重试 5 次。5 次失败后，记录本地日志，不再对该单条数据进行自动补发，等待手动干预或控制指令。

# 4.7.4 数据一致性

为避免数据重复，实时上报和补发上报的所有信息体均需包含全局唯一的数据包ID。  
服务端平台负责根据此ID进行数据的去重处理。

# 4.7.5 补发数据优先级

为确保高风险数据在通信恢复后得到优先处理，客户端在执行补发操作时，应遵循以下优先级策略。

<table><tr><td>优先级</td><td>数据类型</td><td>说明</td></tr><tr><td>高</td><td>事件数据上报(0x06)</td><td>所有事件数据，特别是涉及数据泄露 (0x10）、违规出境（0x11）等高风险事件， 必须优先补发。</td></tr><tr><td></td><td>涉及核心/重要数据的实 时信息(0x20)</td><td>根据《时空数据安全风险项（类别）清单》判 定为“核心”或“重要”的数据处理活动信 息。</td></tr><tr><td>中</td><td>涉及一般数据的实时信息 (0x20)</td><td>除高优先级外的一般性数据处理活动信息。</td></tr><tr><td>低</td><td>平台心跳及其他</td><td>链路维持类消息，通常在实时发送失败后无需 补发，但在特定诊断场景下可能需要。</td></tr></table>

# 4.8 应答机制

本协议采用“请求－应答”的通信模型，以确保数据交互的可靠性。

1. 客户端请求： 客户端主动向服务端发送的请求类或数据上报类消息（如平台登入、实时信息上报、事件信息上报、连接心跳），都应被视为一次“请求”。

2. 服务端应答： 服务端在接收到客户端的请求后，必须在规定的超时时间内（建议为 5 秒）返回一个对应的“应答”消息（如登入应答、通用应答、心跳应答）。

3. 超时与重试： 若客户端在发送请求后，在超时时间内未收到服务端的应答，应认为本次通信失败。对于需要可靠传输的数据上报类消息，应立即启动重试或补发机制（参见 4.7 节）。

此应答机制构成了平台间通信的基础，保证了指令和数据的可达性与一致性。

<table><tr><td colspan="2">企业端平台 (客户端)</td><td></td><td>政府监测平台 (服务端)</td></tr><tr><td></td><td colspan="2"></td><td></td></tr><tr><td rowspan="5">1.鉴权与登入流程</td><td>1.TCP 连接请求</td><td></td><td rowspan="5"></td></tr><tr><td></td><td>2.TCP连接建立</td></tr><tr><td>★</td><td></td></tr><tr><td></td><td>3.平台登入(0x10)</td></tr><tr><td>4.平台登入应答(0x90)</td><td></td></tr><tr><td rowspan="8">2.常规数据交互</td><td rowspan="8"></td><td></td><td rowspan="8"></td></tr><tr><td>5.实时信息上报 (0x20)</td></tr><tr><td>6.平台通用应答(0x81)</td></tr><tr><td>7.事件数据上报(0x30)</td></tr><tr><td></td></tr><tr><td>8.平台通用应答(0x81) 9.客户端心跳 (0x02)</td></tr><tr><td>10.平台通用应答(0x81)</td></tr><tr><td></td></tr><tr><td rowspan="4">3.数据补发流程 (通信中断后)</td><td>11.补发信息上报 (0x21,可分包)</td><td rowspan="4"></td></tr><tr><td>12.平台通用应答(0x81)</td></tr><tr><td>(对最后一包的应答)</td></tr><tr><td></td></tr><tr><td rowspan="4">4.服务端控制流程</td><td>13.控制指令 (0x83,如数据查询)</td><td rowspan="4"></td></tr><tr><td>★ 14.客户端通用应答(0x01)</td></tr><tr><td>(若查询数据，则后续通过FTP/HTTP等方式传输)</td></tr><tr><td></td></tr><tr><td rowspan="5">5.密钥交换流程 (可选)</td><td>15.平台密钥交换 (OxF0,下发公钥)</td><td rowspan="5"></td></tr><tr><td>16.客户端密钥交换 (0x70，上报公钥)</td></tr><tr><td>17.客户端密钥交换(0x70，上报加密后的会话密钥)</td></tr><tr><td>18.平台通用应答(0x81)</td></tr><tr><td></td></tr><tr><td rowspan="4">6.登出流程</td><td></td><td>19.平台登出 (0x03)</td></tr><tr><td rowspan="3">1</td><td>20.TCP连接断开</td><td rowspan="3"></td></tr><tr><td></td></tr><tr><td></td></tr></table>

# 4.9 连接断开

服务端平台应根据以下情况断开与客户端平台的会话连接：

a） 客户端平台主动断开 TCP 连接  
b） 相同身份的企业数据中心建立新连接  
c） 在一定的时间内未收到客户端平台心跳信息（默认 300 秒）  
客户端平台应根据以下情况断开与服务端平台的会话连接：  
a） 服务端平台主动断开 TCP 连接  
b） 相同身份的企业数据中心建立新连接  
c） 数据通信链路正常，达到重传次数后仍未收到应答

# 5. 数据包结构和定义

# 5.1 数据类型

协议中传输的数据类型见表7-1。

表5-1 数据类型  

<table><tr><td>数据类型</td><td>描述及要求</td></tr><tr><td>BYTE</td><td>无符号单字节整型 (字节，8位)</td></tr><tr><td>WORD</td><td>无符号双字节整型 (字，16位)</td></tr><tr><td>DWORD</td><td>无符号四字节整型 (双字，32位)</td></tr><tr><td>BYTE[n]</td><td>n字节</td></tr><tr><td>STRING</td><td>ASCII字符码，若无数据则以Ox00终结</td></tr></table>

注：协议中的地理位置、里程等计量数据，采用定点数方式进行编码。即，使用一个长整型（如 DWORD）来存储原始浮点数值乘以一个固定倍数（如 ${ 1 0 } \Lambda _ { 6 }$ ）后的结果，接收方在解析时再除以该倍数还原。此方法可保证跨平台的字节序和精度一致性。

# 5.2 传输规则

协议采用大端模式（big-endian）的网络字节序来传递字和双字。

 字节（BYTE） 的传输约定：按照字节流的方式传输；  
 字（WORD） 的传输约定：先传递高八位，再传递低八位；  
双字（DWORD） 的传输约定：先传递高 24 位，然后传递高 16 位，再传递高八位，最后传递低八位。

# 5.3 消息结构

每条消息由起始符、消息头、消息体和校验码组成。

# 5.3.1 起始符

起始符固定为 ## (ASCII 码： 0x230x23）。

# 5.3.2 转义规则

为避免消息体中出现与起始符## 或转义符 $^ { \star } \left( 0 \times 2 A \right)$ 冲突，定义转义规则如下：  
发送时，在计算完CRC校验码之后进行转义：  
$0 { \times } 2 3  0 { \times } 2 { \mathsf { A } } , 0 { \times } 0 1$   
$0 { \times } 2 { \mathsf { A } } \mathrel { \mathop {  } } 0 { \times } 2 { \mathsf { A } } , 0 { \times } 0 2$   
接收时，在校验 CRC 之前进行反转义：  
$0 { \times } 2 { \mathsf { A } } , 0 { \times } 0 1 \ldots 0 { \times } 2 3$   
$0 { \times } 2 { \mathsf { A } } , 0 { \times } 0 2  0 { \times } 2 { \mathsf { A } }$

# 5.3.3 数据包结构

一个完整的数据包在转义前的结构定义见下表。

<table><tr><td>组成部分</td><td>定义</td><td>长度</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>消息头</td><td>消息ID</td><td>1</td><td>BYTE</td><td>消息的唯一标识</td></tr><tr><td></td><td>消息体属性</td><td>2</td><td>WORD</td><td>定义消息体分包、加密及长 度</td></tr><tr><td></td><td>企业统一社会 信用代码</td><td>18</td><td>STRING[18]</td><td>企业的18位统一社会信用代 码</td></tr></table>

<table><tr><td></td><td>消息流水号</td><td>2</td><td>WORD</td><td>由发送方维护，从0开始循 环累加</td></tr><tr><td></td><td>消息包封装项</td><td>(可选)</td><td></td><td>当消息体属性中分包标识为 1时存在</td></tr><tr><td>消息体</td><td>数据单元</td><td>N</td><td></td><td>数据单元格式和定义应符合 第6章要求</td></tr><tr><td>校验码</td><td>CRC-16校验码</td><td>2</td><td>WORD</td><td>从消息头到消息体尾部的 CRC-16(CCITT）校验值</td></tr></table>

# 5.3.4 消息体属性

消息体属性字段为WORD类型，结构如下：

<table><tr><td>位(bit)</td><td>15-14</td><td>13</td><td>12-10</td><td>9-0</td></tr><tr><td>定义</td><td>保留</td><td>分包</td><td>加密方式</td><td>消息体长度</td></tr></table>

 消息体长度(bit0-9): 消息体的总长度，最大为1023字节。$\bullet$ 加密方式 (bit10-12): 000 表示不加密；001 表示 SM4 加密；其他保留。. 分包 (bit13): 1 表示消息体为长消息，进行了分包。0 表示不分包。

# 5.3.5 消息包封装项

当消息体属性中分包标识位为1时，消息头中应增加消息包封装项。

<table><tr><td>字段</td><td>长度</td><td>数据类型</td><td>描述</td></tr><tr><td>消息总包数</td><td>2</td><td>WORD</td><td>该消息被分包后的总包 数</td></tr><tr><td>包序号</td><td>2</td><td>WORD</td><td>当前包的序号，从1开 始</td></tr></table>

# 5.3.6 校验码

校验码采用 CRC-16/CCITT-FALSE 算法，多项式为 $\mathtt { X } ^ { \wedge } 1 6 + \mathtt { X } ^ { \wedge } 1 2 + \mathtt { X } ^ { \wedge } 5 + \mathtt { 1 }$ $( 0 \times 1 0 2 1 \cdot$ ）。校验范围为从消息头的第一个字节到消息体的最后一个字节。

# 5.4 命令单元

<table><tr><td>消息 ID</td><td>定义</td><td>数据流向</td><td>备注</td></tr><tr><td colspan="5">客户端消息 (上行)</td></tr><tr><td>0x01</td><td>客户端通用应答</td><td>c-&gt;s</td><td>对服务端下行消息的通用应答</td></tr><tr><td>0x02</td><td>客户端心跳</td><td>c-&gt;s</td><td>用于维持连接</td></tr><tr><td>0x03</td><td>平台登出</td><td>c-&gt;s</td><td>客户端主动断开连接</td></tr><tr><td>0x04</td><td>补发信息上报</td><td>c-&gt;s</td><td>上报历史数据处理活动信息</td></tr><tr><td>0x06</td><td>事件数据上报</td><td>c-&gt;s</td><td>上报客户端数据安全相关事件</td></tr><tr><td>0x10</td><td>平台登入</td><td>c-&gt;s</td><td>客户端鉴权请求</td></tr><tr><td>0x20</td><td>实时信息上报</td><td>c-&gt;s</td><td>上报各数据处理活动信息</td></tr><tr><td>0×70</td><td>客户端密钥交换</td><td>c-&gt;s</td><td>客户端上报公钥或对服务端密钥请求的响应</td></tr><tr><td colspan="4">服务端消息 (下行)</td></tr><tr><td>0x81</td><td>服务端通用应答</td><td>s-&gt;C</td><td>对客户端上行消息的通用应答</td></tr><tr><td>0x83</td><td>控制指令</td><td>s-&gt;C</td><td>服务端向客户端下发控制类指令</td></tr><tr><td>0x90</td><td>平台登入应答</td><td>s-&gt;C</td><td>对平台登入请求的专门应答</td></tr><tr><td>OxF0</td><td>平台密钥交换</td><td>S-&gt;C</td><td>服务端下发公钥或请求客户端公钥</td></tr></table>

# 5.4.2 应答标志

<table><tr><td>编码</td><td>定义</td><td>说明</td></tr><tr><td>0x01</td><td>成功</td><td>接收到的信息正确</td></tr><tr><td>0x02</td><td>错误：企业ID重复</td><td>企业ID 重复错误</td></tr><tr><td>0x03</td><td>错误：企业ID不存在</td><td>企业ID未在系统中备案</td></tr><tr><td>0x04</td><td>错误：鉴权码错误</td><td>鉴权码验证失败</td></tr><tr><td>OxFE</td><td>命令</td><td>表示数据包为命令包，而非应答包</td></tr></table>

# 6. 数据单元格式和定义

# 6.1 通用应答

客户端通用应答 (0x01) 和服务端通用应答（0x81） 使用相同的消息  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>应答流水号</td><td>2</td><td>WORD</td><td>对应请求消息的流水号</td></tr><tr><td>应答ID</td><td>1</td><td>BYTE</td><td>对应请求消息的 ID</td></tr><tr><td>结果</td><td>1</td><td>BYTE</td><td>0：成功/确认；1：失败；2：消息 有误；3：不支持</td></tr></table>

# 6.2 平台登入

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>登入流水号</td><td>2</td><td>WORD</td><td>从1开始，每日循环累加</td></tr><tr><td>平台统一社会信用代码</td><td>18</td><td>STRING[18]</td><td>企业的18位统一社会信用代码</td></tr><tr><td>平台密码</td><td>20</td><td>STRING</td><td>平台登入密码</td></tr><tr><td>平台类型</td><td>1</td><td>BYTE</td><td>0x01：汽车企业；0x02：地图服务商； 0x03：智驾方案提供商；0x04：平台运营 商；0x05:其他</td></tr></table>

# 6.3 平台登出

客户端平台下线前，应向服务端平台发送平台登出消息（命令标识 $0 \times 0 3 ^ { \cdot }$ ）。服务端应监控与客户端平台的TCP连接状态。若连接在未收到登出消息的情况下异常中断，服务端应判定为一次“平台异常登出”事件，并记录相关信息，作为风险评估的输入之。

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>登出流水号</td><td>2</td><td>WORD</td><td>与当次登入流水号一致</td></tr><tr><td>登出原因</td><td>1</td><td>BYTE</td><td>0x01:正常登出;</td></tr></table>

<table><tr><td></td><td></td><td></td><td>0x02:通信异常； 0x03：平台故障; 0x04:超时登出</td></tr></table>

# 6.4 实时信息上报

本命令（命令标识 $0 \times 2 0$ ）用于客户端平台向服务端平台实时上报数据处理活动信息。数据包的数据单元中可封装一个或多个信息体，每个信息体由“信息类型标志”和“信息体”构成，具体格式定义如下。

# 实时信息上报格式

表\* 实时信息上报总体格式  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>信息类型标志(1)</td><td>1</td><td>BYTE</td><td>标识该信息体对应的数 据处理环节，定义见 表8-3。</td></tr><tr><td>信息体(1)</td><td></td><td></td><td>对应信息类型的数据详 情，结构根据信息类型 标志变化。</td></tr><tr><td>：</td><td>：</td><td></td><td>…</td></tr><tr><td>信息类型标志 (n)</td><td>1</td><td>BYTE</td><td>：</td></tr><tr><td>信息体(n)</td><td>一</td><td>1</td><td>：</td></tr></table>

# 信息类型标志

表\* 信息类型标志定义  

<table><tr><td>类型编码</td><td>说明</td></tr><tr><td>0x01</td><td>车端收集阶段数据处理活动信息</td></tr><tr><td>0x02</td><td>车端存储阶段数据处理活动信息</td></tr></table>

<table><tr><td>0x03</td><td>车端传输阶段数据处理活动信息</td></tr><tr><td>0x04</td><td>云端收集阶段数据处理活动信息</td></tr><tr><td>0x05</td><td>云端存储阶段数据处理活动信息</td></tr><tr><td>0x06</td><td>云端传输阶段数据处理活动信息</td></tr><tr><td>0x07</td><td>云端加工阶段数据处理活动信息</td></tr><tr><td>0x08</td><td>云端提供阶段数据处理活动信息</td></tr><tr><td>0x09</td><td>云端公开阶段数据处理活动信息</td></tr><tr><td>0x0a</td><td>云端销毁阶段数据处理活动信息</td></tr></table>

# 6.4.1 车端数据处理活动

收集阶段上报数据格式和定义 （车端）  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>车辆VIN码</td><td>17</td><td>STRING[17]</td><td>17位车辆唯一VIN码</td></tr><tr><td>定位状态</td><td>1</td><td>BYTE</td><td>状态位定义</td></tr><tr><td>经度</td><td>4</td><td>DWORD</td><td>以度为单位的值乘以10^6</td></tr><tr><td>纬度</td><td>4</td><td>DWORD</td><td>以度为单位的值乘以10^6</td></tr><tr><td>高度</td><td>4</td><td>DWORD</td><td>以米为单位的值乘以100</td></tr><tr><td>数据类型</td><td>4</td><td>DWORD</td><td>BitMap 格式。服务端可根据此字段判断后续 “采用的安全处理技术”是否完备。</td></tr><tr><td>业务形式</td><td>1</td><td>BYTE</td><td>0x01：导航电子地图更新服务；0x02：研发测 试；0x03：众源更新；0x04：其他</td></tr><tr><td>安全处理技术</td><td>2</td><td>WORD</td><td>BitMap 格式，按位表示。bito:地理围栏技 术；bit1:位置数据保密处理技术；bit2:属性 脱敏技术；bit3:里程限制技术。</td></tr><tr><td>安全处理技术类型</td><td>1</td><td>BYTE</td><td>bito:自研… （预留)</td></tr></table>

存储阶段上报数据格式和定义 （车端）  

<table><tr><td>地图审图号</td><td>32</td><td>STRING</td><td>如果业务活动涉及车载地图的更新或使用，则 应填写有效审图号。若不涉及，则以0x00填 充。</td></tr></table>

传输阶段上报数据格式和定义 （车端）  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>车辆VIN码</td><td>17</td><td>STRING[17]</td><td>17位车辆唯一VIN码</td></tr><tr><td>本次存储数据关联 里程</td><td>4</td><td>DWORD</td><td>本次上报的数据所对应的道路里程，精度 0.1km</td></tr><tr><td>坐标处理手段</td><td>1</td><td>BYTE</td><td>0x00：未处理真实坐标；0x01：坐标偏转插 件；0x02：坐标偏转算法；0x03：其他</td></tr><tr><td>访问控制状态</td><td>1</td><td>BYTE</td><td>0x01：关闭访问控制；0x02：启用访问控制</td></tr><tr><td>加密存储状态</td><td>1</td><td>BYTE</td><td>是否采用加密存储，0x00：未加密； 0x01:SM2; 0x02:SM4; 0x03:AES; 0x04:RSA; 0x05:其他</td></tr></table>

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>车辆VIN码</td><td>17</td><td>STRING[17]</td><td>17位车辆唯一VIN码</td></tr><tr><td>传输目的地</td><td>18</td><td>STRING[18]</td><td>接收方企业的18位统一社会信用代码</td></tr><tr><td>传输目的地IP</td><td>16</td><td>BYTE[16]</td><td>IPv4 地址使用 IPv4映射的 IPv6 地址格式表示</td></tr><tr><td>传输覆盖里程</td><td>4</td><td>DWORD</td><td>传输数据覆盖里程，0.1km精度</td></tr><tr><td>坐标处理标志</td><td>1</td><td>BYTE</td><td>0x01：未传输真实坐标；0x02：传输真实坐 标</td></tr><tr><td>坐标处理手段</td><td>1</td><td>BYTE</td><td>0x01：坐标偏转插件；0x02：坐标偏转算 法；0x03：其他</td></tr><tr><td>传输区域类型</td><td>1</td><td>BYTE</td><td>0x01：境内；0x02：境外</td></tr></table>

<table><tr><td>通信网络类型</td><td>1</td><td>BYTE</td><td>0x01：公共网络；0x02：专用网络；0x03: 国家认定网络；0x04：其他</td></tr><tr><td>安全传输协议</td><td>1</td><td>BYTE</td><td>0x01:HTTP; 0x02:HTTPS; 0x03:国密通道; 0x04:其他</td></tr><tr><td>车外传输功能</td><td>1</td><td>BYTE</td><td>0x01：具备；0x02：不具备</td></tr></table>

# 6.4.2 云端数据处理活动

为实现对云端数据的全生命周期穿透式监管，所有云端数据处理活动的上报信息中，必须包含“数据包唯一标识”。该标识在数据包首次于云端生成或入库时被创建，并在后续的存储、加工、提供、公开、销毁等所有环节中保持不变且随之上报。

收集阶段上报数据格式和定义 （云端）  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据包唯一标识</td><td>36</td><td>STRING</td><td>数据的唯一标识（推荐采用UUID格式）。</td></tr><tr><td>数据重要程度</td><td>1</td><td>BYTE</td><td>0x01：一般数据；0x02：重要数据</td></tr><tr><td>数据来源类型</td><td>1</td><td>BYTE</td><td>0x01：本企业研采车；0x02：本企业量产 车；0x03：第三方企业提供；0x04：其他</td></tr><tr><td>数据来源方标识</td><td>32</td><td>STRING</td><td>根据“数据来源类型”填写车端VIN或第三方 企业统一社会信用代码。</td></tr><tr><td>数据用途</td><td>1</td><td>BYTE</td><td>0x01：数据汇聚及脱敏处理；0x02：导航电 子地图制作；0x03：场景库制作及服务</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员；0x02：一般数据 操作人员</td></tr></table>

# 存储阶段上报数据格式和定义 （云端）

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据重要程度</td><td>1</td><td>BYTE</td><td>0x01：一般数据0x02：重要数据</td></tr></table>

<table><tr><td>存储设备类型</td><td>1</td><td>BYTE</td><td>0x01：服务器；0x02：存储阵列；0x03：网 络存储；0x04：硬盘；0x05：其他</td></tr><tr><td>存储区域标识</td><td>1</td><td>BYTE</td><td>0x01：境内存储；0x02：境外存储</td></tr><tr><td>存储设备IP</td><td>16</td><td>BYTE[16]</td><td>IPv4 地址使用 IPv4映射的IPv6地址格式表 示；用于识别是在境内还是境外</td></tr><tr><td>存储区类型</td><td>1</td><td>BYTE</td><td>0x01：原始数据处理区；0x02：数据共享 区；0x03：数据中转区；0x04：其他区域</td></tr><tr><td>数据分区标识</td><td>1</td><td>BYTE</td><td>0x01：已分区存储；0x02：未分区存储</td></tr><tr><td>存储保障标识</td><td>1</td><td>BYTE</td><td>Bito：完整性，Bit1：真实性，Bit2：可用性 (1=保障，0=未保障)</td></tr><tr><td>脱敏处理标识</td><td>1</td><td>BYTE</td><td>0x01：已脱敏；0x02：未脱敏</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员；0x02：一般数据 操作人员</td></tr></table>

# 传输阶段上报数据格式和定义 （云端）

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据重要程度</td><td>1</td><td>BYTE</td><td>0x01：一般0x02：重要</td></tr><tr><td>传输目的地</td><td>18</td><td>STRING[18]</td><td>接收方企业的18位统一社会信用代 码</td></tr><tr><td>通信通道类型</td><td>1</td><td>BYTE</td><td>0x01：国密通道；0x02：非国密通 道</td></tr><tr><td>公共网络使用标识</td><td>1</td><td>BYTE</td><td>0x01：使用公共信息网络；0x02: 不使用</td></tr><tr><td>传输方式</td><td>1</td><td>BYTE</td><td>0x01：网络传输；0x02：硬盘拷 贝；0x03：其他</td></tr><tr><td>通信网络类型</td><td>1</td><td>BYTE</td><td>0x01：公共网络；0x02：专用网</td></tr></table>

<table><tr><td></td><td></td><td></td><td>络；0x03：国家认定网络；0x04: 其他</td></tr><tr><td>安全传输协议</td><td>1</td><td>BYTE</td><td>0x01:HTTP; 0X02:HTTPS; 0x03:国密 通道；0x04：其他</td></tr><tr><td>硬盘传输记录</td><td>1</td><td>BYTE</td><td>0x01：记录完整；0x02：记录缺失</td></tr><tr><td>硬盘安全措施</td><td>1</td><td>BYTE</td><td>0x01：安全措施合规；0x02：不合 规</td></tr><tr><td>传输保障标识</td><td>1</td><td>BYTE</td><td>Bit0：完整性，Bit1：真实性, Bit2：可用性(1=保障，0=未保障)</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员；0x02: 一般数据操作人员</td></tr></table>

# 加工阶段上报数据格式和定义 （云端）

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据包唯一标识</td><td>36</td><td>STRING</td><td>被加工的原始数据包ID</td></tr><tr><td>加工类型</td><td>1</td><td>BYTE</td><td>0x01：数据汇聚及脱敏处理; 0x02：导航电子地图制作；0x03: 场景库制作及服务；0x04：互联网 地图服务；0x05：其他</td></tr><tr><td>平面位置精度合规 性</td><td>1</td><td>BYTE</td><td>0x01：符合要求；0x02：不符合要 求</td></tr><tr><td>脱敏状态</td><td>1</td><td>BYTE</td><td>0x01：已脱敏；0x02：未脱敏</td></tr><tr><td>是否生成新数据包</td><td>1</td><td>BYTE</td><td>0x01:是；0x02:否</td></tr><tr><td>新数据包数量</td><td>2</td><td>WORD</td><td>若生成新数据包，此处填写生成的 新数据包个数。否则填0。</td></tr><tr><td>新数据包ID列表</td><td>N</td><td>STRING</td><td>若生成了新数据包，此处填写所有 新包的唯一标识ID，以逗号分隔。</td></tr></table>

提供阶段上报数据格式和定义 （云端）  

<table><tr><td>新数据包数据类型</td><td>4</td><td>DWORD</td><td>BitMap 格式，标识新数据包中包含 的数据类型。</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员；0x02: -般数据操作人员</td></tr></table>

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据包ID列表</td><td>N</td><td>STRING</td><td>本次提供操作所涉及的所有数据包 的唯一标识ID，以逗号分隔。</td></tr><tr><td>提供的数据包数量</td><td>2</td><td>WORD</td><td>本次提供的数据包总数</td></tr><tr><td>接收方类型</td><td>1</td><td>BYTE</td><td>0x01：境内非外商投资企业; 0x02：境内外商投资企业；0x03: 境外接收方；0x04：个人</td></tr><tr><td>接收方安全能力</td><td>1</td><td>BYTE</td><td>0x01：安全能力满足；0x02：不足</td></tr><tr><td>是否满足豁免条件</td><td>1</td><td>BYTE</td><td>0x01:是；0x02:否</td></tr><tr><td>采用的安全处理技 术</td><td>2</td><td>WORD</td><td>BitMap格式</td></tr><tr><td>合同协议签署状态</td><td>1</td><td>BYTE</td><td>0x01：无合同协议；0x02：有合同 协议</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员；0x02: -般数据操作人员</td></tr></table>

# 公开阶段上报数据格式和定义 （云端）

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据包ID列表</td><td>N</td><td>STRING</td><td>公开的数据包唯一标识ID,以逗号分 隔</td></tr><tr><td>公开渠道与范围</td><td>256</td><td>STRING</td><td>填写公开渠道的URL或平台/媒介名</td></tr></table>

<table><tr><td></td><td></td><td></td><td>称，并明确可访问范围。</td></tr><tr><td>地图审核程序执行 机构</td><td>18</td><td>STRING[18]</td><td>执行机构的18位统一社会信用代码</td></tr><tr><td>地图审核程序执行 时间</td><td>6</td><td>BYTE[6]</td><td>BCD 码，YY-MM-DD-hh-mm-ss</td></tr><tr><td>脱敏处理标志</td><td>1</td><td>BYTE</td><td>0x01：未经脱敏处理；0x02：已经 安全处理</td></tr><tr><td>风险评估状态</td><td>1</td><td>BYTE</td><td>0x01：未执行风险评估；0x02：已 执行</td></tr><tr><td>安全风险评估机构 代码</td><td>18</td><td>STRING[18]</td><td>评估机构的18位统一社会信用代码</td></tr><tr><td>风险评估执行时间</td><td>6</td><td>BYTE[6]</td><td>BCD 码，YY-MM-DD-hh-mm-Ss</td></tr><tr><td>数据审查状态</td><td>1</td><td>BYTE</td><td>0x01：履行安全审查；0x02：未履 行</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员；0x02: -般数据操作人员</td></tr></table>

# 销毁阶段上报数据格式和定义 （云端）

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>数据包ID列表</td><td>N</td><td>STRING</td><td>本次销毁的所有数据包的唯一标 识ID，以逗号分隔。</td></tr><tr><td>销毁的数据包数量</td><td>2</td><td>WORD</td><td>本次销毁的数据包总数。</td></tr><tr><td>销毁方式</td><td>1</td><td>BYTE</td><td>0x01：逻辑删除；0x02：文件覆 盖/低级格式化；0x03：物理销毁</td></tr><tr><td>审批状态</td><td>1</td><td>BYTE</td><td>0x01：未经审批；0x02：已经审 批</td></tr></table>

<table><tr><td>销毁流程状态</td><td>1</td><td>BYTE</td><td>0x01：未完成；0x02：已完成</td></tr><tr><td>操作员身份</td><td>1</td><td>BYTE</td><td>0x01：重要数据操作人员; 0x02：一般数据操作人员</td></tr></table>

# 6.5 事件数据上报

本命令（命令标识 $0 \times 0 6 ^ { \cdot }$ ）用于客户端平台在监测到特定的数据安全相关事件时，实时向服务端平台上报。

6.5.1 事件数据上报总体格式  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件数量 (N)</td><td>1</td><td>BYTE</td><td>本次上报的事件总数。</td></tr><tr><td>事件类型 (1)</td><td>1</td><td>BYTE</td><td>第1个事件的类型，定 义见下表。</td></tr><tr><td>事件信息体长度 (1)</td><td>2</td><td>WORD</td><td>第1个事件信息体的字 节长度。</td></tr><tr><td>事件信息体 (1)</td><td></td><td></td><td>第1个事件的详情，结 构随事件类型变化。</td></tr><tr><td></td><td></td><td></td><td></td></tr></table>

# 6.5.2事件类型定义

<table><tr><td>事件类型代码</td><td>事件名称</td><td>上报主体</td><td>触发场景/说明</td></tr><tr><td>车端事件</td><td></td><td></td><td></td></tr><tr><td>0x01</td><td>通信链路中断</td><td>车端</td><td>客户端检测到与服务端长时间无法 连接。</td></tr><tr><td>0x02</td><td>数据缓存溢出</td><td>车端</td><td>因链路中断导致本地缓存空间不 足，部分待上报数据丢失。</td></tr><tr><td>0x03</td><td>平台服务异常</td><td>车端</td><td>客户端自身关键服务（如数据采</td></tr></table>

<table><tr><td></td><td></td><td></td><td>集、加密模块）发生故障。</td></tr><tr><td>0x04</td><td>核心安全策略异常</td><td>车端</td><td>车端核心安全处理技术模块发生故 障或被禁用。</td></tr><tr><td>0x05 - 0x0F</td><td>(预留)</td><td>车端</td><td></td></tr><tr><td>云端事件</td><td></td><td></td><td></td></tr><tr><td>0x10</td><td>数据泄露/丢失</td><td>云端</td><td>敏感数据在存储、传输等环节发生 非授权访问或丢失。</td></tr><tr><td>0x11</td><td>核心安全策略异常</td><td>云端</td><td>如加密模块、脱敏工具等核心安全 组件发生故障或被禁用。</td></tr><tr><td>0x12</td><td>权限越权</td><td>云端</td><td>监测到账号 (特别是高权限账号) 的异常数据访问行为。</td></tr><tr><td>0x13</td><td>数据补发缓存池超 限</td><td>云端</td><td>用于数据补发的本地缓存空间使用 率超过阈值（如90%）。</td></tr><tr><td>0x14</td><td>平台异常登出</td><td>云端 (服务端)</td><td>由服务端生成，记录客户端的非正 常离线。</td></tr><tr><td>Ox15 -OxFF</td><td>(预留)</td><td>云端</td><td></td></tr></table>

# 6.5.3 事件信息体格式

0x01 通信链路中断事件数据格式  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td>BCD 码，YY-MM-DD- hh-mm-ss</td></tr><tr><td>中断持续时间</td><td>4</td><td>DWORD</td><td>单位：秒</td></tr></table>

# 0x02 数据缓存溢出事件数据格式

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr></table>

0x03 平台服务异常事件数据格式  

<table><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td>BCD 码，YY-MM-DD- hh-mm-ss</td></tr><tr><td>丢失数据条数</td><td>4</td><td>DWORD</td><td>溢出导致丢失的数据记 录条数</td></tr><tr><td>丢失数据起始消息ID</td><td>36</td><td>STRING</td><td>丢失数据段的第一条消 息的唯一标识ID（若 可追潮)</td></tr></table>

0x04车端核心安全策略异常事件数据格式  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td>BCD码，YY-MM-DD- hh-mm-ss</td></tr><tr><td>异常模块名称</td><td>32</td><td>STRING</td><td>发生异常的内部服务或 模块名称</td></tr><tr><td>错误代码</td><td>4</td><td>DWORD</td><td>具体的错误码</td></tr></table>

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td>BCD码，YY-MM-DD-hh-mm-ss</td></tr><tr><td>异常模块类型</td><td>1</td><td>BYTE</td><td>0x01：保密处理；0x02：地理围 栏；0x03：属性脱敏；0x04：里程 限制；OxFF:其他</td></tr><tr><td>异常描述</td><td>128</td><td>STRING</td><td>对异常状态的具体描述，如“地理 围栏规则加载失败”。</td></tr></table>

# $\pmb { 0 } \pmb { \times } \pmb { 1 0 }$ 数据泄露/丢失事件数据格式

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr></table>

0x11 云端核心安全策略异常事件数据格式  

<table><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td> BCD 码，YY-MM-DD-hh-mm-ss</td></tr><tr><td>数据包ID列表</td><td>N</td><td>STRING</td><td>涉及泄露/丢失的数据包ID，以逗号 分隔。</td></tr><tr><td>发生阶段</td><td>1</td><td>BYTE</td><td>0x01：收集，0x02:存储，0x03: 加工，0x04:传输，0x05：提供, 0x06:公开</td></tr><tr><td>泄露/丢失原因</td><td>1</td><td>BYTE</td><td>0x01：存储介质丢失，0x02：网络 攻击，0x03：内部人员违规, 0x04：配置错误，OxFF：其他</td></tr></table>

0x12 权限越权事件数据格式  

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件发生时间</td><td>6</td><td>BYTE[]</td><td>BCD 码，YY-MM-DD-hh-mm-ss</td></tr><tr><td>异常模块类型</td><td>1</td><td>BYTE</td><td>0x01：地理围栏；0x02：图像识 别；0x03：语义判定；0x04：去标 识化；OxFF:其他</td></tr><tr><td>异常描述</td><td>128</td><td>STRING</td><td>对异常状态的具体描述，如“去标 识化裁切算法执行失败”。</td></tr></table>

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td>BCD码，YY-MM-DD- hh-mm-ss</td></tr><tr><td>越权账号</td><td>32</td><td>STRING</td><td>实施越权操作的账号 ID或名称。</td></tr><tr><td>源IP地址</td><td>16</td><td>BYTE[16]</td><td>实施越权操作的源IP 地址。</td></tr></table>

<table><tr><td>违规操作描述</td><td>128</td><td>STRING</td><td>对具体越权行为的描 述，如“尝试访问未授 权数据表”。</td></tr></table>

# 0x13 数据补发缓存池超限事件数据格式

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>事件发生时间</td><td>6</td><td>BYTE[6]</td><td>BCD码，YY-MM-DD- hh-mm-ss</td></tr><tr><td>缓存池当前使用率</td><td>1</td><td>BYTE</td><td>当前缓存池已使用容量 的百分比，取值0- 100。</td></tr><tr><td>缓存池容量阈值</td><td>1</td><td>BYTE</td><td>系统设定的缓存池容量 告警阈值，取值0- 100。</td></tr><tr><td>超限时数据条数</td><td>4</td><td>DWORD</td><td>缓存池中待补发的数据 总条数。</td></tr></table>

# 0x14 平台异常登出事件

注：此事件由服务端在检测到客户端TCP连接异常中断时生成，客户端无需上报。

# 6.6 控制指令

本命令（标识 $0 \times 8 3$ ）为服务端向客户端下发控制类指令的统一格式。

<table><tr><td>数据表示内容</td><td>长度/字节</td><td>数据类型</td><td>描述及要求</td></tr><tr><td>控制类型</td><td>1</td><td>BYTE</td><td>0x01：数据查询请求; 0x02：远程参数配置； 0x03：远程诊断……</td></tr><tr><td>控制参数长度</td><td>2</td><td>WORD</td><td>后续控制参数的总长度</td></tr></table>

<table><tr><td>控制参数</td><td>N</td><td>BYTE[N]</td><td>根据“控制类型”不同，结构不同。</td></tr></table>

# 7.通信安全机制

# 7.1 身份鉴别

客户端平台的身份鉴别通过平台登入（命令标识 $0 \times 1 0 ^ { \cdot }$ ）流程完成。

备案与凭证获取：客户端平台在属地监测平台完成备案后，将获得用于平台登入的密码。其平台统一社会信用代码将作为其唯一身份标识。

登入鉴权： 客户端平台连接服务端平台后，必须立即发送平台登入消息。服务端平台通过验证消息中的平台统一社会信用代码和平台密码的有效性，来确认客户端身份。鉴权失败处理：鉴权失败时，服务端平台应通过应答消息返回具体的失败原因，并立即断开连接。

# 7.2 数据加密

本协议通过消息头中的消息体属性字段来支持对数据单元的加密保护。

支持算法：本协议支持SM2（非对称加密）、SM4（对称加密）等国家商用密码算法，以及 AES、RSA 等国际通用算法。

加密范围：加密操作仅针对数据包中的数据单元部分。

安全要求：涉及敏感信息、重要数据和核心数据的数据单元，必须采用加密方式进行传输。

# 7.3 数据校验

本协议通过数据包尾部的校验码字段来保证数据包在传输过程中的完整性。

校验算法： 采用 CRC-16/CCITT-FALSE。

校验范围：从消息头的第一个字节开始，到消息体的最后一个字节。

加解密与校验顺序： 当数据单元需要加密时，发送方应先对数据单元进行加密，然后对包含加密消息体的报文计算 CRC 校验码。接收方在收到数据后，应先对报文进行CRC校验，校验通过后再对消息体进行解密。

# 7.4 密钥管理

为保障对称加密算法（如SM4）密钥的安全分发，本协议定义了密钥交换机制。

<table><tr><td>数据名称</td><td>英文简写</td><td>数据类型</td><td>长度 (字节)</td><td>含义</td></tr><tr><td>密钥类型</td><td>KEY_TYPE</td><td>BYTE</td><td>1</td><td>0x01:SM2 公钥；0x02:SM4 会话密钥</td></tr><tr><td>密钥长度</td><td>KEY_LENGTH</td><td>WORD</td><td>2</td><td>密钥字段的总字节数</td></tr><tr><td>密钥</td><td>KEY</td><td>BYTE[n]</td><td>n</td><td>密钥内容。当交换SM4密钥 时，该密钥内容应使用对方 的 SM2 公钥进行加密保护。</td></tr><tr><td>启用时间</td><td>ENABLE_TIME</td><td>STRING[13]</td><td>13</td><td>密钥启用时间，UTC时间戳 (毫秒级)</td></tr><tr><td>失效时间</td><td>EXPIRE_TIME</td><td>STRING[13]</td><td>13</td><td>密钥失效时间，UTC时间戳 (毫秒级)</td></tr></table>