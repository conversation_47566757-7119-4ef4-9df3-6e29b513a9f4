// 认证相关类型定义

// 用户身份验证信息
export interface PersonalVerificationForm {
  realName: string // 真实姓名
  idCardNumber: string // 身份证号码
  phoneNumber: string // 手机号码
  verifyCode: string // 短信验证码
}

// 认证结果
export interface VerificationResult {
  success: boolean
  message: string
  userType: 'individual' | 'enterprise' // 个人用户还是企业用户
  userId?: string
  token?: string
}

// 企业认证引导信息
export interface EnterpriseGuidance {
  showGuide: boolean
  enterpriseName?: string
  socialCreditCode?: string
  message: string
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  pattern?: RegExp
  message: string
  validator?: (value: string) => boolean
}

// 短信验证码相关
export interface SmsCodeState {
  sending: boolean
  countdown: number
  sent: boolean
}
