<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          实时监控系统运行状态、性能指标和资源使用情况，提供全方位的系统健康度监测
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Badge
          :variant="systemStatus === 'healthy' ? 'default' : 'destructive'"
          class="flex items-center gap-1"
        >
          <div
            :class="`w-2 h-2 rounded-full ${systemStatus === 'healthy' ? 'bg-ocean-success animate-pulse' : 'bg-status-error'}`"
          ></div>
          {{ systemStatus === 'healthy' ? '系统正常' : '系统异常' }}
        </Badge>
        <Button @click="refreshData" :disabled="isRefreshing" variant="outline" size="sm">
          <RotateCcw :class="`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`" />
        </Button>
        <Select v-model="timeRange">
          <SelectTrigger class="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1h">近1小时</SelectItem>
            <SelectItem value="6h">近6小时</SelectItem>
            <SelectItem value="24h">近24小时</SelectItem>
            <SelectItem value="7d">近7天</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">CPU使用率</p>
              <div class="flex items-baseline gap-2">
                <p class="text-2xl font-bold">{{ systemMetrics.cpu.toFixed(1) }}%</p>
                <Badge
                  :variant="
                    systemMetrics.cpu > 80
                      ? 'destructive'
                      : systemMetrics.cpu > 60
                        ? 'secondary'
                        : 'outline'
                  "
                  class="text-xs"
                >
                  {{ systemMetrics.cpu > 80 ? '高' : systemMetrics.cpu > 60 ? '中' : '正常' }}
                </Badge>
              </div>
              <div class="w-full bg-muted rounded-full h-2 mt-2">
                <div
                  class="bg-primary rounded-full h-2 transition-all duration-300"
                  :style="{ width: `${Math.min(systemMetrics.cpu, 100)}%` }"
                ></div>
              </div>
            </div>
            <Cpu class="w-8 h-8 text-primary" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">内存使用率</p>
              <div class="flex items-baseline gap-2">
                <p class="text-2xl font-bold">{{ systemMetrics.memory.toFixed(1) }}%</p>
                <Badge
                  :variant="
                    systemMetrics.memory > 85
                      ? 'destructive'
                      : systemMetrics.memory > 70
                        ? 'secondary'
                        : 'outline'
                  "
                  class="text-xs"
                >
                  {{ systemMetrics.memory > 85 ? '高' : systemMetrics.memory > 70 ? '中' : '正常' }}
                </Badge>
              </div>
              <div class="w-full bg-muted rounded-full h-2 mt-2">
                <div
                  class="bg-ocean-info rounded-full h-2 transition-all duration-300"
                  :style="{ width: `${Math.min(systemMetrics.memory, 100)}%` }"
                ></div>
              </div>
            </div>
            <MemoryStick class="w-8 h-8 text-ocean-info" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">磁盘使用率</p>
              <div class="flex items-baseline gap-2">
                <p class="text-2xl font-bold">{{ systemMetrics.disk.toFixed(1) }}%</p>
                <Badge
                  :variant="
                    systemMetrics.disk > 90
                      ? 'destructive'
                      : systemMetrics.disk > 75
                        ? 'secondary'
                        : 'outline'
                  "
                  class="text-xs"
                >
                  {{ systemMetrics.disk > 90 ? '高' : systemMetrics.disk > 75 ? '中' : '正常' }}
                </Badge>
              </div>
              <div class="w-full bg-muted rounded-full h-2 mt-2">
                <div
                  class="bg-risk-medium rounded-full h-2 transition-all duration-300"
                  :style="{ width: `${Math.min(systemMetrics.disk, 100)}%` }"
                ></div>
              </div>
            </div>
            <HardDrive class="w-8 h-8 text-risk-medium" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">网络流量</p>
              <div class="flex items-baseline gap-2">
                <p class="text-2xl font-bold">
                  {{ formatBytes(systemMetrics.networkIn + systemMetrics.networkOut) }}
                </p>
                <Badge variant="outline" class="text-xs">实时</Badge>
              </div>
              <div class="text-xs text-muted-foreground mt-2">
                ↑{{ formatBytes(systemMetrics.networkOut) }}/s ↓{{
                  formatBytes(systemMetrics.networkIn)
                }}/s
              </div>
            </div>
            <Network class="w-8 h-8 text-ocean-success" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 图表面板 -->
    <div class="space-y-6">
      <!-- 系统资源趋势图 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader class="bg-muted/30 border-b">
            <CardTitle class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <TrendingUp class="w-6 h-6" />
                系统资源使用率
              </div>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-ocean-success rounded-full animate-pulse"></div>
                <span class="text-xs text-muted-foreground">实时</span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent class="p-0">
            <div class="relative">
              <LineChart
                :series="systemResourcesSeries"
                :height="300"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 网络流量图 -->
        <Card>
          <CardHeader class="bg-muted/30 border-b">
            <CardTitle class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Network class="w-6 h-6" />
                网络流量监控
              </div>
              <div class="flex items-center gap-2 text-xs text-muted-foreground">
                <span>↑ {{ formatBytes(systemMetrics.networkOut) }}/s</span>
                <span>↓ {{ formatBytes(systemMetrics.networkIn) }}/s</span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent class="p-0">
            <LineChart
              :series="networkTrafficSeries"
              :height="300"
            />
          </CardContent>
        </Card>
      </div>

      <!-- 应用响应时间和数据库性能 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader class="bg-muted/30 border-b">
            <CardTitle class="flex items-center gap-2">
              <Server class="w-6 h-6" />
              应用响应时间分析
            </CardTitle>
          </CardHeader>
          <CardContent class="p-0">
            <BarChart
              :data="responseTimeBarData"
              :height="280"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="bg-muted/30 border-b">
            <CardTitle class="flex items-center gap-2">
              <Database class="w-6 h-6" />
              数据库连接池状态
            </CardTitle>
          </CardHeader>
          <CardContent class="p-0">
            <PieChart
              :data="databasePoolPieData"
              :height="280"
            />
          </CardContent>
        </Card>
      </div>

      <!-- 系统负载和告警趋势 -->
      <div class="grid grid-cols-1 lg:grid-cols-1 gap-6">
        <Card>
          <CardHeader class="bg-muted/30 border-b">
            <CardTitle class="flex items-center gap-2">
              <AlertTriangle class="w-6 h-6" />
              告警统计趋势
            </CardTitle>
          </CardHeader>
          <CardContent class="p-0">
            <BarChart
              :data="alertTrendBarData"
              :height="280"
            />
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 应用服务状态 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Server class="w-6 h-6" />
          应用服务状态
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-3">
          <div
            v-for="service in applicationServices"
            :key="service.name"
            class="flex items-center justify-between p-3 border rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div :class="getServiceStatusClass(service.status)"></div>
              <div>
                <div class="font-medium">{{ service.name }}</div>
                <div class="text-sm text-muted-foreground">{{ service.description }}</div>
              </div>
            </div>
            <div class="text-right">
              <Badge
                :variant="
                  service.status === 'running'
                    ? 'default'
                    : service.status === 'warning'
                      ? 'secondary'
                      : 'destructive'
                "
              >
                {{
                  service.status === 'running'
                    ? '正常'
                    : service.status === 'warning'
                      ? '警告'
                      : '异常'
                }}
              </Badge>
              <div class="text-xs text-muted-foreground mt-1">
                响应时间: {{ service.responseTime }}ms
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 数据库连接状态 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Database class="w-6 h-6" />
          数据库监控
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div v-for="db in databaseConnections" :key="db.name" class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="font-medium">{{ db.name }}</div>
              <Badge :variant="db.status === 'connected' ? 'default' : 'destructive'">
                {{ db.status === 'connected' ? '已连接' : '连接失败' }}
              </Badge>
            </div>
            <div class="grid grid-cols-3 gap-2 text-sm">
              <div>
                <div class="text-muted-foreground">活动连接</div>
                <div class="font-medium">{{ db.activeConnections }}/{{ db.maxConnections }}</div>
              </div>
              <div>
                <div class="text-muted-foreground">查询/秒</div>
                <div class="font-medium">{{ db.queriesPerSecond }}</div>
              </div>
              <div>
                <div class="text-muted-foreground">平均响应</div>
                <div class="font-medium">{{ db.avgResponseTime }}ms</div>
              </div>
            </div>
            <div class="w-full bg-muted rounded-full h-1">
              <div
                class="bg-primary rounded-full h-1 transition-all duration-300"
                :style="{
                  width: `${Math.min((db.activeConnections / db.maxConnections) * 100, 100)}%`,
                }"
              ></div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 实时日志和告警 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 实时告警 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <AlertTriangle class="w-6 h-6" />
              实时告警
            </div>
            <Badge variant="outline">{{ alerts.length }}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea class="h-64">
            <div class="space-y-2">
              <div v-if="alerts.length === 0" class="text-center py-8 text-muted-foreground">
                <CheckCircle class="w-12 h-12 mx-auto mb-2 text-ocean-success" />
                <p>系统运行正常，暂无告警</p>
              </div>
              <div
                v-else
                v-for="alert in alerts"
                :key="alert.id"
                class="flex items-start gap-3 p-3 border rounded-lg"
              >
                <component
                  :is="getAlertIcon(alert.level)"
                  :class="`w-4 h-4 flex-shrink-0 mt-0.5 ${getAlertIconColor(alert.level)}`"
                />
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2">
                    <Badge :variant="getAlertVariant(alert.level)" class="text-xs">
                      {{ alert.level }}
                    </Badge>
                    <span class="text-xs text-muted-foreground">{{ alert.timestamp }}</span>
                  </div>
                  <div class="font-medium text-sm mt-1">{{ alert.title }}</div>
                  <div class="text-sm text-muted-foreground">{{ alert.message }}</div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      <!-- 系统日志 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <FileText class="w-6 h-6" />
              系统日志
            </div>
            <div class="flex items-center gap-2">
              <Select v-model="logLevel">
                <SelectTrigger class="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部</SelectItem>
                  <SelectItem value="ERROR">错误</SelectItem>
                  <SelectItem value="WARN">警告</SelectItem>
                  <SelectItem value="INFO">信息</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea class="h-64">
            <div class="space-y-1 font-mono text-xs">
              <div v-for="log in filteredLogs" :key="log.id" class="flex items-start gap-2 py-1">
                <span class="text-muted-foreground whitespace-nowrap">{{ log.timestamp }}</span>
                <Badge :variant="getLogLevelVariant(log.level)" class="text-xs px-1">
                  {{ log.level }}
                </Badge>
                <span class="text-muted-foreground">[{{ log.component }}]</span>
                <span class="flex-1 break-all">{{ log.message }}</span>
              </div>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>

    <!-- 系统详细信息 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Info class="w-6 h-6" />
          系统详细信息
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="space-y-2">
            <div class="font-medium text-muted-foreground">系统信息</div>
            <div class="space-y-1 text-sm">
              <div class="flex justify-between">
                <span>操作系统:</span>
                <span>{{ systemInfo.os }}</span>
              </div>
              <div class="flex justify-between">
                <span>系统架构:</span>
                <span>{{ systemInfo.arch }}</span>
              </div>
              <div class="flex justify-between">
                <span>运行时间:</span>
                <span>{{ systemInfo.uptime }}</span>
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <div class="font-medium text-muted-foreground">硬件信息</div>
            <div class="space-y-1 text-sm">
              <div class="flex justify-between">
                <span>CPU核心:</span>
                <span>{{ systemInfo.cpuCores }}核</span>
              </div>
              <div class="flex justify-between">
                <span>总内存:</span>
                <span>{{ formatBytes(systemInfo.totalMemory) }}</span>
              </div>
              <div class="flex justify-between">
                <span>总磁盘:</span>
                <span>{{ formatBytes(systemInfo.totalDisk) }}</span>
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <div class="font-medium text-muted-foreground">应用信息</div>
            <div class="space-y-1 text-sm">
              <div class="flex justify-between">
                <span>应用版本:</span>
                <span>{{ systemInfo.appVersion }}</span>
              </div>
              <div class="flex justify-between">
                <span>部署环境:</span>
                <span>{{ systemInfo.environment }}</span>
              </div>
              <div class="flex justify-between">
                <span>启动时间:</span>
                <span>{{ systemInfo.startTime }}</span>
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <div class="font-medium text-muted-foreground">网络信息</div>
            <div class="space-y-1 text-sm">
              <div class="flex justify-between">
                <span>IP地址:</span>
                <span>{{ systemInfo.serverIp }}</span>
              </div>
              <div class="flex justify-between">
                <span>端口:</span>
                <span>{{ systemInfo.port }}</span>
              </div>
              <div class="flex justify-between">
                <span>域名:</span>
                <span>{{ systemInfo.domain }}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  AlertTriangle,
  CheckCircle,
  Cpu,
  Database,
  FileText,
  HardDrive,
  Info,
  MemoryStick,
  Network,
  RotateCcw,
  Server,
  TrendingUp,
  XCircle,
} from 'lucide-vue-next'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import BarChart from '@/components/charts/BarChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import LineChart from '@/components/charts/LineChart.vue'

interface SystemMetrics {
  cpu: number
  memory: number
  disk: number
  networkIn: number
  networkOut: number
}

interface ApplicationService {
  name: string
  description: string
  status: 'running' | 'warning' | 'error'
  responseTime: number
}

interface DatabaseConnection {
  name: string
  status: 'connected' | 'disconnected'
  activeConnections: number
  maxConnections: number
  queriesPerSecond: number
  avgResponseTime: number
}

interface Alert {
  id: string
  level: 'critical' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
}

interface SystemLog {
  id: string
  timestamp: string
  level: 'ERROR' | 'WARN' | 'INFO' | 'DEBUG'
  component: string
  message: string
}

interface SystemInfo {
  os: string
  arch: string
  uptime: string
  cpuCores: number
  totalMemory: number
  totalDisk: number
  appVersion: string
  environment: string
  startTime: string
  serverIp: string
  port: number
  domain: string
}

// Mock数据生成函数
const generateTimeSeriesData = (points: number, baseValue: number, variance: number) => {
  const data: [string, number][] = []
  const now = Date.now()
  for (let i = points - 1; i >= 0; i--) {
    const timestamp = new Date(now - i * 60000) // 每分钟一个点
    const value = baseValue + (Math.random() - 0.5) * variance
    data.push([timestamp.toLocaleTimeString('zh-CN', { hour12: false }), Math.max(0, value)])
  }
  return data
}

// 图表数据适配 - 修正为正确的组件接口格式
const systemResourcesTimeLabels = computed(() => {
  return generateTimeSeriesData(30, 0, 0).map((item) => item[0])
})

const systemResourcesLineData = computed(() => {
  return {
    CPU使用率: generateTimeSeriesData(30, systemMetrics.value.cpu, 10).map((item) => item[1]),
    内存使用率: generateTimeSeriesData(30, systemMetrics.value.memory, 8).map((item) => item[1]),
    磁盘使用率: generateTimeSeriesData(30, systemMetrics.value.disk, 5).map((item) => item[1]),
  }
})

const networkTrafficTimeLabels = computed(() => {
  return generateTimeSeriesData(30, 0, 0).map((item) => item[0])
})

const networkTrafficLineData = computed(() => {
  const inTraffic = Math.max(systemMetrics.value.networkIn / (1024 * 1024), 1)
  const outTraffic = Math.max(systemMetrics.value.networkOut / (1024 * 1024), 1)
  return {
    '入站流量(MB/s)': generateTimeSeriesData(30, inTraffic, inTraffic * 0.3).map((item) => item[1]),
    '出站流量(MB/s)': generateTimeSeriesData(30, outTraffic, outTraffic * 0.3).map(
      (item) => item[1],
    ),
  }
})

const systemResourcesSeries = computed(() => {
  const colorPalette = ['#3498db', '#17a2b8', '#2ecc71']
  const labels = systemResourcesTimeLabels.value
  const dataMap = systemResourcesLineData.value
  return Object.keys(dataMap).map((name, idx) => ({
    name,
    color: colorPalette[idx % colorPalette.length],
    data: labels.map((label, i) => ({ name: label, value: dataMap[name][i] ?? null })),
    smooth: true,
  }))
})

const networkTrafficSeries = computed(() => {
  const colorPalette = ['#3498db', '#17a2b8']
  const labels = networkTrafficTimeLabels.value
  const dataMap = networkTrafficLineData.value
  return Object.keys(dataMap).map((name, idx) => ({
    name,
    color: colorPalette[idx % colorPalette.length],
    data: labels.map((label, i) => ({ name: label, value: dataMap[name][i] ?? null })),
    smooth: true,
  }))
})

const responseTimeBarData = computed(() => {
  return applicationServices.value.map((service) => ({
    name: service.name,
    value: service.responseTime,
  }))
})

const databasePoolPieData = computed(() => {
  return databaseConnections.value.map((db) => ({
    name: db.name,
    value: db.activeConnections,
  }))
})

const alertTrendBarData = computed(() => {
  const alertData = [
    { name: '严重告警', data: [2, 1, 3, 0, 1, 2, 0] },
    { name: '警告', data: [5, 3, 8, 4, 6, 7, 3] },
    { name: '信息', data: [12, 8, 15, 10, 11, 14, 9] },
  ]

  // 转换为 BarChart 期望的格式
  const result = []
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

  for (let i = 0; i < days.length; i++) {
    result.push({
      name: days[i],
      value: alertData.reduce((sum, alert) => sum + alert.data[i], 0),
    })
  }

  return result
})

const systemStatus = ref<'healthy' | 'warning' | 'critical'>('healthy')
const isRefreshing = ref(false)
const timeRange = ref('1h')
const logLevel = ref('ALL')

// 系统指标
const systemMetrics = ref<SystemMetrics>({
  cpu: 45.2,
  memory: 62.8,
  disk: 34.5,
  networkIn: 1024 * 1024 * 2.5, // 2.5 MB/s
  networkOut: 1024 * 1024 * 1.8, // 1.8 MB/s
})

// 应用服务
const applicationServices = ref<ApplicationService[]>([
  {
    name: 'Web服务器',
    description: 'Nginx反向代理服务',
    status: 'running',
    responseTime: 125,
  },
  {
    name: 'API服务',
    description: 'Node.js应用服务器',
    status: 'running',
    responseTime: 89,
  },
  {
    name: '认证服务',
    description: 'JWT认证微服务',
    status: 'warning',
    responseTime: 234,
  },
  {
    name: '监控代理',
    description: 'Prometheus监控采集',
    status: 'running',
    responseTime: 56,
  },
])

// 数据库连接
const databaseConnections = ref<DatabaseConnection[]>([
  {
    name: 'PostgreSQL主库',
    status: 'connected',
    activeConnections: 15,
    maxConnections: 100,
    queriesPerSecond: 245,
    avgResponseTime: 12,
  },
  {
    name: 'Redis缓存',
    status: 'connected',
    activeConnections: 8,
    maxConnections: 50,
    queriesPerSecond: 1567,
    avgResponseTime: 2,
  },
])

// 告警信息
const alerts = ref<Alert[]>([
  {
    id: '1',
    level: 'warning',
    title: '认证服务响应时间过长',
    message: '认证服务平均响应时间超过200ms，建议检查服务状态',
    timestamp: '2025-08-25 14:32:15',
  },
  {
    id: '2',
    level: 'info',
    title: '系统负载升高',
    message: 'CPU使用率持续在40%以上运行，属于正常范围',
    timestamp: '2025-08-25 14:28:42',
  },
])

// 系统日志
const systemLogs = ref<SystemLog[]>([
  {
    id: '1',
    timestamp: '14:35:22',
    level: 'INFO',
    component: 'AUTH',
    message: '用户登录成功: <EMAIL>',
  },
  {
    id: '2',
    timestamp: '14:35:18',
    level: 'WARN',
    component: 'DB',
    message: '数据库连接池使用率达到80%',
  },
  {
    id: '3',
    timestamp: '14:35:10',
    level: 'INFO',
    component: 'API',
    message: '处理请求: GET /api/v1/dashboard',
  },
  {
    id: '4',
    timestamp: '14:34:55',
    level: 'ERROR',
    component: 'CACHE',
    message: 'Redis连接暂时中断，正在重连...',
  },
  {
    id: '5',
    timestamp: '14:34:45',
    level: 'INFO',
    component: 'SYSTEM',
    message: '系统健康检查通过',
  },
  {
    id: '6',
    timestamp: '14:34:30',
    level: 'DEBUG',
    component: 'MONITOR',
    message: '收集系统指标数据',
  },
])

// 系统信息
const systemInfo = ref<SystemInfo>({
  os: 'Ubuntu 22.04 LTS',
  arch: 'x86_64',
  uptime: '15天 8小时 32分钟',
  cpuCores: 8,
  totalMemory: 32 * 1024 * 1024 * 1024, // 32GB
  totalDisk: 1024 * 1024 * 1024 * 1024, // 1TB
  appVersion: 'v2.1.5',
  environment: 'Production',
  startTime: '2025-08-10 09:15:30',
  serverIp: '*************',
  port: 443,
  domain: 'geoai.gov.cn',
})

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'ALL') {
    return systemLogs.value
  }
  return systemLogs.value.filter((log) => log.level === logLevel.value)
})

// 辅助函数
const formatBytes = (bytes: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

const getAlertIcon = (level: string) => {
  switch (level) {
    case 'critical':
      return XCircle
    case 'warning':
      return AlertTriangle
    case 'info':
      return Info
    default:
      return Info
  }
}

// 服务状态样式函数
const getServiceStatusClass = (status: string) => {
  const baseClasses = 'w-3 h-3 rounded-full'
  switch (status) {
    case 'running':
      return `${baseClasses} bg-status-active`
    case 'warning':
      return `${baseClasses} bg-status-pending`
    default:
      return `${baseClasses} bg-status-error`
  }
}

const getAlertIconColor = (level: string) => {
  switch (level) {
    case 'critical':
      return 'text-risk-critical'
    case 'warning':
      return 'text-status-pending'
    case 'info':
      return 'text-ocean-info'
    default:
      return 'text-ocean-neutral'
  }
}

const getAlertVariant = (level: string) => {
  switch (level) {
    case 'critical':
      return 'destructive'
    case 'warning':
      return 'secondary'
    case 'info':
      return 'outline'
    default:
      return 'outline'
  }
}

const getLogLevelVariant = (level: string) => {
  switch (level) {
    case 'ERROR':
      return 'destructive'
    case 'WARN':
      return 'secondary'
    case 'INFO':
      return 'outline'
    case 'DEBUG':
      return 'outline'
    default:
      return 'outline'
  }
}

// 事件处理
const refreshData = () => {
  isRefreshing.value = true

  // 模拟数据刷新
  setTimeout(() => {
    // 随机更新系统指标
    systemMetrics.value = {
      cpu: Math.random() * 80 + 10,
      memory: Math.random() * 90 + 10,
      disk: Math.random() * 50 + 30,
      networkIn: Math.random() * 1024 * 1024 * 5,
      networkOut: Math.random() * 1024 * 1024 * 3,
    }

    // 更新系统状态
    const maxUsage = Math.max(systemMetrics.value.cpu, systemMetrics.value.memory)
    if (maxUsage > 85) {
      systemStatus.value = 'critical'
    } else if (maxUsage > 70) {
      systemStatus.value = 'warning'
    } else {
      systemStatus.value = 'healthy'
    }

    isRefreshing.value = false
  }, 1000)
}

// 定时刷新
let refreshInterval: NodeJS.Timeout

onMounted(() => {
  // 每30秒自动刷新数据
  refreshInterval = setInterval(() => {
    refreshData()
  }, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
