/*
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-Content-Type-Options: nosniff

# JavaScript and CSS files - no cache to ensure latest version
/assets/*.js
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

/assets/*.css
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Images - cache for 1 day but revalidate
/assets/*.png
  Cache-Control: public, max-age=86400, must-revalidate

/assets/*.jpg
  Cache-Control: public, max-age=86400, must-revalidate

/assets/*.svg
  Cache-Control: public, max-age=86400, must-revalidate

# Fonts - long cache since they rarely change
/assets/*.woff
  Cache-Control: public, max-age=31536000, immutable

/assets/*.woff2
  Cache-Control: public, max-age=31536000, immutable
