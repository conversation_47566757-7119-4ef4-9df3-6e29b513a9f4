<template>
  <div class="space-y-6">
    <!-- 车辆统计概览 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Car class="h-5 w-5" />
            <span>车辆信息概览</span>
          </div>
          <Badge variant="outline"> 共 {{ enterprise.vehicleCount }} 辆车 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ vehicleStats.total }}</div>
            <div class="text-sm text-blue-600">车辆总数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ vehicleStats.online }}</div>
            <div class="text-sm text-green-600">在线车辆</div>
          </div>
          <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-2xl font-bold text-yellow-600">{{ vehicleStats.testing }}</div>
            <div class="text-sm text-yellow-600">测试车辆</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ vehicleStats.brands }}</div>
            <div class="text-sm text-purple-600">车辆品牌</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 车辆类型分布 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center space-x-2">
            <PieChart class="h-5 w-5" />
            <span>车辆类型分布</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="type in vehicleTypes"
              :key="type.name"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: type.color }"></div>
                <span class="text-sm">{{ type.name }}</span>
              </div>
              <span class="text-sm font-medium">{{ type.count }} ({{ type.percentage }}%)</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle class="flex items-center space-x-2">
            <Factory class="h-5 w-5" />
            <span>品牌分布</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="brand in vehicleBrands"
              :key="brand.name"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: brand.color }"></div>
                <span class="text-sm">{{ brand.name }}</span>
              </div>
              <span class="text-sm font-medium">{{ brand.count }}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 车辆VIN清单 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <List class="h-5 w-5" />
            <span>车辆VIN清单</span>
          </div>
          <div class="flex items-center space-x-2">
            <Button size="sm" variant="outline" @click="viewVinList">
              <Eye class="w-4 h-4 mr-2" />
              查看清单
            </Button>
            <Button size="sm" variant="outline" @click="downloadVinList">
              <Download class="w-4 h-4 mr-2" />
              下载清单
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label class="text-sm font-medium">VIN清单文件</Label>
              <div class="flex items-center space-x-2 p-3 border rounded-lg">
                <FileText class="w-5 h-5 text-blue-500" />
                <div class="flex-1">
                  <div class="text-sm font-medium">车辆VIN清单.xlsx</div>
                  <div class="text-xs text-muted-foreground">上传时间：2024-01-15 14:30:25</div>
                </div>
                <Badge variant="outline">Excel</Badge>
              </div>
            </div>

            <div class="space-y-2">
              <Label class="text-sm font-medium">文件状态</Label>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span>文件大小：</span>
                  <span>1.2 MB</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span>记录条数：</span>
                  <span>{{ enterprise.vehicleCount }} 条</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span>验证状态：</span>
                  <Badge variant="default">已验证</Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 自动驾驶系统信息 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Settings class="h-5 w-5" />
          <span>自动驾驶系统信息</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <div
            v-for="(system, index) in autonomousSystems"
            :key="index"
            class="border rounded-lg p-4 space-y-4"
          >
            <div class="flex items-center justify-between">
              <h4 class="font-semibold">{{ system.name }}</h4>
              <Badge :variant="system.status === '已认证' ? 'default' : 'secondary'">
                {{ system.status }}
              </Badge>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="space-y-2">
                <Label class="text-sm font-medium">系统版本</Label>
                <div class="text-sm">{{ system.version }}</div>
              </div>

              <div class="space-y-2">
                <Label class="text-sm font-medium">自动驾驶等级</Label>
                <Badge variant="outline">L{{ system.level }}</Badge>
              </div>

              <div class="space-y-2">
                <Label class="text-sm font-medium">适用车型</Label>
                <div class="text-sm">{{ system.applicableModels.join(', ') }}</div>
              </div>
            </div>

            <!-- 传感器配置 -->
            <div class="space-y-3">
              <Label class="text-sm font-medium">传感器配置</Label>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  v-for="sensor in system.sensors"
                  :key="sensor.type"
                  class="flex items-center justify-between p-2 bg-muted rounded"
                >
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 rounded-full bg-green-500"></div>
                    <span class="text-sm">{{ sensor.type }}</span>
                  </div>
                  <div class="text-sm font-medium">
                    {{ sensor.count }}个 | {{ sensor.precision }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 地图数据应用 -->
            <div class="space-y-3">
              <Label class="text-sm font-medium">地图数据应用</Label>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span>地图提供商：</span>
                  <span>{{ system.mapProvider }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span>地图精度：</span>
                  <span>{{ system.mapPrecision }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span>更新频率：</span>
                  <span>{{ system.updateFrequency }}</span>
                </div>
              </div>
            </div>

            <!-- 系统附件 -->
            <div class="space-y-3">
              <Label class="text-sm font-medium">系统附件</Label>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div
                  v-for="attachment in system.attachments"
                  :key="attachment.name"
                  class="flex items-center space-x-2 p-2 border rounded"
                >
                  <Paperclip class="w-4 h-4 text-muted-foreground" />
                  <div class="flex-1">
                    <div class="text-sm">{{ attachment.name }}</div>
                    <div class="text-xs text-muted-foreground">{{ attachment.size }}</div>
                  </div>
                  <Button size="sm" variant="ghost" @click="downloadAttachment(attachment)">
                    <Download class="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 安全处理技术清单 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Shield class="h-5 w-5" />
            <span>安全处理技术清单</span>
          </div>
          <Button size="sm" variant="outline" @click="viewSecurityTechList">
            <FileText class="w-4 h-4 mr-2" />
            查看详细清单
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <Label class="text-sm font-medium mb-2 block">车端安全技术</Label>
              <div class="space-y-2">
                <div
                  v-for="tech in vehicleSecurityTech"
                  :key="tech"
                  class="flex items-center space-x-2"
                >
                  <Check class="w-4 h-4 text-green-500" />
                  <span class="text-sm">{{ tech }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div>
              <Label class="text-sm font-medium mb-2 block">云端安全技术</Label>
              <div class="space-y-2">
                <div
                  v-for="tech in cloudSecurityTech"
                  :key="tech"
                  class="flex items-center space-x-2"
                >
                  <Check class="w-4 h-4 text-green-500" />
                  <span class="text-sm">{{ tech }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Car,
  PieChart,
  Factory,
  List,
  Eye,
  Download,
  FileText,
  Settings,
  Paperclip,
  Shield,
  Check,
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

// 企业车辆信息数据接口
interface VehicleInfo {
  vin: string // 车辆识别码VIN
  enterprise_id: string // 所属企业ID
  license_plate_number?: string // 车牌号
  vehicle_model?: string // 车辆型号
  vehicle_type?: string // 车辆类型
  registration_date?: string // 注册日期
  status: number // 状态 1:启用 0:停用
  create_time: string // 创建时间
  update_time: string // 更新时间

  // 扩展字段用于UI显示
  brand?: string // 车辆品牌
  auto_level?: number // 自动驾驶等级
  online_status?: '在线' | '离线' // 在线状态
  test_vehicle?: boolean // 是否测试车辆
}

interface Enterprise {
  id: string
  enterprise_name: string // 企业名称
  unified_social_credit_code: string
  address: string
  contact_person: string
  contact_phone: string
  business_scope: string
  registration_date: string
  status: number
  create_time: string
  update_time: string

  // 扩展字段
  type: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

interface Props {
  enterprise: Enterprise
}

const props = defineProps<Props>()

// 车辆统计数据
const vehicleStats = computed(() => ({
  total: props.enterprise.vehicleCount,
  online: Math.floor(props.enterprise.vehicleCount * 0.85),
  testing: Math.floor(props.enterprise.vehicleCount * 0.12),
  brands: 6,
}))

// 车辆类型分布
const vehicleTypes = computed(() => [
  { name: '智能网联车', count: 750, percentage: 60, color: '#3b82f6' },
  { name: '自动驾驶车', count: 250, percentage: 20, color: '#8b5cf6' },
  { name: '传统车联网', count: 150, percentage: 12, color: '#06b6d4' },
  { name: '测试车辆', count: 100, percentage: 8, color: '#84cc16' },
])

// 车辆品牌分布
const vehicleBrands = computed(() => [
  { name: '比亚迪股份有限公司', count: 320, color: '#3b82f6' },
  { name: '小鹏汽车', count: 280, color: '#8b5cf6' },
  { name: '蔚来汽车', count: 210, color: '#06b6d4' },
  { name: '理想汽车', count: 180, color: '#84cc16' },
  { name: '特斯拉', count: 150, color: '#f59e0b' },
  { name: '其他品牌', count: 110, color: '#6b7280' },
])

// 自动驾驶系统信息
const autonomousSystems = computed(() => [
  {
    name: '智行自动驾驶系统 V3.0',
    version: '3.0.5',
    level: 3,
    status: '已认证',
    applicableModels: ['Model A', 'Model B', 'Model C'],
    sensors: [
      { type: '激光雷达', count: 2, precision: '精度±5cm' },
      { type: '毫米波雷达', count: 6, precision: '精度±10cm' },
      { type: '摄像头', count: 8, precision: '1080P高清' },
      { type: '超声波雷达', count: 12, precision: '精度±2cm' },
    ],
    mapProvider: '高德地图',
    mapPrecision: '厘米级',
    updateFrequency: '实时更新',
    attachments: [
      { name: '系统架构说明.pdf', size: '2.5MB' },
      { name: '技术规格书.docx', size: '1.8MB' },
      { name: '测试报告.pdf', size: '5.2MB' },
    ],
  },
  {
    name: '智能辅助驾驶系统 V2.1',
    version: '2.1.3',
    level: 2,
    status: '认证中',
    applicableModels: ['Model D', 'Model E'],
    sensors: [
      { type: '摄像头', count: 4, precision: '720P高清' },
      { type: '毫米波雷达', count: 4, precision: '精度±15cm' },
      { type: '超声波雷达', count: 8, precision: '精度±3cm' },
    ],
    mapProvider: '百度地图',
    mapPrecision: '米级',
    updateFrequency: '每日更新',
    attachments: [
      { name: '系统说明书.pdf', size: '1.2MB' },
      { name: '安全评估报告.pdf', size: '3.1MB' },
    ],
  },
])

// 安全处理技术
const vehicleSecurityTech = computed(() => [
  '数据属性脱敏技术',
  '地理围栏限制技术',
  '车载数据加密存储',
  '安全传输协议',
  '访问控制机制',
  '数据完整性校验',
])

const cloudSecurityTech = computed(() => [
  '云端数据加密存储',
  'VMS数据融合技术',
  '多级访问控制',
  '实时风险监测',
  '数据脱敏处理',
  '安全审计日志',
])

// 方法
const viewVinList = () => {
  console.log('查看VIN清单')
  // 这里实现查看VIN清单的逻辑
}

const downloadVinList = () => {
  console.log('下载VIN清单')
  // 这里实现下载VIN清单的逻辑
}

const downloadAttachment = (attachment: any) => {
  console.log('下载附件:', attachment.name)
  // 这里实现下载附件的逻辑
}

const viewSecurityTechList = () => {
  console.log('查看安全处理技术详细清单')
  // 这里实现查看技术清单的逻辑
}
</script>
