﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="164px" height="44px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="515px" y="724px" width="164px" height="44px" filterUnits="userSpaceOnUse" id="filter454">
      <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="4" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.462745098039216  0 0 0 0 0.556862745098039  0 0 0 0 0.807843137254902  0 0 0 0.996078431372549 0  " in="shadowComposite" />
    </filter>
    <g id="widget455">
      <path d="M 523 728  L 671 728  L 671 756  L 523 756  L 523 728  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" fill-opacity="0" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -515 -724 )">
    <use xlink:href="#widget455" filter="url(#filter454)" />
    <use xlink:href="#widget455" />
  </g>
</svg>