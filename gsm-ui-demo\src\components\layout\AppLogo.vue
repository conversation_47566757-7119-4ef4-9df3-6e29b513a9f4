<template>
  <!-- PNG logo -->
  <div v-if="!imageError && logoSrc" class="app-logo" :class="{ 'app-logo--large': size === 'large', 'app-logo--small': size === 'small' }">
    <img 
      :src="logoSrc" 
      :alt="alt"
      :class="logoClass"
      @error="handleImageError"
    />
  </div>
  
  <!-- SVG fallback -->
  <div v-else class="app-logo app-logo--svg" :class="{ 'app-logo--large': size === 'large', 'app-logo--small': size === 'small' }">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      :class="logoClass"
      class="logo-svg"
    >
      <path
        d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'

interface Props {
  size?: 'small' | 'medium' | 'large'
  alt?: string
  className?: string
  forceTheme?: 'light' | 'dark' | null
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  alt: '地理信息安全监测平台',
  className: '',
  forceTheme: null
})

const imageError = ref(false)
const currentTheme = ref<string>('light')

// 检测主题变化
const detectTheme = () => {
  if (typeof window !== 'undefined' && document.documentElement) {
    const theme = document.documentElement.getAttribute('data-theme') || 
                 (document.documentElement.classList.contains('dark') ? 'dark' : 'light')
    currentTheme.value = theme
    console.log('AppLogo: Theme detected as', theme)
    return theme === 'dark'
  }
  return false
}

// 响应式主题检测，支持强制主题
const isDark = computed(() => {
  if (props.forceTheme) {
    return props.forceTheme === 'dark'
  }
  return currentTheme.value === 'dark'
})

// 监听主题变化
let themeObserver: MutationObserver | null = null

onMounted(() => {
  // 初始检测主题
  detectTheme()
  
  // 监听主题变化
  if (typeof window !== 'undefined' && document.documentElement) {
    themeObserver = new MutationObserver(() => {
      detectTheme()
    })
    
    themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme', 'class']
    })
  }
  
  // 预加载图片检查
  const testImg = new Image()
  testImg.onload = () => {
    imageError.value = false
  }
  testImg.onerror = () => {
    imageError.value = true
    console.warn('Logo images not found, using SVG fallback')
  }
  // 测试加载其中一个logo文件
  testImg.src = getLogoPath('logo01.png')
})

onUnmounted(() => {
  if (themeObserver) {
    themeObserver.disconnect()
  }
})

// 构建logo路径，支持BASE_URL
const getLogoPath = (filename: string) => {
  const base = import.meta.env.BASE_URL || '/'
  return `${base}assets/logos/${filename}`
}

// 根据主题选择logo
const logoSrc = computed(() => {
  if (imageError.value) {
    // 如果图片加载失败，返回fallback SVG
    console.log('AppLogo: Using SVG fallback due to image error')
    return null
  }
  
  // 根据主题选择对应的logo：暗色主题使用logo02（暗色logo），亮色主题使用logo01（亮色logo）
  const logoFile = isDark.value ? 'logo02.png' : 'logo01.png'
  const logoPath = getLogoPath(logoFile)
  console.log('AppLogo: Selected logo', logoFile, 'for theme', currentTheme.value, 'path:', logoPath)
  return logoPath
})

// 样式类
const logoClass = computed(() => {
  const classes = ['logo-image']
  if (props.className) {
    classes.push(props.className)
  }
  return classes.join(' ')
})

// 处理图片加载错误
const handleImageError = () => {
  console.warn('Logo image failed to load, falling back to SVG')
  imageError.value = true
}

</script>

<style scoped>
.app-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-logo img,
.app-logo svg {
  display: block;
  transition: all 0.3s ease;
}

/* 默认中等尺寸 */
.app-logo img,
.app-logo svg {
  width: 2.5rem;  /* w-10 */
  height: 2.5rem; /* h-10 */
}

/* 小尺寸 */
.app-logo--small img,
.app-logo--small svg {
  width: 2rem;    /* w-8 */
  height: 2rem;   /* h-8 */
}

/* 大尺寸 */
.app-logo--large img,
.app-logo--large svg {
  width: 3rem;    /* w-12 */
  height: 3rem;   /* h-12 */
}

/* SVG特定样式 */
.app-logo--svg .logo-svg {
  color: #60a5fa; /* text-blue-400 */
  filter: drop-shadow(rgba(59, 130, 246, 0.3) 0px 0px 8px);
}

.app-logo--svg .logo-svg:hover {
  color: #22d3ee; /* text-cyan-400 */
}

/* 暗色主题下的SVG样式调整 */
:global([data-theme='dark']) .app-logo--svg .logo-svg,
:global(html.dark) .app-logo--svg .logo-svg {
  color: #60a5fa;
}

:global([data-theme='light']) .app-logo--svg .logo-svg,
:global(html:not(.dark)) .app-logo--svg .logo-svg {
  color: #3b82f6; /* 亮色主题下使用更深的蓝色 */
}

/* PNG图片样式 */
.app-logo img {
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .app-logo--large img,
  .app-logo--large svg {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .app-logo img,
  .app-logo svg {
    width: 2rem;
    height: 2rem;
  }
  
  .app-logo--small img,
  .app-logo--small svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}
</style>