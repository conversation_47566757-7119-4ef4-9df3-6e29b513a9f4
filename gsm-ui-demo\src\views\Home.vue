<template>
  <div class="home-container">
    <!-- 顶部横幅 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">{{ $route.meta.title }}</h1>
        <p class="hero-subtitle">实时监控 · 智能分析 · 安全防护</p>
        <div class="hero-stats">
          <div class="stat-item">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">系统可用性</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">24/7</div>
            <div class="stat-label">实时监控</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">1000+</div>
            <div class="stat-label">接入车辆</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能模块 -->
    <div class="features-section">
      <div class="container">
        <h2 class="section-title">核心功能</h2>
        <div class="features-grid">
          <div class="feature-card" @click="navigateTo('/government/vehicle-trace')">
            <div class="feature-icon vehicle-icon">🚗</div>
            <h3 class="feature-title">车辆数据溯源</h3>
            <p class="feature-description">追踪车辆数据流转路径，确保数据安全合规</p>
            <div class="feature-status">[✓] 已启用</div>
          </div>

          <div class="feature-card" @click="navigateTo('/government/cloud-trace')">
            <div class="feature-icon cloud-icon">☁️</div>
            <h3 class="feature-title">云端数据监控</h3>
            <p class="feature-description">实时监控云端数据传输和存储安全</p>
            <div class="feature-status">[✓] 已启用</div>
          </div>

          <div class="feature-card" @click="navigateTo('/government/vehicle-traceback')">
            <div class="feature-icon traceback-icon">🔍</div>
            <h3 class="feature-title">溯源记录查询</h3>
            <p class="feature-description">查看历史溯源记录和分析报告</p>
            <div class="feature-status">[✓] 已启用</div>
          </div>

          <div class="feature-card" @click="navigateTo('/government/cloud-traceback')">
            <div class="feature-icon analysis-icon">📊</div>
            <h3 class="feature-title">数据分析报告</h3>
            <p class="feature-description">生成详细的数据安全分析报告</p>
            <div class="feature-status">[完成] 可用</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="status-section">
      <div class="container">
        <h2 class="section-title">系统状态</h2>
        <div class="status-grid">
          <div class="status-card">
            <div class="status-header">
              <h3>数据处理</h3>
              <span class="status-indicator active"></span>
            </div>
            <div class="status-metrics">
              <div class="metric">
                <span class="metric-label">处理速度</span>
                <span class="metric-value">2.3 GB/s</span>
              </div>
              <div class="metric">
                <span class="metric-label">队列长度</span>
                <span class="metric-value">156</span>
              </div>
            </div>
          </div>

          <div class="status-card">
            <div class="status-header">
              <h3>安全监控</h3>
              <span class="status-indicator active"></span>
            </div>
            <div class="status-metrics">
              <div class="metric">
                <span class="metric-label">威胁检测</span>
                <span class="metric-value">0 个</span>
              </div>
              <div class="metric">
                <span class="metric-label">风险等级</span>
                <span class="metric-value">低</span>
              </div>
            </div>
          </div>

          <div class="status-card">
            <div class="status-header">
              <h3>系统性能</h3>
              <span class="status-indicator active"></span>
            </div>
            <div class="status-metrics">
              <div class="metric">
                <span class="metric-label">CPU 使用率</span>
                <span class="metric-value">45%</span>
              </div>
              <div class="metric">
                <span class="metric-label">内存使用率</span>
                <span class="metric-value">62%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

// 定义组件名称以符合 Vue 规范
defineOptions({
  name: 'HomePage',
})

const router = useRouter()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section {
  padding: 4rem 2rem;
  text-align: center;
  color: white;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.8;
}

.features-section {
  padding: 4rem 2rem;
  background: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.feature-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.feature-status {
  color: #10b981;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-section {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.status-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.status-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
}

.status-indicator.active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-metrics {
  display: grid;
  gap: 1rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  color: #666;
  font-size: 0.9rem;
}

.metric-value {
  font-weight: 600;
  color: #333;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-grid,
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
