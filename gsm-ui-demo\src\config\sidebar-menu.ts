/**
 * Sidebar menu configuration based on role (government | enterprise)
 * and current top-level section (header selection).
 *
 * Data source: docs/app-menu-system.md
 */

export type Role = 'government' | 'enterprise'

export type GovSectionKey = 'overview' | 'record' | 'monitor' | 'system'
export type EntSectionKey = 'overview' | 'registration' | 'risk' | 'management'
export type SectionKey = GovSectionKey | EntSectionKey

export interface SidebarMenuItem {
  title: string
  to?: string
  badge?: string | number
  icon?: string
  children?: SidebarMenuItem[]
}

export interface SidebarSection {
  label: string
  icon?: string
  to?: string
  items: SidebarMenuItem[]
}

/**
 * Government sections and their sidebar items
 * Based on docs/app-menu-system.md structure with proper data-sidebar attributes
 */
const GOV_SIDEBARS: Record<GovSectionKey, SidebarSection> = {
  overview: {
    label: '综合概览',
    icon: 'SquareTerminal',
    to: '/gov/dashboard',
    items: [{ title: '政府端控制台', to: '/gov/dashboard' }],
  },
  record: {
    label: '备案审核',
    icon: 'ClipboardCheck',
    items: [
      { title: '注册信息管理', to: '/gov/record/registration-info', badge: '15', icon: 'FileText' },
      { title: '备案审批管理', to: '/gov/record/approval', badge: '8', icon: 'ClipboardCheck' },
    ],
  },
  monitor: {
    label: '实时监测',
    icon: 'Activity',
    items: [
      {
        title: '风险管理',
        icon: 'ShieldAlert',
        children: [
          { title: '车端风险管理', to: '/gov/monitor/risk/vehicle', badge: 'NEW', icon: 'Car' },
          { title: '云端风险管理', to: '/gov/monitor/risk/cloud', badge: '3', icon: 'CloudAlert' },
        ],
      },
      {
        title: '事件管理',
        icon: 'BellRing',
        children: [
          {
            title: '车端事件管理',
            to: '/gov/monitor/event/vehicle',
            badge: '7',
            icon: 'CarTaxiFront',
          },
          {
            title: '云端事件管理',
            to: '/gov/monitor/event/cloud',
            badge: '2',
            icon: 'CloudLightning',
          },
        ],
      },
      {
        title: '应急溯源管理',
        icon: 'Route',
        children: [
          { title: '车端溯源管理', to: '/gov/monitor/traceback/vehicle', icon: 'Route' },
          { title: '云端溯源管理', to: '/gov/monitor/traceback/cloud', icon: 'CloudCog' },
        ],
      },
      {
        title: '操作信息管理',
        icon: 'ScrollText',
        children: [
          { title: '车端操作管理', to: '/gov/monitor/operation/vehicle', icon: 'FileCog' },
          { title: '云端操作管理', to: '/gov/monitor/operation/cloud', icon: 'CloudCog' },
        ],
      },
    ],
  },
  system: {
    label: '系统管理',
    icon: 'Settings2',
    items: [
      { title: '信息发布', to: '/gov/system/information', icon: 'Megaphone' },
      { title: '用户管理', to: '/gov/system/users', badge: '5', icon: 'UserCog' },
      { title: '监测区域管理', to: '/gov/system/monitor-areas', icon: 'MapPinned' },
      { title: '风险规则管理', to: '/gov/system/rules/risk', icon: 'ShieldCheck' },
      { title: '日志管理', to: '/gov/system/logs', icon: 'FileClock' },
      // 隐藏监控运维、系统安全、图表主题
    ],
  },
}

/**
 * Enterprise sections and their sidebar items
 * Based on docs/app-menu-system.md structure with proper data-sidebar attributes
 */
const ENT_SIDEBARS: Record<EntSectionKey, SidebarSection> = {
  overview: {
    label: '综合概览',
    icon: 'SquareTerminal',
    items: [{ title: '我的工作台', to: '/corp/dashboard' }],
  },
  registration: {
    label: '注册备案',
    icon: 'ClipboardList',
    items: [
      {
        title: '备案填报',
        icon: 'FileEdit',
        children: [
          { title: '企业基本信息', to: '/corp/filing/form/basic-info', icon: 'Building2' },
          {
            title: '测绘资质信息',
            to: '/corp/filing/form/qualification',
            badge: 'NEW',
            icon: 'Shield',
          },
          { title: '防控制度措施', to: '/corp/filing/form/security-policy', icon: 'ClipboardList' },
          { title: '防控技术措施', to: '/corp/filing/form/security-tech', icon: 'Wrench' },
          { title: '车辆备案信息', to: '/corp/filing/form/vehicle-info', badge: '3', icon: 'Car' },
          { title: '数据处理活动', to: '/corp/filing/form/data-activity', icon: 'Database' },
        ],
      },
      { title: '备案管理', to: '/corp/filing/records', badge: '5', icon: 'FolderCog' },
    ],
  },
  risk: {
    label: '风险事件',
    icon: 'AlertTriangle',
    items: [
      { title: '风险统计', to: '/corp/risk-event/risk', badge: '12', icon: 'ShieldAlert' },
      { title: '事件统计', to: '/corp/risk-event/event', badge: '5', icon: 'BarChartBig' },
    ],
  },
  management: {
    label: '企业管理',
    icon: 'Users',
    items: [
      { title: '用户管理', to: '/corp/management/users', badge: '2', icon: 'Users' },
      { title: '通知信息', to: '/corp/notice/notices', icon: 'Bell' },
      { title: '任务管理', to: '/corp/notice/tasks', icon: 'ListTodo' },
    ],
  },
}

export function getSidebarSection(role: Role, section: SectionKey): SidebarSection {
  if (role === 'government') {
    const key = (section as GovSectionKey) || 'overview'
    return GOV_SIDEBARS[key] ?? GOV_SIDEBARS.overview
  }
  const key = (section as EntSectionKey) || 'overview'
  return ENT_SIDEBARS[key] ?? ENT_SIDEBARS.overview
}

/**
 * Helpers to derive SectionKey from current path.
 */
export function resolveRoleFromPath(path: string): Role {
  if (path.startsWith('/corp')) return 'enterprise'
  // /gov 与 /admin 统一视为政府端
  return 'government'
}

export function resolveSectionFromPath(path: string): SectionKey {
  if (path.startsWith('/gov/record')) return 'record'
  if (path.startsWith('/gov/monitor')) return 'monitor'
  if (path.startsWith('/gov/system') || path.startsWith('/admin')) return 'system'
  if (path.startsWith('/gov/dashboard')) return 'overview'

  if (path.startsWith('/corp/filing')) return 'registration'
  if (path.startsWith('/corp/risk-event')) return 'risk'
  if (path.startsWith('/corp/risk')) return 'risk'
  if (path.startsWith('/corp/notice')) return 'management'
  if (path.startsWith('/corp/management')) return 'management'
  if (path.startsWith('/corp/dashboard')) return 'overview'

  return 'overview'
}

/**
 * Get all missing routes based on the menu structure
 */
export function getMissingRoutes(): { path: string; name: string; title: string }[] {
  const missingRoutes: { path: string; name: string; title: string }[] = []

  // Government missing routes
  const govMissingRoutes = [
    // Traceback records routes (already exist but may need adjustment)
    {
      path: '/gov/monitor/traceback/vehicle-records',
      name: 'GovTracebackVehicleRecords',
      title: '车端溯源记录表',
    },
    {
      path: '/gov/monitor/traceback/cloud-records',
      name: 'GovTracebackCloudRecords',
      title: '云端溯源记录表',
    },

    // System management routes that might be missing
    { path: '/gov/system/users', name: 'GovSystemUsers', title: '用户管理' },
    { path: '/gov/system/monitor-areas', name: 'GovSystemMonitorAreas', title: '监测区域管理' },
    { path: '/gov/system/logs', name: 'GovSystemLogs', title: '日志管理' },
    { path: '/gov/system/monitoring', name: 'GovSystemMonitoring', title: '监控运维' },
    { path: '/gov/system/security', name: 'GovSystemSecurity', title: '系统安全' },

    // Event management routes that might be missing
    { path: '/gov/monitor/event/vehicle', name: 'GovMonitorEventVehicle', title: '车端事件管理' },
    { path: '/gov/monitor/event/cloud', name: 'GovMonitorEventCloud', title: '云端事件管理' },

    // Cloud operation log route that might be missing
    {
      path: '/gov/monitor/operation/cloud',
      name: 'GovMonitorOperationCloud',
      title: '云端操作管理',
    },
  ]

  // Enterprise missing routes
  const entMissingRoutes = [
    // Filing management route
    { path: '/corp/filing/records', name: 'EntFilingRecords', title: '备案管理' },

    // Management routes
    { path: '/corp/management/users', name: 'EntManagementUsers', title: '用户管理' },
  ]

  return [...govMissingRoutes, ...entMissingRoutes]
}
