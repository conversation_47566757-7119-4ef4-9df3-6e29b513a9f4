<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理政府监管平台和企业用户账户，支持用户注册审批、权限分配和状态管理
        </p>
      </div>
      <Button @click="handleCreateUser" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增用户
      </Button>
    </div>

    <!-- 用户列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>用户列表</span>
          <Badge variant="outline"> 共 {{ filteredUsers.length }} 个用户 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 筛选条件 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>用户名称</TableHead>
                <TableHead>用户类型</TableHead>
                <TableHead>所属机构</TableHead>
                <TableHead>邮箱</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>注册时间</TableHead>
                <TableHead>最后登录</TableHead>
                <TableHead class="w-[120px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedUsers.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无用户数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedUsers"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="font-medium">{{ item.username }}</div>
                  <div class="text-sm text-muted-foreground">{{ item.realName }}</div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getTypeVariant(item.userType)">{{ item.userType }}</Badge>
                </TableCell>
                <TableCell>{{ item.organization }}</TableCell>
                <TableCell class="text-sm">{{ item.email }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ item.createdAt }}</TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{
                  item.lastLoginAt || '从未登录'
                }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleEdit(item.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '待激活'"
                        @click="handleActivate(item.id)"
                      >
                        <CheckCircle class="w-4 h-4 mr-2" />
                        激活
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '正常'"
                        @click="handleSuspend(item.id)"
                      >
                        <XCircle class="w-4 h-4 mr-2" />
                        停用
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        @click="handleResetPassword(item.id)"
                        class="text-orange-600"
                      >
                        <Key class="w-4 h-4 mr-2" />
                        重置密码
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleDelete(item.id)" class="text-red-600">
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredUsers.length) }} 条， 共
            {{ filteredUsers.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 新增用户弹窗 -->
    <Dialog v-model:open="addUserOpen">
      <DialogContent class="max-w-lg">
        <DialogHeader>
          <DialogTitle>新增用户</DialogTitle>
          <DialogDescription>填写用户基础信息并保存</DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <Label class="text-xs text-muted-foreground">用户名</Label>
              <Input v-model="newUser.username" placeholder="登录用户名" />
            </div>
            <div>
              <Label class="text-xs text-muted-foreground">真实姓名</Label>
              <Input v-model="newUser.realName" placeholder="姓名" />
            </div>
            <div>
              <Label class="text-xs text-muted-foreground">用户类型</Label>
              <Select
                :model-value="newUser.userType"
                @update:model-value="(v) => (newUser.userType = v as any)"
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="政府管理员">政府管理员</SelectItem>
                  <SelectItem value="政府监管员">政府监管员</SelectItem>
                  <SelectItem value="企业管理员">企业管理员</SelectItem>
                  <SelectItem value="企业操作员">企业操作员</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="text-xs text-muted-foreground">所属机构</Label>
              <Input v-model="newUser.organization" placeholder="机构名称" />
            </div>
            <div class="md:col-span-2">
              <Label class="text-xs text-muted-foreground">邮箱</Label>
              <Input v-model="newUser.email" placeholder="<EMAIL>" />
            </div>
            <div>
              <Label class="text-xs text-muted-foreground">状态</Label>
              <Select
                :model-value="newUser.status"
                @update:model-value="(v) => (newUser.status = v as any)"
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="待激活">待激活</SelectItem>
                  <SelectItem value="正常">正常</SelectItem>
                  <SelectItem value="已停用">已停用</SelectItem>
                  <SelectItem value="已锁定">已锁定</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="addUserOpen = false">取消</Button>
          <Button @click="saveNewUser">保存</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { CheckCircle, Edit, Key, MoreHorizontal, Plus, Trash2, XCircle } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

type UserType = '政府管理员' | '政府监管员' | '企业管理员' | '企业操作员'
type UserStatus = '正常' | '待激活' | '已停用' | '已锁定'

interface UserItem {
  id: string
  username: string
  realName: string
  userType: UserType
  organization: string
  email: string
  status: UserStatus
  createdAt: string
  lastLoginAt?: string
}

// 筛选条件
const filters = ref({
  username: '',
  userType: 'ALL' as 'ALL' | UserType,
  organization: '',
  status: 'ALL' as 'ALL' | UserStatus,
  timeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'username',
    label: '用户名称',
    type: 'input',
    placeholder: '请输入用户名或真实姓名',
  },
  {
    key: 'userType',
    label: '用户类型',
    type: 'select',
    placeholder: '请选择用户类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '政府管理员', value: '政府管理员' },
      { label: '政府监管员', value: '政府监管员' },
      { label: '企业管理员', value: '企业管理员' },
      { label: '企业操作员', value: '企业操作员' },
    ],
  },
  {
    key: 'organization',
    label: '所属机构',
    type: 'input',
    placeholder: '请输入机构名称',
  },
  {
    key: 'status',
    label: '用户状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '正常', value: '正常' },
      { label: '待激活', value: '待激活' },
      { label: '已停用', value: '已停用' },
      { label: '已锁定', value: '已锁定' },
    ],
  },
  {
    key: 'timeRange',
    label: '注册时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// Mock数据
const users = ref<UserItem[]>([
  {
    id: 'U-2025-001',
    username: 'gov_admin',
    realName: '李管理员',
    userType: '政府管理员',
    organization: '国家测绘地理信息局',
    email: '<EMAIL>',
    status: '正常',
    createdAt: '2025-01-15 09:30',
    lastLoginAt: '2025-08-21 14:25',
  },
  {
    id: 'U-2025-002',
    username: 'supervisor_01',
    realName: '王监管员',
    userType: '政府监管员',
    organization: '北京市测绘院',
    email: '<EMAIL>',
    status: '正常',
    createdAt: '2025-02-20 16:45',
    lastLoginAt: '2025-08-21 10:15',
  },
  {
    id: 'U-2025-003',
    username: 'corp_admin_geek',
    realName: '张总经理',
    userType: '企业管理员',
    organization: '浙江极氪智能科技有限公司',
    email: '<EMAIL>',
    status: '正常',
    createdAt: '2025-03-10 11:20',
    lastLoginAt: '2025-08-20 16:30',
  },
  {
    id: 'U-2025-004',
    username: 'corp_operator_01',
    realName: '刘操作员',
    userType: '企业操作员',
    organization: '浙江极氪智能科技有限公司',
    email: '<EMAIL>',
    status: '待激活',
    createdAt: '2025-08-18 14:30',
  },
  {
    id: 'U-2025-005',
    username: 'corp_admin_lianxing',
    realName: '陈管理员',
    userType: '企业管理员',
    organization: '联行科技股份有限公司',
    email: '<EMAIL>',
    status: '已停用',
    createdAt: '2025-04-05 09:15',
    lastLoginAt: '2025-07-28 15:45',
  },
])

// 过滤后的数据
const filteredUsers = computed(() => {
  return users.value.filter((user) => {
    if (
      filters.value.username &&
      !user.username.toLowerCase().includes(filters.value.username.toLowerCase()) &&
      !user.realName.includes(filters.value.username)
    ) {
      return false
    }
    if (filters.value.userType !== 'ALL' && user.userType !== filters.value.userType) {
      return false
    }
    if (filters.value.organization && !user.organization.includes(filters.value.organization)) {
      return false
    }
    if (filters.value.status !== 'ALL' && user.status !== filters.value.status) {
      return false
    }
    if (filters.value.timeRange && filters.value.timeRange[0] && filters.value.timeRange[1]) {
      const startDate = new Date(
        filters.value.timeRange[0].getFullYear(),
        filters.value.timeRange[0].getMonth(),
        filters.value.timeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.value.timeRange[1].getFullYear(),
        filters.value.timeRange[1].getMonth(),
        filters.value.timeRange[1].getDate(),
        23,
        59,
        59,
      )
      const createdDate = new Date(user.createdAt.replace(/-/g, '/'))
      if (createdDate < startDate || createdDate > endDate) return false
    }
    return true
  })
})

// 分页数据
const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredUsers.value.length / pageSize.value)),
)
const pagedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredUsers.value.slice(start, start + pageSize.value)
})

// 搜索处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
  console.log('搜索条件:', filters.value)
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    username: '',
    userType: 'ALL',
    organization: '',
    status: 'ALL',
    timeRange: null,
  }
  currentPage.value = 1
}

// 导出数据
const exportData = () => {
  const headers = [
    '序号',
    '用户名',
    '真实姓名',
    '用户类型',
    '所属机构',
    '邮箱',
    '状态',
    '注册时间',
    '最后登录',
  ]
  const rows = filteredUsers.value.map((user, index) => [
    (index + 1).toString(),
    user.username,
    user.realName,
    user.userType,
    user.organization,
    user.email,
    user.status,
    user.createdAt,
    user.lastLoginAt || '从未登录',
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `用户管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

// 新增用户弹窗状态与表单
const addUserOpen = ref(false)

type NewUserForm = {
  username: string
  realName: string
  userType: UserType
  organization: string
  email: string
  status: UserStatus
}

const newUser = ref<NewUserForm>({
  username: '',
  realName: '',
  userType: '政府监管员',
  organization: '',
  email: '',
  status: '待激活',
})

const formatNow = () => {
  const d = new Date()
  const pad = (n: number) => String(n).padStart(2, '0')
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(
    d.getMinutes(),
  )}`
}

const saveNewUser = () => {
  // 演示阶段：最小校验，保证用户名与邮箱有值
  if (!newUser.value.username || !newUser.value.email) {
    // 可加 toast 提示；演示阶段先简单返回
    return
  }
  const id = `U-${Date.now()}`
  const item = {
    id,
    username: newUser.value.username,
    realName: newUser.value.realName || newUser.value.username,
    userType: newUser.value.userType,
    organization: newUser.value.organization || '未填写',
    email: newUser.value.email,
    status: newUser.value.status,
    createdAt: formatNow(),
    lastLoginAt: undefined,
  } as UserItem
  users.value.unshift(item)
  addUserOpen.value = false
  // 重置表单
  newUser.value = {
    username: '',
    realName: '',
    userType: '政府监管员',
    organization: '',
    email: '',
    status: '待激活',
  }
}

// 操作处理函数
const handleCreateUser = () => {
  addUserOpen.value = true
}

const handleEdit = (id: string) => {
  console.log('编辑用户:', id)
}

const handleActivate = (id: string) => {
  const user = users.value.find((u) => u.id === id)
  if (user) {
    user.status = '正常'
  }
  console.log('激活用户:', id)
}

const handleSuspend = (id: string) => {
  const user = users.value.find((u) => u.id === id)
  if (user) {
    user.status = '已停用'
  }
  console.log('停用用户:', id)
}

const handleResetPassword = (id: string) => {
  console.log('重置密码:', id)
}

const handleDelete = (id: string) => {
  const index = users.value.findIndex((u) => u.id === id)
  if (index > -1) {
    users.value.splice(index, 1)
  }
  console.log('删除用户:', id)
}

// Badge样式
const getTypeVariant = (type: UserType) => {
  switch (type) {
    case '政府管理员':
      return 'default'
    case '政府监管员':
      return 'secondary'
    case '企业管理员':
      return 'outline'
    case '企业操作员':
      return 'outline'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: UserStatus) => {
  switch (status) {
    case '正常':
      return 'default'
    case '待激活':
      return 'secondary'
    case '已停用':
      return 'destructive'
    case '已锁定':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>
