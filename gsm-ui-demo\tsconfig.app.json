{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/types/*.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "lib": ["ES2020", "DOM", "DOM.Iterable", "WebWorker"], "types": ["vite/client"], "skipLibCheck": true, "strict": false, "noImplicitAny": false, "noImplicitThis": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "noPropertyAccessFromIndexSignature": false, "exactOptionalPropertyTypes": false, "noUncheckedIndexedAccess": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": false, "paths": {"@/*": ["./src/*"]}}}