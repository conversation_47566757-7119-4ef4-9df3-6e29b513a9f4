<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">编辑政策法规、标准规范、公告通知等信息内容</p>
      </div>
      <Button variant="outline" @click="goBack" class="flex items-center gap-2">
        <ArrowLeft class="w-4 h-4" />
        返回列表
      </Button>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardContent class="pt-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <Label for="title" class="required">文章标题</Label>
              <Input
                id="title"
                v-model="formData.title"
                placeholder="请输入文章标题"
                :class="{ 'border-red-500': errors.title }"
                required
              />
              <p v-if="errors.title" class="text-sm text-red-500">{{ errors.title }}</p>
            </div>

            <div class="space-y-2">
              <Label for="type" class="required">信息类型</Label>
              <Select v-model="formData.type" required>
                <SelectTrigger id="type" :class="{ 'border-red-500': errors.type }">
                  <SelectValue placeholder="请选择信息类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="政策法规">政策法规</SelectItem>
                  <SelectItem value="标准规范">标准规范</SelectItem>
                  <SelectItem value="公告通知">公告通知</SelectItem>
                  <SelectItem value="新闻动态">新闻动态</SelectItem>
                  <SelectItem value="通知公告">通知公告</SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.type" class="text-sm text-red-500">{{ errors.type }}</p>
            </div>
          </div>

          <!-- 摘要 -->
          <div class="space-y-2">
            <Label for="summary" class="required">文章摘要</Label>
            <Textarea
              id="summary"
              v-model="formData.summary"
              placeholder="请输入文章摘要（建议100-200字）"
              :class="{ 'border-red-500': errors.summary }"
              rows="3"
              maxlength="500"
              required
            />
            <div class="flex justify-between">
              <p v-if="errors.summary" class="text-sm text-red-500">{{ errors.summary }}</p>
              <p class="text-sm text-muted-foreground">{{ formData.summary.length }}/500</p>
            </div>
          </div>

          <!-- 封面图片 -->
          <div class="space-y-2">
            <Label for="cover">封面图片</Label>
            <div class="space-y-4">
              <div v-if="formData.coverImage" class="relative w-full max-w-md">
                <img
                  :src="formData.coverImage"
                  alt="封面预览"
                  class="w-full h-48 object-cover rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  class="absolute top-2 right-2"
                  @click="removeCoverImage"
                >
                  <X class="w-4 h-4" />
                </Button>
              </div>
              <div
                v-else
                class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer"
                @click="triggerFileUpload"
                @dragover.prevent
                @drop.prevent="handleFileDrop"
              >
                <Upload class="w-8 h-8 mx-auto mb-4 text-muted-foreground" />
                <p class="text-muted-foreground mb-2">点击上传或拖拽文件到此区域</p>
                <p class="text-sm text-muted-foreground">支持 JPG、PNG 格式，大小不超过 5MB</p>
              </div>
              <input
                ref="fileInput"
                type="file"
                accept="image/jpeg,image/png"
                class="hidden"
                @change="handleFileSelect"
              />
            </div>
          </div>

          <!-- 富文本编辑器 -->
          <div class="space-y-2">
            <Label for="content" class="required">正文内容</Label>
            <div class="border rounded-lg overflow-hidden">
              <!-- 工具栏 -->
              <div class="border-b bg-muted/30 p-3 flex flex-wrap gap-2">
                <div class="flex items-center gap-1 border-r pr-2 mr-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('bold')"
                    :class="{ 'bg-accent': isFormatActive('bold') }"
                  >
                    <Bold class="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('italic')"
                    :class="{ 'bg-accent': isFormatActive('italic') }"
                  >
                    <Italic class="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('underline')"
                    :class="{ 'bg-accent': isFormatActive('underline') }"
                  >
                    <Underline class="w-4 h-4" />
                  </Button>
                </div>

                <div class="flex items-center gap-1 border-r pr-2 mr-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('justifyLeft')"
                  >
                    <AlignLeft class="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('justifyCenter')"
                  >
                    <AlignCenter class="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('justifyRight')"
                  >
                    <AlignRight class="w-4 h-4" />
                  </Button>
                </div>

                <div class="flex items-center gap-1 border-r pr-2 mr-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('insertOrderedList')"
                  >
                    <List class="w-4 h-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="execCommand('insertUnorderedList')"
                  >
                    <ListChecks class="w-4 h-4" />
                  </Button>
                </div>

                <div class="flex items-center gap-1">
                  <Button type="button" variant="ghost" size="sm" @click="insertLink">
                    <Link class="w-4 h-4" />
                  </Button>
                  <Button type="button" variant="ghost" size="sm" @click="insertImage">
                    <ImageIcon class="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <!-- 编辑区域 -->
              <div
                ref="contentEditor"
                contenteditable="true"
                class="min-h-[400px] p-4 prose max-w-none focus:outline-none"
                :class="{ 'border-red-500': errors.content }"
                @input="updateContent"
                @paste="handlePaste"
                @keydown="handleKeydown"
                placeholder="请输入正文内容..."
              ></div>
            </div>
            <p v-if="errors.content" class="text-sm text-red-500">{{ errors.content }}</p>
          </div>

          <!-- 发布设置 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <Label for="publishMode">发布模式</Label>
              <Select v-model="formData.publishMode">
                <SelectTrigger id="publishMode">
                  <SelectValue placeholder="请选择发布模式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">立即发布</SelectItem>
                  <SelectItem value="draft">保存为草稿</SelectItem>
                  <SelectItem value="scheduled">定时发布</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div v-if="formData.publishMode === 'scheduled'" class="space-y-2">
              <Label for="publishTime">发布时间</Label>
              <Input
                id="publishTime"
                v-model="formData.publishTime"
                type="datetime-local"
                :min="minDateTime"
              />
            </div>
          </div>

          <!-- 标签和分类 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <Label for="tags">标签</Label>
              <div class="space-y-2">
                <Input
                  v-model="tagInput"
                  placeholder="输入标签后按回车添加"
                  @keydown.enter.prevent="addTag"
                />
                <div v-if="formData.tags.length > 0" class="flex flex-wrap gap-2">
                  <Badge
                    v-for="(tag, index) in formData.tags"
                    :key="index"
                    variant="secondary"
                    class="flex items-center gap-1"
                  >
                    {{ tag }}
                    <X class="w-3 h-3 cursor-pointer" @click="removeTag(index)" />
                  </Badge>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <Label for="priority">优先级</Label>
              <Select v-model="formData.priority">
                <SelectTrigger id="priority">
                  <SelectValue placeholder="请选择优先级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">高</SelectItem>
                  <SelectItem value="normal">普通</SelectItem>
                  <SelectItem value="low">低</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-end gap-4 pt-6 border-t">
            <Button type="button" variant="outline" @click="resetForm"> 重置 </Button>
            <Button type="button" variant="outline" @click="saveDraft"> 保存草稿 </Button>
            <Button type="submit" :disabled="isSubmitting">
              <Loader2 v-if="isSubmitting" class="w-4 h-4 mr-2 animate-spin" />
              {{ isNew ? '发布信息' : '更新信息' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 链接插入弹窗 -->
    <Sheet v-model:open="linkDialogOpen">
      <SheetContent side="right" class="w-[33vw] min-w-[380px] max-w-[640px]">
        <SheetHeader>
          <SheetTitle>插入链接</SheetTitle>
          <SheetDescription>请输入链接信息</SheetDescription>
        </SheetHeader>
        <div class="space-y-4">
          <div class="space-y-2">
            <Label for="linkText">链接文字</Label>
            <Input id="linkText" v-model="linkData.text" placeholder="请输入链接文字" />
          </div>
          <div class="space-y-2">
            <Label for="linkUrl">链接地址</Label>
            <Input id="linkUrl" v-model="linkData.url" placeholder="https://example.com" />
          </div>
        </div>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="linkDialogOpen = false">取消</Button>
          <Button @click="confirmInsertLink">确认</Button>
        </div>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {
  AlignCenter,
  AlignLeft,
  AlignRight,
  ArrowLeft,
  Bold,
  ImageIcon,
  Italic,
  Link,
  List,
  ListChecks,
  Loader2,
  Underline,
  Upload,
  X,
} from 'lucide-vue-next'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'

const router = useRouter()

interface Props {
  id?: string
}

const props = defineProps<Props>()

type InformationType = '政策法规' | '标准规范' | '公告通知' | '新闻动态' | '通知公告'

interface FormData {
  title: string
  type: InformationType | ''
  summary: string
  content: string
  coverImage: string
  publishMode: 'immediate' | 'draft' | 'scheduled'
  publishTime: string
  tags: string[]
  priority: 'high' | 'normal' | 'low'
}

// 计算是否为新建模式
const isNew = computed(() => !props.id || props.id === 'new')

// 表单数据
const formData = reactive<FormData>({
  title: '',
  type: '',
  summary: '',
  content: '',
  coverImage: '',
  publishMode: 'immediate',
  publishTime: '',
  tags: [],
  priority: 'normal',
})

// 表单验证错误
const errors = reactive({
  title: '',
  type: '',
  summary: '',
  content: '',
})

// 表单状态
const isSubmitting = ref(false)
const tagInput = ref('')
const fileInput = ref<HTMLInputElement>()
const contentEditor = ref<HTMLDivElement>()

// 富文本编辑器状态
const linkDialogOpen = ref(false)
const linkData = reactive({
  text: '',
  url: '',
})

// 最小日期时间（当前时间）
const minDateTime = computed(() => {
  return new Date().toISOString().slice(0, 16)
})

// 返回列表
const goBack = () => {
  router.push('/gov/system/information')
}

// 表单验证
const validateForm = () => {
  errors.title = formData.title ? '' : '请输入文章标题'
  errors.type = formData.type ? '' : '请选择信息类型'
  errors.summary = formData.summary ? '' : '请输入文章摘要'
  errors.content = formData.content ? '' : '请输入正文内容'

  return !Object.values(errors).some((error) => error)
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    console.log('提交数据:', {
      ...formData,
      id: props.id,
    })

    // 成功后返回列表页
    router.push('/gov/system/information')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

// 保存草稿
const saveDraft = async () => {
  console.log('保存草稿:', formData)
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    type: '',
    summary: '',
    content: '',
    coverImage: '',
    publishMode: 'immediate',
    publishTime: '',
    tags: [],
    priority: 'normal',
  })

  if (contentEditor.value) {
    contentEditor.value.innerHTML = ''
  }
}

// 文件上传相关
const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const files = (event.target as HTMLInputElement).files
  if (files && files[0]) {
    handleFileUpload(files[0])
  }
}

const handleFileDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files
  if (files && files[0]) {
    handleFileUpload(files[0])
  }
}

const handleFileUpload = (file: File) => {
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件')
    return
  }

  if (file.size > 5 * 1024 * 1024) {
    alert('文件大小不能超过5MB')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    formData.coverImage = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const removeCoverImage = () => {
  formData.coverImage = ''
}

// 标签管理
const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !formData.tags.includes(tag)) {
    formData.tags.push(tag)
    tagInput.value = ''
  }
}

const removeTag = (index: number) => {
  formData.tags.splice(index, 1)
}

// 富文本编辑器功能
const execCommand = (command: string, value?: string) => {
  document.execCommand(command, false, value)
  contentEditor.value?.focus()
}

const isFormatActive = (format: string) => {
  return document.queryCommandState(format)
}

const updateContent = () => {
  if (contentEditor.value) {
    formData.content = contentEditor.value.innerHTML
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const handleKeydown = (event: KeyboardEvent) => {
  // 处理特殊键盘操作
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        execCommand('bold')
        break
      case 'i':
        event.preventDefault()
        execCommand('italic')
        break
      case 'u':
        event.preventDefault()
        execCommand('underline')
        break
    }
  }
}

// 链接插入
const insertLink = () => {
  const selection = window.getSelection()
  if (selection && selection.toString()) {
    linkData.text = selection.toString()
  }
  linkDialogOpen.value = true
}

const confirmInsertLink = () => {
  if (linkData.url) {
    const linkHtml = `<a href="${linkData.url}" target="_blank">${linkData.text || linkData.url}</a>`
    document.execCommand('insertHTML', false, linkHtml)
  }
  linkDialogOpen.value = false
  linkData.text = ''
  linkData.url = ''
  contentEditor.value?.focus()
}

// 图片插入
const insertImage = () => {
  const url = prompt('请输入图片URL：')
  if (url) {
    const imgHtml = `<img src="${url}" alt="插入的图片" style="max-width: 100%; height: auto;" />`
    document.execCommand('insertHTML', false, imgHtml)
  }
  contentEditor.value?.focus()
}

// 加载数据（编辑模式）
const loadData = async () => {
  if (!isNew.value && props.id) {
    try {
      // 模拟加载数据
      const mockData = {
        title: '示例文章标题',
        type: '政策法规' as InformationType,
        summary: '这是一个示例文章的摘要内容，用于演示编辑功能。',
        content: '<p>这是文章的正文内容，支持富文本编辑。</p>',
        coverImage: '',
        publishMode: 'immediate' as const,
        publishTime: '',
        tags: ['示例', '测试'],
        priority: 'normal' as const,
      }

      Object.assign(formData, mockData)

      // 设置编辑器内容
      await nextTick()
      if (contentEditor.value) {
        contentEditor.value.innerHTML = formData.content
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.required::after {
  content: ' *';
  color: rgb(239 68 68);
}

/* 富文本编辑器样式 */
.prose {
  line-height: 1.6;
}

.prose p {
  margin-bottom: 1em;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose h1 {
  font-size: 1.875rem;
}

.prose h2 {
  font-size: 1.5rem;
}

.prose h3 {
  font-size: 1.25rem;
}

.prose ul,
.prose ol {
  margin: 1em 0;
  padding-left: 2em;
}

.prose li {
  margin: 0.5em 0;
}

.prose a {
  color: rgb(59 130 246);
  text-decoration: underline;
}

.prose img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border-radius: 0.5rem;
}

.prose blockquote {
  border-left: 4px solid rgb(229 231 235);
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: rgb(107 114 128);
}

/* 编辑器聚焦样式 */
[contenteditable='true']:focus {
  outline: none;
}

[contenteditable='true']:empty:before {
  content: attr(placeholder);
  color: rgb(156 163 175);
  font-style: italic;
}
</style>
