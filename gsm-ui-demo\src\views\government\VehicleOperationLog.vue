<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          车端操作日志的记录、查询与分析，按协议字段全量展示，支持数据导出
        </p>
      </div>
    </div>

    <!-- 列表 -->
    <Card>
      <CardHeader>
        <CardTitle>车端操作日志列表</CardTitle>
        <CardDescription>
          共 {{ filteredTotal }} 条记录，当前显示第
          {{ pageSize * (currentPage - 1) + 1 }}
          -
          {{ Math.min(pageSize * currentPage, filteredTotal) }}
          条
        </CardDescription>
      </CardHeader>
      <CardContent>
        <!-- 查询表单 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportCsv"
        />
        <div class="rounded-md border mt-4 overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[60px]">序号</TableHead>
                <TableHead class="min-w-[150px]">注册企业</TableHead>
                <TableHead class="min-w-[140px]">车辆 VIN</TableHead>
                <TableHead class="min-w-[80px]">品牌</TableHead>
                <TableHead class="min-w-[80px]">车型</TableHead>
                <TableHead class="min-w-[80px]">处理阶段</TableHead>
                <TableHead class="min-w-[100px]">操作类型</TableHead>
                <TableHead class="min-w-[200px]">操作详情</TableHead>
                <TableHead class="min-w-[140px]">操作时间</TableHead>
                <TableHead class="min-w-[80px]">操作人</TableHead>
                <TableHead class="min-w-[100px]">来源IP</TableHead>
                <TableHead class="min-w-[80px]">数据量</TableHead>
                <TableHead class="min-w-[80px]">操作结果</TableHead>
                <TableHead class="min-w-[150px]">错误信息</TableHead>
                <TableHead class="min-w-[120px]">追踪ID</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedItems.length === 0">
                <TableCell :colspan="15" class="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedItems"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.enterprise }}</div>
                    <div class="text-xs text-muted-foreground">{{ item.enterpriseCode }}</div>
                  </div>
                </TableCell>
                <TableCell class="font-mono text-xs">{{ item.vin }}</TableCell>
                <TableCell>{{ item.brand }}</TableCell>
                <TableCell>{{ item.model }}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{{ item.stage }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{{ item.operationType }}</Badge>
                </TableCell>
                <TableCell class="max-w-[300px]">
                  <div class="text-sm line-clamp-2" :title="item.operationDetail">
                    {{ item.operationDetail }}
                  </div>
                </TableCell>
                <TableCell class="whitespace-nowrap text-xs">{{ item.operationTime }}</TableCell>
                <TableCell>{{ item.operator || '-' }}</TableCell>
                <TableCell class="font-mono text-xs">{{ maskIP(item.sourceIP) }}</TableCell>
                <TableCell>{{ item.dataSize || '-' }}</TableCell>
                <TableCell>
                  <Badge :variant="item.result === '成功' ? 'default' : 'destructive'">
                    {{ item.result }}
                  </Badge>
                </TableCell>
                <TableCell class="max-w-[200px]">
                  <div
                    v-if="item.errorMsg"
                    class="text-xs text-destructive line-clamp-2"
                    :title="item.errorMsg"
                  >
                    {{ item.errorMsg }}
                  </div>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell class="font-mono text-xs">{{ item.traceId }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页（统一使用 shadcn-vue Pagination） -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredTotal) }} 条， 共 {{ filteredTotal }} 条记录
          </div>
          <div class="flex items-center gap-4">
            <div class="pagination-size-control">
              <span>每页显示</span>
              <Select :model-value="pageSize.toString()" @update:model-value="onPageSizeChange">
                <SelectTrigger class="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span>条</span>
            </div>
            <Pagination
              v-model:page="currentPage"
              :total="filteredTotal"
              :items-per-page="pageSize"
              :sibling-count="1"
              :show-edges="true"
            >
              <PaginationContent v-slot="{ items }">
                <PaginationFirst />
                <PaginationPrevious />
                <template v-for="(item, idx) in items" :key="idx">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === currentPage"
                  />
                  <PaginationEllipsis v-else />
                </template>
                <PaginationNext />
                <PaginationLast />
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type OperationStage = '收集' | '存储' | '传输'
type OperationResult = '成功' | '失败'

interface VehicleOperationLog {
  id: string
  enterprise: string
  enterpriseCode: string
  vin: string
  brand: string
  model: string
  stage: OperationStage
  operationType: string
  operationDetail: string
  operationTime: string
  operator?: string
  sourceIP?: string
  dataSize?: string
  result: OperationResult
  errorMsg?: string
  traceId?: string
}

interface Filters {
  enterprise: string
  vin: string
  brand: string
  model: string
  stage: 'ALL' | OperationStage
  operationType: string
  result: 'ALL' | OperationResult
  timeRange: [Date, Date] | null
}

// 筛选器
const filters = ref<Filters>({
  enterprise: '',
  vin: '',
  brand: '',
  model: '',
  stage: 'ALL',
  operationType: 'ALL',
  result: 'ALL',
  timeRange: null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'enterprise',
    label: '注册企业',
    type: 'input',
    placeholder: '企业名称',
  },
  {
    key: 'vin',
    label: '车辆VIN',
    type: 'input',
    placeholder: 'VIN编号',
  },
  {
    key: 'brand',
    label: '品牌',
    type: 'input',
    placeholder: '车辆品牌',
  },
  {
    key: 'model',
    label: '车型',
    type: 'input',
    placeholder: '车型',
  },
  {
    key: 'stage',
    label: '处理阶段',
    type: 'select',
    placeholder: '选择阶段',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '收集', value: '收集' },
      { label: '存储', value: '存储' },
      { label: '传输', value: '传输' },
    ],
  },
  {
    key: 'operationType',
    label: '操作类型',
    type: 'select',
    placeholder: '操作类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '数据采集', value: '数据采集' },
      { label: '数据存储', value: '数据存储' },
      { label: '数据上传', value: '数据上传' },
      { label: '数据删除', value: '数据删除' },
      { label: '配置更新', value: '配置更新' },
      { label: '系统启动', value: '系统启动' },
      { label: '系统关闭', value: '系统关闭' },
    ],
  },
  {
    key: 'result',
    label: '操作结果',
    type: 'select',
    placeholder: '操作结果',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '成功', value: '成功' },
      { label: '失败', value: '失败' },
    ],
  },
  {
    key: 'timeRange',
    label: '时间范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

const handleSearch = (searchFilters: Record<string, string | string[] | null | Date[]>) => {
  Object.assign(filters.value, searchFilters)
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    enterprise: '',
    vin: '',
    brand: '',
    model: '',
    stage: 'ALL',
    operationType: 'ALL',
    result: 'ALL',
    timeRange: null,
  }
}

// Mock 数据
const operationLogs = ref<VehicleOperationLog[]>([
  {
    id: 'LOG-202501-0001',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    vin: 'LSGJ8A12X34567890',
    brand: '极氪汽车',
    model: 'G1 Pro',
    stage: '收集',
    operationType: '数据采集',
    operationDetail: '启动地理信息数据采集任务，区域：北京市海淀区中关村',
    operationTime: '2025-08-21 10:32:45',
    operator: 'System',
    sourceIP: '*************',
    dataSize: '2.3MB',
    result: '成功',
    traceId: 'TRC-20250821-103245',
  },
  {
    id: 'LOG-202501-0002',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    vin: 'LSGJ8A12X34567890',
    brand: '极氪汽车',
    model: 'G1 Pro',
    stage: '存储',
    operationType: '数据存储',
    operationDetail: '将采集的地理信息数据存储至本地存储系统，应用数据脱敏策略',
    operationTime: '2025-08-21 10:35:12',
    operator: 'System',
    sourceIP: '*************',
    dataSize: '2.1MB',
    result: '成功',
    traceId: 'TRC-20250821-103512',
  },
  {
    id: 'LOG-202501-0003',
    enterprise: '上海车联网服务有限公司',
    enterpriseCode: '91310000987654321Y',
    vin: 'WBAVC31060A123456',
    brand: '联行',
    model: 'L5',
    stage: '传输',
    operationType: '数据上传',
    operationDetail: '上传处理后的地理信息数据至云端服务器，使用加密传输',
    operationTime: '2025-08-21 11:20:33',
    operator: 'upload_service',
    sourceIP: '*********',
    dataSize: '5.8MB',
    result: '失败',
    errorMsg: '网络连接超时，上传任务失败，错误代码：NET_TIMEOUT_001',
    traceId: 'TRC-20250821-112033',
  },
  {
    id: 'LOG-202501-0004',
    enterprise: '深圳自动驾驶技术有限公司',
    enterpriseCode: '91440300567890123Z',
    vin: 'LFPH3ACC9J1234567',
    brand: '南山智能',
    model: 'N7 Max',
    stage: '收集',
    operationType: '配置更新',
    operationDetail: '更新地理信息采集配置，调整采集频率和精度参数',
    operationTime: '2025-08-21 12:05:18',
    operator: 'admin',
    sourceIP: '***********',
    result: '成功',
    traceId: 'TRC-20250821-120518',
  },
  {
    id: 'LOG-202501-0005',
    enterprise: '广州汽车制造有限公司',
    enterpriseCode: '91440000456789012A',
    vin: 'LVGB4ACE8JG123456',
    brand: '羊城',
    model: 'Y3',
    stage: '传输',
    operationType: '数据上传',
    operationDetail: '批量上传今日采集的地理信息数据，共计128个数据包',
    operationTime: '2025-08-21 13:45:22',
    operator: 'batch_upload',
    sourceIP: '**************',
    dataSize: '45.6MB',
    result: '成功',
    traceId: 'TRC-20250821-134522',
  },
  {
    id: 'LOG-202501-0006',
    enterprise: '杭州智能交通有限公司',
    enterpriseCode: '91330000789012345B',
    vin: 'LHGCR1878J8123456',
    brand: '西湖',
    model: 'X5 Plus',
    stage: '存储',
    operationType: '数据删除',
    operationDetail: '执行定期数据清理任务，删除30天前的过期数据',
    operationTime: '2025-08-21 14:00:00',
    operator: 'cleanup_task',
    sourceIP: '**************',
    dataSize: '156.2MB',
    result: '成功',
    traceId: 'TRC-20250821-140000',
  },
  {
    id: 'LOG-202501-0007',
    enterprise: '成都新能源汽车有限公司',
    enterpriseCode: '91510000234567890C',
    vin: 'LSJA24U62JG123456',
    brand: '蓉城',
    model: 'R7',
    stage: '收集',
    operationType: '系统启动',
    operationDetail: '车端地理信息采集系统启动，加载配置文件和安全策略',
    operationTime: '2025-08-21 14:30:45',
    operator: 'System',
    sourceIP: '***********',
    result: '成功',
    traceId: 'TRC-20250821-143045',
  },
  {
    id: 'LOG-202501-0008',
    enterprise: '南京科技创新有限公司',
    enterpriseCode: '91320000345678901D',
    vin: 'LJDCAA224J0123456',
    brand: '金陵',
    model: 'J6',
    stage: '传输',
    operationType: '数据上传',
    operationDetail: '实时上传高精度地图数据，应用差分隐私保护',
    operationTime: '2025-08-21 15:15:30',
    operator: 'realtime_service',
    sourceIP: '**************',
    dataSize: '8.9MB',
    result: '失败',
    errorMsg: '数据校验失败，检测到异常坐标信息，拒绝上传',
    traceId: 'TRC-20250821-151530',
  },
  {
    id: 'LOG-202501-0009',
    enterprise: '天津智驾科技有限公司',
    enterpriseCode: '91120000456789012E',
    vin: 'LTPCA3D4XJF123456',
    brand: '海河',
    model: 'H8 Pro',
    stage: '收集',
    operationType: '数据采集',
    operationDetail: '进入限制区域，自动停止地理信息采集功能',
    operationTime: '2025-08-21 16:20:15',
    operator: 'System',
    sourceIP: '*************',
    result: '成功',
    traceId: 'TRC-20250821-162015',
  },
  {
    id: 'LOG-202501-0010',
    enterprise: '武汉光谷汽车有限公司',
    enterpriseCode: '91420000567890123F',
    vin: 'LWVCA3828JH123456',
    brand: '光谷',
    model: 'GV9',
    stage: '存储',
    operationType: '配置更新',
    operationDetail: '更新数据脱敏规则，增强位置信息保护级别',
    operationTime: '2025-08-21 17:00:00',
    operator: 'security_admin',
    sourceIP: '**************',
    result: '成功',
    traceId: 'TRC-20250821-170000',
  },
])

// IP地址脱敏
const maskIP = (ip?: string) => {
  if (!ip) return '-'
  const parts = ip.split('.')
  if (parts.length !== 4) return ip
  return `${parts[0]}.${parts[1]}.***.***`
}

// 过滤与分页
const filtered = computed(() => {
  const { enterprise, vin, brand, model, stage, operationType, result, timeRange } = filters.value
  return operationLogs.value.filter((log) => {
    if (enterprise && !log.enterprise.includes(enterprise)) return false
    if (vin && !log.vin.includes(vin)) return false
    if (brand && !log.brand.includes(brand)) return false
    if (model && !log.model.includes(model)) return false
    if (stage !== 'ALL' && log.stage !== stage) return false
    if (operationType !== 'ALL' && log.operationType !== operationType) return false
    if (result !== 'ALL' && log.result !== result) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
        0,
        0,
        0,
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const logTime = new Date(log.operationTime.replace(/-/g, '/')).getTime()
      if (logTime < start || logTime > end) return false
    }
    return true
  })
})
const filteredTotal = computed(() => filtered.value.length)

const currentPage = ref(1)
const pageSize = ref(10)
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.value.slice(start, start + pageSize.value)
})

const onPageSizeChange = (val: string) => {
  const n = parseInt(val)
  if (!Number.isNaN(n) && n > 0) {
    pageSize.value = n
    currentPage.value = 1
  }
}

// 导出 CSV
const exportCsv = () => {
  const headers = [
    '注册企业',
    '企业代码',
    '车辆VIN',
    '品牌',
    '车型',
    '处理阶段',
    '操作类型',
    '操作详情',
    '操作时间',
    '操作人',
    '来源IP',
    '数据量',
    '操作结果',
    '错误信息',
    '追踪ID',
  ]
  const rows = filtered.value.map((log) => [
    log.enterprise,
    log.enterpriseCode,
    log.vin,
    log.brand,
    log.model,
    log.stage,
    log.operationType,
    log.operationDetail.replace(/\n/g, ' '),
    log.operationTime,
    log.operator || '',
    log.sourceIP || '',
    log.dataSize || '',
    log.result,
    log.errorMsg || '',
    log.traceId || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `车端操作日志_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}
</script>
