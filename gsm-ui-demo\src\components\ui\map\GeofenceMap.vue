<template>
  <div class="relative w-full h-full">
    <!-- 地图容器 -->
    <div :id="mapId" class="w-full h-full rounded-lg overflow-hidden bg-gray-100 border"></div>
    
    <!-- 地图控制层 -->
    <div class="absolute top-4 right-4 z-10 space-y-2">
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 space-y-2">
        <div class="text-sm font-medium text-gray-700">地图控制</div>
        <div class="flex flex-col space-y-2">
          <Button size="sm" variant="outline" @click="zoomIn" class="w-full">
            <Plus class="w-3 h-3" />
          </Button>
          <Button size="sm" variant="outline" @click="zoomOut" class="w-full">
            <Minus class="w-3 h-3" />
          </Button>
          <Button size="sm" variant="outline" @click="resetView" class="w-full text-xs">
            重置
          </Button>
        </div>
      </div>
      
      <!-- 图层切换 -->
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 space-y-2">
        <div class="text-sm font-medium text-gray-700">地图图层</div>
        <div class="space-y-1">
          <label class="flex items-center text-xs">
            <input type="radio" v-model="mapType" value="satellite" class="mr-2" />
            卫星图
          </label>
          <label class="flex items-center text-xs">
            <input type="radio" v-model="mapType" value="roadmap" class="mr-2" />
            道路图
          </label>
          <label class="flex items-center text-xs">
            <input type="radio" v-model="mapType" value="hybrid" class="mr-2" />
            混合图
          </label>
        </div>
      </div>
    </div>
    
    <!-- 地图信息面板 -->
    <div class="absolute bottom-4 left-4 z-10">
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 space-y-2 min-w-64">
        <div class="text-sm font-medium text-gray-700">区域信息</div>
        <div class="space-y-1 text-xs text-gray-600">
          <div class="flex justify-between">
            <span>区域名称:</span>
            <span class="font-medium">{{ areaData.name || '未命名区域' }}</span>
          </div>
          <div class="flex justify-between">
            <span>区域类型:</span>
            <Badge :variant="getAreaTypeVariant(areaData.type)" class="text-xs">
              {{ areaData.type }}
            </Badge>
          </div>
          <div class="flex justify-between">
            <span>中心坐标:</span>
            <span class="font-mono">{{ areaData.longitude?.toFixed(6) }}, {{ areaData.latitude?.toFixed(6) }}</span>
          </div>
          <div class="flex justify-between">
            <span>监控半径:</span>
            <span class="font-medium">{{ areaData.radius }} km</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 模拟地图内容 -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div class="relative">
        <!-- 地图背景网格 -->
        <svg class="w-full h-full absolute inset-0" viewBox="0 0 800 600">
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e5e7eb" stroke-width="1" opacity="0.3"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
        
        <!-- 地理围栏圆圈 -->
        <div 
          class="absolute border-4 border-red-500 rounded-full opacity-60"
          :style="{
            width: `${circleSize}px`,
            height: `${circleSize}px`,
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
            borderColor: getCircleColor(areaData.type)
          }"
        >
          <!-- 圆心标记 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div 
              class="w-4 h-4 rounded-full shadow-lg"
              :class="getCenterMarkerClass(areaData.type)"
            >
              <div class="w-2 h-2 bg-white rounded-full absolute inset-0 m-auto"></div>
            </div>
          </div>
        </div>
        
        <!-- 模拟道路 -->
        <svg class="w-full h-full absolute inset-0" viewBox="0 0 800 600">
          <path d="M100,300 Q400,100 700,300" stroke="#6b7280" stroke-width="3" fill="none" opacity="0.4" stroke-dasharray="5,5" />
          <path d="M200,200 Q400,400 600,200" stroke="#6b7280" stroke-width="2" fill="none" opacity="0.4" />
          <path d="M150,500 L650,400" stroke="#6b7280" stroke-width="2" fill="none" opacity="0.4" stroke-dasharray="3,3" />
        </svg>
        
        <!-- 模拟建筑物 -->
        <div class="absolute top-24 left-32 w-8 h-6 bg-gray-400 opacity-40 rounded-sm"></div>
        <div class="absolute top-32 left-40 w-6 h-8 bg-gray-500 opacity-40 rounded-sm"></div>
        <div class="absolute bottom-32 right-32 w-10 h-8 bg-gray-400 opacity-40 rounded-sm"></div>
        <div class="absolute bottom-40 right-20 w-6 h-10 bg-gray-600 opacity-40 rounded-sm"></div>
        
        <!-- 车辆标记示例 -->
        <div v-if="showVehicles" class="absolute top-48 left-96">
          <div class="relative">
            <Car class="w-6 h-6 text-blue-600 transform rotate-45" />
            <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-1 rounded whitespace-nowrap">
              京A12345
            </div>
          </div>
        </div>
        
        <!-- 告警位置标记 -->
        <div v-if="showAlerts" class="absolute top-36 right-48">
          <div class="relative animate-pulse">
            <AlertTriangle class="w-6 h-6 text-red-500" />
            <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-red-600 text-white text-xs px-1 rounded whitespace-nowrap">
              异常告警
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图例 -->
    <div class="absolute bottom-4 right-4 z-10">
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3">
        <div class="text-sm font-medium text-gray-700 mb-2">图例</div>
        <div class="space-y-1 text-xs">
          <div class="flex items-center">
            <div class="w-3 h-3 border-2 border-red-500 rounded-full mr-2"></div>
            <span>监测区域边界</span>
          </div>
          <div class="flex items-center">
            <Car class="w-3 h-3 text-blue-600 mr-2" />
            <span>实时车辆</span>
          </div>
          <div class="flex items-center">
            <AlertTriangle class="w-3 h-3 text-red-500 mr-2" />
            <span>告警位置</span>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-1 bg-gray-400 mr-2"></div>
            <span>道路网络</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { AlertTriangle, Car, Minus, Plus } from 'lucide-vue-next'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface Props {
  areaData: {
    name?: string
    type?: string
    longitude?: number
    latitude?: number
    radius?: number
  }
  showVehicles?: boolean
  showAlerts?: boolean
  editable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  areaData: () => ({
    name: '',
    type: '一般区域',
    longitude: 116.4074,
    latitude: 39.9042,
    radius: 5
  }),
  showVehicles: true,
  showAlerts: true,
  editable: false
})

const emit = defineEmits<{
  'update:areaData': [value: any]
  'center-changed': [lng: number, lat: number]
  'radius-changed': [radius: number]
}>()

const mapId = ref(`map-${Date.now()}`)
const mapType = ref('roadmap')
const zoom = ref(13)

// 基于半径计算圆圈显示大小 (像素)
const circleSize = computed(() => {
  // 简单的缩放公式: 半径1km = 20px
  return Math.max(40, Math.min(400, props.areaData.radius * 20))
})

const getAreaTypeVariant = (type: string) => {
  const variants: Record<string, any> = {
    '敏感区域': 'destructive',
    '禁行区域': 'secondary',
    '重点区域': 'default',
    '一般区域': 'outline'
  }
  return variants[type] || 'outline'
}

const getCircleColor = (type: string) => {
  const colors: Record<string, string> = {
    '敏感区域': '#ef4444',
    '禁行区域': '#f97316',
    '重点区域': '#3b82f6',
    '一般区域': '#10b981'
  }
  return colors[type] || '#6b7280'
}

const getCenterMarkerClass = (type: string) => {
  const classes: Record<string, string> = {
    '敏感区域': 'bg-red-500',
    '禁行区域': 'bg-orange-500',
    '重点区域': 'bg-blue-500',
    '一般区域': 'bg-green-500'
  }
  return classes[type] || 'bg-gray-500'
}

const zoomIn = () => {
  zoom.value = Math.min(20, zoom.value + 1)
}

const zoomOut = () => {
  zoom.value = Math.max(3, zoom.value - 1)
}

const resetView = () => {
  zoom.value = 13
  mapType.value = 'roadmap'
}

// 监听地图类型变化
watch(mapType, (newType) => {
  console.log('地图类型切换为:', newType)
})

// 监听区域数据变化
watch(() => props.areaData, (newData) => {
  console.log('区域数据更新:', newData)
}, { deep: true })

onMounted(() => {
  console.log(`地图组件初始化完成: ${mapId.value}`)
})
</script>