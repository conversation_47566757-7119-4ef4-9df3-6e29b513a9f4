<template>
  <Card class="chart-card relative overflow-hidden">
    <!-- 加载遮罩 -->
    <div
      v-if="loading"
      class="absolute inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm"
    >
      <div class="flex flex-col items-center gap-3">
        <Loader2 class="h-8 w-8 animate-spin text-primary" />
        <span class="text-sm font-medium text-muted-foreground">加载中...</span>
      </div>
    </div>

    <!-- 卡片头部 -->
    <CardHeader v-if="title || subtitle || $slots.action" class="pb-4">
      <div class="flex items-start justify-between">
        <div class="flex items-start gap-4">
          <!-- 图标 - 增大尺寸 -->
          <component
            v-if="icon"
            :is="icon"
            :class="['mt-1 flex-shrink-0', iconClass || 'h-8 w-8 text-primary']"
          />

          <!-- 标题和副标题 - 加强样式 -->
          <div class="space-y-2">
            <CardTitle
              v-if="title"
              class="text-xl font-bold leading-tight tracking-tight text-card-foreground"
            >
              {{ title }}
            </CardTitle>
            <CardDescription
              v-if="subtitle"
              class="text-base text-muted-foreground/80 leading-relaxed"
            >
              {{ subtitle }}
            </CardDescription>
          </div>
        </div>

        <!-- 操作按钮插槽 -->
        <div v-if="$slots.action" class="flex-shrink-0">
          <slot name="action" />
        </div>
      </div>
    </CardHeader>

    <!-- 卡片内容 -->
    <CardContent :class="contentClass">
      <!-- 图表容器 -->
      <div
        :class="['chart-wrapper relative overflow-hidden', heightClass || defaultHeightClass]"
        :style="heightStyle"
      >
        <slot />
      </div>

      <!-- 底部信息插槽 -->
      <div v-if="$slots.footer" class="mt-6 pt-4 border-t border-border/50">
        <slot name="footer" />
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed, type Component } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-vue-next'

interface Props {
  // 基础属性
  title?: string
  subtitle?: string
  icon?: Component | string
  iconClass?: string
  loading?: boolean

  // 高度控制
  height?: string | number
  heightClass?: string

  // 样式定制
  contentClass?: string
  variant?: 'default' | 'compact' | 'spacious'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  variant: 'default',
})

// 计算默认高度类 - 增大尺寸避免遮挡
const defaultHeightClass = computed(() => {
  switch (props.variant) {
    case 'compact':
      return 'h-56 sm:h-64 lg:h-72'
    case 'spacious':
      return 'h-96 sm:h-[28rem] lg:h-[32rem]'
    default:
      return 'h-72 sm:h-80 lg:h-[22rem]'
  }
})

// 计算高度样式 - 提高最小高度
const heightStyle = computed(() => {
  if (typeof props.height === 'number') {
    return { height: `${props.height}px`, minHeight: '280px' }
  }
  if (typeof props.height === 'string' && props.height.includes('px')) {
    return { height: props.height, minHeight: '280px' }
  }
  return { minHeight: '280px' }
})
</script>

<style scoped>
.chart-card {
  @apply transition-all duration-200 border-border/50;
}

.chart-card:hover {
  @apply shadow-lg border-border/70;
}

/* 确保图表容器填满可用空间并优化内边距 */
.chart-wrapper {
  @apply w-full relative;
  /* 确保图表有足够的渲染空间 */
  padding: 8px;
}

/* 深色模式优化 */
:global(.dark) .chart-card {
  @apply bg-card/50 backdrop-blur-sm border-border/30;
}

:global(.dark) .chart-card:hover {
  @apply border-border/50 shadow-xl;
}

/* 响应式内边距 - 更合理的间距 */
@media (max-width: 640px) {
  .chart-card :deep(.card-header) {
    @apply px-4 py-4;
  }

  .chart-card :deep(.card-content) {
    @apply px-4 pb-4 pt-2;
  }

  .chart-wrapper {
    padding: 4px;
  }
}

@media (min-width: 640px) {
  .chart-card :deep(.card-header) {
    @apply px-6 py-5;
  }

  .chart-card :deep(.card-content) {
    @apply px-6 pb-6 pt-2;
  }

  .chart-wrapper {
    padding: 8px;
  }
}

@media (min-width: 1024px) {
  .chart-card :deep(.card-header) {
    @apply px-7 py-6;
  }

  .chart-card :deep(.card-content) {
    @apply px-7 pb-7 pt-3;
  }

  .chart-wrapper {
    padding: 12px;
  }
}

/* 图表标题样式增强 */
.chart-card :deep(.card-title) {
  @apply font-bold text-card-foreground tracking-tight;
  font-size: 1.25rem;
  line-height: 1.3;
}

.chart-card :deep(.card-description) {
  @apply text-muted-foreground/80;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* 加载状态优化 */
.chart-card .loading-overlay {
  @apply backdrop-blur-md bg-background/90;
}
</style>
