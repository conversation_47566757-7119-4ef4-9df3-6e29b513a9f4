<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          填报企业的车辆基本信息，包括车辆识别码、车牌号、生产日期等信息
        </p>
      </div>
    </div>

    <!-- 填报表单 -->
    <Card>
      <CardHeader>
        <CardTitle>车辆基本信息</CardTitle>
        <CardDescription>请填写车辆的详细信息，带 * 的为必填项</CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 车辆识别码 -->
            <div class="space-y-2">
              <Label for="vin">
                车辆识别码（VIN）
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="vin"
                v-model="formData.vin"
                placeholder="请输入17位车辆识别码"
                maxlength="17"
              />
            </div>

            <!-- 车牌号 -->
            <div class="space-y-2">
              <Label for="plateNumber">
                车牌号
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="plateNumber"
                v-model="formData.plateNumber"
                placeholder="例：京A12345"
              />
            </div>

            <!-- 车辆类型 -->
            <div class="space-y-2">
              <Label for="vehicleType">
                车辆类型
                <span class="text-red-500">*</span>
              </Label>
              <Select v-model="formData.vehicleType">
                <SelectTrigger id="vehicleType">
                  <SelectValue placeholder="请选择车辆类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="轿车">轿车</SelectItem>
                  <SelectItem value="SUV">SUV</SelectItem>
                  <SelectItem value="MPV">MPV</SelectItem>
                  <SelectItem value="货车">货车</SelectItem>
                  <SelectItem value="客车">客车</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 品牌 -->
            <div class="space-y-2">
              <Label for="brand">
                品牌
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="brand"
                v-model="formData.brand"
                placeholder="请输入车辆品牌"
              />
            </div>

            <!-- 型号 -->
            <div class="space-y-2">
              <Label for="model">
                型号
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="model"
                v-model="formData.model"
                placeholder="请输入车辆型号"
              />
            </div>

            <!-- 生产日期 -->
            <div class="space-y-2">
              <Label for="productionDate">
                生产日期
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="productionDate"
                v-model="formData.productionDate"
                type="date"
                placeholder="选择生产日期"
                required
              />
            </div>

            <!-- 自动驾驶等级 -->
            <div class="space-y-2">
              <Label for="autoLevel">
                自动驾驶等级
                <span class="text-red-500">*</span>
              </Label>
              <Select v-model="formData.autoLevel">
                <SelectTrigger id="autoLevel">
                  <SelectValue placeholder="请选择自动驾驶等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="L0">L0 - 无自动驾驶</SelectItem>
                  <SelectItem value="L1">L1 - 驾驶辅助</SelectItem>
                  <SelectItem value="L2">L2 - 部分自动驾驶</SelectItem>
                  <SelectItem value="L3">L3 - 有条件自动驾驶</SelectItem>
                  <SelectItem value="L4">L4 - 高度自动驾驶</SelectItem>
                  <SelectItem value="L5">L5 - 完全自动驾驶</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 车辆状态 -->
            <div class="space-y-2">
              <Label for="status">
                车辆状态
                <span class="text-red-500">*</span>
              </Label>
              <Select v-model="formData.status">
                <SelectTrigger id="status">
                  <SelectValue placeholder="请选择车辆状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="在用">在用</SelectItem>
                  <SelectItem value="测试">测试</SelectItem>
                  <SelectItem value="停用">停用</SelectItem>
                  <SelectItem value="报废">报废</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 所属企业 -->
            <div class="space-y-2">
              <Label for="enterprise">
                所属企业
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="enterprise"
                v-model="formData.enterprise"
                placeholder="请输入所属企业名称"
              />
            </div>

            <!-- 使用地区 -->
            <div class="space-y-2">
              <Label for="region">
                使用地区
                <span class="text-red-500">*</span>
              </Label>
              <Input
                id="region"
                v-model="formData.region"
                placeholder="请输入使用地区"
              />
            </div>
          </div>

          <!-- 传感器配置 -->
          <div class="space-y-4">
            <Label>传感器配置</Label>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="camera"
                  v-model:checked="formData.sensors.camera"
                />
                <Label for="camera" class="font-normal">摄像头</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="lidar"
                  v-model:checked="formData.sensors.lidar"
                />
                <Label for="lidar" class="font-normal">激光雷达</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="radar"
                  v-model:checked="formData.sensors.radar"
                />
                <Label for="radar" class="font-normal">毫米波雷达</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="ultrasonic"
                  v-model:checked="formData.sensors.ultrasonic"
                />
                <Label for="ultrasonic" class="font-normal">超声波雷达</Label>
              </div>
            </div>
          </div>

          <!-- 备注 -->
          <div class="space-y-2">
            <Label for="remark">备注</Label>
            <Textarea
              id="remark"
              v-model="formData.remark"
              placeholder="请输入备注信息（选填）"
              class="min-h-[100px]"
            />
          </div>

          <!-- 提交按钮 -->
          <div class="flex justify-end gap-4">
            <Button type="button" variant="outline" @click="handleReset">
              重置
            </Button>
            <Button type="submit">
              提交
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const router = useRouter()

// 表单数据
const formData = ref({
  vin: '',
  plateNumber: '',
  vehicleType: '',
  brand: '',
  model: '',
  productionDate: null,
  autoLevel: '',
  status: '',
  enterprise: '',
  region: '',
  sensors: {
    camera: false,
    lidar: false,
    radar: false,
    ultrasonic: false,
  },
  remark: '',
})

// 提交表单
const handleSubmit = () => {
  console.log('提交表单数据:', formData.value)
  // 这里可以添加提交逻辑
  alert('车辆信息已提交！')
  // 提交成功后跳转
  router.push('/corp/filing/vehicle-list')
}

// 重置表单
const handleReset = () => {
  formData.value = {
    vin: '',
    plateNumber: '',
    vehicleType: '',
    brand: '',
    model: '',
    productionDate: null,
    autoLevel: '',
    status: '',
    enterprise: '',
    region: '',
    sensors: {
      camera: false,
      lidar: false,
      radar: false,
      ultrasonic: false,
    },
    remark: '',
  }
}
</script>