<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          请填写企业数据处理活动相关信息，包含车辆产品信息、数据采集区域、处理方式等
        </p>
      </div>
      <Badge variant="outline" class="text-sm"> 步骤 5 / 5 </Badge>
    </div>

    <!-- 进度条 -->
    <div class="w-full bg-muted rounded-full h-2">
      <div
        class="bg-primary h-2 rounded-full transition-all duration-300"
        style="width: 100%"
      ></div>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Database class="w-5 h-5" />
          数据处理活动信息
        </CardTitle>
        <CardDescription>
          企业数据处理活动的详细信息，包含车辆信息、活动方式、处理目的等
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-8">
          <!-- 车辆产品信息 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <Car class="w-5 h-5" />
                车辆产品信息
              </CardTitle>
              <CardDescription>企业车辆产品及其自动驾驶系统配置信息</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <!-- 车辆列表操作 -->
              <div class="flex items-center justify-between">
                <h4 class="font-medium">车辆信息列表</h4>
                <div class="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    @click="importVehicles"
                    class="flex items-center gap-2"
                  >
                    <Upload class="w-4 h-4" />
                    模板导入
                  </Button>
                  <Button type="button" @click="addVehicle" class="flex items-center gap-2">
                    <Plus class="w-4 h-4" />
                    添加车辆
                  </Button>
                </div>
              </div>

              <div v-if="vehicles.length === 0" class="text-center py-12 text-muted-foreground">
                <Car class="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
                <p>暂无车辆信息</p>
                <p class="text-sm">点击"添加车辆"按钮开始填写</p>
              </div>

              <!-- 车辆卡片列表 -->
              <div v-for="(vehicle, index) in vehicles" :key="vehicle.id" class="space-y-4">
                <Card class="relative">
                  <div
                    class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
                  ></div>
                  <CardHeader class="pb-3">
                    <div class="flex items-center justify-between">
                      <CardTitle class="text-lg font-semibold">车辆 {{ index + 1 }}</CardTitle>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        @click="removeVehicle(index)"
                        class="text-red-600 hover:text-red-700"
                      >
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent class="space-y-4">
                    <!-- 基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div class="space-y-2">
                        <Label :for="`brand-${index}`" class="text-base font-semibold">
                          品牌 <span class="text-red-500">*</span>
                        </Label>
                        <Input
                          :id="`brand-${index}`"
                          v-model="vehicle.brand"
                          placeholder="请输入品牌"
                          :class="{ 'border-red-500': errors[`vehicle_${index}_brand`] }"
                        />
                        <p v-if="errors[`vehicle_${index}_brand`]" class="text-sm text-red-500">
                          {{ errors[`vehicle_${index}_brand`] }}
                        </p>
                      </div>
                      <div class="space-y-2">
                        <Label :for="`model-${index}`" class="text-base font-semibold">
                          车型 <span class="text-red-500">*</span>
                        </Label>
                        <Input
                          :id="`model-${index}`"
                          v-model="vehicle.model"
                          placeholder="请输入车型"
                          :class="{ 'border-red-500': errors[`vehicle_${index}_model`] }"
                        />
                        <p v-if="errors[`vehicle_${index}_model`]" class="text-sm text-red-500">
                          {{ errors[`vehicle_${index}_model`] }}
                        </p>
                      </div>
                      <div class="space-y-2">
                        <Label :for="`vin-${index}`" class="text-base font-semibold">
                          VIN码 <span class="text-red-500">*</span>
                        </Label>
                        <Input
                          :id="`vin-${index}`"
                          v-model="vehicle.vin"
                          placeholder="请输入17位VIN码"
                          maxlength="17"
                          :class="{ 'border-red-500': errors[`vehicle_${index}_vin`] }"
                        />
                        <p v-if="errors[`vehicle_${index}_vin`]" class="text-sm text-red-500">
                          {{ errors[`vehicle_${index}_vin`] }}
                        </p>
                      </div>
                      <div class="space-y-2">
                        <Label :for="`quantity-${index}`" class="text-base font-semibold">
                          数量 <span class="text-red-500">*</span>
                        </Label>
                        <Input
                          :id="`quantity-${index}`"
                          v-model="vehicle.quantity"
                          type="number"
                          min="1"
                          placeholder="请输入数量"
                          :class="{ 'border-red-500': errors[`vehicle_${index}_quantity`] }"
                        />
                        <p v-if="errors[`vehicle_${index}_quantity`]" class="text-sm text-red-500">
                          {{ errors[`vehicle_${index}_quantity`] }}
                        </p>
                      </div>
                    </div>

                    <!-- 自动驾驶系统配置 -->
                    <div class="space-y-4">
                      <h5 class="font-medium">自动驾驶系统配置</h5>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                          <Label :for="`system-name-${index}`" class="text-base font-semibold">
                            系统名称及版本 <span class="text-red-500">*</span>
                          </Label>
                          <Input
                            :id="`system-name-${index}`"
                            v-model="vehicle.systemName"
                            placeholder="请输入自动驾驶系统名称及版本"
                            :class="{ 'border-red-500': errors[`vehicle_${index}_systemName`] }"
                          />
                          <p
                            v-if="errors[`vehicle_${index}_systemName`]"
                            class="text-sm text-red-500"
                          >
                            {{ errors[`vehicle_${index}_systemName`] }}
                          </p>
                        </div>
                        <div class="space-y-2">
                          <Label :for="`sensor-config-${index}`" class="text-base font-semibold">
                            传感器数量与精度 <span class="text-red-500">*</span>
                          </Label>
                          <Input
                            :id="`sensor-config-${index}`"
                            v-model="vehicle.sensorConfig"
                            placeholder="请输入传感器配置信息"
                            :class="{ 'border-red-500': errors[`vehicle_${index}_sensorConfig`] }"
                          />
                          <p
                            v-if="errors[`vehicle_${index}_sensorConfig`]"
                            class="text-sm text-red-500"
                          >
                            {{ errors[`vehicle_${index}_sensorConfig`] }}
                          </p>
                        </div>
                      </div>
                      <div class="space-y-2">
                        <Label :for="`map-application-${index}`" class="text-base font-semibold">
                          地图数据应用情况 <span class="text-red-500">*</span>
                        </Label>
                        <Textarea
                          :id="`map-application-${index}`"
                          v-model="vehicle.mapApplication"
                          placeholder="请描述地图数据的应用情况，包括数据来源、更新频率、应用场景等"
                          rows="3"
                          :class="{ 'border-red-500': errors[`vehicle_${index}_mapApplication`] }"
                        />
                        <p
                          v-if="errors[`vehicle_${index}_mapApplication`]"
                          class="text-sm text-red-500"
                        >
                          {{ errors[`vehicle_${index}_mapApplication`] }}
                        </p>
                      </div>
                    </div>

                    <!-- 地理信息安全处理技术 -->
                    <div class="space-y-3">
                      <Label class="text-base font-semibold">
                        所用地理信息安全处理技术（多选） <span class="text-red-500">*</span>
                      </Label>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-encryption-${index}`"
                            v-model:checked="vehicle.securityTech.encryption"
                          />
                          <Label :for="`tech-encryption-${index}`" class="text-sm">数据加密</Label>
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-desensitization-${index}`"
                            v-model:checked="vehicle.securityTech.desensitization"
                          />
                          <Label :for="`tech-desensitization-${index}`" class="text-sm"
                            >数据脱敏</Label
                          >
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-anonymization-${index}`"
                            v-model:checked="vehicle.securityTech.anonymization"
                          />
                          <Label :for="`tech-anonymization-${index}`" class="text-sm"
                            >匿名化处理</Label
                          >
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-access-control-${index}`"
                            v-model:checked="vehicle.securityTech.accessControl"
                          />
                          <Label :for="`tech-access-control-${index}`" class="text-sm"
                            >访问控制</Label
                          >
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-audit-${index}`"
                            v-model:checked="vehicle.securityTech.auditLogging"
                          />
                          <Label :for="`tech-audit-${index}`" class="text-sm">审计日志</Label>
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-geofencing-${index}`"
                            v-model:checked="vehicle.securityTech.geofencing"
                          />
                          <Label :for="`tech-geofencing-${index}`" class="text-sm">地理围栏</Label>
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-backup-${index}`"
                            v-model:checked="vehicle.securityTech.dataBackup"
                          />
                          <Label :for="`tech-backup-${index}`" class="text-sm">数据备份</Label>
                        </div>
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`tech-other-${index}`"
                            v-model:checked="vehicle.securityTech.other"
                          />
                          <Label :for="`tech-other-${index}`" class="text-sm">其他</Label>
                        </div>
                      </div>
                      <p
                        v-if="errors[`vehicle_${index}_securityTech`]"
                        class="text-sm text-red-500"
                      >
                        {{ errors[`vehicle_${index}_securityTech`] }}
                      </p>
                    </div>

                    <!-- 技术说明 -->
                    <div class="space-y-2">
                      <Label :for="`tech-description-${index}`" class="text-base font-semibold">
                        技术说明
                      </Label>
                      <Textarea
                        :id="`tech-description-${index}`"
                        v-model="vehicle.techDescription"
                        placeholder="请详细说明使用的插件/算法、自研/第三方机构、认证情况等"
                        rows="3"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>

          <!-- 数据采集区域与路线 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <MapPin class="w-5 h-5" />
                数据采集区域与路线
              </CardTitle>
              <CardDescription>数据采集的地理范围和路线信息</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <Label class="text-base font-semibold">
                  采集范围 <span class="text-red-500">*</span>
                </Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="collection-full"
                      v-model:checked="formData.collectionArea.fullArea"
                      @update:checked="handleCollectionAreaChange"
                    />
                    <Label for="collection-full" class="text-sm">全域采集</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="collection-partial"
                      v-model:checked="formData.collectionArea.partialArea"
                      @update:checked="handleCollectionAreaChange"
                    />
                    <Label for="collection-partial" class="text-sm">部分区域采集</Label>
                  </div>
                </div>
                <p v-if="errors.collection_area" class="text-sm text-red-500">
                  {{ errors.collection_area }}
                </p>
              </div>

              <!-- 部分区域说明文件上传 -->
              <div v-if="formData.collectionArea.partialArea" class="space-y-2">
                <Label class="text-base font-semibold">
                  部分区域说明文件 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.collectionArea.uploadedFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('area-description')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="areaDescriptionFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('area-description', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.collectionArea.uploadedFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.collectionArea.uploadedFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('area-description')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.area_description_file" class="text-sm text-red-500">
                  {{ errors.area_description_file }}
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 数据处理活动方式 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <Users class="w-5 h-5" />
                数据处理活动方式
              </CardTitle>
              <CardDescription>数据处理的合作方式及合作单位信息</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <Label class="text-base font-semibold">
                  处理方式 <span class="text-red-500">*</span>
                </Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="processing-independent"
                      v-model:checked="formData.processingMethod.independent"
                      @update:checked="handleProcessingMethodChange"
                    />
                    <Label for="processing-independent" class="text-sm">自行处理</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="processing-cooperation"
                      v-model:checked="formData.processingMethod.cooperation"
                      @update:checked="handleProcessingMethodChange"
                    />
                    <Label for="processing-cooperation" class="text-sm">合作处理</Label>
                  </div>
                </div>
                <p v-if="errors.processing_method" class="text-sm text-red-500">
                  {{ errors.processing_method }}
                </p>
              </div>

              <!-- 合作单位信息 -->
              <div
                v-if="formData.processingMethod.cooperation"
                class="space-y-4 p-4 bg-muted rounded-lg"
              >
                <div class="flex items-center justify-between">
                  <h4 class="font-medium">合作单位信息</h4>
                  <Button
                    type="button"
                    @click="addPartner"
                    class="flex items-center gap-2"
                    size="sm"
                  >
                    <Plus class="w-4 h-4" />
                    添加合作单位
                  </Button>
                </div>

                <div v-if="partners.length === 0" class="text-center py-8 text-muted-foreground">
                  <Users class="w-8 h-8 mx-auto mb-2 text-muted-foreground/50" />
                  <p class="text-sm">暂无合作单位信息</p>
                </div>

                <div
                  v-for="(partner, index) in partners"
                  :key="partner.id"
                  class="space-y-3 p-3 border rounded-lg bg-background"
                >
                  <div class="flex items-center justify-between">
                    <h5 class="font-medium text-sm">合作单位 {{ index + 1 }}</h5>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removePartner(index)"
                      class="text-red-600 hover:text-red-700"
                    >
                      <Trash2 class="w-3 h-3" />
                    </Button>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div class="space-y-1">
                      <Label :for="`partner-name-${index}`" class="text-xs font-medium">
                        单位名称 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`partner-name-${index}`"
                        v-model="partner.name"
                        placeholder="请输入单位名称"
                        size="sm"
                        :class="{ 'border-red-500': errors[`partner_${index}_name`] }"
                      />
                      <p v-if="errors[`partner_${index}_name`]" class="text-xs text-red-500">
                        {{ errors[`partner_${index}_name`] }}
                      </p>
                    </div>
                    <div class="space-y-1">
                      <Label :for="`partner-code-${index}`" class="text-xs font-medium">
                        信用代码 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`partner-code-${index}`"
                        v-model="partner.creditCode"
                        placeholder="请输入统一社会信用代码"
                        maxlength="18"
                        size="sm"
                        :class="{ 'border-red-500': errors[`partner_${index}_code`] }"
                      />
                      <p v-if="errors[`partner_${index}_code`]" class="text-xs text-red-500">
                        {{ errors[`partner_${index}_code`] }}
                      </p>
                    </div>
                    <div class="space-y-1">
                      <Label :for="`partner-qualification-${index}`" class="text-xs font-medium">
                        资质情况 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`partner-qualification-${index}`"
                        v-model="partner.qualification"
                        placeholder="请输入资质情况"
                        size="sm"
                        :class="{ 'border-red-500': errors[`partner_${index}_qualification`] }"
                      />
                      <p
                        v-if="errors[`partner_${index}_qualification`]"
                        class="text-xs text-red-500"
                      >
                        {{ errors[`partner_${index}_qualification`] }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 数据处理目的 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <Target class="w-5 h-5" />
                数据处理目的
              </CardTitle>
              <CardDescription>数据处理的具体用途和目标</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <Label class="text-base font-semibold">
                  处理目的（多选） <span class="text-red-500">*</span>
                </Label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="purpose-desensitization"
                      v-model:checked="formData.processingPurpose.dataDesensitization"
                    />
                    <Label for="purpose-desensitization" class="text-sm">数据脱敏</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="purpose-scenario"
                      v-model:checked="formData.processingPurpose.scenarioLibrary"
                    />
                    <Label for="purpose-scenario" class="text-sm">场景库制作与处理</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="purpose-navigation"
                      v-model:checked="formData.processingPurpose.navigationMapping"
                    />
                    <Label for="purpose-navigation" class="text-sm">导航电子地图制作</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="purpose-internet-map"
                      v-model:checked="formData.processingPurpose.internetMapService"
                    />
                    <Label for="purpose-internet-map" class="text-sm">互联网地图服务</Label>
                  </div>
                </div>
                <p v-if="errors.processing_purpose" class="text-sm text-red-500">
                  {{ errors.processing_purpose }}
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 数据使用范围 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg flex items-center gap-2">
                <Share class="w-5 h-5" />
                数据使用范围
              </CardTitle>
              <CardDescription>数据的使用范围和对外提供情况</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <Label class="text-base font-semibold">
                  使用范围（多选） <span class="text-red-500">*</span>
                </Label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox id="usage-export" v-model:checked="formData.dataUsage.dataExport" />
                    <Label for="usage-export" class="text-sm">出境</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="usage-provide"
                      v-model:checked="formData.dataUsage.dataProvision"
                    />
                    <Label for="usage-provide" class="text-sm">提供</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="usage-public"
                      v-model:checked="formData.dataUsage.dataDisclosure"
                    />
                    <Label for="usage-public" class="text-sm">公开</Label>
                  </div>
                </div>
                <p v-if="errors.data_usage" class="text-sm text-red-500">
                  {{ errors.data_usage }}
                </p>
              </div>

              <!-- 提供对象信息 -->
              <div
                v-if="formData.dataUsage.dataProvision"
                class="space-y-4 p-4 bg-muted rounded-lg"
              >
                <div class="flex items-center justify-between">
                  <h4 class="font-medium">提供对象信息</h4>
                  <Button
                    type="button"
                    @click="addRecipient"
                    class="flex items-center gap-2"
                    size="sm"
                  >
                    <Plus class="w-4 h-4" />
                    添加提供对象
                  </Button>
                </div>

                <div v-if="recipients.length === 0" class="text-center py-8 text-muted-foreground">
                  <Share class="w-8 h-8 mx-auto mb-2 text-muted-foreground/50" />
                  <p class="text-sm">暂无提供对象信息</p>
                </div>

                <div
                  v-for="(recipient, index) in recipients"
                  :key="recipient.id"
                  class="space-y-3 p-3 border rounded-lg bg-background"
                >
                  <div class="flex items-center justify-between">
                    <h5 class="font-medium text-sm">提供对象 {{ index + 1 }}</h5>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeRecipient(index)"
                      class="text-red-600 hover:text-red-700"
                    >
                      <Trash2 class="w-3 h-3" />
                    </Button>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div class="space-y-1">
                      <Label :for="`recipient-name-${index}`" class="text-xs font-medium">
                        对象名称 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`recipient-name-${index}`"
                        v-model="recipient.name"
                        placeholder="请输入对象名称"
                        size="sm"
                        :class="{ 'border-red-500': errors[`recipient_${index}_name`] }"
                      />
                      <p v-if="errors[`recipient_${index}_name`]" class="text-xs text-red-500">
                        {{ errors[`recipient_${index}_name`] }}
                      </p>
                    </div>
                    <div class="space-y-1">
                      <Label :for="`recipient-code-${index}`" class="text-xs font-medium">
                        信用代码 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`recipient-code-${index}`"
                        v-model="recipient.creditCode"
                        placeholder="请输入统一社会信用代码"
                        maxlength="18"
                        size="sm"
                        :class="{ 'border-red-500': errors[`recipient_${index}_code`] }"
                      />
                      <p v-if="errors[`recipient_${index}_code`]" class="text-xs text-red-500">
                        {{ errors[`recipient_${index}_code`] }}
                      </p>
                    </div>
                    <div class="space-y-1">
                      <Label :for="`recipient-qualification-${index}`" class="text-xs font-medium">
                        资质情况 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`recipient-qualification-${index}`"
                        v-model="recipient.qualification"
                        placeholder="请输入资质情况"
                        size="sm"
                        :class="{ 'border-red-500': errors[`recipient_${index}_qualification`] }"
                      />
                      <p
                        v-if="errors[`recipient_${index}_qualification`]"
                        class="text-xs text-red-500"
                      >
                        {{ errors[`recipient_${index}_qualification`] }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" @click="handlePrevious">
              <ChevronLeft class="w-4 h-4 mr-2" />
              上一步
            </Button>
            <div class="flex gap-2">
              <Button type="button" variant="outline" @click="handleSave">
                <Save class="w-4 h-4 mr-2" />
                保存（暂存）
              </Button>
              <Button type="submit" @click="handleSubmit">
                <Send class="w-4 h-4 mr-2" />
                提交审核
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 保存提示弹窗 -->
    <Dialog v-model:open="saveDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>保存成功</DialogTitle>
          <DialogDescription> 您的数据处理活动信息已保存，可稍后继续填报。 </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end">
          <Button @click="saveDialogOpen = false">确定</Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 提交确认弹窗 -->
    <Dialog v-model:open="submitDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>确认提交</DialogTitle>
          <DialogDescription>
            确认提交企业注册备案信息？提交后将进入审核流程，审核期间无法修改。
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="submitDialogOpen = false">取消</Button>
          <Button @click="confirmSubmit">确认提交</Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 提交成功弹窗 -->
    <Dialog v-model:open="successDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>提交成功</DialogTitle>
          <DialogDescription>
            您的企业注册备案信息已成功提交，请等待政府部门审核。您可以在"注册信息管理"页面查看审核进度。
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end">
          <Button @click="handleBackToManagement">前往查看</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Car,
  ChevronLeft,
  Database,
  FileText,
  MapPin,
  Plus,
  Save,
  Send,
  Share,
  Target,
  Trash2,
  Upload,
  Users,
  X,
} from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const router = useRouter()

interface SecurityTech {
  encryption: boolean
  desensitization: boolean
  anonymization: boolean
  accessControl: boolean
  auditLogging: boolean
  geofencing: boolean
  dataBackup: boolean
  other: boolean
}

interface Vehicle {
  id: string
  brand: string
  model: string
  vin: string
  quantity: string
  systemName: string
  sensorConfig: string
  mapApplication: string
  securityTech: SecurityTech
  techDescription: string
}

interface Partner {
  id: string
  name: string
  creditCode: string
  qualification: string
}

interface Recipient {
  id: string
  name: string
  creditCode: string
  qualification: string
}

// 车辆列表
const vehicles = ref<Vehicle[]>([])

// 合作单位列表
const partners = ref<Partner[]>([])

// 提供对象列表
const recipients = ref<Recipient[]>([])

// 表单数据
const formData = reactive({
  collectionArea: {
    fullArea: false,
    partialArea: false,
    uploadedFile: null as File | null,
  },
  processingMethod: {
    independent: false,
    cooperation: false,
  },
  processingPurpose: {
    dataDesensitization: false,
    scenarioLibrary: false,
    navigationMapping: false,
    internetMapService: false,
  },
  dataUsage: {
    dataExport: false,
    dataProvision: false,
    dataDisclosure: false,
  },
})

// 表单验证错误
const errors = reactive<Record<string, string>>({})

// 其他状态
const saveDialogOpen = ref(false)
const submitDialogOpen = ref(false)
const successDialogOpen = ref(false)

// 文件输入引用
const areaDescriptionFileInput = ref<HTMLInputElement>()

// 创建新车辆对象
const createNewVehicle = (): Vehicle => ({
  id: `vehicle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  brand: '',
  model: '',
  vin: '',
  quantity: '',
  systemName: '',
  sensorConfig: '',
  mapApplication: '',
  securityTech: {
    encryption: false,
    desensitization: false,
    anonymization: false,
    accessControl: false,
    auditLogging: false,
    geofencing: false,
    dataBackup: false,
    other: false,
  },
  techDescription: '',
})

// 创建新合作单位对象
const createNewPartner = (): Partner => ({
  id: `partner_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name: '',
  creditCode: '',
  qualification: '',
})

// 创建新提供对象
const createNewRecipient = (): Recipient => ({
  id: `recipient_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  name: '',
  creditCode: '',
  qualification: '',
})

// 车辆管理
const addVehicle = () => {
  vehicles.value.push(createNewVehicle())
}

const removeVehicle = (index: number) => {
  vehicles.value.splice(index, 1)
}

const importVehicles = () => {
  console.log('导入车辆模板')
  // 这里可以实现模板导入功能
}

// 合作单位管理
const addPartner = () => {
  partners.value.push(createNewPartner())
}

const removePartner = (index: number) => {
  partners.value.splice(index, 1)
}

// 提供对象管理
const addRecipient = () => {
  recipients.value.push(createNewRecipient())
}

const removeRecipient = (index: number) => {
  recipients.value.splice(index, 1)
}

// 处理采集区域选择
const handleCollectionAreaChange = () => {
  // 确保只能选择一个
  if (formData.collectionArea.fullArea && formData.collectionArea.partialArea) {
    formData.collectionArea.fullArea = false
  }

  // 如果选择全域，清除部分区域的文件
  if (formData.collectionArea.fullArea) {
    formData.collectionArea.uploadedFile = null
    if (areaDescriptionFileInput.value) {
      areaDescriptionFileInput.value.value = ''
    }
  }
}

// 处理处理方式选择
const handleProcessingMethodChange = () => {
  // 如果取消合作处理，清空合作单位
  if (!formData.processingMethod.cooperation) {
    partners.value = []
  }
}

// 文件上传处理
const triggerFileUpload = (type: string) => {
  if (type === 'area-description') {
    areaDescriptionFileInput.value?.click()
  }
}

const handleFileUpload = (type: string, event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 验证文件类型和大小
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ]
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      errors.area_description_file = '文件格式不支持，请上传 PDF、DOC 或 DOCX 格式的文件'
      return
    }

    if (file.size > maxSize) {
      errors.area_description_file = '文件大小超过限制，请上传小于 10MB 的文件'
      return
    }

    if (type === 'area-description') {
      formData.collectionArea.uploadedFile = file
      delete errors.area_description_file
    }
  }
}

const removeFile = (type: string) => {
  if (type === 'area-description') {
    formData.collectionArea.uploadedFile = null
    if (areaDescriptionFileInput.value) {
      areaDescriptionFileInput.value.value = ''
    }
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表单验证（原型演示版本 - 校验已禁用）
const validateForm = () => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => {
    delete errors[key]
  })

  // 原型演示模式：直接返回true，跳过所有校验
  console.log('原型演示模式：数据处理活动信息表单校验已禁用，直接通过')
  return true

  /* 原始校验逻辑（原型完成后可恢复）
  let isValid = true

  // 车辆信息验证
  if (vehicles.value.length === 0) {
    errors.vehicle_general = '请至少添加一项车辆信息'
    isValid = false
  }

  vehicles.value.forEach((vehicle, index) => {
    if (!vehicle.brand.trim()) {
      errors[`vehicle_${index}_brand`] = '请输入品牌'
      isValid = false
    }

    if (!vehicle.model.trim()) {
      errors[`vehicle_${index}_model`] = '请输入车型'
      isValid = false
    }

    if (!vehicle.vin.trim()) {
      errors[`vehicle_${index}_vin`] = '请输入VIN码'
      isValid = false
    } else if (vehicle.vin.length !== 17) {
      errors[`vehicle_${index}_vin`] = 'VIN码必须为17位'
      isValid = false
    }

    if (!vehicle.quantity || parseInt(vehicle.quantity) <= 0) {
      errors[`vehicle_${index}_quantity`] = '请输入正确的数量'
      isValid = false
    }

    if (!vehicle.systemName.trim()) {
      errors[`vehicle_${index}_systemName`] = '请输入系统名称及版本'
      isValid = false
    }

    if (!vehicle.sensorConfig.trim()) {
      errors[`vehicle_${index}_sensorConfig`] = '请输入传感器配置信息'
      isValid = false
    }

    if (!vehicle.mapApplication.trim()) {
      errors[`vehicle_${index}_mapApplication`] = '请描述地图数据应用情况'
      isValid = false
    }

    // 验证安全技术选择
    const hasSecurityTech = Object.values(vehicle.securityTech).some((value) => value === true)
    if (!hasSecurityTech) {
      errors[`vehicle_${index}_securityTech`] = '请至少选择一项地理信息安全处理技术'
      isValid = false
    }
  })

  // 数据采集区域验证
  if (!formData.collectionArea.fullArea && !formData.collectionArea.partialArea) {
    errors.collection_area = '请选择数据采集范围'
    isValid = false
  }

  if (formData.collectionArea.partialArea && !formData.collectionArea.uploadedFile) {
    errors.area_description_file = '请上传部分区域说明文件'
    isValid = false
  }

  // 数据处理方式验证
  if (!formData.processingMethod.independent && !formData.processingMethod.cooperation) {
    errors.processing_method = '请选择数据处理方式'
    isValid = false
  }

  // 合作单位验证
  if (formData.processingMethod.cooperation) {
    if (partners.value.length === 0) {
      errors.partners_general = '请至少添加一个合作单位'
      isValid = false
    }

    partners.value.forEach((partner, index) => {
      if (!partner.name.trim()) {
        errors[`partner_${index}_name`] = '请输入单位名称'
        isValid = false
      }

      if (!partner.creditCode.trim()) {
        errors[`partner_${index}_code`] = '请输入信用代码'
        isValid = false
      } else if (!/^[A-Z0-9]{18}$/i.test(partner.creditCode)) {
        errors[`partner_${index}_code`] = '信用代码格式不正确'
        isValid = false
      }

      if (!partner.qualification.trim()) {
        errors[`partner_${index}_qualification`] = '请输入资质情况'
        isValid = false
      }
    })
  }

  // 数据处理目的验证
  const hasPurpose = Object.values(formData.processingPurpose).some((value) => value === true)
  if (!hasPurpose) {
    errors.processing_purpose = '请至少选择一项数据处理目的'
    isValid = false
  }

  // 数据使用范围验证
  const hasUsage = Object.values(formData.dataUsage).some((value) => value === true)
  if (!hasUsage) {
    errors.data_usage = '请至少选择一项数据使用范围'
    isValid = false
  }

  // 提供对象验证
  if (formData.dataUsage.dataProvision) {
    if (recipients.value.length === 0) {
      errors.recipients_general = '请至少添加一个提供对象'
      isValid = false
    }

    recipients.value.forEach((recipient, index) => {
      if (!recipient.name.trim()) {
        errors[`recipient_${index}_name`] = '请输入对象名称'
        isValid = false
      }

      if (!recipient.creditCode.trim()) {
        errors[`recipient_${index}_code`] = '请输入信用代码'
        isValid = false
      } else if (!/^[A-Z0-9]{18}$/i.test(recipient.creditCode)) {
        errors[`recipient_${index}_code`] = '信用代码格式不正确'
        isValid = false
      }

      if (!recipient.qualification.trim()) {
        errors[`recipient_${index}_qualification`] = '请输入资质情况'
        isValid = false
      }
    })
  }

  return isValid
  */
}

// 上一步
const handlePrevious = () => {
  router.push('/corp/filing/form/security-tech')
}

// 保存（暂存）
const handleSave = () => {
  console.log('保存数据处理活动信息:', {
    vehicles: vehicles.value,
    formData,
    partners: partners.value,
    recipients: recipients.value,
  })

  // 保存到本地存储
  localStorage.setItem(
    'enterprise_data_activity',
    JSON.stringify({
      vehicles: vehicles.value.map((v) => ({
        ...v,
        // 不保存文件对象
      })),
      formData: {
        ...formData,
        collectionArea: {
          ...formData.collectionArea,
          uploadedFileName: formData.collectionArea.uploadedFile?.name,
        },
      },
      partners: partners.value,
      recipients: recipients.value,
    }),
  )

  saveDialogOpen.value = true
}

// 提交审核
const handleSubmit = () => {
  if (validateForm()) {
    submitDialogOpen.value = true
  }
}

// 确认提交
const confirmSubmit = () => {
  handleSave()
  submitDialogOpen.value = false
  successDialogOpen.value = true
}

// 跳转到管理页面
const handleBackToManagement = () => {
  successDialogOpen.value = false
  router.push('/corp/filing/records')
}

// 页面加载时尝试恢复数据
const loadSavedData = () => {
  const saved = localStorage.getItem('enterprise_data_activity')
  if (saved) {
    try {
      const data = JSON.parse(saved)

      if (data.vehicles) {
        vehicles.value = data.vehicles.map((v: any) => ({
          ...createNewVehicle(),
          ...v,
        }))
      }

      if (data.formData) {
        Object.assign(formData, {
          ...data.formData,
          collectionArea: {
            ...data.formData.collectionArea,
            uploadedFile: null, // 文件不能恢复
          },
        })
      }

      if (data.partners) {
        partners.value = data.partners
      }

      if (data.recipients) {
        recipients.value = data.recipients
      }
    } catch (e) {
      console.warn('Failed to load saved data:', e)
    }
  }
}

// 组件挂载时加载保存的数据并初始化
onMounted(() => {
  loadSavedData()

  // 如果没有车辆数据，默认添加一个
  if (vehicles.value.length === 0) {
    addVehicle()
  }
})
</script>

<style scoped>
/* 自定义样式 */
.border-red-500 {
  border-color: rgb(239 68 68);
}
</style>
