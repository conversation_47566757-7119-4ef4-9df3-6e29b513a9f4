{"envId": "cloud1-0gc8cbzg3efd6a99", "framework": {"name": "vite-spa", "plugins": {"frontend": {"use": "@cloudbase/framework-plugin-website", "inputs": {"outputPath": "gsm-ui-demo/dist", "cloudPath": "/", "ignore": [".git", ".github", "node_modules", "cloudbaserc.json"], "envVariables": {}, "headers": [{"source": "**/*.@(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|otf)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}]}}}}, "functions": [], "region": "ap-shanghai"}