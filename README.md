# 时空数据安全监测平台 (GSM-UIDemo)

> 🚀 **地理信息安全监测平台前端原型**

## 项目概述

时空数据安全监测平台是一个面向智能网联汽车时空数据安全监管的基础设施平台前端原型，旨在构建"**多级**"分布式监管架构。本项目为**静态演示原型（Demo Prototype）**，重点展示完整的业务功能界面和用户交互体验。

### 核心使命

- 🔒 **数据安全监管**：对智能网联汽车时空数据处理活动进行全生命周期监测
- ⚠️ **风险预警防控**：实时识别违规采集、超范围传输、越权访问等安全风险
- 📋 **合规服务支撑**：为企业提供政策解读、风险评估、合规指导等服务
- 🌟 **产业生态赋能**：推动智能网联汽车产业健康发展

### 项目定位

- **项目类型**：静态演示原型（无需后端接口）
- **开发目的**：展示界面内容和模块的业务跳转交互
- **部署环境**：腾讯云 CloudBase 静态托管
- **访问方式**：Demo 环境自动放行认证

## 用户体系架构

### 政府端用户

- **超级管理员**：系统最高权限管理
- **安全监测员**：实时监控与预警管理
- **审核专员**：企业备案审批
- **监督检查员**：合规检查与监督
- **数据分析员**：数据统计与分析

### 企业端用户

- **管理员**：企业系统管理
- **操作员**：日常操作与数据上报
- **审计员**：合规审计与风险管理

## 功能模块

### 🏛️ 政府端功能模块 (/gov)

| 模块         | 功能                                                                                             | 说明                         |
| ------------ | ------------------------------------------------------------------------------------------------ | ---------------------------- |
| **综合概览** | 首页控制台                                                                                       | 统计数据、地图展示、风险预警 |
| **备案管理** | 注册信息管理<br>备案审批管理                                                                     | 企业备案审核与管理           |
| **实时监测** | 风险管理（车端/云端）<br>事件管理（车端/云端）<br>应急溯源（车端/云端）<br>操作信息（车端/云端） | 全方位监控与管理             |
| **系统管理** | 用户/角色/组织管理<br>区域/监测区域管理<br>规则库/日志/字典管理<br>系统配置/监控/安全            | 系统配置与管理               |

### 🏢 企业端功能模块 (/corp)

| 模块           | 功能                                                                         | 说明             |
| -------------- | ---------------------------------------------------------------------------- | ---------------- |
| **首页控制台** | 企业概览                                                                     | 企业数据统计展示 |
| **注册备案**   | 企业基本信息<br>测绘资质信息<br>数据安全防控措施<br>车辆信息<br>数据处理活动 | 完整的备案流程   |
| **风险事件**   | 风险统计<br>事件统计                                                         | 风险与事件分析   |
| **通知待办**   | 通知信息<br>任务管理                                                         | 消息与任务处理   |
| **企业管理**   | 用户管理<br>角色权限管理                                                     | 企业内部管理     |

### 🌐 公共模块 (/)

- 首页门户落地页
- 企业接入申请页
- 政策法规列表页
- 用户登录页（政府/企业）
- 账号认证激活页
- 找回密码页

## 项目结构

```text
GSM-UIDemo/
├── docs/                          # 项目文档
│   ├── 业务架构分析.md            # 业务架构详解
│   ├── 系统前端原型页面开发任务明细.md  # 页面开发任务
│   └── 通信协议-技术评审版.md     # 技术协议文档
├── gsm-ui-demo/                   # 前端 UI 项目
│   ├── src/
│   │   ├── components/           # 组件目录
│   │   │   ├── ui/              # shadcn-vue官方组件（严格官方）
│   │   │   ├── charts/          # 图表组件（ECharts/D3）
│   │   │   ├── layout/          # 布局组件
│   │   │   └── enterprise-info/ # 企业特定组件
│   │   ├── views/               # 页面视图
│   │   │   ├── government/      # 政府端页面（30+页面）
│   │   │   └── enterprise/      # 企业端页面（15+页面）
│   │   ├── modules/             # 功能模块
│   │   │   ├── public/          # 公共模块（登录、落地页）
│   │   │   └── external/        # 外部模块
│   │   ├── router/              # 路由配置
│   │   ├── stores/              # Pinia状态管理
│   │   └── services/            # API服务层
│   ├── public/                   # 静态资源
│   └── dist/                     # 构建输出
├── 地理信息安全监测平台原型/       # Axure原型文件
└── .github/workflows/             # CI/CD配置
```

## 技术栈

### 核心框架

- **Vue 3.5** - 采用 Composition API 和 `<script setup>` 模式
- **TypeScript 5.8** - 严格类型检查，提供类型安全
- **Vite 7.0** - 极速的开发服务器和构建工具
- **Vue Router 4** - 官方路由管理，支持嵌套路由
- **Pinia** - 官方状态管理方案

### UI 组件库

- **shadcn-vue** - 官方组件库（⚠️ 严格使用官方 CLI 添加）
- **Radix Vue** - 底层无样式组件，提供可访问性
- **Tailwind CSS 3.4** - 原子化 CSS 框架，HSL 主题系统
- **Lucide Vue Next** - 精美的开源图标库
- **Tailwind Animate** - 动画效果库

### 数据可视化

- **ECharts 5.6** - 强大的图表库，支持复杂可视化
- **Vue-ECharts 7.0** - ECharts 的 Vue 集成方案
- **D3.js 7.9** - 专业数据可视化，用于特殊图表
- **Leaflet 1.9** - 轻量级地图库，支持地理围栏展示

### 开发工具

- **ESLint 9** - 代码规范检查
- **Prettier 3.6** - 代码自动格式化
- **Vue DevTools 8** - Vue 调试工具
- **GitLeaks** - 密钥泄露扫描
- **Pre-commit** - Git 钩子管理

## 快速开始

### 环境要求

- Node.js >= 20.19.0 || >= 22.12.0
- npm >= 8.0.0

### 安装依赖

```bash
cd gsm-ui-demo
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 开发状态

### 📊 当前进度

- ✅ **已完成**：基础框架、路由系统、主题切换、侧边栏导航
- ✅ **已开发**：约 30 个核心页面（政府端 20+，企业端 10+）
- 🚧 **开发中**：剩余 20+页面逐步完善
- 📋 **总页面数**：约 53 个页面（政府端 30+，企业端 15+，公共 8）

### 🎯 近期目标

1. 完成所有政府端核心功能页面
2. 完善企业端备案流程页面
3. 优化地图和图表交互体验
4. 增加 loading 状态和错误处理

## 开发规范

### ⚠️ 核心原则（必须遵守）

1. **官方组件优先** - 只使用 `npx shadcn-vue@latest add` 添加组件
2. **禁止简化版本** - 不创建简化版、不模仿、不自定义组件
3. **系统性解决** - 直面问题根源，避免临时方案和技术债务
4. **质量优于速度** - 代码质量和可维护性始终优先

### UI/UX 规范

- 🎨 图表配色采用暗色系（暗蓝、暗红、暗绿）
- 💬 所有弹窗字段信息必须完整
- ⚠️ 删除等关键操作需二次确认
- 🖱️ 鼠标悬停显示详细提示信息

详细规范请参阅 [DEVELOPMENT_RULES.md](./DEVELOPMENT_RULES.md)

## 项目特色

### 技术亮点

- 🎨 **现代化 UI** - 基于 shadcn-vue 的精美组件库，统一设计语言
- 🔧 **类型安全** - 完整的 TypeScript 支持，杜绝运行时错误
- 📱 **响应式设计** - 适配多种设备，PC/平板/手机全覆盖
- 🚀 **极速开发** - Vite 7.0 提供毫秒级热更新
- 🌓 **主题切换** - 支持明暗主题，HSL 色彩系统
- 🗺️ **地图可视化** - 集成 Leaflet 展示地理围栏和车辆分布
- 📊 **数据可视化** - ECharts/D3.js 双引擎，满足各类图表需求
- 🔐 **权限控制** - 完善的路由守卫和角色权限管理
- 🤖 **AI 辅助** - Claude Code 智能开发支持

### 开发规范亮点

- ✅ **严格组件标准** - 只使用 shadcn-vue 官方组件，禁止自制
- ✅ **系统性解决方案** - 直面问题根源，避免技术债务
- ✅ **统一设计规范** - 政府端与企业端风格一致
- ✅ **完善的文档** - 详细的开发文档和业务说明

## 项目文档

### 📚 核心文档

- [业务架构分析](./docs/地理信息安全监测平台-业务架构分析.md) - 详细的业务逻辑说明
- [系统前端原型页面开发任务明细](./docs/地理信息安全监测平台-系统前端原型页面开发任务明细%20v2.md) - 完整的页面开发清单
- [开发规范](./DEVELOPMENT_RULES.md) - 必须遵守的开发标准
- [Claude 开发指南](./CLAUDE.md) - Claude Code 专用开发说明

### 🔧 技术文档

- [UI 项目说明](./README-UI.md) - UI 组件使用指南
- [通信协议](./docs/地理信息安全监测平台通信协议-技术评审版.md) - 接口协议规范
- [TypeScript 修复总结](./gsm-ui-demo/TypeScript-Fix-Summary.md) - 类型问题解决方案

## 部署信息

### 🚀 自动部署

- **平台**：腾讯云 CloudBase
- **方式**：GitHub Actions 自动部署
- **触发**：推送到 main 分支自动部署
- **输出**：静态文件托管在 `gsm-ui-demo/dist`

### 📦 手动部署

```bash
# 构建项目
cd gsm-ui-demo
npm run build

# 部署到CloudBase
npm run deploy
```

## 贡献指南

### 开发流程

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/功能名称`)
3. 按照开发规范编写代码
4. 提交更改 (`git commit -m 'feat: 添加某某功能'`)
5. 推送到分支 (`git push origin feature/功能名称`)
6. 开启 Pull Request

### 提交规范

- `feat:` 新功能
- `fix:` 修复问题
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建/工具相关

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 常见问题

### Q: 这是一个完整的生产系统吗？

A: 不是，这是一个**静态演示原型**，用于展示界面和交互流程，不包含后端接口。

### Q: 为什么没有表单验证？

A: 作为演示原型，重点在于展示业务流程和界面设计，按钮可以直接提交或跳转。

### Q: 如何添加新的组件？

A: 必须使用 `npx shadcn-vue@latest add [component]` 命令，禁止手动创建。

### Q: 支持哪些浏览器？

A: 现代浏览器（Chrome、Firefox、Safari、Edge 最新版本）

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 技术交流: 提交 Issue 或 Pull Request

---

© 2025 地理信息安全监测平台项目组

## 版本与发布

- 主分支 main 合并后会自动部署到 CloudBase（见 .github/workflows/deploy.yml）。
- 从 vX.Y.Z 或 vX.Y.Z-<qualifier> 创建 Git Tag 时，将触发自动发布流程，构建并上传 Release 资产（见 .github/workflows/release-on-tag.yml）。
- 示例：
  - v0.1.1-iframe-update
  - v0.2.0

## Release 资产说明（dist.zip 与 iframe-bundle.zip）

发布时会生成并上传两类压缩包，满足不同集成场景：

1) dist.zip（完整站点包）
- 内容：gsm-ui-demo/dist 全量构建产物
- 适用：直接部署到任意静态托管（Nginx/OSS/CloudBase 等）
- 使用方法：解压到站点根目录即可

2) iframe-bundle.zip（大屏独立集成包）
- 内容：仅包含大屏所需的两个目录
  - /iframe（government-screen.html、/iframe/vendor 等）
  - /axure（大屏依赖的样式与脚本、图片资源）
- 适用：在外部系统中以内嵌 <iframe> 的方式独立集成大屏
- 使用方法：确保将 /iframe 与 /axure 两个目录部署在同一站点根路径下（与父页面同源更佳，见下文“同源要求”）

## Iframe 集成指引

1) 部署与同源要求
- 将 iframe-bundle.zip 解压后的 /iframe 与 /axure 部署到父系统相同的站点（推荐同域同源）。
- 大屏入口地址固定为 /iframe/government-screen.html。确保该路径可被父页面访问。
- 当前大屏页面会校验 postMessage 消息来源，默认仅接受与自身相同 origin 的消息；因此父页面与大屏需同源，或按需调整校验逻辑后再使用。

2) 父页面嵌入示例

```html
<!-- 父系统页面 -->
<iframe id="gov-screen" src="/iframe/government-screen.html" style="width:100%;height:100vh;border:0" sandbox="allow-scripts allow-popups allow-forms allow-same-origin"></iframe>
<script>
  const frame = document.getElementById('gov-screen')
  // 监听来自 iframe 的密钥请求
  window.addEventListener('message', (event) => {
    if (!event.data || event.data.type !== 'requestAmapKey') return
    // 将高德地图 API Key 通过 postMessage 回传给 iframe
    // 注意：为满足同源校验，targetOrigin 应设置为与 iframe 页面相同的 origin
    const targetOrigin = window.location.origin
    frame.contentWindow?.postMessage({ type: 'amapConfig', key: 'YOUR_AMAP_API_KEY' }, targetOrigin)
  })
</script>
```

3) Iframe 端（已内置）
- 大屏页面 /iframe/government-screen.html 在加载后会主动向父窗口发送 { type: 'requestAmapKey' }，并在收到 { type: 'amapConfig', key } 后初始化 AMap。
- 为了安全，iframe 页面会校验 event.origin 与自身 origin 是否一致，不一致则忽略消息。

4) 运行期依赖
- 需要提供有效的高德地图浏览器端 API Key。
- 若父系统有 CSP（Content-Security-Policy），请允许加载 AMap 相关域名资源。

## 版本策略

- 语义化版本：MAJOR.MINOR.PATCH
  - 修复与小改动：+PATCH（例如 0.1.1）
  - 向后兼容的新特性：+MINOR（例如 0.2.0）
  - 重大不兼容变更：+MAJOR
- 可选后缀：-alpha/-beta/-rc/-dev 等，用于标记预发布或专题分支（例如 v0.2.0-beta.1）。
- 标签与 UI 子项目版本对齐：发布 Tag 后同步更新 gsm-ui-demo/package.json 的 version 字段，以便外部系统跟踪版本。
- Release 说明：概述本次变更范围、影响面、升级指引与回滚方案，保持可追溯。
