<template>
  <div class="min-h-screen bg-background">
    <div class="flex items-center justify-between p-6 border-b">
      <h1 class="text-2xl font-bold">{{ $route.meta.title }}</h1>
      <Button
        @click="saveTraceInfo"
        :disabled="!canSaveTrace"
        class="bg-emerald-600 hover:bg-emerald-700 text-white"
        >保存溯源信息</Button
      >
    </div>

    <div class="flex h-screen">
      <!-- 左侧：溯源记录历史 -->
      <div class="w-1/3 border-r bg-muted/10 p-6 overflow-y-auto">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              溯源记录历史
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TraceFilterForm
              :filter-fields="filterFields"
              :show-export="true"
              :initial-values="searchForm"
              @search="handleSearch"
              @reset="resetFilters"
              @export="exportCsv"
            />
            <div class="mt-4 space-y-2">
              <div
                v-for="(record, idx) in filteredRecords"
                :key="idx"
                class="p-3 rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors"
                :class="{
                  'bg-accent': selectedRecord?.id === record.id,
                  'border-primary': selectedRecord?.id === record.id,
                }"
                @click="selectRecord(record)"
              >
                <div class="flex justify-between items-start mb-2">
                  <span class="font-medium text-sm">{{ record.eventId }}</span>
                  <Badge
                    :variant="
                      record.eventLevel === '特别重大'
                        ? 'destructive'
                        : record.eventLevel === '重大'
                          ? 'default'
                          : 'secondary'
                    "
                  >
                    {{ formatLevel(record.eventLevel) }}
                  </Badge>
                </div>
                <div class="text-xs text-muted-foreground space-y-1">
                  <div>VIN: {{ record.vin }}</div>
                  <div>企业: {{ record.enterprise }}</div>
                  <div>时间: {{ formatDate(record.eventTimestamp) }}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：溯源看板 -->
      <div class="w-2/3 p-6 overflow-y-auto">
        <div
          v-if="!selectedRecord && !externalEventData"
          class="h-full flex items-center justify-center text-muted-foreground"
        >
          <div class="text-center">
            <div class="text-4xl mb-4">
              <Search class="w-16 h-16 text-muted-foreground" />
            </div>
            <p>请从左侧选择一条溯源记录以开始分析</p>
          </div>
        </div>
        <div v-else class="space-y-6">
          <!-- 1. 车端风险基本信息卡片 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <div class="flex items-center justify-between">
                <CardTitle class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-slate-600 rounded-full"></div>
                  车端风险基本信息
                </CardTitle>
                <div class="flex items-center gap-2">
                  <Badge
                    v-if="tracingStarted && !tracing"
                    variant="secondary"
                    class="bg-emerald-600 text-white"
                    >溯源成功</Badge
                  >
                  <Button
                    v-else
                    @click="startTracing"
                    :disabled="tracing"
                    class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                  >
                    {{ tracing ? '溯源中...' : '开始溯源' }}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold mb-3">风险事件详情</h4>
                  <div class="space-y-2 text-sm">
                    <div>
                      <span class="text-muted-foreground">事件ID:</span>
                      {{ (selectedRecord || externalEventData)?.eventId }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">风险类型:</span>
                      {{ getRiskTypeLabel((selectedRecord || externalEventData)?.eventType) }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">风险级别:</span>
                      <Badge
                        :variant="
                          (selectedRecord || externalEventData)?.eventLevel === '特别重大'
                            ? 'destructive'
                            : 'default'
                        "
                      >
                        {{ formatLevel((selectedRecord || externalEventData)?.eventLevel) }}
                      </Badge>
                    </div>
                    <div>
                      <span class="text-muted-foreground">发生时间:</span>
                      {{ formatDate((selectedRecord || externalEventData)?.eventTimestamp) }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">处置状态:</span>
                      <Badge variant="outline">{{
                        (selectedRecord || externalEventData)?.disposalStatus
                      }}</Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-3">涉事车辆信息</h4>
                  <div class="space-y-2 text-sm">
                    <div>
                      <span class="text-muted-foreground">VIN码:</span>
                      {{ (selectedRecord || externalEventData)?.vin }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">所属企业:</span>
                      {{ (selectedRecord || externalEventData)?.enterprise }}
                    </div>
                    <div v-if="(selectedRecord || externalEventData)?.eventLatitude">
                      <span class="text-muted-foreground">位置信息:</span>
                      {{
                        ((selectedRecord || externalEventData).eventLatitude / 1000000).toFixed(6)
                      }},
                      {{
                        ((selectedRecord || externalEventData).eventLongitude / 1000000).toFixed(6)
                      }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">风险描述:</span>
                      <p class="mt-1 p-2 bg-muted rounded text-xs">
                        {{ (selectedRecord || externalEventData)?.eventDescription }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div v-if="!(tracing || tracingStarted)" class="pl-8 text-sm text-muted-foreground">
            提示：点击“开始溯源”以执行溯源流程与分析
          </div>

          <!-- 2. 溯源图谱卡片 -->
          <Card v-show="tracing || tracingStarted" class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <div class="flex items-center justify-between">
                <CardTitle class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-blue-700 rounded-full"></div>
                  溯源图谱
                </CardTitle>
                <Badge variant="outline" class="text-blue-700 border-blue-200 bg-blue-50"
                  >数据传播溯源流程</Badge
                >
              </div>
            </CardHeader>
            <CardContent>
              <LineageVisualization
                :data="lineageData"
                :height="540"
                theme="light"
                layout="linear"
              />
            </CardContent>
          </Card>

          <!-- 3. 风险溯源分析 -->
          <Card v-show="tracingStarted" class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <div class="w-3 h-3 bg-teal-700 rounded-full"></div>
                风险溯源分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-3 gap-6">
                <div>
                  <h4 class="font-semibold mb-3">数据包信息</h4>
                  <div class="space-y-2 text-sm">
                    <div
                      v-for="log in (selectedRecord || externalEventData)?.traceData || []"
                      :key="log.logId"
                      class="p-2 border rounded"
                    >
                      <div class="font-medium">{{ log.logId }}</div>
                      <div class="text-muted-foreground">阶段: {{ log.processingStage }}</div>
                      <div class="text-muted-foreground">
                        时间: {{ formatDate(log.stageTimestamp) }}
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-3">风险点描述</h4>
                  <div class="space-y-2">
                    <div
                      v-for="risk in (selectedRecord || externalEventData)?.riskPoints || []"
                      :key="risk.stage"
                      class="p-3 bg-destructive/10 border border-destructive/20 rounded"
                    >
                      <div class="font-medium text-destructive">{{ risk.stage }}</div>
                      <div class="text-sm mt-1">{{ risk.description }}</div>
                      <div class="text-xs text-muted-foreground mt-1">
                        风险字段: {{ risk.field }}
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-3">关联责任主体</h4>
                  <div class="space-y-2">
                    <Button variant="outline" size="sm" class="w-full justify-start">
                      {{ (selectedRecord || externalEventData)?.enterprise }}
                    </Button>
                    <div class="text-xs text-muted-foreground p-2">
                      <div>企业ID: {{ (selectedRecord || externalEventData)?.enterpriseId }}</div>
                      <div>责任类型: 数据采集与处理</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 4. 溯源报告 -->
          <Card v-show="tracingStarted" class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <div class="w-3 h-3 bg-emerald-700 rounded-full"></div>
                溯源报告
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="bg-muted/50 rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                  <h4 class="font-semibold">车端风险溯源报告</h4>
                  <div class="space-x-2">
                    <Button variant="outline" size="sm">预览</Button>
                    <Button size="sm" class="bg-emerald-700 hover:bg-emerald-800">导出PDF</Button>
                  </div>
                </div>
                <div class="text-sm text-muted-foreground space-y-2">
                  <div>
                    • 风险事件: {{ (selectedRecord || externalEventData)?.eventDescription }}
                  </div>
                  <div>
                    • 涉及数据类型:
                    {{ (selectedRecord || externalEventData)?.dataTypes?.join(', ') || '未知' }}
                  </div>
                  <div>
                    • 溯源深度:
                    {{ (selectedRecord || externalEventData)?.traceData?.length || 0 }} 个处理阶段
                  </div>
                  <div>
                    • 风险等级: {{ formatLevel((selectedRecord || externalEventData)?.eventLevel) }}
                  </div>
                  <div>• 建议处置: 立即停止相关数据处理活动，进行安全评估</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <!-- 全局模态 Loading -->
    <div
      v-if="tracing"
      class="fixed inset-0 z-[60] bg-black/40 backdrop-blur-sm flex items-center justify-center"
    >
      <div
        class="bg-white dark:bg-slate-900 border rounded-lg px-8 py-6 shadow-xl flex items-center gap-3"
      >
        <span
          class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></span>
        <span class="text-sm text-slate-700 dark:text-slate-200"
          >正在溯源中，第 {{ Math.max(1, traceStep) }} 步...</span
        >
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search } from 'lucide-vue-next'
import TraceFilterForm, {
  type FilterField as TraceFilterField,
} from '@/components/ui/filter/TraceFilterForm.vue'
import LineageVisualization from '@/components/charts/LineageVisualization.vue'
import { getRiskTypeLabel } from '@/lib/riskTypeMapping'
import { buildVehicleDemoTrace } from '@/lib/demoTraceTemplates'

const searchForm = ref({ eventId: '', enterprise: '', vin: '', dateRange: null })
const selectedRecord = ref<any>(null)

const formatLevel = (level: string | undefined) => {
  if (!level) return ''
  if (level === '特别重大') return '高'
  if (level === '重大') return '中'
  return level
}
const tracingStarted = ref(false)
const tracing = ref(false)
const traceStep = ref(0)
let traceTimer: number | null = null

// 可保存条件：外部进入 + 已完成溯源 + 有来源数据
const canSaveTrace = computed(() => {
  return (
    isExternalEntry.value &&
    tracingStarted.value &&
    !!(externalEventData.value || selectedRecord.value)
  )
})

// 开始溯源（逐步显示 + 全局加载态 + 成功提示）
const startTracing = async () => {
  if (!selectedRecord.value && !externalEventData.value) return
  tracing.value = true
  tracingStarted.value = false
  traceStep.value = 0
  const steps = (selectedRecord.value || externalEventData.value).traceData?.length || 0
  const interval = 800
  traceTimer && clearInterval(traceTimer)
  traceTimer = window.setInterval(() => {
    traceStep.value++
    if (traceStep.value >= steps) {
      traceTimer && clearInterval(traceTimer)
      tracing.value = false
      tracingStarted.value = true
    }
  }, interval)
}

// 判断是否从外部跳转过来（通过路由参数或查询参数）
const route = useRoute()
const isExternalEntry = ref(false)
const externalEventData = ref<any>(null) // 存储外部传入的事件数据

const initFromRoute = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const hasExternal = urlParams.has('external') || route.query.external !== undefined
  const eventId = (route.query.eventId as string) || urlParams.get('eventId') || ''
  if (!hasExternal && !eventId) return
  isExternalEntry.value = true
  // 每次外部进入都初始化一次“新会话”
  tracing.value = false
  tracingStarted.value = false
  traceStep.value = 0
  selectedRecord.value = null

  // 使用模板生成一条新的外部来源数据（可重复）
  const found = records.value.find((r) => r.eventId === eventId)
  const base: any = found || {}
  // 演示阶段：统一采用车辆端溯源模板，确保外部跳转也有完整溯源步骤
  const demo = buildVehicleDemoTrace({
    vin: (route.query.vin as string) || base.vin,
    enterprise: (route.query.enterprise as string) || base.enterprise,
    eventLongitude: base.eventLongitude,
    eventLatitude: base.eventLatitude,
    dataTypes: base.dataTypes,
  })
  externalEventData.value = {
    eventId: eventId || base.eventId || `EVT-V-${Date.now()}`,
    enterprise: (route.query.enterprise as string) || base.enterprise || '示例企业',
    enterpriseId: base.enterpriseId || 'UNKNOWN',
    vin: (route.query.vin as string) || base.vin || 'LSVAP2A17PXXXXXXXX',
    eventType: (route.query.eventType as string) || base.eventType || '0x01',
    eventLevel: (route.query.eventLevel as string) || base.eventLevel || '重大',
    eventTimestamp: Date.now(),
    eventLongitude: base.eventLongitude || 0,
    eventLatitude: base.eventLatitude || 0,
    eventDescription: (route.query.desc as string) || base.eventDescription || '外部跳转的风险事件',
    disposalStatus: base.disposalStatus || '待处置',
    dataTypes: demo.dataTypes,
    traceData: demo.traceData,
    riskPoints: demo.riskPoints,
  }
}

// 筛选表单配置
const filterFields: TraceFilterField[] = [
  {
    key: 'eventId',
    label: '事件ID',
    type: 'input',
    placeholder: '事件ID',
  },
  {
    key: 'enterprise',
    label: '企业名称',
    type: 'input',
    placeholder: '企业名称',
  },
  {
    key: 'vin',
    label: '车辆VIN',
    type: 'input',
    placeholder: '车辆VIN码',
  },
  {
    key: 'dateRange',
    label: '时间范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

// 模拟车端风险数据（基于设计文档要求，专注于两种风险类型）
const records = ref([
  // 车端风险1：数据在车辆存储前，未按照国家认定的地理信息保密处理技术完成处理
  {
    id: 1,
    eventId: 'EVT-V-20250826-001',
    enterpriseId: '91110000000000001A', // 18位统一社会信用代码
    vin: 'LSVAP2A17PXXXXXXXX',
    eventType: '0x01', // 车端数据泄露/丢失
    eventLevel: '特别重大',
    eventTimestamp: 1756219860000,
    eventLongitude: 116334567, // 定点数方式（度*10^6）
    eventLatitude: 39912345, // 定点数方式（度*10^6）
    eventDescription: '车端风险1：数据在车辆存储前，未按照国家认定的地理信息保密处理技术完成处理',
    disposalStatus: '待处置',
    enterprise: '北京某智能汽车制造有限公司',
    dataTypes: ['点云数据', '影像数据', '轨迹数据'],
    traceData: [
      {
        logId: 'LOG-V-COL-20250826-101100-A17P',
        processingStage: '数据收集',
        stageTimestamp: 1756219860000,
        isRisk: false,
        vin: 'LSVAP2A17PXXXXXXXX',
        positionStatus: 0x01, // 定位状态
        longitude: 116334567, // 经度（度*10^6）
        latitude: 39912345, // 纬度（度*10^6）
        altitude: 5000, // 高度（米*100）
        dataType: 0x0007, // 数据类型位图（点云+影像+轨迹）
        businessForm: 0x01, // 导航电子地图更新服务
        securityTech: 0x0000, // 安全处理技术（全部未启用）
        securityTechType: 0x00,
        mapAuditNum: '0x00', // 地图审图号为空
        riskDescription: '收集阶段未应用国家认定的地理信息保密处理技术',
      },
      {
        logId: 'LOG-V-STO-20250826-101105-A17P',
        processingStage: '数据存储',
        stageTimestamp: 1756219865000,
        isRisk: true,
        vin: 'LSVAP2A17PXXXXXXXX',
        associatedMileage: 1250, // 关联里程（0.1km精度）
        coordinateProcessing: 0x00, // 坐标处理手段：未处理真实坐标
        accessControlStatus: 0x01, // 关闭访问控制
        encryptionStorageStatus: 0x00, // 未加密
        riskDescription: '存储前未进行国家认定的地理信息保密处理技术处理，直接明文存储',
      },
      {
        logId: 'LOG-V-TRN-20250826-101110-A17P',
        processingStage: '数据传输',
        stageTimestamp: 1756219870000,
        isRisk: true,
        vin: 'LSVAP2A17PXXXXXXXX',
        transmissionDestination: '91110000000000002B', // 接收方企业代码
        destinationIP: [
          0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 192, 168, 1, 100,
        ], // IPv4映射IPv6
        transmissionMileage: 1250,
        coordinateProcessedFlag: 0x02, // 传输真实坐标
        coordinateProcessingMethod: 0x00, // 未处理
        transmissionAreaType: 0x01, // 境内
        networkType: 0x01, // 公共网络
        securityProtocol: 0x01, // HTTP
        externalTransmission: 0x01, // 具备车外传输功能
        riskDescription: '传输未经保密处理的真实坐标数据',
      },
    ],
    riskPoints: [
      {
        stage: '数据收集',
        description: '未启用国家认定的地理信息保密处理技术',
        field: 'securityTech',
      },
      {
        stage: '数据存储',
        description: '存储前未进行保密处理技术处理',
        field: 'coordinateProcessing',
      },
      {
        stage: '数据传输',
        description: '传输真实坐标数据，未经脱敏处理',
        field: 'coordinateProcessedFlag',
      },
    ],
  },
  // 车端风险1：数据在车辆存储前，未按照国家认定的地理信息保密处理技术完成处理（另一实例）
  {
    id: 2,
    eventId: 'EVT-V-20250826-002',
    enterpriseId: '91310000000000003C',
    vin: 'LSVBP2B18PXXXXXXXX',
    eventType: '0x01',
    eventLevel: '重大',
    eventTimestamp: 1756223700000,
    eventLongitude: 121473700,
    eventLatitude: 31238900,
    eventDescription: '车端风险1：数据在车辆存储前，未按照国家认定的地理信息保密处理技术完成处理',
    disposalStatus: '处置中',
    enterprise: '上海某自动驾驶科技有限公司',
    dataTypes: ['影像数据', '点云数据'],
    traceData: [
      {
        logId: 'LOG-V-COL-20250826-143000-B18P',
        processingStage: '数据收集',
        stageTimestamp: 1756223700000,
        isRisk: false,
        vin: 'LSVBP2B18PXXXXXXXX',
        positionStatus: 0x01,
        longitude: 121473700,
        latitude: 31238900,
        altitude: 1200,
        dataType: 0x0003, // 影像+点云
        businessForm: 0x03, // 众源更新
        securityTech: 0x0001, // 仅启用地理围栏
        securityTechType: 0x00,
        mapAuditNum: 'GS(2024)1234号',
        riskDescription: '仅启用地理围栏技术，缺乏位置数据保密处理技术',
      },
      {
        logId: 'LOG-V-STO-20250826-143005-B18P',
        processingStage: '数据存储',
        stageTimestamp: 1756223705000,
        isRisk: true,
        vin: 'LSVBP2B18PXXXXXXXX',
        associatedMileage: 2500,
        coordinateProcessing: 0x00, // 未处理真实坐标
        accessControlStatus: 0x02, // 启用访问控制
        encryptionStorageStatus: 0x01, // SM2加密
        riskDescription: '虽加密存储但未进行坐标保密处理',
      },
    ],
    riskPoints: [
      {
        stage: '数据收集',
        description: '缺乏位置数据保密处理技术和属性脱敏技术',
        field: 'securityTech',
      },
      {
        stage: '数据存储',
        description: '存储前未进行坐标保密处理',
        field: 'coordinateProcessing',
      },
    ],
  },
  // 车端事件1：核心安全策略失效
  {
    id: 3,
    eventId: 'EVT-V-20250827-003',
    enterpriseId: '91440000000000004D',
    vin: 'LSVCP2C19PXXXXXXXX',
    eventType: '0x04', // 核心安全策略异常
    eventLevel: '特别重大',
    eventTimestamp: 1756306200000,
    eventLongitude: 114067890,
    eventLatitude: 22547123,
    eventDescription: '车端事件1：核心安全策略失效',
    disposalStatus: '待处置',
    enterprise: '深圳某智能网联汽车有限公司',
    dataTypes: ['点云数据', '影像数据', '轨迹数据'],
    traceData: [
      {
        logId: 'LOG-V-SEC-20250827-081500-C19P',
        processingStage: '安全策略验证',
        stageTimestamp: 1756306200000,
        isRisk: true,
        eventOccurTime: [25, 8, 27, 8, 15, 0], // BCD码：YY-MM-DD-hh-mm-ss
        abnormalModuleType: 0x01, // 保密处理
        abnormalDescription: '位置数据保密处理技术模块失效，无法对地理坐标进行保密处理',
        riskDescription: '核心安全策略失效：位置数据保密处理技术模块故障',
      },
      {
        logId: 'LOG-V-SEC-20250827-081501-C19P',
        processingStage: '安全策略验证',
        stageTimestamp: 1756306201000,
        isRisk: true,
        eventOccurTime: [25, 8, 27, 8, 15, 1],
        abnormalModuleType: 0x02, // 地理围栏
        abnormalDescription: '地理围栏规则加载失败，无法判断车辆是否在敏感区域',
        riskDescription: '核心安全策略失效：地理围栏模块故障',
      },
    ],
    riskPoints: [
      {
        stage: '安全策略验证',
        description: '位置数据保密处理技术模块失效',
        field: 'abnormalModuleType',
      },
      {
        stage: '安全策略验证',
        description: '地理围栏规则加载失败',
        field: 'abnormalModuleType',
      },
    ],
  },
  // 车端事件1：核心安全策略失效（另一实例）
  {
    id: 4,
    eventId: 'EVT-V-************',
    enterpriseId: '91510000000000005E',
    vin: 'LSVDP2D20PXXXXXXXX',
    eventType: '0x04',
    eventLevel: '重大',
    eventTimestamp: 1756392600000,
    eventLongitude: 104073890,
    eventLatitude: 30652123,
    eventDescription: '车端事件1：核心安全策略失效',
    disposalStatus: '已处置',
    enterprise: '成都某新能源汽车有限公司',
    dataTypes: ['影像数据', '轨迹数据'],
    traceData: [
      {
        logId: 'LOG-V-SEC-20250828-221500-D20P',
        processingStage: '安全策略验证',
        stageTimestamp: 1756392600000,
        isRisk: true,
        eventOccurTime: [25, 8, 28, 22, 15, 0],
        abnormalModuleType: 0x03, // 属性脱敏
        abnormalDescription: '属性脱敏模块执行失败，车辆识别信息未能有效脱敏',
        riskDescription: '核心安全策略失效：属性脱敏技术模块故障',
      },
      {
        logId: 'LOG-V-SEC-20250828-221505-D20P',
        processingStage: '安全策略验证',
        stageTimestamp: 1756392605000,
        isRisk: true,
        eventOccurTime: [25, 8, 28, 22, 15, 5],
        abnormalModuleType: 0x04, // 里程限制
        abnormalDescription: '里程限制技术失效，超出预设的数据收集里程限制',
        riskDescription: '核心安全策略失效：里程限制技术失效',
      },
    ],
    riskPoints: [
      {
        stage: '安全策略验证',
        description: '属性脱敏模块执行失败',
        field: 'abnormalModuleType',
      },
      {
        stage: '安全策略验证',
        description: '里程限制技术失效',
        field: 'abnormalModuleType',
      },
    ],
  },
])

const filteredRecords = computed(() => {
  return records.value.filter((record) => {
    return (
      (!searchForm.value.eventId || record.eventId.includes(searchForm.value.eventId)) &&
      (!searchForm.value.enterprise || record.enterprise.includes(searchForm.value.enterprise)) &&
      (!searchForm.value.vin || record.vin.includes(searchForm.value.vin))
    )
  })
})

// 数据血缘图谱数据处理
const lineageData = computed(() => {
  const currentRecord = selectedRecord.value || externalEventData.value
  if (!currentRecord?.traceData) return []
  const full = currentRecord.traceData.map((trace: any, index: number) => ({
    id: `node-${index}`,
    name: trace.processingStage,
    stage: trace.processingStage,
    timestamp: trace.stageTimestamp,
    isRisk: trace.isRisk || false,
    riskDescription: trace.riskDescription,
    logId: trace.logId,
    details: {
      数据类型: getDataTypeLabel(currentRecord.dataTypes),
      处理状态: trace.isRisk ? '异常' : '正常',
      VIN码: currentRecord.vin,
      企业: currentRecord.enterprise,
    },
  }))
  if (tracing.value) {
    return full.slice(0, Math.max(1, traceStep.value))
  }
  if (tracingStarted.value) return full
  return []
})

// 数据类型标签转换
const getDataTypeLabel = (dataTypes: string[]) => {
  return dataTypes?.join(', ') || '未知类型'
}

const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const fetchRecords = () => {
  console.log('刷新车端溯源记录')
}

const handleSearch = (searchFilters: Record<string, any>) => {
  Object.assign(searchForm.value, searchFilters)
  console.log('搜索条件:', searchForm.value)
}

const resetFilters = () => {
  searchForm.value = { eventId: '', enterprise: '', vin: '', dateRange: null }
}

const saveTraceInfo = () => {
  if (!canSaveTrace.value) return
  const source: any = externalEventData.value || selectedRecord.value
  if (!source) return
  // 标准化记录结构，确保左侧列表可用（允许重复事件ID，每次都生成新记录）
  const newRecord = {
    id: Date.now(),
    eventId: source.eventId,
    enterpriseId: source.enterpriseId || 'UNKNOWN',
    vin: source.vin || 'UNKNOWN',
    eventType: source.eventType || '0x01',
    eventLevel: source.eventLevel || '重大',
    eventTimestamp: source.eventTimestamp || Date.now(),
    eventLongitude: source.eventLongitude || 0,
    eventLatitude: source.eventLatitude || 0,
    eventDescription: source.eventDescription || '外部来源风险事件',
    disposalStatus: source.disposalStatus || '待处置',
    enterprise: source.enterprise || '未知企业',
    dataTypes: source.dataTypes || [],
    traceData: source.traceData || [],
    riskPoints: source.riskPoints || [],
  }
  records.value.unshift(newRecord)
  selectedRecord.value = newRecord
  // 保存后认为不是外部态
  isExternalEntry.value = false
  console.log('保存溯源信息完成:', selectedRecord.value?.eventId)
}

const exportCsv = () => {
  const headers = ['事件ID', '企业名称', '车辆VIN', '发生时间', '风险级别', '处置状态']
  const rows = filteredRecords.value.map((r) => [
    r.eventId,
    r.enterprise,
    r.vin,
    formatDate(r.eventTimestamp),
    r.eventLevel,
    r.disposalStatus,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `车端溯源记录_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const selectRecord = (record: any) => {
  selectedRecord.value = record
  tracingStarted.value = false // 重置溯源状态
  externalEventData.value = null // 清除外部数据
  console.log('选择记录:', record)
}

onMounted(() => {
  fetchRecords()
  initFromRoute()
})

// 监听路由查询变化，支持同页多次外部跳转触发新会话
watch(
  () => route.fullPath,
  () => {
    initFromRoute()
  },
)

onUnmounted(() => {
  if (traceTimer) clearInterval(traceTimer)
})
</script>
