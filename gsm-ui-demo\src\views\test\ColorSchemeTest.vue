<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold">统一配色方案测试页面</h1>
    
    <!-- 风险等级图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>风险等级分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="riskData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>风险等级柱状图 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart
            :data="riskData"
            :height="200"
            :auto-color="true"
          />
        </CardContent>
      </Card>
    </div>

    <!-- 企业类型图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>企业类型分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="enterpriseData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>企业类型柱状图 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart
            :data="enterpriseData"
            :height="200"
            :auto-color="true"
          />
        </CardContent>
      </Card>
    </div>

    <!-- 车辆类型图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>车辆类型分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="vehicleData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>车辆类型柱状图 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart
            :data="vehicleData"
            :height="200"
            :auto-color="true"
          />
        </CardContent>
      </Card>
    </div>

    <!-- 非风险类柱状图测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>普通数据柱状图 (应使用barChart配色)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart
            :data="normalData"
            :height="200"
            :auto-color="true"
          />
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>状态分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="statusData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>
    </div>

    <!-- 配色方案说明 -->
    <Card>
      <CardHeader>
        <CardTitle>配色方案说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div>
            <h3 class="font-semibold text-red-600">风险等级配色 (红橙黄色系)</h3>
            <p class="text-sm text-muted-foreground">高风险: #d81e06, 中风险: #ba850d, 低风险: #c2f507</p>
          </div>
          <div>
            <h3 class="font-semibold text-blue-600">车辆类型配色 (与注册信息页面一致)</h3>
            <p class="text-sm text-muted-foreground">智能网联车: #3b82f6, 自动驾驶车: #8b5cf6, 传统车联网: #06b6d4, 测试车辆: #84cc16</p>
          </div>
          <div>
            <h3 class="font-semibold text-purple-600">企业类型配色</h3>
            <p class="text-sm text-muted-foreground">整车生产企业: #3498db, 平台运营商: #9b59b6, 智驾方案提供商: #17a2b8, 地图服务商: #28a745</p>
          </div>
          <div>
            <h3 class="font-semibold text-green-600">状态配色</h3>
            <p class="text-sm text-muted-foreground">通过: #10b981, 注册中: #f59e0b, 未通过: #ef4444, 待审核: #8b5cf6</p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import PieChart from '@/components/ui/chart/PieChart.vue'
import BarChart from '@/components/ui/chart/BarChart.vue'

// 风险等级测试数据
const riskData = [
  { name: '高风险', value: 15 },
  { name: '中风险', value: 35 },
  { name: '低风险', value: 50 },
]

// 企业类型测试数据
const enterpriseData = [
  { name: '整车生产企业', value: 120 },
  { name: '平台运营商', value: 85 },
  { name: '智驾方案提供商', value: 65 },
  { name: '地图服务商', value: 45 },
]

// 车辆类型测试数据
const vehicleData = [
  { name: '智能网联车', value: 8956 },
  { name: '自动驾驶车', value: 3245 },
  { name: '传统车联网', value: 2134 },
  { name: '测试车辆', value: 1497 },
]

// 普通数据测试
const normalData = [
  { name: '数据A', value: 100 },
  { name: '数据B', value: 200 },
  { name: '数据C', value: 150 },
  { name: '数据D', value: 300 },
]

// 状态数据测试
const statusData = [
  { name: '通过', value: 298 },
  { name: '注册中', value: 23 },
  { name: '未通过', value: 15 },
  { name: '待审核', value: 23 },
]
</script>
