<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold">统一配色方案测试页面</h1>

    <!-- 风险等级图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>风险等级分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="riskData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>风险等级柱状图 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="riskData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>
    </div>

    <!-- 企业类型图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>企业类型分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="enterpriseData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>企业类型柱状图 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="enterpriseData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>
    </div>

    <!-- 车辆类型图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>车辆类型分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="vehicleData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>车辆类型柱状图 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="vehicleData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>
    </div>

    <!-- 处置状态图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>处置状态分布 (蓝色系)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="disposalStatusData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>处置状态柱状图 (蓝色系)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="disposalStatusData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>
    </div>

    <!-- 处理阶段图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>处理阶段分布 (蓝色系)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="processingStageData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>处理阶段柱状图 (蓝色系)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="processingStageData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>
    </div>

    <!-- 事件类型图表测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>事件类型分布 (蓝色系)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="eventTypeData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>事件类型柱状图 (蓝色系)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="eventTypeData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>
    </div>

    <!-- 非风险类柱状图测试 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>普通数据柱状图 (应使用barChart配色)</CardTitle>
        </CardHeader>
        <CardContent>
          <BarChart :data="normalData" :height="200" :auto-color="true" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>审批状态分布 (自动推断)</CardTitle>
        </CardHeader>
        <CardContent>
          <PieChart
            :data="approvalStatusData"
            :height="200"
            chart-type="doughnut"
            :show-legend="true"
            :auto-color="true"
          />
        </CardContent>
      </Card>
    </div>

    <!-- 配色方案说明 -->
    <Card>
      <CardHeader>
        <CardTitle>配色方案说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div>
            <h3 class="font-semibold text-red-600">风险等级配色 (红橙黄色系)</h3>
            <p class="text-sm text-muted-foreground">
              高风险: #d81e06, 中风险: #ba850d, 低风险: #c2f507
            </p>
          </div>
          <div>
            <h3 class="font-semibold text-blue-600">处置状态配色 (高对比度蓝色系)</h3>
            <p class="text-sm text-muted-foreground">
              未处理: #1e40af, 处理中: #3b82f6, 已处理: #10b981, 已完成: #f59e0b, 已归档: #8b5cf6
            </p>
          </div>
          <div>
            <h3 class="font-semibold text-blue-600">处理阶段配色 (高对比度蓝色系)</h3>
            <p class="text-sm text-muted-foreground">
              收集: #1e40af, 存储: #3b82f6, 传输: #10b981, 加工: #f59e0b, 提供: #8b5cf6, 公开:
              #ef4444, 销毁: #6b7280
            </p>
          </div>
          <div>
            <h3 class="font-semibold text-blue-600">事件类型配色 (高对比度蓝色系)</h3>
            <p class="text-sm text-muted-foreground">
              数据泄露: #1e40af, 违规采集: #3b82f6, 传输异常: #10b981, 存储风险: #f59e0b, 权限异常:
              #8b5cf6, API安全: #ef4444
            </p>
          </div>
          <div>
            <h3 class="font-semibold text-green-600">车辆类型配色 (高对比度优化)</h3>
            <p class="text-sm text-muted-foreground">
              智能网联车: #10b981, 自动驾驶车: #8b5cf6, 传统车联网: #f59e0b, 测试车辆: #3498db,
              其他车辆: #ef4444
            </p>
          </div>
          <div>
            <h3 class="font-semibold text-green-600">企业类型配色 (高对比度优化)</h3>
            <p class="text-sm text-muted-foreground">
              整车生产企业: #10b981, 平台运营商: #8b5cf6, 智驾方案提供商: #f59e0b, 地图服务商:
              #3498db, 其他: #ef4444
            </p>
          </div>
          <div>
            <h3 class="font-semibold text-green-600">审批状态配色</h3>
            <p class="text-sm text-muted-foreground">
              通过: #10b981, 注册中: #f59e0b, 未通过: #ef4444, 待审核: #8b5cf6
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import PieChart from '@/components/ui/chart/PieChart.vue'
import BarChart from '@/components/ui/chart/BarChart.vue'

// 风险等级测试数据
const riskData = [
  { name: '高风险', value: 15 },
  { name: '中风险', value: 35 },
  { name: '低风险', value: 50 },
]

// 企业类型测试数据
const enterpriseData = [
  { name: '整车生产企业', value: 120 },
  { name: '平台运营商', value: 85 },
  { name: '智驾方案提供商', value: 65 },
  { name: '地图服务商', value: 45 },
]

// 车辆类型测试数据
const vehicleData = [
  { name: '智能网联车', value: 8956 },
  { name: '自动驾驶车', value: 3245 },
  { name: '传统车联网', value: 2134 },
  { name: '测试车辆', value: 1497 },
]

// 处置状态测试数据
const disposalStatusData = [
  { name: '未处理', value: 45 },
  { name: '处理中', value: 32 },
  { name: '已处理', value: 78 },
  { name: '已完成', value: 156 },
]

// 处理阶段测试数据
const processingStageData = [
  { name: '收集', value: 120 },
  { name: '存储', value: 98 },
  { name: '传输', value: 87 },
  { name: '加工', value: 65 },
  { name: '提供', value: 43 },
  { name: '公开', value: 21 },
  { name: '销毁', value: 12 },
]

// 事件类型测试数据
const eventTypeData = [
  { name: '数据泄露', value: 15 },
  { name: '违规采集', value: 23 },
  { name: '传输异常', value: 18 },
  { name: '存储风险', value: 12 },
  { name: '权限异常', value: 8 },
  { name: 'API安全', value: 6 },
]

// 普通数据测试
const normalData = [
  { name: '数据A', value: 100 },
  { name: '数据B', value: 200 },
  { name: '数据C', value: 150 },
  { name: '数据D', value: 300 },
]

// 审批状态数据测试
const approvalStatusData = [
  { name: '通过', value: 298 },
  { name: '注册中', value: 23 },
  { name: '未通过', value: 15 },
  { name: '待审核', value: 23 },
]
</script>
