﻿表名: risk_records,,,,,,,
,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,risk_id,风险ID,VARCHAR,64,○,,唯一风险ID (业务主键)
3,log_id,关联日志ID,VARCHAR,64,○,,关联到触发风险的log_main.log_id
4,risk_category,风险类别,SMALLINT,,○,,"数据字典, e.g., 违规采集, 非法传输..."
5,risk_level,风险等级,SMALLINT,,○,,"数据字典: 1-高, 2-中, 3-低"
6,event_timestamp,风险时间戳,BIGINT,,○,,毫秒级时间戳
7,event_longitude,风险位置经度,INTEGER,,,,"WGS84, 乘以10^6"
8,event_latitude,风险位置纬度,INTEGER,,,,"WGS84, 乘以10^6"
9,risk_description,风险描述,TEXT,,○,,风险详情
10,status,处置状态,SMALLINT,,○,,"数据字典: 1-待处置, 2-处置中, 3-已关闭"
11,handler_id,处置人ID,VARCHAR,32,,,关联system_users.id
12,handle_time,处置时间,TIMESTAMP,,,,完成处置的时间
