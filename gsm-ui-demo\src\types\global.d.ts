/// <reference types="vite/client" />
/// <reference types="vue/macros-global" />

// 全局 DOM 类型声明
declare global {
  // 浏览器全局对象
  interface Window {
    matchMedia(query: string): MediaQueryList
    setInterval(handler: TimerHandler, timeout?: number): number
    clearInterval(id?: number): void
    open(url?: string, target?: string, features?: string): Window | null
    location: Location
    getSelection(): Selection | null
    getComputedStyle(element: Element): CSSStyleDeclaration
    print(): void
    scrollTo(options: { top: number; behavior?: string }): void
    L?: any // Leaflet 全局对象
  }

  // 媒体查询
  interface MediaQueryList {
    matches: boolean
    addEventListener(type: 'change', listener: (this: MediaQueryList, ev: MediaQueryListEvent) => any): void
    removeEventListener(type: 'change', listener: (this: MediaQueryList, ev: MediaQueryListEvent) => any): void
  }

  // DOM 元素类型扩展
  interface HTMLElement {
    requestFullscreen(): Promise<void>
    clientWidth: number
    clientHeight: number
    setAttribute(name: string, value: string): void
    href?: string // 用于 <a> 元素
    download?: string // 用于 <a> 元素
  }

  interface HTMLAnchorElement extends HTMLElement {
    href: string
    download: string
  }

  interface HTMLInputElement extends HTMLElement {
    files: FileList | null
    value: string
    type: string
  }

  interface HTMLDivElement extends HTMLElement {
    // 继承 HTMLElement 的所有属性
  }

  interface HTMLImageElement extends HTMLElement {
    src: string
    onload: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onerror: ((this: GlobalEventHandlers, ev: Event) => any) | null
  }

  interface SVGSVGElement extends SVGElement {
    // SVG 根元素
  }

  interface SVGGElement extends SVGElement {
    // SVG 组元素
  }

  interface SVGLineElement extends SVGElement {
    // SVG 线元素
  }

  interface Document {
    fullscreenElement: Element | null
    exitFullscreen(): Promise<void>
    createElement<K extends keyof HTMLElementTagNameMap>(tagName: K): HTMLElementTagNameMap[K]
    createElement(tagName: string): HTMLElement
    getElementById(elementId: string): HTMLElement | null
    querySelector<K extends keyof HTMLElementTagNameMap>(selectors: K): HTMLElementTagNameMap[K] | null
    querySelector(selectors: string): Element | null
    documentElement: HTMLElement
    body: HTMLBodyElement
    title: string
    cookie: string
    execCommand(commandId: string, showUI?: boolean, value?: string): boolean
    queryCommandState(commandId: string): boolean
  }

  // 事件类型扩展
  interface Event {
    target: EventTarget | null
    preventDefault(): void
  }

  interface MouseEvent extends Event {
    offsetX: number
    offsetY: number
  }

  interface DragEvent extends MouseEvent {
    dataTransfer: DataTransfer | null
  }

  interface ClipboardEvent extends Event {
    clipboardData: DataTransfer | null
  }

  interface KeyboardEvent extends UIEvent {
    ctrlKey: boolean
    metaKey: boolean
    key: string
    preventDefault(): void
  }

  // Navigator API
  interface Navigator {
    share?: (data?: ShareData) => Promise<void>
    clipboard?: {
      writeText(data: string): Promise<void>
    }
  }

  interface ShareData {
    title?: string
    text?: string
    url?: string
  }

  // 文件 API
  interface FileReader {
    onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null
    readAsDataURL(file: Blob): void
  }

  // 定时器类型
  type TimerHandler = (...args: any[]) => void

  // 全局函数
  function alert(message?: any): void
  function confirm(message?: string): boolean
  function prompt(message?: string, defaultText?: string): string | null
  function setInterval(handler: TimerHandler, timeout?: number, ...arguments: any[]): number
  function clearInterval(id?: number): void

  // 类型别名
  type Timeout = ReturnType<typeof setTimeout>
}

export {}
