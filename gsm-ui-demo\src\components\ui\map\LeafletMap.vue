<template>
  <div class="leaflet-map-container">
    <LMap
      ref="mapRef"
      :zoom="zoom"
      :center="center"
      :options="mapOptions"
      @ready="onMapReady"
      class="h-full w-full"
    >
      <!-- 基础瓦片层 -->
      <LTileLayer
        :url="tileLayerUrl"
        :attribution="tileLayerAttribution"
        :options="tileLayerOptions"
      />

      <!-- 天津行政区域高亮 -->
      <LGeoJson
        v-if="tianjinGeoJson"
        :geojson="tianjinGeoJson"
        :options="regionStyle"
        @ready="onRegionReady"
      >
        <LPopup>
          <div class="region-popup">
            <h4>天津市</h4>
            <p>监管区域</p>
            <p>节点总数: {{ totalNodes }}</p>
            <p>在线节点: {{ onlineNodes }}</p>
          </div>
        </LPopup>
      </LGeoJson>

      <!-- 地理围栏（更小范围的监测区域） -->
      <LPolygon
        v-if="fenceCoordinates"
        :lat-lngs="fenceCoordinates"
        :color="fenceStyle.color"
        :weight="fenceStyle.weight"
        :opacity="fenceStyle.opacity"
        :fill-color="fenceStyle.fillColor"
        :fill-opacity="fenceStyle.fillOpacity"
      >
        <LPopup>
          <div class="fence-popup">
            <h4>监测围栏区域</h4>
            <p>重点监管范围</p>
            <p>活跃车辆: {{ activeVehicles }}</p>
          </div>
        </LPopup>
      </LPolygon>

      <!-- 车端/云端节点标记 -->
      <LMarker
        v-for="node in mapNodes"
        :key="node.id"
        :lat-lng="[node.lat, node.lng]"
        @click="selectNode(node)"
      >
        <LIcon :icon-anchor="[16, 32]">
          <div
            class="custom-marker"
            :class="[`marker-${node.type}`, `risk-${node.riskLevel}`]"
            :title="getNodeTooltip(node)"
          >
            <div class="marker-icon">
              <component :is="node.type === 'vehicle' ? Car : Cloud" class="w-4 h-4" />
            </div>
            <div class="marker-pulse"></div>
          </div>
        </LIcon>
        <LPopup>
          <div class="node-popup">
            <h4>{{ node.name }}</h4>
            <p><strong>类型:</strong> {{ node.type === 'vehicle' ? '车端节点' : '云端节点' }}</p>
            <p>
              <strong>状态:</strong>
              <span :class="['status', node.status === '在线' ? 'online' : 'offline']">
                {{ node.status }}
              </span>
            </p>
            <p>
              <strong>风险等级:</strong>
              <span :class="['risk-level', node.riskLevel]">{{ node.riskLevel }}</span>
            </p>
            <p><strong>坐标:</strong> {{ node.lat.toFixed(4) }}, {{ node.lng.toFixed(4) }}</p>
            <p v-if="node.type === 'vehicle'"><strong>VIN:</strong> {{ node.vin || 'N/A' }}</p>
            <p v-if="node.type === 'cloud'"><strong>企业:</strong> {{ node.company || 'N/A' }}</p>
          </div>
        </LPopup>
      </LMarker>

      <!-- 地图控件 -->
      <LControl position="topright">
        <div class="map-controls">
          <button @click="zoomIn" class="control-btn" title="放大">
            <Plus class="w-4 h-4" />
          </button>
          <button @click="zoomOut" class="control-btn" title="缩小">
            <Minus class="w-4 h-4" />
          </button>
          <button @click="resetView" class="control-btn" title="重置视图">
            <RotateCcw class="w-4 h-4" />
          </button>
          <button @click="toggleFullscreen" class="control-btn" title="全屏">
            <Maximize class="w-4 h-4" />
          </button>
        </div>
      </LControl>

      <!-- 图例 -->
      <LControl position="bottomright">
        <div class="map-legend">
          <h4>图例</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-marker vehicle"></div>
              <span>车端节点</span>
            </div>
            <div class="legend-item">
              <div class="legend-marker cloud"></div>
              <span>云端节点</span>
            </div>
            <div class="legend-item">
              <div class="legend-color low"></div>
              <span>低风险</span>
            </div>
            <div class="legend-item">
              <div class="legend-color medium"></div>
              <span>中风险</span>
            </div>
            <div class="legend-item">
              <div class="legend-color high"></div>
              <span>高风险</span>
            </div>
          </div>
        </div>
      </LControl>
    </LMap>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  LMap,
  LTileLayer,
  LMarker,
  LPopup,
  LPolygon,
  LGeoJson,
  LControl,
  LIcon,
} from '@vue-leaflet/vue-leaflet'
import { Plus, Minus, RotateCcw, Maximize, Car, Cloud } from 'lucide-vue-next'
import 'leaflet/dist/leaflet.css'
import L from 'leaflet'
import 'leaflet.heat'
import { districtService } from '@/services/districtService'

// 修复 Leaflet 默认图标问题
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
})

// HeatLayer loose typing for demo (avoid TS module augmentation issues)
type HeatLatLng = [number, number, number]
type HeatLayerAny = any

declare global {
  interface Window {
    L?: any & {
      heatLayer: (latlngs: HeatLatLng[], options?: any) => HeatLayerAny
    }
  }
}

interface MapNode {
  id: string
  name: string
  type: 'vehicle' | 'cloud'
  lat: number
  lng: number
  status: '在线' | '离线'
  riskLevel: '低' | '中' | '高'
  vin?: string
  company?: string
}

interface Props {
  isDark?: boolean
  nodes?: MapNode[]
  centerPosition?: [number, number]
  initialZoom?: number
}

const props = withDefaults(defineProps<Props>(), {
  isDark: false,
  nodes: () => [],
  centerPosition: () => [39.084158, 117.200983], // 天津市中心
  initialZoom: 10,
})

const emit = defineEmits<{
  nodeClick: [node: MapNode]
  mapReady: [map: L.Map]
}>()

// 地图实例和状态
const mapRef = ref()
const zoom = ref(props.initialZoom)
const center = ref(props.centerPosition)
const heatmapLayer = ref<HeatLayerAny | null>(null)

// 地图配置
const mapOptions = {
  zoomControl: false,
  attributionControl: true,
}

// 瓦片层配置
const tileLayerUrl = computed(() => {
  return props.isDark
    ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
    : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
})

const tileLayerAttribution = computed(() => {
  return props.isDark
    ? '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/attribution">CARTO</a>'
    : '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
})

const tileLayerOptions = {
  maxZoom: 19,
}

// 动态加载天津市精确边界数据
const tianjinGeoJson = ref<any>(null)

// 加载天津行政边界数据
const loadTianjinBoundary = async () => {
  try {
    // 从环境变量获取API密钥
    const apiKey = import.meta.env.VITE_AMAP_API_KEY || ''

    console.log('开始加载天津市边界数据...')

    // 首先尝试使用DistrictService获取真实边界数据
    const boundaryData = await districtService.getTianjinBoundary(apiKey)

    if (boundaryData) {
      // 将单个Feature转换为FeatureCollection格式
      tianjinGeoJson.value = {
        type: 'FeatureCollection',
        features: [boundaryData],
      }
      console.log('天津边界数据加载成功（来自DistrictService）')
    } else {
      throw new Error('DistrictService返回空数据')
    }
  } catch (error) {
    console.error('加载天津边界数据时出错:', error)
    console.log('尝试加载本地静态边界数据')

    // 备用方案：尝试从静态文件加载
    try {
      const response = await fetch('/data/tianjin-boundary.json')
      if (response.ok) {
        const data = await response.json()
        tianjinGeoJson.value = data
        console.log('天津边界数据加载成功（来自静态文件）')
      } else {
        throw new Error('静态文件加载失败')
      }
    } catch (staticError) {
      console.warn('无法加载静态边界数据，使用默认简化数据:', staticError)
      // 最终备用：使用简化的默认边界数据
      tianjinGeoJson.value = {
        type: 'FeatureCollection' as const,
        features: [
          {
            type: 'Feature' as const,
            properties: {
              name: '天津市',
              adcode: '120000',
            },
            geometry: {
              type: 'Polygon' as const,
              coordinates: [
                [
                  [116.72, 38.82],
                  [117.82, 38.82],
                  [118.15, 39.15],
                  [118.42, 39.45],
                  [118.25, 40.25],
                  [117.35, 40.35],
                  [116.82, 40.15],
                  [116.45, 39.75],
                  [116.55, 39.25],
                  [116.72, 38.82],
                ],
              ] as number[][][],
            },
          },
        ],
      }
    }
  }
}

// 区域样式配置 - 增强色彩对比度
const regionStyle = computed(() => ({
  // 浅色主题：深色边界和填充；暗色主题：浅色边界和填充
  color: props.isDark ? '#93c5fd' : '#1e40af', // 暗色主题用浅蓝色，浅色主题用深蓝色
  weight: 3,
  opacity: 0.9, // 提高不透明度以增强对比度
  fillColor: props.isDark ? 'rgba(147, 197, 253, 0.2)' : 'rgba(30, 64, 175, 0.15)', // 调整填充颜色对比度
  fillOpacity: 0.4, // 提高填充不透明度
}))

// 地理围栏坐标（天津市区重点区域）
const fenceCoordinates = ref([
  [39.15, 117.1] as [number, number], // 左上
  [39.15, 117.35] as [number, number], // 右上
  [39.0, 117.35] as [number, number], // 右下
  [39.0, 117.1] as [number, number], // 左下
])

// 地理围栏样式 - 增强色彩对比度
const fenceStyle = computed(() => ({
  // 浅色主题：深色边界和填充；暗色主题：浅色边界和填充
  color: props.isDark ? '#fcd34d' : '#d97706', // 暗色主题用浅黄色，浅色主题用深橙色
  weight: 2,
  opacity: 1.0, // 最高不透明度确保可见性
  fillColor: props.isDark ? 'rgba(252, 211, 77, 0.25)' : 'rgba(217, 119, 6, 0.2)', // 调整填充颜色对比度
  fillOpacity: 0.5, // 提高填充不透明度
}))

// 地图节点数据
const mapNodes = computed(() => {
  if (props.nodes.length > 0) {
    return props.nodes
  }

  // 默认模拟数据
  return [
    {
      id: 'vehicle_1',
      name: '车端节点 001',
      type: 'vehicle' as const,
      lat: 39.084158,
      lng: 117.200983,
      status: '在线' as const,
      riskLevel: '低' as const,
      vin: 'LSGJ****1234',
    },
    {
      id: 'vehicle_2',
      name: '车端节点 002',
      type: 'vehicle' as const,
      lat: 39.100158,
      lng: 117.180983,
      status: '在线' as const,
      riskLevel: '高' as const,
      vin: 'WBAV****5678',
    },
    {
      id: 'cloud_1',
      name: '云端节点 001',
      type: 'cloud' as const,
      lat: 39.070158,
      lng: 117.220983,
      status: '在线' as const,
      riskLevel: '中' as const,
      company: '高德地图',
    },
    {
      id: 'cloud_2',
      name: '云端节点 002',
      type: 'cloud' as const,
      lat: 39.110158,
      lng: 117.240983,
      status: '离线' as const,
      riskLevel: '低' as const,
      company: '百度地图',
    },
  ]
})

// 统计信息
const totalNodes = computed(() => mapNodes.value.length)
const onlineNodes = computed(() => mapNodes.value.filter((node) => node.status === '在线').length)
const activeVehicles = computed(
  () => mapNodes.value.filter((node) => node.type === 'vehicle' && node.status === '在线').length,
)

// 生成热力图数据
const generateHeatmapData = () => {
  const heatData: [number, number, number][] = []

  // 基于节点数据生成热力图点
  mapNodes.value.forEach((node) => {
    let intensity = 0.5

    // 根据风险等级调整强度
    if (node.riskLevel === '高') intensity = 1.0
    else if (node.riskLevel === '中') intensity = 0.7
    else intensity = 0.3

    // 在线状态也影响强度
    if (node.status === '离线') intensity *= 0.5

    heatData.push([node.lat, node.lng, intensity])
  })

  // 添加一些模拟热点区域
  const hotspots = [
    { lat: 39.084158, lng: 117.200983, intensity: 0.8 }, // 天津市中心
    { lat: 39.117, lng: 117.19, intensity: 0.6 }, // 和平区
    { lat: 39.041, lng: 117.291, intensity: 0.7 }, // 河东区
    { lat: 39.156, lng: 117.138, intensity: 0.5 }, // 南开区
    { lat: 39.228, lng: 117.138, intensity: 0.4 }, // 西青区
  ]

  hotspots.forEach((spot) => {
    // 在热点周围生成多个数据点
    for (let i = 0; i < 8; i++) {
      const offsetLat = (Math.random() - 0.5) * 0.02
      const offsetLng = (Math.random() - 0.5) * 0.02
      const randomIntensity = spot.intensity * (0.5 + Math.random() * 0.5)
      heatData.push([spot.lat + offsetLat, spot.lng + offsetLng, randomIntensity])
    }
  })

  return heatData
}

// 初始化热力图
const initHeatmap = () => {
  if (mapRef.value?.leafletObject && typeof window !== 'undefined' && (window as any).L.heatLayer) {
    const map = mapRef.value.leafletObject

    // 如果已经存在热力图层，先移除
    if (heatmapLayer.value) {
      map.removeLayer(heatmapLayer.value)
    }

    const heatData = generateHeatmapData()

    // 热力图渐变配置 - 根据主题调整色彩对比度
    const heatmapGradient = props.isDark
      ? {
          // 暗色主题：使用更亮的颜色以提高对比度
          0.4: '#4ade80', // 亮绿色 - 低风险
          0.65: '#fbbf24', // 亮黄色 - 中风险
          1.0: '#f87171', // 亮红色 - 高风险
        }
      : {
          // 浅色主题：使用更深的颜色以提高对比度
          0.4: '#16a34a', // 深绿色 - 低风险
          0.65: '#d97706', // 深橙色 - 中风险
          1.0: '#dc2626', // 深红色 - 高风险
        }

    heatmapLayer.value = (window as any).L.heatLayer(heatData, {
      radius: 25,
      blur: 15,
      maxZoom: 17,
      max: 1.0,
      gradient: heatmapGradient,
    })

    heatmapLayer.value.addTo(map)
  }
}

// 方法
const onMapReady = () => {
  console.log('地图准备完成')
  emit('mapReady', mapRef.value?.leafletObject)

  // 初始化热力图
  setTimeout(() => {
    initHeatmap()
  }, 500)
}

const onRegionReady = () => {
  console.log('行政区域加载完成')
}

const getNodeTooltip = (node: MapNode) => {
  return `${node.name} - ${node.type === 'vehicle' ? '车端' : '云端'} - ${node.status} - ${node.riskLevel}风险`
}

const selectNode = (node: MapNode) => {
  console.log('选中节点:', node)
  emit('nodeClick', node)
}

const zoomIn = () => {
  if (mapRef.value?.leafletObject) {
    mapRef.value.leafletObject.zoomIn()
  }
}

const zoomOut = () => {
  if (mapRef.value?.leafletObject) {
    mapRef.value.leafletObject.zoomOut()
  }
}

const resetView = () => {
  zoom.value = props.initialZoom
  center.value = [...props.centerPosition]
  if (mapRef.value?.leafletObject) {
    mapRef.value.leafletObject.setView(props.centerPosition, props.initialZoom)
  }
}

const toggleFullscreen = () => {
  if (typeof document !== 'undefined') {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }
}

// 监听主题变化
watch(
  () => props.isDark,
  (newValue) => {
    console.log('主题切换:', newValue ? '暗色' : '亮色')
  },
  { immediate: true },
)

// 监听节点数据变化，更新热力图
watch(
  () => props.nodes,
  () => {
    setTimeout(() => {
      initHeatmap()
    }, 100)
  },
  { deep: true },
)

onMounted(() => {
  console.log('LeafletMap 组件已挂载')
  // 加载天津边界数据
  loadTianjinBoundary()
})
</script>

<style scoped>
.leaflet-map-container {
  @apply relative w-full h-full rounded-lg overflow-hidden border border-border;
}

/* 自定义标记样式 */
.custom-marker {
  @apply relative w-8 h-8 rounded-full flex items-center justify-center text-white font-bold shadow-lg cursor-pointer transition-all duration-300;
  z-index: 100;
}

.custom-marker.marker-vehicle {
  @apply bg-blue-500 border-2 border-blue-300;
}

.custom-marker.marker-cloud {
  @apply bg-purple-500 border-2 border-purple-300;
}

.custom-marker.risk-高 {
  @apply shadow-red-500/50;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.6);
  animation: pulse-high 1.5s infinite;
}

.custom-marker.risk-中 {
  @apply shadow-yellow-500/50;
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.6);
  animation: pulse-medium 2s infinite;
}

.custom-marker.risk-低 {
  @apply shadow-green-500/50;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.4);
}

.marker-icon {
  @apply text-lg;
}

.marker-pulse {
  @apply absolute inset-0 rounded-full opacity-30;
  animation: marker-pulse 2s infinite;
}

.marker-vehicle .marker-pulse {
  @apply bg-blue-500;
}

.marker-cloud .marker-pulse {
  @apply bg-purple-500;
}

@keyframes pulse-high {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes pulse-medium {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes marker-pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  70% {
    transform: scale(2);
    opacity: 0;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 地图控件样式 */
.map-controls {
  @apply flex flex-col gap-1 bg-background/90 backdrop-blur-sm rounded-lg p-1 border border-border shadow-lg;
}

.control-btn {
  @apply p-2 bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors duration-200 cursor-pointer;
}

/* 图例样式 */
.map-legend {
  @apply bg-background/90 backdrop-blur-sm rounded-lg p-3 border border-border shadow-lg min-w-[120px];
}

.map-legend h4 {
  @apply text-sm font-semibold mb-2 text-foreground;
}

.legend-items {
  @apply space-y-1;
}

.legend-item {
  @apply flex items-center gap-2 text-xs text-foreground;
}

.legend-marker {
  @apply w-3 h-3 rounded-full border-2;
}

.legend-marker.vehicle {
  @apply bg-blue-500 border-blue-300;
}

.legend-marker.cloud {
  @apply bg-purple-500 border-purple-300;
}

.legend-color {
  @apply w-3 h-3 rounded-sm;
}

.legend-color.low {
  @apply bg-green-500;
}

.legend-color.medium {
  @apply bg-yellow-500;
}

.legend-color.high {
  @apply bg-red-500;
}

/* 弹窗样式 */
.region-popup,
.fence-popup,
.node-popup {
  @apply text-foreground;
}

.region-popup h4,
.fence-popup h4,
.node-popup h4 {
  @apply font-semibold mb-1 text-primary;
}

.region-popup p,
.fence-popup p,
.node-popup p {
  @apply text-sm mb-1;
}

.node-popup .status.online {
  @apply text-green-600 font-semibold;
}

.node-popup .status.offline {
  @apply text-red-600 font-semibold;
}

.node-popup .risk-level {
  @apply font-semibold;
}

.node-popup .risk-level.低 {
  @apply text-green-600;
}

.node-popup .risk-level.中 {
  @apply text-yellow-600;
}

.node-popup .risk-level.高 {
  @apply text-red-600;
}

/* Leaflet 弹窗样式覆盖 */
:deep(.leaflet-popup-content-wrapper) {
  @apply bg-background border border-border rounded-lg shadow-xl;
}

:deep(.leaflet-popup-tip) {
  @apply bg-background border-border;
}

:deep(.leaflet-popup-close-button) {
  @apply text-foreground hover:text-primary font-bold text-lg;
}

/* 深色主题适配 */
[data-theme='dark'] .custom-marker.marker-vehicle {
  @apply border-blue-400;
}

[data-theme='dark'] .custom-marker.marker-cloud {
  @apply border-purple-400;
}
</style>
