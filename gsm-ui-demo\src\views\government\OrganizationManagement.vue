<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理注册备案企业的组织信息，支持企业查询、新增、修改和删除等操作
        </p>
      </div>
      <Button @click="handleCreateEnterprise" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增企业
      </Button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">企业总数</p>
              <p class="text-2xl font-bold">{{ stats.total }}</p>
            </div>
            <Building2 class="w-8 h-8 text-primary" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">正常运营</p>
              <p class="text-2xl font-bold text-green-600">{{ stats.active }}</p>
            </div>
            <CheckCircle class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">待审核</p>
              <p class="text-2xl font-bold text-orange-600">{{ stats.pending }}</p>
            </div>
            <Clock class="w-8 h-8 text-orange-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">已注销</p>
              <p class="text-2xl font-bold text-red-600">{{ stats.disabled }}</p>
            </div>
            <XCircle class="w-8 h-8 text-red-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 企业列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>企业列表</span>
          <Badge variant="outline"> 共 {{ filteredEnterprises.length }} 家企业 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <!-- 筛选条件 - 紧贴表格 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>企业名称</TableHead>
                <TableHead>企业类型</TableHead>
                <TableHead>统一社会信用代码</TableHead>
                <TableHead>联系人</TableHead>
                <TableHead>车辆数量</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>注册时间</TableHead>
                <TableHead class="w-[140px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedEnterprises.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无企业数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(enterprise, index) in pagedEnterprises"
                :key="enterprise.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component
                      :is="getEnterpriseIcon(enterprise.type)"
                      class="w-4 h-4 text-primary"
                    />
                    <div>
                      <div class="font-medium">{{ enterprise.name }}</div>
                      <div class="text-xs text-muted-foreground">ID: {{ enterprise.id }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getTypeVariant(enterprise.type)">{{ enterprise.type }}</Badge>
                </TableCell>
                <TableCell class="font-mono text-xs">{{ enterprise.creditCode }}</TableCell>
                <TableCell>
                  <div v-if="enterprise.contact">
                    <div class="font-medium text-sm">{{ enterprise.contact.name }}</div>
                    <div class="text-xs text-muted-foreground">{{ enterprise.contact.phone }}</div>
                  </div>
                  <span v-else class="text-muted-foreground">未填写</span>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" class="text-xs">
                    {{ enterprise.vehicleCount }} 辆
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(enterprise.status)">{{
                    enterprise.status
                  }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{
                  enterprise.registrationDate
                }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleView(enterprise.id)">
                        <Eye class="w-4 h-4 mr-2" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleEdit(enterprise.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑信息
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleViewVehicles(enterprise.id)">
                        <Car class="w-4 h-4 mr-2" />
                        车辆信息
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="enterprise.status === '待审核'"
                        @click="handleApprove(enterprise.id)"
                        class="text-green-600"
                      >
                        <CheckCircle class="w-4 h-4 mr-2" />
                        审核通过
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="enterprise.status === '待审核'"
                        @click="handleReject(enterprise.id)"
                        class="text-red-600"
                      >
                        <XCircle class="w-4 h-4 mr-2" />
                        审核驳回
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="enterprise.status === '正常'"
                        @click="handleSuspend(enterprise.id)"
                        class="text-orange-600"
                      >
                        <Ban class="w-4 h-4 mr-2" />
                        暂停服务
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="enterprise.status === '暂停'"
                        @click="handleRestore(enterprise.id)"
                        class="text-blue-600"
                      >
                        <RotateCcw class="w-4 h-4 mr-2" />
                        恢复服务
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        @click="handleDelete(enterprise.id)"
                        class="text-red-600"
                        :disabled="enterprise.vehicleCount > 0"
                      >
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除企业
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredEnterprises.length) }} 条， 共
            {{ filteredEnterprises.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 企业详情对话框 -->
    <Sheet :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <SheetContent
        side="right"
        class="w-[66vw] min-w-[640px] max-w-[1100px] max-h-[100vh] overflow-y-auto"
      >
        <SheetHeader>
          <SheetTitle>企业详情 - {{ currentEnterprise?.name }}</SheetTitle>
          <SheetDescription> 查看企业基本信息、资质信息和备案状态 </SheetDescription>
        </SheetHeader>

        <div v-if="currentEnterprise" class="space-y-6 py-4">
          <Tabs default-value="basic" class="w-full">
            <TabsList class="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="qualification">资质信息</TabsTrigger>
              <TabsTrigger value="vehicles">车辆信息</TabsTrigger>
              <TabsTrigger value="records">备案记录</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">企业名称</Label>
                  <p class="text-sm">{{ currentEnterprise.name }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">企业类型</Label>
                  <Badge :variant="getTypeVariant(currentEnterprise.type)">{{
                    currentEnterprise.type
                  }}</Badge>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground"
                    >统一社会信用代码</Label
                  >
                  <p class="text-sm font-mono">{{ currentEnterprise.creditCode }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">注册资本</Label>
                  <p class="text-sm">{{ currentEnterprise.registeredCapital }}</p>
                </div>
                <div class="col-span-2">
                  <Label class="text-base font-semibold text-muted-foreground">注册地址</Label>
                  <p class="text-sm">{{ currentEnterprise.address }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">联系人</Label>
                  <p class="text-sm">{{ currentEnterprise.contact?.name }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">联系电话</Label>
                  <p class="text-sm">{{ currentEnterprise.contact?.phone }}</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="qualification" class="space-y-4">
              <div class="text-sm text-muted-foreground">企业测绘资质信息将在此处显示</div>
            </TabsContent>

            <TabsContent value="vehicles" class="space-y-4">
              <div class="flex items-center justify-between">
                <Badge variant="outline"> 车辆总数: {{ currentEnterprise.vehicleCount }} 辆 </Badge>
                <Button variant="outline" size="sm">
                  <Car class="w-4 h-4 mr-2" />
                  查看车辆列表
                </Button>
              </div>
              <div class="text-sm text-muted-foreground">车辆详细信息将在此处显示</div>
            </TabsContent>

            <TabsContent value="records" class="space-y-4">
              <div class="text-sm text-muted-foreground">企业备案记录将在此处显示</div>
            </TabsContent>
          </Tabs>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showDetailDialog = false">关闭</Button>
          <Button @click="handleEdit(currentEnterprise?.id)">编辑企业</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>

    <!-- 审批对话框 -->
    <Sheet :open="showApprovalDialog" @update:open="showApprovalDialog = $event">
      <SheetContent side="right" class="w-[33vw] min-w-[420px] max-w-[640px]">
        <SheetHeader>
          <SheetTitle>审批确认</SheetTitle>
          <SheetDescription>
            您正在{{ approvalType === 'approve' ? '通过' : '驳回' }}企业 "{{
              currentEnterprise?.name
            }}" 的注册申请
          </SheetDescription>
        </SheetHeader>

        <div class="space-y-4 py-4">
          <div>
            <Label for="approval-comment">审批意见</Label>
            <Textarea
              id="approval-comment"
              v-model="approvalComment"
              :placeholder="
                approvalType === 'approve' ? '请输入审批通过的备注（可选）' : '请输入驳回原因'
              "
              rows="3"
            />
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showApprovalDialog = false">取消</Button>
          <Button
            @click="confirmApproval"
            :variant="approvalType === 'approve' ? 'default' : 'destructive'"
          >
            确认{{ approvalType === 'approve' ? '通过' : '驳回' }}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Ban,
  Building2,
  Car,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  Factory,
  MapPin,
  MoreHorizontal,
  Plus,
  RotateCcw,
  Trash2,
  Truck,
  Users,
  XCircle,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type EnterpriseType = '整车生产企业' | '平台运营商' | '智驾方案提供商' | '地图服务商' | '其他'
type EnterpriseStatus = '正常' | '待审核' | '暂停' | '已注销'

interface Contact {
  name: string
  phone: string
  email?: string
}

interface Enterprise {
  id: string
  name: string
  type: EnterpriseType
  creditCode: string
  address: string
  registeredCapital: string
  contact: Contact
  vehicleCount: number
  status: EnterpriseStatus
  registrationDate: string
}

// 统计数据
const stats = ref({
  total: 45,
  active: 38,
  pending: 5,
  disabled: 2,
})

// 筛选条件
const filters = ref({
  name: '',
  type: 'ALL' as 'ALL' | EnterpriseType,
  status: 'ALL' as 'ALL' | EnterpriseStatus,
  creditCode: '',
  timeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '企业名称',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'type',
    label: '企业类型',
    type: 'select',
    placeholder: '请选择企业类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '整车生产企业', value: '整车生产企业' },
      { label: '平台运营商', value: '平台运营商' },
      { label: '智驾方案提供商', value: '智驾方案提供商' },
      { label: '地图服务商', value: '地图服务商' },
      { label: '其他', value: '其他' },
    ],
  },
  {
    key: 'status',
    label: '企业状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '正常', value: '正常' },
      { label: '待审核', value: '待审核' },
      { label: '暂停', value: '暂停' },
      { label: '已注销', value: '已注销' },
    ],
  },
  {
    key: 'creditCode',
    label: '信用代码',
    type: 'input',
    placeholder: '请输入统一社会信用代码',
  },
  {
    key: 'timeRange',
    label: '注册时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const showDetailDialog = ref(false)
const showApprovalDialog = ref(false)
const currentEnterprise = ref<Enterprise | null>(null)
const approvalType = ref<'approve' | 'reject'>('approve')
const approvalComment = ref('')

// Mock 数据
const enterprises = ref<Enterprise[]>([
  {
    id: 'ENT-2025-001',
    name: '浙江极氪智能科技有限公司',
    type: '整车生产企业',
    creditCode: '91110000MA01234567',
    address: '北京市海淀区中关村软件园二期15号楼',
    registeredCapital: '10000万元',
    contact: {
      name: '张总经理',
      phone: '010-12345678',
      email: '<EMAIL>',
    },
    vehicleCount: 156,
    status: '正常',
    registrationDate: '2025-03-10 11:20:00',
  },
  {
    id: 'ENT-2025-002',
    name: '联行科技股份有限公司',
    type: '平台运营商',
    creditCode: '91110000MA98765432',
    address: '上海市浦东新区张江高科技园区',
    registeredCapital: '5000万元',
    contact: {
      name: '李运营总监',
      phone: '021-98765432',
      email: '<EMAIL>',
    },
    vehicleCount: 89,
    status: '正常',
    registrationDate: '2025-04-05 09:15:00',
  },
  {
    id: 'ENT-2025-003',
    name: '智行科技有限公司',
    type: '智驾方案提供商',
    creditCode: '91440300MA11223344',
    address: '深圳市南山区科技园南区',
    registeredCapital: '3000万元',
    contact: {
      name: '王技术总监',
      phone: '0755-11223344',
      email: '<EMAIL>',
    },
    vehicleCount: 45,
    status: '待审核',
    registrationDate: '2025-08-15 14:30:00',
  },
  {
    id: 'ENT-2025-004',
    name: '精准地图服务有限公司',
    type: '地图服务商',
    creditCode: '91310000MA55667788',
    address: '上海市徐汇区漕河泾开发区',
    registeredCapital: '2000万元',
    contact: {
      name: '刘产品经理',
      phone: '021-55667788',
      email: '<EMAIL>',
    },
    vehicleCount: 0,
    status: '待审核',
    registrationDate: '2025-08-18 16:45:00',
  },
  {
    id: 'ENT-2025-005',
    name: '创新出行科技有限公司',
    type: '其他',
    creditCode: '91440100MA33445566',
    address: '广州市天河区珠江新城',
    registeredCapital: '1500万元',
    contact: {
      name: '陈创始人',
      phone: '020-33445566',
      email: '<EMAIL>',
    },
    vehicleCount: 23,
    status: '暂停',
    registrationDate: '2025-06-01 10:00:00',
  },
])

// 计算属性
const filteredEnterprises = computed(() => {
  return enterprises.value.filter((enterprise) => {
    if (filters.value.name && !enterprise.name.includes(filters.value.name)) {
      return false
    }
    if (filters.value.type !== 'ALL' && enterprise.type !== filters.value.type) {
      return false
    }
    if (filters.value.status !== 'ALL' && enterprise.status !== filters.value.status) {
      return false
    }
    if (filters.value.creditCode && !enterprise.creditCode.includes(filters.value.creditCode)) {
      return false
    }
    if (filters.value.timeRange && filters.value.timeRange[0] && filters.value.timeRange[1]) {
      const startDate = new Date(
        filters.value.timeRange[0].getFullYear(),
        filters.value.timeRange[0].getMonth(),
        filters.value.timeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.value.timeRange[1].getFullYear(),
        filters.value.timeRange[1].getMonth(),
        filters.value.timeRange[1].getDate(),
        23,
        59,
        59,
      )
      const registrationDate = new Date(enterprise.registrationDate.replace(/-/g, '/'))
      if (registrationDate < startDate || registrationDate > endDate) return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredEnterprises.value.length / pageSize.value)),
)

const pagedEnterprises = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredEnterprises.value.slice(start, start + pageSize.value)
})

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    name: '',
    type: 'ALL',
    status: 'ALL',
    creditCode: '',
    timeRange: null,
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = [
    '序号',
    '企业名称',
    '企业类型',
    '信用代码',
    '联系人',
    '联系电话',
    '车辆数量',
    '状态',
    '注册时间',
  ]
  const rows = filteredEnterprises.value.map((enterprise, index) => [
    (index + 1).toString(),
    enterprise.name,
    enterprise.type,
    enterprise.creditCode,
    enterprise.contact.name,
    enterprise.contact.phone,
    enterprise.vehicleCount.toString(),
    enterprise.status,
    enterprise.registrationDate,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `企业组织架构_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleCreateEnterprise = () => {
  console.log('创建新企业')
}

const handleView = (id: string) => {
  const enterprise = enterprises.value.find((e) => e.id === id)
  if (enterprise) {
    currentEnterprise.value = enterprise
    showDetailDialog.value = true
  }
}

const handleEdit = (id: string) => {
  console.log('编辑企业:', id)
}

const handleViewVehicles = (id: string) => {
  console.log('查看车辆信息:', id)
}

const handleApprove = (id: string) => {
  const enterprise = enterprises.value.find((e) => e.id === id)
  if (enterprise) {
    currentEnterprise.value = enterprise
    approvalType.value = 'approve'
    approvalComment.value = ''
    showApprovalDialog.value = true
  }
}

const handleReject = (id: string) => {
  const enterprise = enterprises.value.find((e) => e.id === id)
  if (enterprise) {
    currentEnterprise.value = enterprise
    approvalType.value = 'reject'
    approvalComment.value = ''
    showApprovalDialog.value = true
  }
}

const handleSuspend = (id: string) => {
  const enterprise = enterprises.value.find((e) => e.id === id)
  if (enterprise) {
    enterprise.status = '暂停'
  }
}

const handleRestore = (id: string) => {
  const enterprise = enterprises.value.find((e) => e.id === id)
  if (enterprise) {
    enterprise.status = '正常'
  }
}

const handleDelete = (id: string) => {
  const index = enterprises.value.findIndex((e) => e.id === id)
  if (index > -1) {
    enterprises.value.splice(index, 1)
  }
}

const confirmApproval = () => {
  if (currentEnterprise.value) {
    if (approvalType.value === 'approve') {
      currentEnterprise.value.status = '正常'
    } else {
      currentEnterprise.value.status = '已注销'
    }
  }
  showApprovalDialog.value = false
  approvalComment.value = ''
}

// 样式函数
const getTypeVariant = (type: EnterpriseType) => {
  switch (type) {
    case '整车生产企业':
      return 'default'
    case '平台运营商':
      return 'secondary'
    case '智驾方案提供商':
      return 'outline'
    case '地图服务商':
      return 'outline'
    case '其他':
      return 'outline'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: EnterpriseStatus) => {
  switch (status) {
    case '正常':
      return 'default'
    case '待审核':
      return 'secondary'
    case '暂停':
      return 'destructive'
    case '已注销':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getEnterpriseIcon = (type: EnterpriseType) => {
  switch (type) {
    case '整车生产企业':
      return Factory
    case '平台运营商':
      return Building2
    case '智驾方案提供商':
      return Users
    case '地图服务商':
      return MapPin
    case '其他':
      return Building2
    default:
      return Building2
  }
}
</script>
