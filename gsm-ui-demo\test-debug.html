<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>高德地图调试测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .debug-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
      }
      .error {
        background: #f8d7da;
        border-left-color: #dc3545;
      }
      .success {
        background: #d4edda;
        border-left-color: #28a745;
      }
      .warning {
        background: #fff3cd;
        border-left-color: #ffc107;
      }
      .map-container {
        width: 100%;
        height: 400px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin: 20px 0;
      }
      .btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      .btn:hover {
        background: #0056b3;
      }
      .btn.secondary {
        background: #6c757d;
      }
      .btn.secondary:hover {
        background: #545b62;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>高德地图调试测试</h1>

      <div class="debug-info">
        <h3>环境信息</h3>
        <p><strong>当前URL:</strong> <span id="current-url"></span></p>
        <p><strong>Origin:</strong> <span id="current-origin"></span></p>
        <p><strong>端口:</strong> <span id="current-port"></span></p>
        <p><strong>协议:</strong> <span id="current-protocol"></span></p>
      </div>

      <div class="debug-info">
        <h3>API Key 状态</h3>
        <p><strong>VITE_AMAP_API_KEY:</strong> <span id="api-key-status">检查中...</span></p>
      </div>

      <div class="debug-info" id="cors-test">
        <h3>CORS 测试</h3>
        <button class="btn" onclick="testCors()">测试 CORS</button>
        <div id="cors-result"></div>
      </div>

      <div class="debug-info">
        <h3>地图测试</h3>
        <button class="btn" onclick="testMapLoad()">测试地图加载</button>
        <button class="btn secondary" onclick="testLocaLoad()">测试 Loca 加载</button>
        <div id="map-result"></div>
      </div>

      <div class="map-container" id="test-map"></div>

      <div class="debug-info">
        <h3>控制台日志</h3>
        <div
          id="console-logs"
          style="
            max-height: 200px;
            overflow-y: auto;
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
          "
        ></div>
      </div>
    </div>

    <script>
      // 页面加载时显示环境信息
      document.addEventListener('DOMContentLoaded', function () {
        document.getElementById('current-url').textContent = window.location.href
        document.getElementById('current-origin').textContent = window.location.origin
        document.getElementById('current-port').textContent = window.location.port || '80/443'
        document.getElementById('current-protocol').textContent = window.location.protocol

        // 检查 API Key
        checkApiKey()
      })

      // 检查 API Key
      function checkApiKey() {
        const apiKey = 'a139182760d79f73aa3b86826382da6f' // 应该从环境变量获取
        const statusEl = document.getElementById('api-key-status')

        if (apiKey && apiKey !== 'a139182760d79f73aa3b86826382da6f') {
          statusEl.textContent = `✅ 已配置: ${apiKey.substring(0, 10)}...`
          statusEl.parentElement.parentElement.className = 'debug-info success'
        } else {
          statusEl.textContent = '❌ 未配置或使用默认值'
          statusEl.parentElement.parentElement.className = 'debug-info error'
        }
      }

      // 测试 CORS
      function testCors() {
        const resultEl = document.getElementById('cors-result')
        resultEl.innerHTML = '测试中...'

        fetch('https://restapi.amap.com/v3/ip?key=a139182760d79f73aa3b86826382da6f', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
          .then((response) => {
            if (response.ok) {
              return response.json()
            } else {
              throw new Error(`HTTP ${response.status}`)
            }
          })
          .then((data) => {
            resultEl.innerHTML = `<span style="color: green;">✅ CORS 测试成功: ${JSON.stringify(data)}</span>`
            document.getElementById('cors-test').className = 'debug-info success'
          })
          .catch((error) => {
            resultEl.innerHTML = `<span style="color: red;">❌ CORS 测试失败: ${error.message}</span>`
            document.getElementById('cors-test').className = 'debug-info error'
          })
      }

      // 测试地图加载
      function testMapLoad() {
        const resultEl = document.getElementById('map-result')
        resultEl.innerHTML = '正在加载高德地图...'

        // 动态加载高德地图 JS API
        const script = document.createElement('script')
        script.src = `https://webapi.amap.com/maps?v=2.0&key=a139182760d79f73aa3b86826382da6f&plugin=AMap.Scale,AMap.ToolBar,AMap.ControlBar,AMap.MapType`
        script.onload = function () {
          resultEl.innerHTML = '<span style="color: green;">✅ 高德地图 JS API 加载成功</span>'

          // 创建地图实例
          try {
            const map = new AMap.Map('test-map', {
              center: [117.200983, 39.084158],
              zoom: 10,
              mapStyle: 'amap://styles/normal',
            })

            map.on('complete', function () {
              resultEl.innerHTML += '<br><span style="color: green;">✅ 地图实例创建成功</span>'
            })

            map.on('error', function (error) {
              resultEl.innerHTML += `<br><span style="color: red;">❌ 地图加载错误: ${error}</span>`
            })
          } catch (error) {
            resultEl.innerHTML += `<br><span style="color: red;">❌ 地图实例创建失败: ${error.message}</span>`
          }
        }
        script.onerror = function () {
          resultEl.innerHTML = '<span style="color: red;">❌ 高德地图 JS API 加载失败</span>'
        }

        document.head.appendChild(script)
      }

      // 测试 Loca 加载
      function testLocaLoad() {
        const resultEl = document.getElementById('map-result')
        resultEl.innerHTML = '正在加载 Loca 库...'

        const script = document.createElement('script')
        script.src = `https://webapi.amap.com/loca?v=2.0.0&key=a139182760d79f73aa3b86826382da6f`
        script.onload = function () {
          resultEl.innerHTML = '<span style="color: green;">✅ Loca 库加载成功</span>'

          // 检查 Loca 类
          setTimeout(() => {
            const classes = ['Container', 'HeatmapLayer', 'ScatterLayer', 'HexagonLayer']
            let availableClasses = []
            let missingClasses = []

            classes.forEach((cls) => {
              if (window.Loca && window.Loca[cls]) {
                availableClasses.push(cls)
              } else {
                missingClasses.push(cls)
              }
            })

            if (availableClasses.length > 0) {
              resultEl.innerHTML += `<br><span style="color: green;">✅ 可用类: ${availableClasses.join(', ')}</span>`
            }
            if (missingClasses.length > 0) {
              resultEl.innerHTML += `<br><span style="color: orange;">⚠️ 缺失类: ${missingClasses.join(', ')}</span>`
            }
          }, 1000)
        }
        script.onerror = function () {
          resultEl.innerHTML = '<span style="color: red;">❌ Loca 库加载失败</span>'
        }

        document.head.appendChild(script)
      }

      // 捕获控制台日志
      const originalLog = console.log
      const originalError = console.error
      const originalWarn = console.warn

      const logsEl = document.getElementById('console-logs')

      console.log = function (...args) {
        logsEl.innerHTML += '<div>LOG: ' + args.join(' ') + '</div>'
        logsEl.scrollTop = logsEl.scrollHeight
        originalLog.apply(console, args)
      }

      console.error = function (...args) {
        logsEl.innerHTML += '<div style="color: red;">ERROR: ' + args.join(' ') + '</div>'
        logsEl.scrollTop = logsEl.scrollHeight
        originalError.apply(console, args)
      }

      console.warn = function (...args) {
        logsEl.innerHTML += '<div style="color: orange;">WARN: ' + args.join(' ') + '</div>'
        logsEl.scrollTop = logsEl.scrollHeight
        originalWarn.apply(console, args)
      }
    </script>
  </body>
</html>
