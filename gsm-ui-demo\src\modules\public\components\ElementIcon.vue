<template>
  <svg 
    :width="iconSize" 
    :height="iconSize" 
    :style="iconStyle"
    viewBox="0 0 1024 1024"
    fill="currentColor"
  >
    <path :d="iconPath"></path>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string
  size?: string | number
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: '1em',
  color: 'currentColor'
})

const iconSize = computed(() => {
  return typeof props.size === 'number' ? `${props.size}px` : props.size
})

const iconStyle = computed(() => ({
  color: props.color,
  display: 'inline-block',
  verticalAlign: 'middle'
}))

const iconPath = computed(() => {
  // SVG路径映射 - 使用简化的图标路径
  const iconMap: Record<string, string> = {
    // 政策相关 - 文档图标
    'document': 'M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM668 345.9L621.5 312 572 347.4V124h96v221.9z m-180 0L441.5 312 392 347.4V124h96v221.9z m180 253.9L621.5 566 572 601.4V378h96v221.8z m-180 0L441.5 566 392 601.4V378h96v221.8z',
    
    // 政府相关 - 办公楼图标
    'office-building': 'M176 64h672c8.8 0 16 7.2 16 16v864c0 8.8-7.2 16-16 16H176c-8.8 0-16-7.2-16-16V80c0-8.8 7.2-16 16-16z m496 144H352v144h320V208z m0 208H352v144h320V416z m0 208H352v144h320V624z',
    
    // 企业相关 - 商店图标
    'shop': 'M882 272.1V144c0-17.7-14.3-32-32-32H174c-17.7 0-32 14.3-32 32v128.1c-16.7 1-30 14.9-30 31.9v131.7a177 177 0 0 0 14.4 70.4c4.3 10.2 9.6 19.8 15.6 28.9v345c0 17.6 14.3 32 32 32h274.4c17.7 0 32-14.4 32-32V640h-42.8V512h85.5v128H448v240h176V512h85.5v128h-42.8v240h274.4c17.7 0 32-14.4 32-32V535c6-9.1 11.3-18.7 15.6-28.9A177 177 0 0 0 1002 435.7V304c0-17-13.3-30.9-30-31.9z',
    
    // 警告相关 - 警告图标
    'warning': 'M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z',
    
    // 连接相关 - 链接图标
    'connection': 'M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z',
    
    // 合作相关 - 用户图标
    'user': 'M858.5 763.6a374 374 0 0 0-80.6-119.5 375.63 375.63 0 0 0-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 0 0-80.6 119.5A371.7 371.7 0 0 0 136 901.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 0 0 8-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z',
    
    // 汽车相关 - 卡车图标
    'truck': 'M880 298H732l-4-44c-2-16-16-28-32-28H328c-16 0-30 12-32 28l-4 44H148c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h76c4.4 0 8-3.6 8-8v-52c0-4.4-3.6-8-8-8h-44V374h168v52c0 4.4 3.6 8 8 8h52c4.4 0 8-3.6 8-8v-52h168v52c0 4.4 3.6 8 8 8h52c4.4 0 8-3.6 8-8v-52h168v310h-44c-4.4 0-8 3.6-8 8v52c0 4.4 3.6 8 8 8h76c17.7 0 32-14.3 32-32V330c0-17.7-14.3-32-32-32z',
    
    // 地图相关 - 地图位置图标
    'map-location': 'M854.6 289.1a362.49 362.49 0 0 0-79.9-115.7 370.83 370.83 0 0 0-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84.1 44.5-118.2 77.8A363.6 363.6 0 0 0 169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0 0 22.4 6.1c7.8 0 15.5-2.1 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.6 884 431c0-49.2-9.9-97-29.4-142zM512 615c-97.2 0-176-78.8-176-176s78.8-176 176-176 176 78.8 176 176-78.8 176-176 176z',
    
    // 机器人/AI相关 - CPU图标
    'cpu': 'M320 256h384v512H320z m-32-64h448c17.7 0 32 14.3 32 32v576c0 17.7-14.3 32-32 32H288c-17.7 0-32-14.3-32-32V224c0-17.7 14.3-32 32-32z m-64 128h32v64h-32v-64z m0 128h32v64h-32v-64z m0 128h32v64h-32v-64z m576-256h32v64h-32v-64z m0 128h32v64h-32v-64z m0 128h32v64h-32v-64z m-256-384v32h-64v-32h64z m-128 0v32h-64v-32h64z m-128 0v32h-64v-32h64z m384 576v32h-64v-32h64z m-128 0v32h-64v-32h64z m-128 0v32h-64v-32h64z'
  }
  
  return iconMap[props.name] || iconMap['document']
})
</script>

<style scoped>
svg {
  display: inline-block;
  vertical-align: middle;
}
</style>