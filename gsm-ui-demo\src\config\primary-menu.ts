/**
 * Primary menu config for government and enterprise apps
 */
export type Role = 'government' | 'enterprise'

export interface PrimaryMenuItem {
  label: string
  to: string
  exact?: boolean
}

export const PRIMARY_MENUS: Record<Role, PrimaryMenuItem[]> = {
  government: [
    { label: '综合概览', to: '/gov/dashboard', exact: true },
    { label: '备案审核', to: '/gov/record' },
    { label: '实时监测', to: '/gov/monitor' },
    { label: '系统管理', to: '/gov/system/information' },
  ],
  enterprise: [
    { label: '综合概览', to: '/corp/dashboard', exact: true },
    { label: '注册备案', to: '/corp/filing' },
    { label: '风险事件', to: '/corp/risk-event' },
    { label: '企业管理', to: '/corp/management' },
  ],
}

export function getPrimaryMenu(role: Role): PrimaryMenuItem[] {
  return PRIMARY_MENUS[role] ?? PRIMARY_MENUS.government
}
