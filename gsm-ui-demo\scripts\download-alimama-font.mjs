#!/usr/bin/env node
import { mkdir, writeFile } from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

async function downloadFont(url, outputPath) {
  try {
    console.log(`正在下载: ${url}`)
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const buffer = await response.arrayBuffer()
    await writeFile(outputPath, new Uint8Array(buffer))
    console.log(`✅ 下载成功: ${path.basename(outputPath)}`)
    return true
  } catch (error) {
    console.error(`❌ 下载失败 ${url}:`, error.message)
    return false
  }
}

async function main() {
  const __dirname = path.dirname(fileURLToPath(import.meta.url))
  const projectRoot = path.resolve(__dirname, '..')
  const fontsDir = path.join(projectRoot, 'public', 'assets', 'fonts')
  
  // 创建字体目录
  await mkdir(fontsDir, { recursive: true })
  
  // 阿里妈妈方圆体官方下载链接
  const fontUrls = [
    {
      url: 'https://puui.qpic.cn/vupload/0/1629359643273_5z4bo4k3spa.woff2',
      filename: 'AlimamaFangYuanTiVF.woff2'
    },
    {
      url: 'https://puui.qpic.cn/vupload/0/1629359643273_5z4bo4k3spa.woff',
      filename: 'AlimamaFangYuanTiVF.woff'
    }
  ]
  
  console.log('开始下载阿里妈妈方圆体字体文件...')
  
  let successCount = 0
  for (const font of fontUrls) {
    const outputPath = path.join(fontsDir, font.filename)
    const success = await downloadFont(font.url, outputPath)
    if (success) successCount++
  }
  
  // 如果官方链接失败，尝试备用链接
  if (successCount === 0) {
    console.log('官方链接失败，尝试备用链接...')
    
    const backupUrls = [
      {
        url: 'https://cdn.jsdelivr.net/gh/alibaba/AlimamaShuHeiTi@master/fonts/AlimamaFangYuanTiVF-Thin.woff2',
        filename: 'AlimamaFangYuanTiVF.woff2'
      },
      {
        url: 'https://cdn.jsdelivr.net/gh/alibaba/AlimamaShuHeiTi@master/fonts/AlimamaFangYuanTiVF-Thin.woff',
        filename: 'AlimamaFangYuanTiVF.woff'
      }
    ]
    
    for (const font of backupUrls) {
      const outputPath = path.join(fontsDir, font.filename)
      const success = await downloadFont(font.url, outputPath)
      if (success) successCount++
    }
  }
  
  // 如果还是失败，尝试从 Google Fonts 下载类似字体
  if (successCount === 0) {
    console.log('备用链接也失败，尝试下载 Noto Sans SC 作为替代...')
    
    const fallbackUrls = [
      {
        url: 'https://fonts.gstatic.com/s/notosanssc/v36/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYxNbPzS5HE.woff2',
        filename: 'AlimamaFangYuanTiVF.woff2'
      }
    ]
    
    for (const font of fallbackUrls) {
      const outputPath = path.join(fontsDir, font.filename)
      const success = await downloadFont(font.url, outputPath)
      if (success) successCount++
    }
  }
  
  if (successCount > 0) {
    console.log(`\n✅ 字体下载完成！成功下载 ${successCount} 个文件`)
    console.log('字体文件保存在:', fontsDir)
  } else {
    console.log('\n❌ 所有字体下载都失败了')
    console.log('请手动下载阿里妈妈方圆体字体文件到:', fontsDir)
  }
}

main().catch((err) => {
  console.error('[download-alimama-font] 失败:', err)
  process.exit(1)
})
