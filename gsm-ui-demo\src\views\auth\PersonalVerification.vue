<template>
  <div class="auth-layout">
    <div class="auth-container">
      <!-- 这里将由 kilocode 完成个人认证表单组件 -->
      <PersonalVerificationForm 
        @on-verification-success="handleVerificationSuccess"
        @on-enterprise-guide="handleEnterpriseGuide"
      />
      
      <!-- 企业认证引导 -->
      <div v-if="showEnterpriseGuide" class="enterprise-guide">
        <Card>
          <CardHeader>
            <CardTitle>检测到企业身份</CardTitle>
            <CardDescription>您的身份信息显示为企业用户，是否需要进行企业实名认证？</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="guide-actions">
              <Button @click="goToEnterpriseRegistration" class="mr-2">
                进行企业认证
              </Button>
              <Button variant="outline" @click="skipEnterpriseGuide">
                暂时跳过
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { VerificationResult, EnterpriseGuidance } from '@/types/auth'

// 个人验证表单组件 - 由 kilocode 开发
import PersonalVerificationForm from './PersonalVerificationForm.vue'

const router = useRouter()
const showEnterpriseGuide = ref(false)
const verificationResult = ref<VerificationResult | null>(null)

// 处理个人认证成功
const handleVerificationSuccess = (result: VerificationResult) => {
  console.log('认证成功:', result)
  verificationResult.value = result
  
  // 如果是企业用户，显示企业认证引导
  if (result.userType === 'enterprise') {
    showEnterpriseGuide.value = true
  } else {
    // 个人用户直接跳转到登录页面或首页
    router.push({ name: 'Login' })
  }
}

// 处理企业认证引导
const handleEnterpriseGuide = (guidance: EnterpriseGuidance) => {
  showEnterpriseGuide.value = guidance.showGuide
}

// 跳转到企业注册填报页面
const goToEnterpriseRegistration = () => {
  // TODO: 跳转到 P-028 企业基本信息填报页面
  router.push({ path: '/corp/filing/form/basic-info' })
}

// 跳过企业认证引导
const skipEnterpriseGuide = () => {
  showEnterpriseGuide.value = false
  router.push({ name: 'Login' })
}
</script>

<style scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  padding: 1rem;
}

.auth-container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.enterprise-guide {
  margin-top: 1.5rem;
  animation: slideUp 0.3s ease-out;
}

.guide-actions {
  display: flex;
  gap: 0.5rem;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 640px) {
  .guide-actions {
    flex-direction: column;
  }
  
  .guide-actions .mr-2 {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
</style>