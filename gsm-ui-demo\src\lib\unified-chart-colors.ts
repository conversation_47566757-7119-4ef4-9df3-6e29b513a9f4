/**
 * 统一图表配色方案 - 系统化配色体系
 * 基于数据类型和业务语义进行分类配色
 */

// 核心配色方案 - 系统化分类
export const UNIFIED_CHART_COLORS = {
  // 主要配色方案 - 基于车辆类型统计的配色
  primary: [
    '#3b82f6', // 智能网联车 - 蓝色
    '#8b5cf6', // 自动驾驶车 - 紫色
    '#06b6d4', // 传统车联网 - 青色
    '#84cc16', // 测试车辆 - 绿色
    '#f59e0b', // 其他车辆 - 橙色
    '#6b7280', // 其他 - 灰色
    '#10b981', // 补充色 - 绿色
    '#ef4444', // 补充色 - 红色
  ],

  // 风险等级配色 - 红橙黄色系（高中低风险专用）
  risk: [
    '#d81e06', // 高风险 - 红色
    '#ba850d', // 中风险 - 橙黄色
    '#c2f507', // 低风险 - 黄绿色
  ],

  // 处置状态配色 - 蓝色系（处置状态专用）
  disposalStatus: [
    '#1e40af', // 未处理 - 深蓝色
    '#3b82f6', // 处理中 - 蓝色
    '#60a5fa', // 已处理 - 浅蓝色
    '#93c5fd', // 已完成 - 更浅蓝色
    '#dbeafe', // 已归档 - 极浅蓝色
  ],

  // 处理阶段配色 - 蓝色系（数据处理阶段专用）
  processingStages: [
    '#1e3a8a', // 收集 - 深蓝色
    '#1e40af', // 存储 - 蓝色
    '#2563eb', // 传输 - 中蓝色
    '#3b82f6', // 加工 - 标准蓝色
    '#60a5fa', // 提供 - 浅蓝色
    '#93c5fd', // 公开 - 更浅蓝色
    '#dbeafe', // 销毁 - 极浅蓝色
  ],

  // 事件类型配色 - 蓝色系（车端/云端事件类型专用）
  eventTypes: [
    '#1e40af', // 数据泄露 - 深蓝色
    '#2563eb', // 违规采集 - 蓝色
    '#3b82f6', // 传输异常 - 标准蓝色
    '#60a5fa', // 存储风险 - 浅蓝色
    '#93c5fd', // 权限异常 - 更浅蓝色
    '#1d4ed8', // API安全 - 中深蓝色
    '#2dd4bf', // 算法安全 - 青蓝色
    '#0891b2', // 合规风险 - 深青色
  ],

  // 企业类型配色 - 来自注册信息页面
  enterprise: [
    '#3498db', // 整车生产企业 - 蓝色
    '#9b59b6', // 平台运营商 - 紫色
    '#17a2b8', // 智驾方案提供商 - 青色
    '#28a745', // 地图服务商 - 绿色
    '#5f9ea0', // 其他 - 青灰
  ],

  // 审批状态配色 - 语义化配色
  approvalStatus: [
    '#10b981', // 通过/已完成 - 绿色
    '#f59e0b', // 注册中/处理中 - 橙色
    '#ef4444', // 未通过/失败 - 红色
    '#8b5cf6', // 待审核/待处理 - 紫色
    '#95a5a6', // 未知/其他 - 灰色
  ],

  // 车辆类型配色 - 与primary保持一致
  vehicleTypes: [
    '#3b82f6', // 智能网联车 - 蓝色
    '#8b5cf6', // 自动驾驶车 - 紫色
    '#06b6d4', // 传统车联网 - 青色
    '#84cc16', // 测试车辆 - 绿色
    '#f59e0b', // 其他车辆 - 橙色
  ],

  // 非风险类柱状图专用配色 - 与车辆类型统计保持一致
  barChart: [
    '#3b82f6', // 蓝色
    '#8b5cf6', // 紫色
    '#06b6d4', // 青色
    '#84cc16', // 绿色
    '#f59e0b', // 橙色
    '#6b7280', // 灰色
    '#10b981', // 补充绿色
    '#ef4444', // 补充红色
  ],

  // 兼容性别名
  status: [
    '#10b981', // 通过/已完成 - 绿色
    '#f59e0b', // 注册中/处理中 - 橙色
    '#ef4444', // 未通过/失败 - 红色
    '#8b5cf6', // 待审核/待处理 - 紫色
    '#95a5a6', // 未知/其他 - 灰色
  ],
  stages: [
    '#1e3a8a', // 收集 - 深蓝色
    '#1e40af', // 存储 - 蓝色
    '#2563eb', // 传输 - 中蓝色
    '#3b82f6', // 加工 - 标准蓝色
    '#60a5fa', // 提供 - 浅蓝色
    '#93c5fd', // 公开 - 更浅蓝色
    '#dbeafe', // 销毁 - 极浅蓝色
  ],
} as const

// 配色方案映射 - 用于向后兼容
export const COLOR_SCHEME_MAPPING = {
  // 将旧的配色方案映射到新的统一配色
  oceanDepths: 'primary',
  sunsetWarmth: 'primary',
  forestGrove: 'primary',
  modernCorporate: 'primary',
  pastelDreams: 'primary',
  neonTech: 'primary',
  vintageEarth: 'primary',
  arcticAurora: 'primary',
  floralPalette: 'primary',
  summerAfternoon: 'primary',
  retroFuturistic: 'primary',
  resilience: 'primary',
} as const

// 获取统一配色方案的函数
export function getUnifiedColorScheme(
  scheme: keyof typeof UNIFIED_CHART_COLORS | keyof typeof COLOR_SCHEME_MAPPING,
): string[] {
  // 如果是统一配色方案中的键，直接返回
  if (scheme in UNIFIED_CHART_COLORS) {
    return [...UNIFIED_CHART_COLORS[scheme as keyof typeof UNIFIED_CHART_COLORS]]
  }

  // 如果是需要映射的旧配色方案，映射到新配色
  if (scheme in COLOR_SCHEME_MAPPING) {
    const mappedScheme = COLOR_SCHEME_MAPPING[scheme as keyof typeof COLOR_SCHEME_MAPPING]
    return [...UNIFIED_CHART_COLORS[mappedScheme]]
  }

  // 默认返回主要配色方案
  return [...UNIFIED_CHART_COLORS.primary]
}

// 判断是否为风险等级图表的函数
export function isRiskChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const riskKeywords = [
    '风险等级',
    '风险分布',
    '高风险',
    '中风险',
    '低风险',
    '高',
    '中',
    '低',
    'risk',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return riskKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为处置状态图表的函数
export function isDisposalStatusChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const statusKeywords = [
    '处置状态',
    '处理状态',
    '未处理',
    '处理中',
    '已处理',
    '已完成',
    '已归档',
    '处置',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return statusKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为处理阶段图表的函数
export function isProcessingStageChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const stageKeywords = [
    '处理阶段',
    '数据阶段',
    '收集',
    '存储',
    '传输',
    '加工',
    '提供',
    '公开',
    '销毁',
    '阶段',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return stageKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为事件类型图表的函数
export function isEventTypeChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const eventKeywords = [
    '事件类型',
    '事件统计',
    '数据泄露',
    '违规采集',
    '传输异常',
    '存储风险',
    '权限异常',
    'API安全',
    '算法安全',
    '合规风险',
    '车端事件',
    '云端事件',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return eventKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为企业类型图表的函数
export function isEnterpriseChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const enterpriseKeywords = [
    '平台',
    '智驾',
    '地图',
    '企业类型',
    '厂商',
    'enterprise',
    'company',
    '整车',
    '生产',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return enterpriseKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为车辆类型图表的函数
export function isVehicleChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const vehicleKeywords = ['车辆', '智能网联', '自动驾驶', '传统车联网', '测试车辆', 'vehicle']
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return vehicleKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为审批状态图表的函数
export function isApprovalStatusChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const approvalKeywords = ['审批状态', '注册状态', '通过', '未通过', '待审核', '注册中', '审批']
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return approvalKeywords.some((keyword) => dataText.includes(keyword))
}

// 自动推断配色方案的函数 - 系统化分类
export function inferColorScheme(data: any[]): keyof typeof UNIFIED_CHART_COLORS {
  // 优先级顺序：风险等级 > 处置状态 > 处理阶段 > 事件类型 > 企业类型 > 车辆类型 > 审批状态 > 默认
  if (isRiskChart(data)) return 'risk'
  if (isDisposalStatusChart(data)) return 'disposalStatus'
  if (isProcessingStageChart(data)) return 'processingStages'
  if (isEventTypeChart(data)) return 'eventTypes'
  if (isEnterpriseChart(data)) return 'enterprise'
  if (isVehicleChart(data)) return 'vehicleTypes'
  if (isApprovalStatusChart(data)) return 'approvalStatus'
  return 'primary'
}
