/**
 * 统一图表配色方案 - 系统化配色体系
 * 基于数据类型和业务语义进行分类配色
 */

// 核心配色方案 - 系统化分类
export const UNIFIED_CHART_COLORS = {
  // 主要配色方案 - 高对比度配色（基于成功案例优化）
  primary: [
    '#10b981', // 智能网联车 - 绿色（主色调）
    '#8b5cf6', // 自动驾驶车 - 紫色
    '#f59e0b', // 传统车联网 - 橙色
    '#3498db', // 测试车辆 - 蓝色
    '#ef4444', // 其他车辆 - 红色
    '#06b6d4', // 补充色 - 青色
    '#84cc16', // 补充色 - 绿色
    '#6b7280', // 补充色 - 灰色
  ],

  // 风险等级配色 - 红橙黄色系（高中低风险专用）
  risk: [
    '#d81e06', // 高风险 - 红色
    '#ba850d', // 中风险 - 橙黄色
    '#c2f507', // 低风险 - 黄绿色
  ],

  // 处置状态配色 - 高对比度蓝色系（处置状态专用）
  disposalStatus: [
    '#1e40af', // 未处理 - 深蓝色
    '#3b82f6', // 处理中 - 蓝色
    '#10b981', // 已处理 - 绿色（高对比度）
    '#f59e0b', // 已完成 - 橙色（高对比度）
    '#8b5cf6', // 已归档 - 紫色（高对比度）
  ],

  // 处理阶段配色 - 高对比度蓝色系（数据处理阶段专用）
  processingStages: [
    '#1e40af', // 收集 - 深蓝色
    '#3b82f6', // 存储 - 蓝色
    '#10b981', // 传输 - 绿色
    '#f59e0b', // 加工 - 橙色
    '#8b5cf6', // 提供 - 紫色
    '#ef4444', // 公开 - 红色
    '#6b7280', // 销毁 - 灰色
  ],

  // 事件类型配色 - 高对比度蓝色系（车端/云端事件类型专用）
  eventTypes: [
    '#1e40af', // 数据泄露 - 深蓝色
    '#3b82f6', // 违规采集 - 蓝色
    '#10b981', // 传输异常 - 绿色
    '#f59e0b', // 存储风险 - 橙色
    '#8b5cf6', // 权限异常 - 紫色
    '#ef4444', // API安全 - 红色
    '#06b6d4', // 算法安全 - 青色
    '#84cc16', // 合规风险 - 绿色
  ],

  // 企业类型配色 - 高对比度优化
  enterprise: [
    '#10b981', // 整车生产企业 - 绿色
    '#8b5cf6', // 平台运营商 - 紫色
    '#f59e0b', // 智驾方案提供商 - 橙色
    '#3498db', // 地图服务商 - 蓝色
    '#ef4444', // 其他 - 红色
  ],

  // 审批状态配色 - 语义化配色
  approvalStatus: [
    '#10b981', // 通过/已完成 - 绿色
    '#f59e0b', // 注册中/处理中 - 橙色
    '#ef4444', // 未通过/失败 - 红色
    '#8b5cf6', // 待审核/待处理 - 紫色
    '#95a5a6', // 未知/其他 - 灰色
  ],

  // 车辆类型配色 - 与primary保持一致（高对比度）
  vehicleTypes: [
    '#10b981', // 智能网联车 - 绿色
    '#8b5cf6', // 自动驾驶车 - 紫色
    '#f59e0b', // 传统车联网 - 橙色
    '#3498db', // 测试车辆 - 蓝色
    '#ef4444', // 其他车辆 - 红色
  ],

  // 非风险类柱状图专用配色 - 高对比度配色（基于成功案例）
  barChart: [
    '#10b981', // 绿色 - 主色调
    '#8b5cf6', // 紫色 - 高对比度
    '#f59e0b', // 橙色 - 高对比度
    '#3498db', // 蓝色 - 高对比度
    '#ef4444', // 红色 - 高对比度
    '#06b6d4', // 青色 - 补充色
    '#84cc16', // 绿色 - 补充色
    '#6b7280', // 灰色 - 补充色
  ],

  // 兼容性别名
  status: [
    '#10b981', // 通过/已完成 - 绿色
    '#f59e0b', // 注册中/处理中 - 橙色
    '#ef4444', // 未通过/失败 - 红色
    '#8b5cf6', // 待审核/待处理 - 紫色
    '#95a5a6', // 未知/其他 - 灰色
  ],
  stages: [
    '#1e3a8a', // 收集 - 深蓝色
    '#1e40af', // 存储 - 蓝色
    '#2563eb', // 传输 - 中蓝色
    '#3b82f6', // 加工 - 标准蓝色
    '#60a5fa', // 提供 - 浅蓝色
    '#93c5fd', // 公开 - 更浅蓝色
    '#dbeafe', // 销毁 - 极浅蓝色
  ],
} as const

// 配色方案映射 - 用于向后兼容
export const COLOR_SCHEME_MAPPING = {
  // 将旧的配色方案映射到新的统一配色
  oceanDepths: 'primary',
  sunsetWarmth: 'primary',
  forestGrove: 'primary',
  modernCorporate: 'primary',
  pastelDreams: 'primary',
  neonTech: 'primary',
  vintageEarth: 'primary',
  arcticAurora: 'primary',
  floralPalette: 'primary',
  summerAfternoon: 'primary',
  retroFuturistic: 'primary',
  resilience: 'primary',
} as const

// 获取统一配色方案的函数
export function getUnifiedColorScheme(
  scheme: keyof typeof UNIFIED_CHART_COLORS | keyof typeof COLOR_SCHEME_MAPPING,
): string[] {
  // 如果是统一配色方案中的键，直接返回
  if (scheme in UNIFIED_CHART_COLORS) {
    return [...UNIFIED_CHART_COLORS[scheme as keyof typeof UNIFIED_CHART_COLORS]]
  }

  // 如果是需要映射的旧配色方案，映射到新配色
  if (scheme in COLOR_SCHEME_MAPPING) {
    const mappedScheme = COLOR_SCHEME_MAPPING[scheme as keyof typeof COLOR_SCHEME_MAPPING]
    return [...UNIFIED_CHART_COLORS[mappedScheme]]
  }

  // 默认返回主要配色方案
  return [...UNIFIED_CHART_COLORS.primary]
}

// 判断是否为风险等级图表的函数
export function isRiskChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const riskKeywords = [
    '风险等级',
    '风险分布',
    '高风险',
    '中风险',
    '低风险',
    '高',
    '中',
    '低',
    'risk',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return riskKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为处置状态图表的函数
export function isDisposalStatusChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const statusKeywords = [
    '处置状态',
    '处理状态',
    '未处理',
    '处理中',
    '已处理',
    '已完成',
    '已归档',
    '处置',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return statusKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为处理阶段图表的函数
export function isProcessingStageChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const stageKeywords = [
    '处理阶段',
    '数据阶段',
    '收集',
    '存储',
    '传输',
    '加工',
    '提供',
    '公开',
    '销毁',
    '阶段',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return stageKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为事件类型图表的函数
export function isEventTypeChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const eventKeywords = [
    '事件类型',
    '事件统计',
    '数据泄露',
    '违规采集',
    '传输异常',
    '存储风险',
    '权限异常',
    'API安全',
    '算法安全',
    '合规风险',
    '车端事件',
    '云端事件',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return eventKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为企业类型图表的函数
export function isEnterpriseChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const enterpriseKeywords = [
    '平台',
    '智驾',
    '地图',
    '企业类型',
    '厂商',
    'enterprise',
    'company',
    '整车',
    '生产',
  ]
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return enterpriseKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为车辆类型图表的函数
export function isVehicleChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const vehicleKeywords = ['车辆', '智能网联', '自动驾驶', '传统车联网', '测试车辆', 'vehicle']
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return vehicleKeywords.some((keyword) => dataText.includes(keyword))
}

// 判断是否为审批状态图表的函数
export function isApprovalStatusChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false

  const approvalKeywords = ['审批状态', '注册状态', '通过', '未通过', '待审核', '注册中', '审批']
  const dataText = data.map((item) => String(item?.name || '')).join(' ')

  return approvalKeywords.some((keyword) => dataText.includes(keyword))
}

// 自动推断配色方案的函数 - 系统化分类
export function inferColorScheme(data: any[]): keyof typeof UNIFIED_CHART_COLORS {
  // 优先级顺序：风险等级 > 处置状态 > 处理阶段 > 事件类型 > 企业类型 > 车辆类型 > 审批状态 > 默认
  if (isRiskChart(data)) return 'risk'
  if (isDisposalStatusChart(data)) return 'disposalStatus'
  if (isProcessingStageChart(data)) return 'processingStages'
  if (isEventTypeChart(data)) return 'eventTypes'
  if (isEnterpriseChart(data)) return 'enterprise'
  if (isVehicleChart(data)) return 'vehicleTypes'
  if (isApprovalStatusChart(data)) return 'approvalStatus'
  return 'primary'
}
