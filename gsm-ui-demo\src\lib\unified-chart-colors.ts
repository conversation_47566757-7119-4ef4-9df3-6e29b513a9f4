/**
 * 统一图表配色方案
 * 整合所有功能内页的图表配色，实现主体化组件化，排除冗余配色体系
 */

// 核心配色方案 - 基于注册信息页面的配色
export const UNIFIED_CHART_COLORS = {
  // 主要配色方案 - 基于车辆类型统计的配色
  primary: [
    '#3b82f6', // 智能网联车 - 蓝色
    '#8b5cf6', // 自动驾驶车 - 紫色
    '#06b6d4', // 传统车联网 - 青色
    '#84cc16', // 测试车辆 - 绿色
    '#f59e0b', // 其他车辆 - 橙色
    '#6b7280', // 其他 - 灰色
    '#10b981', // 补充色 - 绿色
    '#ef4444', // 补充色 - 红色
  ],

  // 风险等级配色 - 红橙黄色系，保持已应用的配色不变
  risk: [
    '#d81e06', // 高风险 - 红色
    '#ba850d', // 中风险 - 橙黄色
    '#c2f507', // 低风险 - 黄绿色
  ],

  // 企业类型配色 - 来自注册信息页面
  enterprise: [
    '#3498db', // 整车生产企业 - 蓝色
    '#9b59b6', // 平台运营商 - 紫色
    '#17a2b8', // 智驾方案提供商 - 青色
    '#28a745', // 地图服务商 - 绿色
    '#5f9ea0', // 其他 - 青灰
  ],

  // 状态配色 - 来自注册信息页面
  status: [
    '#10b981', // 通过/已完成 - 绿色
    '#f59e0b', // 注册中/处理中 - 橙色
    '#ef4444', // 未通过/失败 - 红色
    '#8b5cf6', // 待审核/待处理 - 紫色
    '#95a5a6', // 未知/其他 - 灰色
  ],

  // 车辆类型配色 - 与primary保持一致
  vehicleTypes: [
    '#3b82f6', // 智能网联车 - 蓝色
    '#8b5cf6', // 自动驾驶车 - 紫色
    '#06b6d4', // 传统车联网 - 青色
    '#84cc16', // 测试车辆 - 绿色
    '#f59e0b', // 其他车辆 - 橙色
  ],

  // 处理阶段配色 (7阶段)
  stages: [
    '#3b82f6', // 收集 - 蓝色
    '#8b5cf6', // 存储 - 紫色
    '#06b6d4', // 传输 - 青色
    '#84cc16', // 加工 - 绿色
    '#f59e0b', // 提供 - 橙色
    '#ef4444', // 公开 - 红色
    '#6b7280', // 销毁 - 灰色
  ],

  // 非风险类柱状图专用配色 - 与车辆类型统计保持一致
  barChart: [
    '#3b82f6', // 蓝色
    '#8b5cf6', // 紫色
    '#06b6d4', // 青色
    '#84cc16', // 绿色
    '#f59e0b', // 橙色
    '#6b7280', // 灰色
    '#10b981', // 补充绿色
    '#ef4444', // 补充红色
  ],
} as const

// 配色方案映射 - 用于向后兼容
export const COLOR_SCHEME_MAPPING = {
  // 将旧的配色方案映射到新的统一配色
  oceanDepths: 'primary',
  sunsetWarmth: 'primary',
  forestGrove: 'primary',
  modernCorporate: 'primary',
  pastelDreams: 'primary',
  neonTech: 'primary',
  vintageEarth: 'primary',
  arcticAurora: 'primary',
  floralPalette: 'primary',
  summerAfternoon: 'primary',
  retroFuturistic: 'primary',
  resilience: 'primary',
} as const

// 获取统一配色方案的函数
export function getUnifiedColorScheme(
  scheme: keyof typeof UNIFIED_CHART_COLORS | keyof typeof COLOR_SCHEME_MAPPING
): string[] {
  // 如果是统一配色方案中的键，直接返回
  if (scheme in UNIFIED_CHART_COLORS) {
    return [...UNIFIED_CHART_COLORS[scheme as keyof typeof UNIFIED_CHART_COLORS]]
  }
  
  // 如果是需要映射的旧配色方案，映射到新配色
  if (scheme in COLOR_SCHEME_MAPPING) {
    const mappedScheme = COLOR_SCHEME_MAPPING[scheme as keyof typeof COLOR_SCHEME_MAPPING]
    return [...UNIFIED_CHART_COLORS[mappedScheme]]
  }
  
  // 默认返回主要配色方案
  return [...UNIFIED_CHART_COLORS.primary]
}

// 判断是否为风险类图表的函数
export function isRiskChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false
  
  const riskKeywords = ['风险', '告警', '异常', '严重', '高', '中', '低', 'risk', '危险']
  const dataText = data.map(item => String(item?.name || '')).join(' ')
  
  return riskKeywords.some(keyword => dataText.includes(keyword))
}

// 判断是否为企业类型图表的函数
export function isEnterpriseChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false
  
  const enterpriseKeywords = ['平台', '智驾', '地图', '企业类型', '厂商', 'enterprise', 'company', '整车', '生产']
  const dataText = data.map(item => String(item?.name || '')).join(' ')
  
  return enterpriseKeywords.some(keyword => dataText.includes(keyword))
}

// 判断是否为车辆类型图表的函数
export function isVehicleChart(data: any[]): boolean {
  if (!data || !Array.isArray(data)) return false
  
  const vehicleKeywords = ['车辆', '智能网联', '自动驾驶', '传统车联网', '测试车辆', 'vehicle']
  const dataText = data.map(item => String(item?.name || '')).join(' ')
  
  return vehicleKeywords.some(keyword => dataText.includes(keyword))
}

// 自动推断配色方案的函数
export function inferColorScheme(data: any[]): keyof typeof UNIFIED_CHART_COLORS {
  if (isRiskChart(data)) return 'risk'
  if (isEnterpriseChart(data)) return 'enterprise'
  if (isVehicleChart(data)) return 'vehicleTypes'
  return 'primary'
}
