========================================
时空数据安全监测平台 - 项目运行指导
========================================

项目简介：
这是一个基于 Vue 3 + TypeScript + Vite 构建的地理信息安全监测平台静态演示原型。
主要用于展示界面内容和模块的业务跳转交互。

========================================
一、Windows 环境准备
========================================

1. 安装 Node.js
   - 访问官网：https://nodejs.org/
   - 下载 LTS 版本（推荐 20.19.0 或更高版本）
   - 运行安装程序，按默认设置安装
   - 安装完成后，打开 PowerShell 或命令提示符
   - 验证安装：
     node --version
     npm --version

2. 验证环境
   确保 Node.js 版本符合要求：^20.19.0 || >=22.12.0

========================================
二、项目文件说明
========================================

你收到的文件包含：
- dist.zip：已构建好的静态网站文件
- 项目运行指导.txt：本指导文件

========================================
三、快速启动（推荐方式）
========================================

方式A：使用 serve 工具（推荐，支持路由回退）

1. 解压 dist.zip 到任意目录，例如：D:\website\dist

2. 打开 PowerShell，进入 dist 的父目录：
   cd D:\website

3. 临时启动（无需安装）：
   npx -y serve -s dist -l 5173

4. 或者全局安装一次（推荐）：
   npm install -g serve
   serve -s dist -l 5173

5. 打开浏览器访问：http://localhost:5173/

6. 停止服务：在 PowerShell 中按 Ctrl+C

========================================
四、备用启动方式
========================================

方式B：使用 Python（如果电脑已安装 Python 3）

1. 解压 dist.zip，进入 dist 目录：
   cd D:\website\dist

2. 启动简易服务器：
   python -m http.server 8080

3. 访问：http://localhost:8080/

注意：此方式不支持单页应用路由回退，刷新子页面可能出现 404

========================================
五、项目功能说明
========================================

这是一个静态演示原型，包含以下主要功能模块：

1. 政府端功能：
   - 企业注册审批管理
   - 数据活动备案管理
   - 风险监测与预警
   - 统计分析看板
   - 监督检查管理

2. 企业端功能：
   - 企业信息管理
   - 数据安全防控措施
   - 风险告警处理
   - 合规报告提交

3. 特色功能：
   - 响应式设计，支持桌面和移动端
   - 深色/浅色主题切换
   - 地图可视化展示
   - 数据图表分析
   - 溯源追踪可视化

========================================
六、常见问题解决
========================================

Q1：端口被占用怎么办？
A：修改端口号，例如：serve -s dist -l 8080

Q2：页面显示空白或报错？
A：确保使用 HTTP 服务器访问，不要直接双击 index.html

Q3：子页面刷新后显示 404？
A：使用方式A（serve -s），-s 参数提供单页应用支持

Q4：Node.js 安装失败？
A：以管理员身份运行安装程序，或从官网下载最新版本

Q5：npm 命令不识别？
A：重启 PowerShell，或检查环境变量 PATH 设置

========================================
七、项目技术栈
========================================

- 前端框架：Vue 3.5 + TypeScript
- 构建工具：Vite 7.0
- UI 组件：shadcn-vue + Tailwind CSS
- 状态管理：Pinia
- 路由：Vue Router 4
- 图表库：ECharts + D3.js
- 地图：高德地图 API

========================================
八、联系支持
========================================

如遇到技术问题，请联系刁国亮。

运行成功标志：
- 浏览器能正常打开网站
- 页面显示完整，无明显错误
- 可以正常点击导航和按钮
- 图表和地图正常显示

========================================
最后更新：2025年09月
========================================