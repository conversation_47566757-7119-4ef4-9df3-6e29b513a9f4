﻿表名: event_records,,,,,,,
,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,event_id,事件ID,VARCHAR,64,○,,唯一事件ID (业务主键)
3,event_type,事件类型,SMALLINT,,○,,"数据字典, e.g., 系统升级, 配置变更, 异常告警"
4,event_description,事件描述,TEXT,,○,,
5,event_timestamp,事件时间戳,BIGINT,,○,,毫秒级时间戳
6,source_module,来源模块,VARCHAR,128,,,
7,related_id,关联ID,VARCHAR,64,,,关联的ID (如关联的风险ID)
8,create_time,创建时间,TIMESTAMP,,○,,
