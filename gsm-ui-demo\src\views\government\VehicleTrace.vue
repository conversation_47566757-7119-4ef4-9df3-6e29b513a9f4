<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
      <p class="text-muted-foreground">
        基于数据血缘关系的车端风险溯源追踪，提供完整的数据传播路径分析
      </p>
    </div>

    <!-- 主体两列布局 -->
    <div class="grid grid-cols-12 gap-6">
      <!-- 左侧：溯源记录历史 -->
      <div class="col-span-12 lg:col-span-3">
        <Card class="h-full">
          <CardHeader class="pb-3">
            <CardTitle class="text-sm flex items-center gap-2">
              <History class="w-4 h-4" />
              溯源记录历史
            </CardTitle>
          </CardHeader>
          <CardContent>
            <!-- 筛选表单 -->
            <div class="space-y-3 mb-4">
              <div class="grid grid-cols-2 gap-2">
                <Select v-model="filterEnterprise">
                  <SelectTrigger class="h-8">
                    <SelectValue placeholder="企业" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部企业</SelectItem>
                    <SelectItem value="ENT-A-001">智驾科技A</SelectItem>
                    <SelectItem value="ENT-B-002">云控科技B</SelectItem>
                  </SelectContent>
                </Select>

                <Select v-model="filterLevel">
                  <SelectTrigger class="h-8">
                    <SelectValue placeholder="风险级别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部级别</SelectItem>
                    <SelectItem value="特别重大">特别重大</SelectItem>
                    <SelectItem value="重大">重大</SelectItem>
                    <SelectItem value="一般">一般</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div class="flex gap-1">
                <Button size="sm" variant="outline" @click="clearFilter" class="h-7 px-2 text-xs">
                  <FilterX class="w-3 h-3 mr-1" />
                  清空
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  @click="refreshRecords"
                  class="h-7 px-2 text-xs"
                >
                  <RefreshCw class="w-3 h-3 mr-1" />
                  刷新
                </Button>
              </div>
            </div>

            <!-- 记录列表 -->
            <div class="space-y-2 overflow-y-auto max-h-[calc(100vh-350px)]">
              <div
                v-for="record in filteredRecords"
                :key="record.eventId"
                class="border rounded-lg p-3 hover:bg-muted/40 cursor-pointer transition-colors"
                :class="{ 'ring-2 ring-primary': selectedRecord?.eventId === record.eventId }"
                @click="selectRecord(record)"
              >
                <div class="flex items-start justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <Badge :variant="getLevelVariant(record.eventLevel)" class="text-xs">
                      {{ record.eventLevel }}
                    </Badge>
                    <Badge variant="outline" class="text-xs">
                      {{ getRiskTypeLabel(record.eventType) }}
                    </Badge>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    @click.stop="deleteRecord(record)"
                    class="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                  >
                    <X class="w-3 h-3" />
                  </Button>
                </div>

                <div class="text-base font-semibold mb-1">{{ record.enterpriseName }}</div>
                <div class="text-xs text-muted-foreground font-mono mb-2">
                  VIN: {{ record.vin }}
                </div>
                <div class="text-xs text-muted-foreground line-clamp-2 mb-2">
                  {{ record.eventDescription }}
                </div>
                <div class="text-xs text-muted-foreground flex justify-between">
                  <span>{{ formatTime(record.eventTimestamp) }}</span>
                  <Badge :variant="getStatusVariant(record.disposalStatus)" class="text-xs h-4">
                    {{ record.disposalStatus }}
                  </Badge>
                </div>
              </div>

              <div
                v-if="filteredRecords.length === 0"
                class="text-center py-8 text-muted-foreground"
              >
                <FileX class="w-12 h-12 mx-auto mb-2 opacity-50" />
                <div class="text-sm">暂无溯源记录</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：溯源看板 -->
      <div class="col-span-12 lg:col-span-9">
        <div class="relative">
          <!-- 垂直时间轴线 -->
          <div
            class="absolute left-4 top-0 bottom-0 w-px bg-gradient-to-b from-primary via-primary/60 to-primary/20"
          ></div>

          <!-- 未选择记录时的提示 -->
          <Card v-if="!selectedRecord" class="mb-6">
            <CardContent class="py-12">
              <div class="text-center space-y-4">
                <Target class="w-16 h-16 text-muted-foreground mx-auto" />
                <div>
                  <h3 class="text-lg font-semibold mb-2">请选择风险事件</h3>
                  <p class="text-muted-foreground mb-4">
                    从左侧记录列表中选择具体的风险事件进行溯源分析
                  </p>
                  <Button @click="goToRiskManagement" variant="outline">
                    <ArrowLeft class="w-4 h-4 mr-1" />
                    返回车端安全风险
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 溯源看板内容 -->
          <div v-if="selectedRecord" class="space-y-6">
            <!-- 1. 车端风险基本信息卡片 -->
            <div class="relative pl-10">
              <div
                class="absolute left-2 w-4 h-4 bg-primary rounded-full border-4 border-background"
              ></div>
              <Card class="ocean-depths-card">
                <CardHeader>
                  <div class="flex items-center justify-between">
                    <CardTitle class="flex items-center gap-2">
                      <AlertTriangle class="w-5 h-5 text-destructive" />
                      车端风险基本信息
                    </CardTitle>
                    <div class="flex items-center gap-2">
                      <Badge variant="secondary" class="px-3 py-1">
                        事件ID: {{ selectedRecord.eventId }}
                      </Badge>
                      <Button
                        v-if="!traceStarted"
                        @click="startTrace"
                        class="bg-primary text-primary-foreground hover:bg-primary/90"
                      >
                        <Play class="w-4 h-4 mr-1" />
                        开始溯源
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div class="text-sm text-muted-foreground">企业名称</div>
                      <div class="font-medium">{{ selectedRecord.enterpriseName }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-muted-foreground">车辆VIN</div>
                      <div class="font-medium font-mono">{{ selectedRecord.vin }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-muted-foreground">风险类型</div>
                      <Badge variant="outline">{{
                        getRiskTypeLabel(selectedRecord.eventType)
                      }}</Badge>
                    </div>
                    <div>
                      <div class="text-sm text-muted-foreground">风险等级</div>
                      <Badge :variant="getLevelVariant(selectedRecord.eventLevel)">
                        {{ selectedRecord.eventLevel }}
                      </Badge>
                    </div>
                    <div class="md:col-span-2">
                      <div class="text-sm text-muted-foreground">事件描述</div>
                      <div class="text-sm">{{ selectedRecord.eventDescription }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-muted-foreground">发生时间</div>
                      <div class="font-medium">{{ formatTime(selectedRecord.eventTimestamp) }}</div>
                    </div>
                    <div>
                      <div class="text-sm text-muted-foreground">处置状态</div>
                      <Badge :variant="getStatusVariant(selectedRecord.disposalStatus)">
                        {{ selectedRecord.disposalStatus }}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- 2. 溯源图谱卡片 -->
            <div class="relative pl-10" v-if="traceStarted">
              <div
                class="absolute left-2 w-4 h-4 bg-primary rounded-full border-4 border-background"
              ></div>
              <Card class="ocean-depths-card">
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <GitBranch class="w-5 h-5" />
                    溯源图谱
                  </CardTitle>
                  <CardDescription>数据传播溯源流程图 - 深海主题可视化</CardDescription>
                </CardHeader>
                <CardContent class="p-0">
                  <div class="ocean-depths-flow-container">
                    <div class="flow-chart">
                      <div
                        v-for="(step, index) in traceSteps"
                        :key="index"
                        class="flow-step"
                        :class="{
                          'step-normal': step.status === 'normal',
                          'step-warning': step.status === 'warning',
                          'step-error': step.status === 'error',
                        }"
                      >
                        <div class="step-icon">
                          <component :is="step.icon" class="w-5 h-5" />
                        </div>
                        <div class="step-content">
                          <div class="step-title">{{ step.stage }}</div>
                          <div class="step-subtitle">{{ step.action }}</div>
                          <div class="step-time">{{ step.timestamp }}</div>
                        </div>
                        <div class="step-status">
                          <CheckCircle
                            v-if="step.status === 'normal'"
                            class="w-4 h-4 text-green-400"
                          />
                          <AlertTriangle
                            v-else-if="step.status === 'warning'"
                            class="w-4 h-4 text-yellow-400"
                          />
                          <XCircle v-else class="w-4 h-4 text-red-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- 3. 风险溯源分析 -->
            <div class="relative pl-10" v-if="traceStarted">
              <div
                class="absolute left-2 w-4 h-4 bg-primary rounded-full border-4 border-background"
              ></div>
              <Card class="ocean-depths-card">
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <SearchCheck class="w-5 h-5" />
                    风险溯源分析
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs v-model="analysisTab" class="w-full">
                    <TabsList class="grid w-full grid-cols-3">
                      <TabsTrigger value="datapacket">数据包信息</TabsTrigger>
                      <TabsTrigger value="riskpoint">风险点描述</TabsTrigger>
                      <TabsTrigger value="responsible">关联责任主体</TabsTrigger>
                    </TabsList>

                    <TabsContent value="datapacket" class="mt-4">
                      <div class="space-y-4">
                        <h4 class="font-medium">数据包详情</h4>
                        <div class="grid grid-cols-2 gap-4">
                          <div>
                            <div class="text-sm text-muted-foreground">数据包ID</div>
                            <div class="font-mono">{{ selectedRecord.dataPacketId }}</div>
                          </div>
                          <div>
                            <div class="text-sm text-muted-foreground">数据类型位图</div>
                            <div class="font-mono">{{ selectedRecord.dataTypeBitmap }}</div>
                          </div>
                          <div>
                            <div class="text-sm text-muted-foreground">通信协议</div>
                            <div>{{ selectedRecord.protocol || 'HTTP' }}</div>
                          </div>
                          <div>
                            <div class="text-sm text-muted-foreground">加密状态</div>
                            <Badge variant="destructive">{{
                              selectedRecord.encryptionStatus || '未加密存储'
                            }}</Badge>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="riskpoint" class="mt-4">
                      <div class="space-y-4">
                        <h4 class="font-medium">风险点分析</h4>
                        <div class="space-y-3">
                          <div
                            v-for="(risk, index) in riskPoints"
                            :key="index"
                            class="p-3 border rounded-lg"
                          >
                            <div class="flex items-start justify-between mb-2">
                              <div class="font-medium text-sm">{{ risk.stage }}</div>
                              <Badge
                                :variant="risk.severity === 'high' ? 'destructive' : 'secondary'"
                              >
                                {{ risk.severity === 'high' ? '高风险' : '中风险' }}
                              </Badge>
                            </div>
                            <div class="text-sm text-muted-foreground">{{ risk.description }}</div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="responsible" class="mt-4">
                      <div class="space-y-4">
                        <h4 class="font-medium">责任主体</h4>
                        <div class="space-y-3">
                          <div
                            v-for="(entity, index) in responsibleEntities"
                            :key="index"
                            class="p-3 border rounded-lg"
                          >
                            <div class="flex items-start justify-between mb-2">
                              <div class="font-medium">{{ entity.name }}</div>
                              <Button
                                size="sm"
                                variant="outline"
                                @click="viewEntityDetails(entity)"
                                class="h-6 px-2 text-xs"
                              >
                                查看详情
                              </Button>
                            </div>
                            <div class="text-sm text-muted-foreground">{{ entity.role }}</div>
                            <div class="text-sm">责任环节：{{ entity.stages.join('、') }}</div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>

            <!-- 4. 溯源报告 -->
            <div class="relative pl-10" v-if="traceStarted">
              <div
                class="absolute left-2 w-4 h-4 bg-primary rounded-full border-4 border-background"
              ></div>
              <Card class="ocean-depths-card">
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <FileText class="w-5 h-5" />
                    溯源报告
                  </CardTitle>
                  <CardDescription>生成完整的溯源分析报告，支持预览和导出</CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="flex items-center justify-between mb-4">
                    <div>
                      <div class="font-medium">车端应急溯源报告</div>
                      <div class="text-sm text-muted-foreground">
                        {{ selectedRecord.enterpriseName }} | {{ selectedRecord.vin }}
                      </div>
                    </div>
                    <div class="flex gap-2">
                      <Button variant="outline" @click="previewReport">
                        <Eye class="w-4 h-4 mr-1" />
                        预览
                      </Button>
                      <Button @click="exportToPDF">
                        <Download class="w-4 h-4 mr-1" />
                        导出PDF
                      </Button>
                    </div>
                  </div>

                  <div class="border rounded-lg p-4 bg-muted/20">
                    <div class="text-sm space-y-2">
                      <div><strong>报告摘要：</strong></div>
                      <div class="text-muted-foreground ml-4">
                        • 风险事件：{{ selectedRecord.eventDescription }}<br />
                        • 涉及阶段：数据收集、存储、传输<br />
                        • 风险等级：{{ selectedRecord.eventLevel }}<br />
                        • 建议处置：立即停止相关数据处理活动，进行安全评估
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 报告预览对话框 -->
    <Dialog v-model:open="reportDialogOpen">
      <DialogContent class="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>溯源报告预览</DialogTitle>
        </DialogHeader>
        <div class="max-h-[70vh] overflow-y-auto p-4 border rounded-lg bg-white">
          <div class="space-y-4 text-sm">
            <div class="text-center mb-6">
              <h2 class="text-xl font-bold mb-2">车端应急溯源报告</h2>
              <div class="text-muted-foreground">{{ formatDate(new Date()) }}</div>
            </div>

            <div class="space-y-4">
              <section>
                <h3 class="font-semibold border-b pb-1 mb-2">基本信息</h3>
                <div class="grid grid-cols-2 gap-2 text-xs">
                  <div><strong>事件ID：</strong>{{ selectedRecord?.eventId }}</div>
                  <div><strong>企业名称：</strong>{{ selectedRecord?.enterpriseName }}</div>
                  <div><strong>车辆VIN：</strong>{{ selectedRecord?.vin }}</div>
                  <div><strong>风险等级：</strong>{{ selectedRecord?.eventLevel }}</div>
                </div>
              </section>

              <section>
                <h3 class="font-semibold border-b pb-1 mb-2">溯源分析</h3>
                <div class="text-xs space-y-2">
                  <div>通过数据血缘分析，识别出以下风险传播路径：</div>
                  <div v-for="(step, index) in traceSteps" :key="index" class="ml-4">
                    {{ index + 1 }}. {{ step.stage }} - {{ step.action }}
                  </div>
                </div>
              </section>

              <section>
                <h3 class="font-semibold border-b pb-1 mb-2">处置建议</h3>
                <div class="text-xs">
                  1. 立即停止相关数据处理活动<br />
                  2. 对违规数据进行安全处理<br />
                  3. 加强安全技术措施<br />
                  4. 建立长效监管机制
                </div>
              </section>
            </div>
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="reportDialogOpen = false">关闭</Button>
          <Button @click="exportToPDF">导出PDF</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  AlertTriangle,
  ArrowLeft,
  CheckCircle,
  Download,
  Eye,
  FileText,
  FileX,
  FilterX,
  GitBranch,
  History,
  Play,
  RefreshCw,
  SearchCheck,
  Target,
  X,
  XCircle,
} from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { getRiskTypeLabel } from '@/lib/riskTypeMapping'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface VehicleRiskEvent {
  eventId: string
  enterpriseId: string
  enterpriseName: string
  vin: string
  eventType: string
  eventLevel: '特别重大' | '重大' | '较大' | '一般'
  eventTimestamp: number
  eventDescription: string
  disposalStatus: '待处置' | '处置中' | '已处置' | '已上报'
  dataPacketId?: string
  dataTypeBitmap?: string
  protocol?: string
  encryptionStatus?: string
}

interface TraceStep {
  stage: string
  action: string
  timestamp: string
  status: 'normal' | 'warning' | 'error'
  icon: any
}

interface RiskPoint {
  stage: string
  description: string
  severity: 'high' | 'medium' | 'low'
}

interface ResponsibleEntity {
  name: string
  role: string
  stages: string[]
}

const router = useRouter()

// 筛选条件
const filterEnterprise = ref('all')
const filterLevel = ref('all')

// 选中的记录
const selectedRecord = ref<VehicleRiskEvent | null>(null)

// 溯源状态
const traceStarted = ref(false)

// 分析标签页
const analysisTab = ref('datapacket')

// 报告对话框
const reportDialogOpen = ref(false)

// Mock 数据：车端风险事件记录（根据文档要求的演示数据）
const mockRecords = ref<VehicleRiskEvent[]>([
  {
    eventId: 'EVT-V-20250826-001',
    enterpriseId: 'ENT-A-001',
    enterpriseName: '智驾科技有限公司',
    vin: 'LSVAP2A17PXXXXXXXX',
    eventType: 'RT-001',
    eventLevel: '特别重大',
    eventTimestamp: 1756219860000,
    eventDescription: '数据在车辆存储前，未按照国家认定的地理信息保密处理技术完成处理',
    disposalStatus: '待处置',
    dataPacketId: 'LOG-V-STO-20250826-101105-A17P',
    dataTypeBitmap: '00011',
    encryptionStatus: '未加密存储',
  },
  {
    eventId: 'EVT-V-20250826-002',
    enterpriseId: 'ENT-B-002',
    enterpriseName: '云控科技股份',
    vin: 'LSVBP2B18PXXXXXXXX',
    eventType: 'RT-021',
    eventLevel: '特别重大',
    eventTimestamp: 1756223700000,
    eventDescription: '核心安全策略失效：车辆安全模块检测到异常数据传输行为',
    disposalStatus: '处置中',
    dataPacketId: 'LOG-V-TRN-20250826-111500-B18P',
    dataTypeBitmap: '00110',
    protocol: 'HTTP',
  },
  {
    eventId: 'EVT-V-20250826-003',
    enterpriseId: 'ENT-A-001',
    enterpriseName: '智驾科技有限公司',
    vin: 'LSGJ8A12X34567890',
    eventType: 'RT-001',
    eventLevel: '重大',
    eventTimestamp: 1756220000000,
    eventDescription: '车载系统在违规区域采集敏感地理信息，未执行保密处理技术',
    disposalStatus: '已上报',
    dataPacketId: 'LOG-V-COL-20250826-101100-001',
    dataTypeBitmap: '00011',
  },
  {
    eventId: 'EVT-V-20250826-004',
    enterpriseId: 'ENT-B-002',
    enterpriseName: '云控科技股份',
    vin: 'LSVCP3C19PXXXXXXXX',
    eventType: 'RT-021',
    eventLevel: '较大',
    eventTimestamp: 1756225000000,
    eventDescription: '核心安全策略失效：数据完整性验证机制异常',
    disposalStatus: '处置中',
    dataPacketId: 'LOG-V-SEC-20250826-112000-C19P',
    dataTypeBitmap: '01000',
  },
])

// 筛选后的记录
const filteredRecords = computed(() => {
  let records = mockRecords.value

  if (filterEnterprise.value !== 'all') {
    records = records.filter((r) => r.enterpriseId === filterEnterprise.value)
  }

  if (filterLevel.value !== 'all') {
    records = records.filter((r) => r.eventLevel === filterLevel.value)
  }

  return records
})

// 溯源步骤
const traceSteps = computed<TraceStep[]>(() => {
  if (!selectedRecord.value) return []

  // 根据事件类型返回不同的溯源步骤
  if (selectedRecord.value.eventType === 'RT-001') {
    return [
      {
        stage: '数据收集',
        action: '车载终端采集地理信息',
        timestamp: '10:30:00',
        status: 'normal',
        icon: 'Database',
      },
      {
        stage: '保密处理',
        action: '地理信息保密处理技术',
        timestamp: '10:30:15',
        status: 'error',
        icon: 'Shield',
      },
      {
        stage: '数据存储',
        action: '明文存储到车载设备',
        timestamp: '10:32:45',
        status: 'error',
        icon: 'HardDrive',
      },
      {
        stage: '数据传输',
        action: '上传至云端处理中心',
        timestamp: '10:33:12',
        status: 'warning',
        icon: 'Upload',
      },
    ]
  } else {
    return [
      {
        stage: '安全验证',
        action: '核心安全策略检查',
        timestamp: '11:15:00',
        status: 'error',
        icon: 'ShieldCheck',
      },
      {
        stage: '数据传输',
        action: '异常数据传输行为',
        timestamp: '11:15:30',
        status: 'error',
        icon: 'Wifi',
      },
      {
        stage: '完整性验证',
        action: '数据完整性检查失败',
        timestamp: '11:16:00',
        status: 'error',
        icon: 'CheckCircle',
      },
      {
        stage: '异常处理',
        action: '安全事件告警',
        timestamp: '11:16:15',
        status: 'warning',
        icon: 'AlertTriangle',
      },
    ]
  }
})

// 风险点分析
const riskPoints = computed<RiskPoint[]>(() => {
  if (!selectedRecord.value) return []

  if (selectedRecord.value.eventType === 'RT-001') {
    return [
      {
        stage: '保密处理阶段',
        description: '未按照国家认定的地理信息保密处理技术标准执行数据脱敏',
        severity: 'high',
      },
      {
        stage: '存储阶段',
        description: '敏感地理信息以明文形式存储在车载设备中',
        severity: 'high',
      },
      {
        stage: '访问控制阶段',
        description: '关闭访问控制机制，存在未授权访问风险',
        severity: 'medium',
      },
    ]
  } else {
    return [
      {
        stage: '安全策略阶段',
        description: '核心安全策略模块检测机制失效，无法有效识别异常行为',
        severity: 'high',
      },
      {
        stage: '数据传输阶段',
        description: '异常数据传输行为未被及时阻断',
        severity: 'high',
      },
      {
        stage: '完整性验证阶段',
        description: '数据完整性验证机制异常，可能导致数据损坏',
        severity: 'medium',
      },
    ]
  }
})

// 责任主体
const responsibleEntities = computed<ResponsibleEntity[]>(() => {
  if (!selectedRecord.value) return []

  return [
    {
      name: selectedRecord.value.enterpriseName,
      role: '数据处理者',
      stages: ['数据收集', '存储处理', '传输控制'],
    },
    {
      name: '车载设备制造商',
      role: '设备提供方',
      stages: ['硬件安全', '系统集成'],
    },
    {
      name: '软件服务商',
      role: '技术支撑方',
      stages: ['软件开发', '安全策略'],
    },
  ]
})

// 选择记录
const selectRecord = (record: VehicleRiskEvent) => {
  selectedRecord.value = record
  traceStarted.value = false
}

// 开始溯源
const startTrace = () => {
  traceStarted.value = true
}

// 清空筛选
const clearFilter = () => {
  filterEnterprise.value = 'all'
  filterLevel.value = 'all'
}

// 刷新记录
const refreshRecords = () => {
  // 模拟刷新，实际项目中可以重新请求数据
  console.log('刷新记录')
}

// 删除记录
const deleteRecord = (record: VehicleRiskEvent) => {
  const index = mockRecords.value.findIndex((r) => r.eventId === record.eventId)
  if (index > -1) {
    mockRecords.value.splice(index, 1)
    if (selectedRecord.value?.eventId === record.eventId) {
      selectedRecord.value = null
      traceStarted.value = false
    }
  }
}

// 查看实体详情
const viewEntityDetails = (entity: ResponsibleEntity) => {
  console.log('查看实体详情:', entity)
}

// 预览报告
const previewReport = () => {
  reportDialogOpen.value = true
}

// 导出PDF
const exportToPDF = () => {
  if (!selectedRecord.value) return

  const reportData = {
    title: '车端应急溯源报告',
    eventInfo: selectedRecord.value,
    traceSteps: traceSteps.value,
    riskPoints: riskPoints.value,
    responsibleEntities: responsibleEntities.value,
    generatedAt: new Date().toISOString(),
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], {
    type: 'application/json;charset=utf-8;',
  })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `车端溯源报告_${selectedRecord.value.eventId}_${formatDate(new Date())}.json`
  link.click()
  URL.revokeObjectURL(url)

  reportDialogOpen.value = false
}

// 返回风险管理
const goToRiskManagement = () => {
  router.push('/gov/monitor/risk/vehicle')
}

// 辅助函数
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const formatDate = (date: Date) => {
  return date.toISOString().slice(0, 10)
}

const getLevelVariant = (level: string) => {
  switch (level) {
    case '特别重大':
      return 'destructive'
    case '重大':
      return 'destructive'
    case '较大':
      return 'secondary'
    case '一般':
      return 'outline'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case '待处置':
      return 'outline'
    case '处置中':
      return 'secondary'
    case '已处置':
      return 'default'
    case '已上报':
      return 'destructive'
    default:
      return 'outline'
  }
}

// 初始化时选择第一条记录
onMounted(() => {
  if (mockRecords.value.length > 0) {
    selectedRecord.value = mockRecords.value[0]
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 深海主题样式 */
.ocean-depths-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border: 1px solid rgba(100, 116, 139, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark .ocean-depths-card {
  background: linear-gradient(135deg, rgba(2, 6, 23, 0.98), rgba(15, 23, 42, 0.95));
  border: 1px solid rgba(51, 65, 85, 0.3);
}

.ocean-depths-flow-container {
  background: linear-gradient(
    180deg,
    rgba(2, 6, 23, 0.9) 0%,
    rgba(15, 23, 42, 0.95) 50%,
    rgba(30, 41, 59, 0.9) 100%
  );
  min-height: 400px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.ocean-depths-flow-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.flow-chart {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.flow-step {
  display: flex;
  items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.step-normal {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.step-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.step-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 0.25rem;
}

.step-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.25rem;
}

.step-time {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  font-family: monospace;
}

.step-status {
  display: flex;
  align-items: center;
}

.flow-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}
</style>
