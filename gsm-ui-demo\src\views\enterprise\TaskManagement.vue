<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理企业待办任务与已办任务，支持任务处理、申诉、确认等操作
        </p>
      </div>
    </div>

    <!-- Tab 切换 -->
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="pending" class="flex items-center gap-2">
          <Clock class="w-5 h-5" />
          待办任务
          <Badge v-if="pendingCount > 0" variant="destructive" class="ml-1">{{
            pendingCount
          }}</Badge>
        </TabsTrigger>
        <TabsTrigger value="completed" class="flex items-center gap-2">
          <CheckCircle2 class="w-5 h-5" />
          已办任务
        </TabsTrigger>
      </TabsList>

      <!-- 待办任务 Tab -->
      <TabsContent value="pending" class="space-y-6">
        <!-- 待办任务列表 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>待办任务列表</span>
              <Badge variant="outline"> 共 {{ filteredPendingTasks.length }} 个待办任务 </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CompactFilterForm
              :filter-fields="pendingFilterFields"
              :show-export="false"
              :initial-values="pendingFilters"
              @search="handlePendingSearch"
              @reset="resetPendingFilters"
            />
            <div class="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[80px]">序号</TableHead>
                    <TableHead>任务类别</TableHead>
                    <TableHead>任务标题</TableHead>
                    <TableHead>紧急程度</TableHead>
                    <TableHead>接收时间</TableHead>
                    <TableHead>截止时间</TableHead>
                    <TableHead class="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="pagedPendingTasks.length === 0">
                    <TableCell :colspan="7" class="h-24 text-center text-muted-foreground">
                      暂无待办任务
                    </TableCell>
                  </TableRow>
                  <TableRow
                    v-for="(item, index) in pagedPendingTasks"
                    :key="item.id"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{ (pendingCurrentPage - 1) * pageSize + index + 1 }}</TableCell>
                    <TableCell>
                      <Badge :variant="categoryVariant(item.category)">{{ item.category }}</Badge>
                    </TableCell>
                    <TableCell class="max-w-[300px]">
                      <div class="font-medium">{{ item.title }}</div>
                      <div class="text-sm text-muted-foreground line-clamp-1">
                        {{ item.description }}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="priorityVariant(item.priority)">{{ item.priority }}</Badge>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">{{ item.receivedAt }}</TableCell>
                    <TableCell class="whitespace-nowrap">
                      <span :class="{ 'text-red-600': isOverdue(item.deadline) }">
                        {{ item.deadline }}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" @click="viewTaskDetail(item)">处理</Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <!-- 分页 -->
            <div class="flex items-center justify-between mt-4">
              <div class="text-sm text-muted-foreground">
                显示第 {{ (pendingCurrentPage - 1) * pageSize + 1 }} -
                {{ Math.min(pendingCurrentPage * pageSize, filteredPendingTasks.length) }} 条， 共
                {{ filteredPendingTasks.length }} 条记录
              </div>
              <div class="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  @click="pendingCurrentPage = Math.max(1, pendingCurrentPage - 1)"
                  :disabled="pendingCurrentPage === 1"
                >
                  上一页
                </Button>
                <span class="text-sm"> {{ pendingCurrentPage }} / {{ pendingTotalPages }} </span>
                <Button
                  variant="outline"
                  size="sm"
                  @click="pendingCurrentPage = Math.min(pendingTotalPages, pendingCurrentPage + 1)"
                  :disabled="pendingCurrentPage === pendingTotalPages"
                >
                  下一页
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 已办任务 Tab -->
      <TabsContent value="completed" class="space-y-6">
        <!-- 已办任务列表 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>已办任务列表</span>
              <Badge variant="outline"> 共 {{ filteredCompletedTasks.length }} 个已办任务 </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CompactFilterForm
              :filter-fields="completedFilterFields"
              :show-export="false"
              :initial-values="completedFilters"
              @search="handleCompletedSearch"
              @reset="resetCompletedFilters"
            />
            <div class="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[80px]">序号</TableHead>
                    <TableHead>任务类别</TableHead>
                    <TableHead>任务标题</TableHead>
                    <TableHead>接收时间</TableHead>
                    <TableHead>处置时间</TableHead>
                    <TableHead>处置结果</TableHead>
                    <TableHead class="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="pagedCompletedTasks.length === 0">
                    <TableCell :colspan="7" class="h-24 text-center text-muted-foreground">
                      暂无已办任务
                    </TableCell>
                  </TableRow>
                  <TableRow
                    v-for="(item, index) in pagedCompletedTasks"
                    :key="item.id"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{ (completedCurrentPage - 1) * pageSize + index + 1 }}</TableCell>
                    <TableCell>
                      <Badge :variant="categoryVariant(item.category)">{{ item.category }}</Badge>
                    </TableCell>
                    <TableCell class="max-w-[300px]">
                      <div class="font-medium">{{ item.title }}</div>
                      <div class="text-sm text-muted-foreground line-clamp-1">
                        {{ item.description }}
                      </div>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">{{ item.receivedAt }}</TableCell>
                    <TableCell class="whitespace-nowrap">{{ item.completedAt }}</TableCell>
                    <TableCell>
                      <Badge :variant="resultVariant(item.result)">{{ item.result }}</Badge>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" @click="viewTaskDetail(item)"
                        >详情</Button
                      >
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <!-- 分页 -->
            <div class="flex items-center justify-between mt-4">
              <div class="text-sm text-muted-foreground">
                显示第 {{ (completedCurrentPage - 1) * pageSize + 1 }} -
                {{ Math.min(completedCurrentPage * pageSize, filteredCompletedTasks.length) }} 条，
                共 {{ filteredCompletedTasks.length }} 条记录
              </div>
              <div class="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  @click="completedCurrentPage = Math.max(1, completedCurrentPage - 1)"
                  :disabled="completedCurrentPage === 1"
                >
                  上一页
                </Button>
                <span class="text-sm">
                  {{ completedCurrentPage }} / {{ completedTotalPages }}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  @click="
                    completedCurrentPage = Math.min(completedTotalPages, completedCurrentPage + 1)
                  "
                  :disabled="completedCurrentPage === completedTotalPages"
                >
                  下一页
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>

    <!-- 任务详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{{ selectedTask?.title }}</DialogTitle>
          <DialogDescription>
            {{ selectedTask?.category }} | {{ selectedTask?.receivedAt }}
          </DialogDescription>
        </DialogHeader>

        <div v-if="selectedTask" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div class="text-xs text-muted-foreground">任务类别</div>
              <div class="text-base font-semibold">{{ selectedTask.category }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">紧急程度</div>
              <div class="text-base font-semibold">{{ (selectedTask as any).priority || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">接收时间</div>
              <div class="text-base font-semibold">{{ selectedTask.receivedAt }}</div>
            </div>
            <div v-if="'deadline' in selectedTask">
              <div class="text-xs text-muted-foreground">截止时间</div>
              <div class="text-base font-semibold">{{ selectedTask.deadline }}</div>
            </div>
            <div v-if="'completedAt' in selectedTask">
              <div class="text-xs text-muted-foreground">处置时间</div>
              <div class="text-base font-semibold">{{ selectedTask.completedAt }}</div>
            </div>
            <div v-if="'result' in selectedTask">
              <div class="text-xs text-muted-foreground">处置结果</div>
              <div class="text-base font-semibold">{{ selectedTask.result }}</div>
            </div>
          </div>

          <!-- 任务描述 -->
          <div class="space-y-2">
            <div class="text-xs text-muted-foreground">任务描述</div>
            <div class="text-sm whitespace-pre-line">{{ selectedTask.description }}</div>
          </div>

          <!-- 任务详情 -->
          <div v-if="selectedTask.details" class="space-y-4">
            <h4 class="font-medium">详细信息</h4>

            <!-- 注册反馈详情 -->
            <div v-if="selectedTask.category === '注册反馈'" class="space-y-3">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <div class="text-xs text-muted-foreground">申请人</div>
                  <div class="text-base font-semibold">
                    {{ selectedTask.details.applicant || '-' }}
                  </div>
                </div>
                <div>
                  <div class="text-xs text-muted-foreground">申请时间</div>
                  <div class="text-base font-semibold">
                    {{ selectedTask.details.applicationTime || '-' }}
                  </div>
                </div>
              </div>
              <div v-if="selectedTask.details.failureReason">
                <div class="text-xs text-muted-foreground">失败原因</div>
                <div class="text-sm text-red-600">{{ selectedTask.details.failureReason }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">截止填报时间</div>
                <div class="text-base font-semibold">
                  {{ selectedTask.details.filingDeadline || '-' }}
                </div>
              </div>
            </div>

            <!-- 风险提醒详情 -->
            <div v-if="selectedTask.category === '风险提醒'" class="space-y-3">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <div class="text-xs text-muted-foreground">风险ID</div>
                  <div class="text-base font-semibold">
                    {{ selectedTask.details.riskId || '-' }}
                  </div>
                </div>
                <div>
                  <div class="text-xs text-muted-foreground">风险类别</div>
                  <div class="text-base font-semibold">
                    {{ selectedTask.details.riskType || '-' }}
                  </div>
                </div>
                <div>
                  <div class="text-xs text-muted-foreground">产生时间</div>
                  <div class="text-base font-semibold">
                    {{ selectedTask.details.occurTime || '-' }}
                  </div>
                </div>
                <div>
                  <div class="text-xs text-muted-foreground">处理阶段</div>
                  <div class="text-base font-semibold">{{ selectedTask.details.stage || '-' }}</div>
                </div>
                <div>
                  <div class="text-xs text-muted-foreground">风险等级</div>
                  <div class="text-base font-semibold">{{ selectedTask.details.level || '-' }}</div>
                </div>
                <div v-if="selectedTask.details.vin">
                  <div class="text-xs text-muted-foreground">车辆VIN</div>
                  <div class="text-base font-semibold">{{ selectedTask.details.vin }}</div>
                </div>
              </div>
              <div v-if="selectedTask.details.suggestion">
                <div class="text-xs text-muted-foreground">处置建议</div>
                <div class="text-sm">{{ selectedTask.details.suggestion }}</div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end gap-2">
            <Button variant="outline" @click="detailOpen = false">关闭</Button>

            <!-- 待办任务操作 -->
            <div v-if="!('result' in selectedTask)" class="flex gap-2">
              <Button v-if="selectedTask.category === '注册反馈'" @click="viewFilingInfo">
                查看填报信息
              </Button>
              <Button
                v-if="selectedTask.category === '风险提醒'"
                variant="outline"
                @click="confirmRisk"
              >
                确认风险
              </Button>
              <Button v-if="selectedTask.category === '风险提醒'" @click="appealRisk">
                申诉风险
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 风险申诉弹窗 -->
    <Dialog v-model:open="appealOpen">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>风险申诉</DialogTitle>
          <DialogDescription>请填写申诉信息，我们将在3个工作日内处理您的申诉</DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <!-- 基本信息展示 -->
          <div v-if="selectedTask" class="p-4 bg-muted rounded-lg">
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div>风险ID：{{ selectedTask.details?.riskId }}</div>
              <div>风险等级：{{ selectedTask.details?.level }}</div>
              <div>处理阶段：{{ selectedTask.details?.stage }}</div>
              <div>产生时间：{{ selectedTask.details?.occurTime }}</div>
            </div>
          </div>

          <!-- 申诉表单 -->
          <div class="space-y-4">
            <div class="space-y-2">
              <Label for="appealReason">申诉原因 *</Label>
              <Textarea
                id="appealReason"
                v-model="appealForm.reason"
                placeholder="请详细说明申诉原因"
                class="min-h-[120px]"
              />
            </div>

            <div class="space-y-2">
              <Label for="appealEvidence">申诉材料</Label>
              <div
                class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center"
              >
                <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                <p class="text-sm text-muted-foreground">点击或拖拽文件到此处上传申诉材料</p>
                <p class="text-xs text-muted-foreground mt-1">
                  支持 PDF、DOC、DOCX、JPG、PNG 格式，最大 10MB
                </p>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="appellant">申诉人 *</Label>
                <Input
                  id="appellant"
                  v-model="appealForm.appellant"
                  placeholder="请输入申诉人姓名"
                />
              </div>
              <div class="space-y-2">
                <Label for="contact">联系方式 *</Label>
                <Input
                  id="contact"
                  v-model="appealForm.contact"
                  placeholder="请输入联系电话或邮箱"
                />
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-2">
            <Button variant="outline" @click="appealOpen = false">取消</Button>
            <Button @click="submitAppeal">提交申诉</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { CheckCircle2, Clock, Upload } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type TaskCategory = '注册反馈' | '风险提醒' | '风险处置' | '事件处置'
type Priority = '紧急' | '一般' | '不紧急'
type TaskResult = '已确认' | '已申诉' | '已处理' | '已驳回'

interface TaskDetails {
  // 注册反馈
  applicant?: string
  applicationTime?: string
  failureReason?: string
  filingDeadline?: string

  // 风险提醒
  riskId?: string
  riskType?: string
  occurTime?: string
  stage?: string
  level?: string
  vin?: string
  suggestion?: string
}

interface PendingTask {
  id: string
  category: TaskCategory
  title: string
  description: string
  priority: Priority
  receivedAt: string
  deadline: string
  details?: TaskDetails
}

interface CompletedTask {
  id: string
  category: TaskCategory
  title: string
  description: string
  receivedAt: string
  completedAt: string
  result: TaskResult
  details?: TaskDetails
}

// 活动Tab
const activeTab = ref('pending')

// 弹窗状态
const detailOpen = ref(false)
const appealOpen = ref(false)
const selectedTask = ref<PendingTask | CompletedTask | null>(null)

// 申诉表单
const appealForm = ref({
  reason: '',
  appellant: '',
  contact: '',
})

// 待办任务筛选条件
const pendingFilters = ref({
  category: 'ALL' as 'ALL' | TaskCategory,
  priority: 'ALL' as 'ALL' | Priority,
  timeRange: null as [Date, Date] | null,
})

// 待办任务筛选表单配置
const pendingFilterFields: FilterField[] = [
  {
    key: 'category',
    label: '任务类别',
    type: 'select',
    placeholder: '请选择类别',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '注册反馈', value: '注册反馈' },
      { label: '风险提醒', value: '风险提醒' },
      { label: '风险处置', value: '风险处置' },
      { label: '事件处置', value: '事件处置' },
    ],
  },
  {
    key: 'priority',
    label: '紧急程度',
    type: 'select',
    placeholder: '请选择紧急程度',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '紧急', value: '紧急' },
      { label: '一般', value: '一般' },
      { label: '不紧急', value: '不紧急' },
    ],
  },
  {
    key: 'timeRange',
    label: '接收时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 已办任务筛选条件
const completedFilters = ref({
  category: 'ALL' as 'ALL' | TaskCategory,
  timeRange: null as [Date, Date] | null,
})

// 已办任务筛选表单配置
const completedFilterFields: FilterField[] = [
  {
    key: 'category',
    label: '任务类别',
    type: 'select',
    placeholder: '请选择类别',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '注册反馈', value: '注册反馈' },
      { label: '风险提醒', value: '风险提醒' },
      { label: '风险处置', value: '风险处置' },
      { label: '事件处置', value: '事件处置' },
    ],
  },
  {
    key: 'timeRange',
    label: '处置时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// Mock数据 - 待办任务
const pendingTasks = ref<PendingTask[]>([
  {
    id: 'PT-2025-001',
    category: '注册反馈',
    title: '企业注册信息填报失败反馈',
    description:
      '您提交的企业注册信息存在问题，请重新填报相关信息。主要问题：测绘资质证书扫描件不清晰，企业类型选择错误。',
    priority: '紧急',
    receivedAt: '2025-08-21 09:30',
    deadline: '2025-08-28 18:00',
    details: {
      applicant: '张经理',
      applicationTime: '2025-08-20 16:45',
      failureReason: '测绘资质证书扫描件不清晰，企业类型选择错误',
      filingDeadline: '2025-08-28 18:00',
    },
  },
  {
    id: 'PT-2025-002',
    category: '风险提醒',
    title: '车端数据采集违规风险提醒',
    description: '检测到您企业车辆在敏感区域进行高频数据采集，存在违规风险，请及时处理。',
    priority: '紧急',
    receivedAt: '2025-08-21 14:15',
    deadline: '2025-08-22 18:00',
    details: {
      riskId: 'VR-202501-0001',
      riskType: '车端/数据采集',
      occurTime: '2025-08-21 10:32',
      stage: '收集',
      level: '高',
      vin: 'LSGJ8A12X34567890',
      suggestion: '立即停止相关采集任务；核查采集清单；对采集数据做紧急脱敏与隔离；提交整改报告。',
    },
  },
  {
    id: 'PT-2025-003',
    category: '事件处置',
    title: '数据安全事件处置要求',
    description: '根据安全监测发现的数据异常传输事件，要求企业立即采取相应处置措施。',
    priority: '一般',
    receivedAt: '2025-08-20 16:30',
    deadline: '2025-08-25 18:00',
  },
])

// Mock数据 - 已办任务
const completedTasks = ref<CompletedTask[]>([
  {
    id: 'CT-2025-001',
    category: '风险提醒',
    title: '云端存储访问控制风险已确认',
    description: '企业已确认云端存储访问控制配置风险，并采取相应整改措施。',
    receivedAt: '2025-08-18 10:20',
    completedAt: '2025-08-19 15:30',
    result: '已确认',
    details: {
      riskId: 'CR-202501-0002',
      riskType: '云端/访问控制',
      occurTime: '2025-08-18 09:15',
      stage: '存储',
      level: '中',
    },
  },
  {
    id: 'CT-2025-002',
    category: '注册反馈',
    title: '测绘资质信息更新完成',
    description: '企业测绘资质信息更新已完成审核，符合相关要求。',
    receivedAt: '2025-08-15 14:00',
    completedAt: '2025-08-16 10:45',
    result: '已处理',
  },
])

// 待办任务统计
const pendingCount = computed(() => pendingTasks.value.length)

// 过滤后的数据
const filteredPendingTasks = computed(() => {
  const { category, priority, timeRange } = pendingFilters.value
  return pendingTasks.value.filter((task) => {
    if (category !== 'ALL' && task.category !== category) return false
    if (priority !== 'ALL' && task.priority !== priority) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const received = new Date(task.receivedAt.replace(/-/g, '/')).getTime()
      if (received < start || received > end) return false
    }
    return true
  })
})

const filteredCompletedTasks = computed(() => {
  const { category, timeRange } = completedFilters.value
  return completedTasks.value.filter((task) => {
    if (category !== 'ALL' && task.category !== category) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const completed = new Date(task.completedAt.replace(/-/g, '/')).getTime()
      if (completed < start || completed > end) return false
    }
    return true
  })
})

// 分页
const pageSize = ref(10)

const pendingCurrentPage = ref(1)
const pendingTotalPages = computed(() =>
  Math.max(1, Math.ceil(filteredPendingTasks.value.length / pageSize.value)),
)
const pagedPendingTasks = computed(() => {
  const start = (pendingCurrentPage.value - 1) * pageSize.value
  return filteredPendingTasks.value.slice(start, start + pageSize.value)
})

const completedCurrentPage = ref(1)
const completedTotalPages = computed(() =>
  Math.max(1, Math.ceil(filteredCompletedTasks.value.length / pageSize.value)),
)
const pagedCompletedTasks = computed(() => {
  const start = (completedCurrentPage.value - 1) * pageSize.value
  return filteredCompletedTasks.value.slice(start, start + pageSize.value)
})

// 处理函数
const handlePendingSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(pendingFilters.value, searchFilters)
  }
  pendingCurrentPage.value = 1
  console.log('待办任务搜索条件:', pendingFilters.value)
}

const resetPendingFilters = () => {
  pendingFilters.value = {
    category: 'ALL',
    priority: 'ALL',
    timeRange: null,
  }
  pendingCurrentPage.value = 1
}

const handleCompletedSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(completedFilters.value, searchFilters)
  }
  completedCurrentPage.value = 1
  console.log('已办任务搜索条件:', completedFilters.value)
}

const resetCompletedFilters = () => {
  completedFilters.value = {
    category: 'ALL',
    timeRange: null,
  }
  completedCurrentPage.value = 1
}

const viewTaskDetail = (task: PendingTask | CompletedTask) => {
  selectedTask.value = task
  detailOpen.value = true
}

const isOverdue = (deadline: string) => {
  const now = new Date().getTime()
  const deadlineTime = new Date(deadline.replace(/-/g, '/')).getTime()
  return deadlineTime < now
}

const viewFilingInfo = () => {
  console.log('跳转到填报信息页面')
  detailOpen.value = false
}

const confirmRisk = () => {
  console.log('确认风险')
  detailOpen.value = false
}

const appealRisk = () => {
  appealForm.value = { reason: '', appellant: '', contact: '' }
  appealOpen.value = true
}

const submitAppeal = () => {
  console.log('提交申诉:', appealForm.value)
  appealOpen.value = false
  detailOpen.value = false
}

// Badge 样式
const categoryVariant = (category: TaskCategory) => {
  switch (category) {
    case '注册反馈':
      return 'default'
    case '风险提醒':
      return 'destructive'
    case '风险处置':
      return 'secondary'
    case '事件处置':
      return 'outline'
    default:
      return 'outline'
  }
}

const priorityVariant = (priority: Priority) => {
  switch (priority) {
    case '紧急':
      return 'destructive'
    case '一般':
      return 'default'
    case '不紧急':
      return 'secondary'
    default:
      return 'outline'
  }
}

const resultVariant = (result: TaskResult) => {
  switch (result) {
    case '已确认':
      return 'default'
    case '已申诉':
      return 'secondary'
    case '已处理':
      return 'default'
    case '已驳回':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
