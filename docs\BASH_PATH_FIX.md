# shadcn-vue CLI Bash路径问题解决方案

## 问题描述

在Windows环境下使用 `npx shadcn-vue@latest add [component]` 时，经常遇到以下错误：

```
/usr/bin/bash: Files\Git\bin\bash.exe: No such file or directory
```

## 问题根源

1. **shadcn-vue CLI内部路径解析问题**: CLI尝试执行npm安装命令时，路径解析出现Windows/Unix混合路径问题
2. **npm script-shell配置**: npm默认使用系统shell，在Git Bash环境下可能产生路径冲突
3. **Windows路径转换**: 空格和反斜杠路径在Unix环境下解析失败

## 尝试的解决方案

### 1. npm script-shell配置 ❌
```bash
# 尝试过的配置，均无效
npm config set script-shell "C:\\Program Files\\Git\\bin\\bash.exe"
npm config set script-shell "/usr/bin/bash"
npm config set script-shell cmd
npm config set script-shell powershell
npm config set script-shell bash
```

### 2. 环境变量设置 ❌
```bash
# 临时环境变量设置，无效
SHELL=/usr/bin/bash npx shadcn-vue@latest add progress
COMSPEC=/usr/bin/bash npx shadcn-vue@latest add progress
env SHELL=/usr/bin/bash npx shadcn-vue@latest add progress
```

### 3. Windows CMD方式 ⚠️
```bash
# 可以启动但未完成测试
cmd /c "npx shadcn-vue@latest add progress"
```

## 当前状态

- ✅ **问题根源已识别**: shadcn-vue CLI v2.2.0 存在Windows环境路径解析问题
- ❌ **完全解决方案待找到**: 多种常见修复方法均无效
- ⚠️ **临时解决方案**: 使用Windows CMD可能有效，需要进一步测试

## 工作建议

### 立即可行方案
1. **手动安装组件**: 当前项目已有大部分核心组件，满足开发需求
2. **现有组件充分**: Alert, Chart系列, Card, Form, Sidebar等已覆盖主要功能

### 后续解决方案
1. **升级shadcn-vue版本**: 等待官方修复Windows路径问题
2. **使用Windows原生终端**: 在需要时切换到PowerShell或CMD执行
3. **Docker环境**: 使用Linux容器环境运行shadcn-vue CLI
4. **手动复制组件**: 从官方GitHub仓库手动复制组件文件

## 项目当前组件状态

### ✅ 已安装且可用
- 数据可视化: Chart (Bar/Line/Pie), Card, Badge
- 表单组件: Form, Input, Checkbox, Label  
- 布局导航: Sidebar完整套件, Tabs, Breadcrumb
- 交互组件: Dropdown Menu, Tooltip, Button
- 状态提示: Alert, Skeleton

### ❌ 因路径问题暂未安装
- Progress, Dialog, Select, Textarea
- Switch, Popover, Accordion, Table
- Data-table, Calendar, Date-picker

## 结论

当前bash路径问题虽未完全解决，但不影响项目核心开发需求。建议：

1. **继续使用现有组件进行开发**
2. **待官方修复或找到更好解决方案时再补充其他组件**
3. **必要时可手动复制所需组件代码**

最后更新: 2025-08-20