<template>
  <ContentPageLayout>
    <div class="content-detail-page">
      <div class="container">
        <div class="content-header">
          <nav class="breadcrumb">
            <router-link to="/" class="breadcrumb-link">首页</router-link>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-current">新闻详情</span>
          </nav>
          <button @click="$router.back()" class="back-button">← 返回</button>
        </div>

        <article class="content-article">
          <div class="article-meta">
            <span class="article-category">新闻动态</span>
            <span class="article-date">{{ formatDate(content.publishDate) }}</span>
            <span class="article-views">阅读量: {{ content.views }}</span>
          </div>
          <h1 class="article-title">{{ content.title }}</h1>
          <p class="article-subtitle">{{ content.subtitle }}</p>

          <div class="article-content" v-html="content.content"></div>
        </article>
      </div>
    </div>
  </ContentPageLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ContentPageLayout from './ContentPageLayout.vue'

const route = useRoute()

const content = ref({
  id: '1',
  title: '智能网联汽车数据安全监管政策新动态',
  subtitle: '国家相关部门发布最新政策指导意见，加强行业数据安全规范',
  publishDate: '2024-12-15',
  views: 1856,
  category: '新闻动态',
  content: `
    <h2>政策背景</h2>
    <p>随着智能网联汽车产业的快速发展，车辆产生的时空数据量呈指数级增长。这些数据在推动技术创新的同时，也带来了数据安全和隐私保护的挑战。</p>
    
    <h2>主要内容</h2>
    <p>新政策明确了以下几个方面的要求：</p>
    <ul>
      <li>建立健全数据安全管理制度</li>
      <li>加强数据分类分级管理</li>
      <li>完善数据安全技术保护措施</li>
      <li>建立数据安全风险评估机制</li>
    </ul>
    
    <h2>实施意义</h2>
    <p>这一政策的实施将有助于规范智能网联汽车行业的数据处理行为，保护用户隐私权益，维护国家数据安全，促进行业健康发展。</p>
  `,
})

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

onMounted(() => {
  const id = route.params.id as string
  if (id && id !== 'default') {
    // 根据ID加载具体的内容
    // 这里可以调用API获取数据
  }
})
</script>

<style scoped>
.content-detail-page {
  width: 100%;
}

.container {
  width: 100%;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.breadcrumb-link {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--text-color-secondary);
}

.breadcrumb-current {
  color: var(--text-color-secondary);
}

.back-button {
  padding: 0.5rem 1rem;
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.content-article {
  background: var(--card-background);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-color);
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.article-category {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.article-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.article-subtitle {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin: 0 0 2rem 0;
  font-style: italic;
}

.article-content {
  color: var(--text-color);
  line-height: 1.8;
}

.article-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
  color: var(--text-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.article-content p {
  margin: 1rem 0;
  text-align: justify;
}

.article-content ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.article-content li {
  margin: 0.5rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-detail-page {
    padding: 1rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .content-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .article-title {
    font-size: 1.5rem;
  }

  .content-article {
    padding: 1.5rem;
  }
}
</style>
