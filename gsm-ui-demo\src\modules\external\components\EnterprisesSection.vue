<template>
  <section class="gsm-external-module enterprises-section">
    <div class="external-container">
      <div class="section-header">
        <h2 class="section-title">合规示范企业</h2>
        <p class="section-subtitle">
          行业龙头企业率先响应国家政策，积极接入平台开展数据安全合规工作
        </p>
      </div>

      <!-- 分类选项卡 -->
      <div class="category-tabs">
        <button
          v-for="category in categories"
          :key="category.key"
          :class="['tab-button', { active: activeCategory === category.key }]"
          @click="setActiveCategory(category.key)"
        >
          <span class="tab-icon">
            <ElementIcon :name="category.icon" :size="20" :color="'currentColor'" />
          </span>
          <span class="tab-text">{{ category.name }}</span>
          <span class="tab-count">({{ getEnterprisesByType(category.type).length }})</span>
        </button>
      </div>

      <!-- 企业展示区域 -->
      <div class="enterprises-display">
        <transition name="fade" mode="out-in">
          <div :key="activeCategory" class="enterprises-grid">
            <div
              v-for="(enterprise, index) in currentEnterprises"
              :key="enterprise.id"
              class="enterprise-item"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="enterprise-logo">
                <img
                  :src="`/assets/logos/${enterprise.logo}.svg`"
                  :alt="enterprise.name"
                  @error="handleImageError"
                />
              </div>
              <span class="enterprise-name">{{ enterprise.name }}</span>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ElementIcon from './ElementIcon.vue'

// 当前激活的分类
const activeCategory = ref('automotive')

// 分类定义
const categories = [
  { key: 'automotive', name: '整车厂商', type: '汽车企业', icon: 'truck' },
  { key: 'map', name: '地图服务商', type: '地图服务商', icon: 'map-location' },
  { key: 'autonomous', name: '智驾方案提供商', type: '智驾方案提供商', icon: 'cpu' },
]

// 设置激活分类
const setActiveCategory = (categoryKey: string) => {
  activeCategory.value = categoryKey
}

// 获取当前分类的企业列表
const currentEnterprises = computed(() => {
  const currentCategory = categories.find((cat) => cat.key === activeCategory.value)
  return currentCategory ? getEnterprisesByType(currentCategory.type) : []
})

const getEnterprisesByType = (type: string) => {
  return enterprises.filter((enterprise) => enterprise.type === type)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 如果图片加载失败，显示企业名称的首字母
  const enterprise = enterprises.find((e) => img.alt === e.name)
  if (enterprise) {
    img.style.display = 'none'
    const parent = img.parentElement
    if (parent) {
      parent.innerHTML = enterprise.name.charAt(0)
      parent.classList.add('fallback-logo')
    }
  }
}

const enterprises = [
  // 传统汽车厂商
  { id: 1, name: '中国一汽', type: '汽车企业', logo: 'faw' },
  { id: 2, name: '上汽集团', type: '汽车企业', logo: 'saic' },
  { id: 3, name: '东风汽车', type: '汽车企业', logo: 'dongfeng' },
  { id: 4, name: '北汽集团', type: '汽车企业', logo: 'baic' },
  { id: 5, name: '广汽集团', type: '汽车企业', logo: 'gac' },
  { id: 6, name: '长安汽车', type: '汽车企业', logo: 'changan' },
  { id: 7, name: '吉利汽车', type: '汽车企业', logo: 'geely' },
  { id: 8, name: '长城汽车', type: '汽车企业', logo: 'gwm' },

  // 新能源汽车厂商
  { id: 9, name: '比亚迪股份有限公司', type: '汽车企业', logo: 'byd' },
  { id: 10, name: '蔚来汽车', type: '汽车企业', logo: 'nio' },
  { id: 11, name: '小鹏汽车', type: '汽车企业', logo: 'xpeng' },
  { id: 12, name: '理想汽车', type: '汽车企业', logo: 'li' },
  { id: 13, name: '零跑汽车', type: '汽车企业', logo: 'leapmotor' },
  { id: 14, name: '小米汽车', type: '汽车企业', logo: 'xiaomi' },

  // 地图服务商
  { id: 15, name: '百度地图', type: '地图服务商', logo: 'baidu' },
  { id: 16, name: '高德地图', type: '地图服务商', logo: 'amap' },
  { id: 17, name: '腾讯地图', type: '地图服务商', logo: 'tencent' },
  { id: 18, name: '四维图新', type: '地图服务商', logo: 'navinfo' },

  // 智驾方案提供商
  { id: 19, name: '华为技术', type: '智驾方案提供商', logo: 'huawei' },
  { id: 20, name: '地平线', type: '智驾方案提供商', logo: 'horizon' },
  { id: 21, name: '商汤科技', type: '智驾方案提供商', logo: 'sensetime' },
  { id: 22, name: '广州文远知行科技有限公司', type: '智驾方案提供商', logo: 'weride' },
  { id: 23, name: '北京车网科技发展有限公司', type: '智驾方案提供商', logo: 'default' },
  { id: 24, name: '浙江极氪智能科技有限公司', type: '汽车企业', logo: 'default' },
]
</script>

<style scoped>
/* 服务企业区域 */
.enterprises-section {
  padding: 6rem 0;
  background: var(--external-surface-color);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--external-text-color);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--external-primary-color) 0%,
    var(--external-primary-hover) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.125rem;
  margin-top: 1rem;
  color: var(--external-text-color-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 分类选项卡 */
.category-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.2rem 2.5rem;
  background: var(--external-card-bg);
  border: 1px solid var(--external-border-color);
  border-radius: 16px;
  color: var(--external-text-color-secondary);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--external-glow-color), transparent);
  transition: left 0.5s ease;
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button:hover {
  border-color: var(--external-primary-color);
  color: var(--external-primary-color);
  transform: translateY(-3px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.1),
    0 2px 6px rgba(0, 128, 204, 0.2);
  background: var(--external-primary-bg);
}

.tab-button.active {
  background: linear-gradient(135deg, var(--external-primary-color), var(--external-primary-hover));
  border-color: var(--external-primary-color);
  color: white;
  box-shadow:
    0 6px 20px rgba(0, 128, 204, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tab-button.active:hover {
  transform: translateY(-4px);
  box-shadow:
    0 8px 25px rgba(0, 128, 204, 0.5),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.tab-icon {
  font-size: 1.2rem;
}

.tab-text {
  font-weight: 600;
}

.tab-count {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.3rem 0.6rem;
  border-radius: 8px;
  min-width: 1.8rem;
  text-align: center;
  line-height: 1;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

/* 激活状态下的统计数字 */
.tab-button.active .tab-count {
  background: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.95);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 非激活状态下的统计数字 */
.tab-button:not(.active) .tab-count {
  background: var(--external-primary-color);
  color: white;
  font-weight: 700;
  box-shadow: 0 2px 6px rgba(0, 128, 204, 0.3);
}

/* 悬停效果 */
.tab-button:not(.active):hover .tab-count {
  background: var(--external-primary-hover);
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 128, 204, 0.4);
}

/* 企业展示区域 */
.enterprises-display {
  background: var(--external-card-bg);
  border-radius: 16px;
  padding: 2.5rem;
  border: 1px solid var(--external-border-color);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: fit-content;
}

.enterprises-display:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.enterprises-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, min(250px, 1fr)));
  gap: 1.5rem;
  align-content: start;
  justify-content: center;
  max-width: 100%;
}

/* 切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.enterprise-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1.5rem;
  background: var(--external-surface-color);
  border: 1px solid var(--external-border-light);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enterprise-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--external-primary-color), var(--external-primary-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enterprise-item:hover {
  border-color: var(--external-primary-color);
  background: var(--external-card-bg);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.enterprise-item:hover::before {
  opacity: 1;
}

.enterprise-logo {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background: var(--external-card-bg);
  border: 1px solid var(--external-border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--external-primary-color);
  transition: all 0.3s ease;
  overflow: hidden;
}

.enterprise-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 8px;
}

.enterprise-logo.fallback-logo {
  background: var(--external-primary-color);
  color: white;
  font-size: 1.25rem;
}

.enterprise-item:hover .enterprise-logo {
  border-color: var(--external-primary-color);
  transform: scale(1.05);
}

.enterprise-name {
  font-weight: 600;
  color: var(--external-text-color);
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Light theme adjustments */
.gsm-external-module[data-theme='light'] .enterprises-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.gsm-external-module[data-theme='light'] .enterprises-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid-light-enterprises" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="%23cbd5e1" stroke-width="0.5" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23grid-light-enterprises)"/></svg>');
  opacity: 0.5;
  z-index: 0;
}

.gsm-external-module[data-theme='light'] .external-container {
  position: relative;
  z-index: 1;
}

.gsm-external-module[data-theme='light'] .section-title {
  color: #1e293b;
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.gsm-external-module[data-theme='light'] .section-subtitle {
  color: #64748b;
}

.gsm-external-module[data-theme='light'] .tab-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.6);
  color: #64748b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.gsm-external-module[data-theme='light'] .tab-button:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.08),
    0 2px 6px rgba(59, 130, 246, 0.15);
}

.gsm-external-module[data-theme='light'] .tab-button.active {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  border-color: #3b82f6;
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.08);
}

.gsm-external-module[data-theme='light'] .tab-button.active:hover {
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

.gsm-external-module[data-theme='light'] .tab-button:not(.active) .tab-count {
  background: #3b82f6;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.25);
}

.gsm-external-module[data-theme='light'] .tab-button:not(.active):hover .tab-count {
  background: #2563eb;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
}

.gsm-external-module[data-theme='light'] .enterprises-display {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.gsm-external-module[data-theme='light'] .enterprise-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(203, 213, 225, 0.4);
}

.gsm-external-module[data-theme='light'] .enterprise-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.08);
}

.gsm-external-module[data-theme='light'] .enterprise-logo {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.5);
  color: #3b82f6;
}

.gsm-external-module[data-theme='light'] .enterprise-item:hover .enterprise-logo {
  border-color: #3b82f6;
}

.gsm-external-module[data-theme='light'] .enterprise-name {
  color: #1e293b;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .enterprises-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enterprises-section {
    padding: 4rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .category-tabs {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .tab-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
    padding: 0.875rem 1.5rem;
  }

  .enterprises-display {
    padding: 1.5rem;
    min-height: fit-content;
  }

  .enterprises-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
  }

  .enterprise-item {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .tab-icon {
    font-size: 1rem;
  }

  .enterprises-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}
</style>
