import { defineStore } from 'pinia'
import { ref, computed, watchEffect } from 'vue'
import { useColorMode, usePreferredDark } from '@vueuse/core'

export const useThemeStore = defineStore('theme', () => {
  // System dark mode preference
  const prefersDark = usePreferredDark()
  
  // Configure useColorMode
  const mode = useColorMode({
    selector: 'html',
    attribute: 'class',
    initialValue: 'auto',
    storageKey: 'vueuse-color-scheme',
    emitAuto: true,
    disableTransition: false
  })

  // Computed property to get the actual theme value
  const theme = computed(() => {
    if (mode.value === 'auto') {
      return prefersDark.value ? 'dark' : 'light'
    }
    return mode.value
  })

  // Watch for changes and ensure proper class application
  watchEffect(() => {
    const html = document.documentElement
    if (mode.value === 'auto') {
      if (prefersDark.value) {
        html.classList.add('dark')
        html.classList.remove('light')
      } else {
        html.classList.add('light')
        html.classList.remove('dark')
      }
    } else if (mode.value === 'dark') {
      html.classList.add('dark')
      html.classList.remove('light')
    } else {
      html.classList.add('light')
      html.classList.remove('dark')
    }
  })

  // Toggle theme between light and dark
  const toggleTheme = () => {
    if (mode.value === 'light') {
      mode.value = 'dark'
    } else if (mode.value === 'dark') {
      mode.value = 'light'
    } else {
      // If auto, switch to the opposite of current
      mode.value = prefersDark.value ? 'light' : 'dark'
    }
  }

  // Set theme to a specific value
  const setTheme = (value: 'light' | 'dark' | 'auto') => {
    mode.value = value
  }

  return {
    mode,
    theme,
    prefersDark,
    toggleTheme,
    setTheme
  }
})