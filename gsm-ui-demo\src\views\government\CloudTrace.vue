<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
      <p class="text-muted-foreground">
        云端安全事件溯源分析，支持七阶段处理活动追溯与责任主体链路分析
      </p>
    </div>

    <!-- 主体两列布局 -->
    <div class="grid grid-cols-12 gap-4">
      <!-- 左侧：溯源记录历史（全页统一位置） -->
      <div class="col-span-12 lg:col-span-3">
        <Card class="h-full">
          <CardHeader class="pb-3">
            <CardTitle class="text-sm">溯源记录历史</CardTitle>
          </CardHeader>
          <CardContent class="space-y-3 overflow-y-auto max-h-[calc(100vh-220px)] pr-2">
            <div
              v-for="(record, index) in traceRecords"
              :key="index"
              class="border rounded-lg p-3 hover:bg-muted/40 cursor-pointer"
              @click="viewRecord(record)"
            >
              <div class="flex items-start justify-between">
                <div>
                  <div class="text-base font-semibold">{{ record.packetInfo.id }}</div>
                  <div class="text-xs text-muted-foreground mt-1">
                    {{ record.packetInfo.enterprise || record.packetInfo.vin }} |
                    {{ record.packetInfo.dataType }}
                  </div>
                </div>
                <Badge variant="outline">{{
                  record.traceType === 'vehicle' ? '车端' : '云端'
                }}</Badge>
              </div>
              <div class="text-xs text-muted-foreground mt-1">
                {{ new Date(record.timestamp).toLocaleString('zh-CN') }}
              </div>
            </div>
            <div v-if="traceRecords.length === 0" class="text-center py-8 text-muted-foreground">
              暂无溯源记录
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：详情区域（风险信息卡片 + 溯源可视化 + 七阶段详情 + 责任主体关联） -->
      <div class="col-span-12 lg:col-span-9 space-y-6">
        <!-- 风险信息卡片 -->
        <Card v-if="selectedRisk">
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span class="flex items-center gap-2">
                <Cloud class="w-5 h-5 text-primary" />
                云端安全风险事件信息
              </span>
              <div class="flex items-center gap-2">
                <Badge v-if="selectedRisk" variant="outline">风险ID: {{ selectedRisk.id }}</Badge>
                <Badge v-if="currentTraceId" variant="secondary"
                  >会话ID: {{ currentTraceId }}</Badge
                >
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <div class="text-sm text-muted-foreground">企业名称</div>
                <div class="font-medium">{{ selectedRisk.enterprise }}</div>
              </div>
              <div>
                <div class="text-sm text-muted-foreground">企业类型</div>
                <Badge variant="secondary">{{ selectedRisk.enterpriseType }}</Badge>
              </div>
              <div>
                <div class="text-sm text-muted-foreground">处理阶段</div>
                <Badge variant="outline">{{ selectedRisk.stage }}</Badge>
              </div>
              <div>
                <div class="text-sm text-muted-foreground">风险等级</div>
                <Badge :variant="levelVariant(selectedRisk.level)">{{ selectedRisk.level }}</Badge>
              </div>
              <div>
                <div class="text-sm text-muted-foreground">发生时间</div>
                <div class="font-medium">{{ selectedRisk.occurAt }}</div>
              </div>
              <div>
                <div class="text-sm text-muted-foreground">当前状态</div>
                <Badge :variant="statusVariant(selectedRisk.status)">{{
                  selectedRisk.status
                }}</Badge>
              </div>
              <div class="col-span-2">
                <div class="text-sm text-muted-foreground">风险描述</div>
                <div class="text-sm line-clamp-2" :title="selectedRisk.description">
                  {{ selectedRisk.description }}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 没有风险时的提示 -->
        <Card v-if="!selectedRisk">
          <CardContent class="py-12">
            <div class="text-center space-y-4">
              <Search class="w-16 h-16 text-muted-foreground mx-auto" />
              <div>
                <h3 class="text-lg font-semibold mb-2">未选择风险事件</h3>
                <p class="text-muted-foreground mb-4">
                  请从云端安全风险页面选择具体的风险事件进行溯源分析
                </p>
                <Button @click="goToRiskManagement">
                  <ArrowLeft class="w-4 h-4 mr-1" />
                  返回云端安全风险
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 溯源可视化 -->
        <Card v-if="selectedRisk" class="min-h-[720px]">
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span class="flex items-center gap-2">
                <Network class="w-5 h-5" />
                云端数据处理链路溯源
              </span>
              <div class="flex gap-2">
                <Button variant="outline" size="sm" @click="showProcessFlow">
                  <Workflow class="w-4 h-4 mr-1" />
                  处理流程图
                </Button>
                <Button variant="outline" size="sm" @click="exportTraceReport">
                  <FileDown class="w-4 h-4 mr-1" />
                  导出报告
                </Button>
                <Button
                  size="sm"
                  @click="triggerSaveRecord"
                  class="bg-primary text-primary-foreground"
                >
                  保存溯源信息
                </Button>
              </div>
            </CardTitle>
            <CardDescription>
              可视化展示云端数据处理的七阶段完整链路，支持责任主体关联分析
            </CardDescription>
          </CardHeader>
          <CardContent class="p-0">
            <TraceVisualization
              ref="traceVisRef"
              :title="`云端风险溯源 - ${selectedRisk.id}`"
              :subtitle="`${selectedRisk.enterprise} | ${selectedRisk.enterpriseType}`"
              trace-type="cloud"
              @save-record="handleSaveRecord"
              @trace-started="onTraceStarted"
              class="h-[720px]"
            />
          </CardContent>
        </Card>

        <!-- 七阶段处理活动详情 -->
        <Card v-if="selectedRisk">
          <CardHeader>
            <CardTitle>数据处理活动溯源链路</CardTitle>
            <CardDescription
              >展示收集/存储/传输/加工/提供/公开/销毁七阶段的详细处理信息</CardDescription
            >
          </CardHeader>
          <CardContent>
            <div class="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[80px]">序号</TableHead>
                    <TableHead>处理阶段</TableHead>
                    <TableHead>处理活动</TableHead>
                    <TableHead>违规行为</TableHead>
                    <TableHead class="min-w-[120px]">发生时间</TableHead>
                    <TableHead>责任主体</TableHead>
                    <TableHead>支撑单位</TableHead>
                    <TableHead>操作日志</TableHead>
                    <TableHead class="w-[120px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow
                    v-for="(item, index) in cloudViolationData"
                    :key="index"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{ index + 1 }}</TableCell>
                    <TableCell>
                      <Badge :variant="getStageVariant(item.stage)">{{ item.stage }}</Badge>
                    </TableCell>
                    <TableCell>{{ item.activity }}</TableCell>
                    <TableCell class="max-w-[200px]">
                      <div class="text-sm line-clamp-2">{{ item.violation }}</div>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">{{ item.timestamp }}</TableCell>
                    <TableCell>
                      <div class="space-y-1">
                        <div class="font-medium text-sm">{{ item.responsible.name }}</div>
                        <div class="text-xs text-muted-foreground">{{ item.responsible.role }}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div class="space-y-1" v-if="item.supportUnit">
                        <div class="font-medium text-sm">{{ item.supportUnit.name }}</div>
                        <div class="text-xs text-muted-foreground">{{ item.supportUnit.type }}</div>
                      </div>
                      <span v-else class="text-muted-foreground">-</span>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="ghost"
                        @click="showOperationLog(item)"
                        class="h-6 px-2 text-xs"
                      >
                        查看日志
                      </Button>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" @click="drillDown(item)">
                        深度分析
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <!-- 责任主体关联图 -->
        <Card v-if="selectedRisk">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Users class="w-5 h-5" />
              责任主体关联分析
            </CardTitle>
            <CardDescription>展示涉及此次风险事件的所有责任主体及其关联关系</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="entity in responsibleEntities"
                :key="entity.id"
                class="border rounded-lg p-4 space-y-3"
              >
                <div class="flex items-center justify-between">
                  <Badge :variant="getEntityVariant(entity.type)">{{ entity.type }}</Badge>
                  <Badge variant="outline" class="text-xs">{{ entity.level }}</Badge>
                </div>
                <div class="space-y-2">
                  <div class="font-medium">{{ entity.name }}</div>
                  <div class="text-sm text-muted-foreground">
                    统一社会信用代码：{{ entity.creditCode }}
                  </div>
                  <div class="text-sm text-muted-foreground">
                    参与阶段：{{ entity.stages.join('、') }}
                  </div>
                  <div class="text-sm">责任描述：{{ entity.responsibility }}</div>
                </div>
                <div class="flex justify-end">
                  <Button size="sm" variant="ghost" @click="viewEntityDetail(entity)">
                    查看详情
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      <!-- /右侧详情 -->
    </div>
    <!-- /两列布局 -->

    <!-- 处理流程图弹窗 -->
    <Dialog v-model:open="flowDialogOpen">
      <DialogContent class="max-w-5xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>云端数据处理流程图</DialogTitle>
          <DialogDescription> 七阶段数据处理活动的详细流程示意图 </DialogDescription>
        </DialogHeader>
        <div class="h-[500px] bg-muted rounded-md flex items-center justify-center">
          <div class="text-center space-y-2">
            <Workflow class="w-16 h-16 text-muted-foreground mx-auto" />
            <p class="text-muted-foreground">流程图组件占位</p>
            <p class="text-xs text-muted-foreground">实际项目中可集成流程图绘制组件</p>
          </div>
        </div>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="flowDialogOpen = false">关闭</Button>
          <Button @click="exportFlowChart">导出流程图</Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 操作日志弹窗 -->
    <Dialog v-model:open="logDialogOpen">
      <DialogContent class="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>操作日志详情</DialogTitle>
          <DialogDescription> {{ selectedViolation?.stage }} 阶段的详细操作记录 </DialogDescription>
        </DialogHeader>
        <div class="max-h-[400px] overflow-y-auto">
          <div v-if="selectedViolation" class="space-y-3">
            <div
              v-for="(log, index) in selectedViolation.logs"
              :key="index"
              class="border rounded-lg p-3 space-y-2"
            >
              <div class="flex justify-between items-start">
                <div class="font-medium text-sm">{{ log.action }}</div>
                <Badge
                  :variant="
                    log.level === 'ERROR'
                      ? 'destructive'
                      : log.level === 'WARN'
                        ? 'secondary'
                        : 'outline'
                  "
                >
                  {{ log.level }}
                </Badge>
              </div>
              <div class="text-sm text-muted-foreground">{{ log.details }}</div>
              <div class="flex justify-between text-xs text-muted-foreground">
                <span>操作人: {{ log.operator }}</span>
                <span>{{ log.timestamp }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-end">
          <Button variant="outline" @click="logDialogOpen = false">关闭</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ComponentPublicInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeft,
  Cloud,
  FileDown,
  History,
  Network,
  Search,
  Users,
  Workflow,
} from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import TraceVisualization from '@/components/TraceVisualization.vue'

type RiskLevel = '高' | '中' | '低'
type HandleStatus = '未处理' | '处理中' | '已处理'
type CloudStage = '收集' | '存储' | '传输' | '加工' | '提供' | '公开' | '销毁'

interface CloudRiskItem {
  id: string
  enterprise: string
  enterpriseType: string
  stage: CloudStage
  level: RiskLevel
  description: string
  status: HandleStatus
  occurAt: string
  resolvedAt?: string
}

interface CloudViolationItem {
  stage: CloudStage
  activity: string
  violation: string
  timestamp: string
  responsible: {
    name: string
    role: string
  }
  supportUnit?: {
    name: string
    type: string
  }
  logs: OperationLog[]
}

interface OperationLog {
  action: string
  details: string
  operator: string
  timestamp: string
  level: 'INFO' | 'WARN' | 'ERROR'
}

interface ResponsibleEntity {
  id: string
  type: '主体企业' | '支撑单位' | '第三方服务商' | '监管机构'
  name: string
  creditCode: string
  level: '直接责任' | '间接责任' | '监管责任'
  stages: CloudStage[]
  responsibility: string
}

interface TraceRecord {
  packetInfo: any
  traceSequence: any[]
  timestamp: string
  traceType: 'vehicle' | 'cloud'
}

const route = useRoute()
const router = useRouter()

// 引用 TraceVisualization 实例与当前会话ID
const traceVisRef =
  ref<
    ComponentPublicInstance<{ saveRecord: () => void; replayRecord: (record: TraceRecord) => void }>
  >()
const currentTraceId = ref<string | null>(null)

// 弹窗状态
const flowDialogOpen = ref(false)
const logDialogOpen = ref(false)

// 选中的风险和违规数据
const selectedRisk = ref<CloudRiskItem | null>(null)
const selectedViolation = ref<CloudViolationItem | null>(null)

// 溯源记录
const traceRecords = ref<TraceRecord[]>([])

// Mock 数据 - 云端风险数据
const mockCloudRiskData: Record<string, CloudRiskItem> = {
  'CR-202501-0001': {
    id: 'CR-202501-0001',
    enterprise: '北京数据科技有限公司',
    enterpriseType: '数据服务商',
    stage: '加工',
    level: '高',
    description:
      '在数据加工过程中违规使用原始地理坐标信息，未按要求进行脱敏处理，存在敏感信息泄露风险。',
    status: '处理中',
    occurAt: '2025-08-20 15:24',
  },
}

// 云端七阶段违规数据
const cloudViolationData = computed<CloudViolationItem[]>(() => {
  if (!selectedRisk.value) return []

  return [
    {
      stage: '收集',
      activity: '数据汇聚收集',
      violation: '从车端收集数据时未验证数据来源合法性，接收了包含敏感地理信息的违规数据包',
      timestamp: '2025-08-20 15:20:15',
      responsible: {
        name: '北京数据科技有限公司',
        role: '数据收集方',
      },
      supportUnit: {
        name: '云端数据网关服务',
        type: '技术支撑单位',
      },
      logs: [
        {
          action: '数据接收处理',
          details: '从车载终端接收数据包，进行初步格式验证',
          operator: '数据接收服务',
          timestamp: '2025-08-20 15:20:15',
          level: 'INFO',
        },
        {
          action: '合规性检查失败',
          details: '合规检查服务异常，跳过了敏感数据验证环节',
          operator: '合规检查服务',
          timestamp: '2025-08-20 15:20:18',
          level: 'ERROR',
        },
      ],
    },
    {
      stage: '存储',
      activity: '分布式存储',
      violation: '将含有敏感信息的数据直接存储到生产环境，违反数据分级存储要求',
      timestamp: '2025-08-20 15:21:03',
      responsible: {
        name: '云存储服务提供方',
        role: '存储服务提供商',
      },
      logs: [
        {
          action: '数据分级判断',
          details: '对接收数据进行敏感级别判断',
          operator: '数据分级服务',
          timestamp: '2025-08-20 15:21:00',
          level: 'INFO',
        },
        {
          action: '存储策略违规',
          details: '敏感数据被错误分配到普通存储区域',
          operator: '存储调度服务',
          timestamp: '2025-08-20 15:21:03',
          level: 'ERROR',
        },
      ],
    },
    {
      stage: '传输',
      activity: '内部数据流转',
      violation: '在内部系统间传输时使用了不安全的传输协议，存在中间人攻击风险',
      timestamp: '2025-08-20 15:22:30',
      responsible: {
        name: '内部网络管理部门',
        role: '网络管理方',
      },
      logs: [
        {
          action: '传输协议选择',
          details: '系统自动选择传输协议进行内部数据流转',
          operator: '传输调度器',
          timestamp: '2025-08-20 15:22:25',
          level: 'INFO',
        },
        {
          action: '协议降级',
          details: '因网络拥塞，传输协议从TLS 1.3降级为HTTP',
          operator: '网络优化模块',
          timestamp: '2025-08-20 15:22:30',
          level: 'WARN',
        },
      ],
    },
    {
      stage: '加工',
      activity: 'AI模型训练',
      violation: '在模型训练过程中直接使用原始敏感数据，未进行脱敏和匿名化处理',
      timestamp: '2025-08-20 15:24:45',
      responsible: {
        name: 'AI算法团队',
        role: '数据加工方',
      },
      supportUnit: {
        name: 'GPU计算集群服务商',
        type: '计算资源提供方',
      },
      logs: [
        {
          action: '数据预处理',
          details: '对训练数据进行清洗和格式化处理',
          operator: '数据预处理服务',
          timestamp: '2025-08-20 15:24:30',
          level: 'INFO',
        },
        {
          action: '脱敏处理跳过',
          details: '为保证训练效果，跳过了地理坐标脱敏处理步骤',
          operator: '模型训练服务',
          timestamp: '2025-08-20 15:24:45',
          level: 'ERROR',
        },
      ],
    },
    {
      stage: '提供',
      activity: 'API服务接口',
      violation: '通过API接口向第三方提供数据时，未充分验证第三方资质和用途',
      timestamp: '2025-08-20 15:26:12',
      responsible: {
        name: 'API网关管理方',
        role: '接口服务提供方',
      },
      logs: [
        {
          action: 'API调用验证',
          details: '验证第三方API调用凭证和权限',
          operator: 'API网关',
          timestamp: '2025-08-20 15:26:10',
          level: 'INFO',
        },
        {
          action: '资质验证不充分',
          details: '未深度验证第三方企业的数据使用资质',
          operator: '权限验证服务',
          timestamp: '2025-08-20 15:26:12',
          level: 'WARN',
        },
      ],
    },
  ]
})

// 责任主体实体
const responsibleEntities = computed<ResponsibleEntity[]>(() => {
  if (!selectedRisk.value) return []

  return [
    {
      id: 'entity-001',
      type: '主体企业',
      name: '北京数据科技有限公司',
      creditCode: '91110000MA0123456X',
      level: '直接责任',
      stages: ['收集', '存储', '加工'],
      responsibility: '作为数据处理主体，负责确保整个数据处理流程的合规性，对违规行为承担直接责任',
    },
    {
      id: 'entity-002',
      type: '支撑单位',
      name: '云端数据网关服务',
      creditCode: '91110000MA0234567Y',
      level: '间接责任',
      stages: ['收集', '传输'],
      responsibility: '提供数据接收和传输技术支撑，对技术实现过程中的安全漏洞承担间接责任',
    },
    {
      id: 'entity-003',
      type: '第三方服务商',
      name: 'GPU计算集群服务商',
      creditCode: '91110000MA0345678Z',
      level: '间接责任',
      stages: ['加工'],
      responsibility: '提供AI模型训练的计算资源，对计算环境的安全配置承担间接责任',
    },
    {
      id: 'entity-004',
      type: '监管机构',
      name: '北京市数据安全监管局',
      creditCode: '91110000000000001A',
      level: '监管责任',
      stages: ['收集', '存储', '传输', '加工', '提供'],
      responsibility: '负责监督数据处理活动的合规性，对监管不到位承担监管责任',
    },
  ]
})

// 根据路由参数设置选中的风险
onMounted(() => {
  const riskId = route.query.riskId as string
  if (riskId && mockCloudRiskData[riskId]) {
    selectedRisk.value = mockCloudRiskData[riskId]
  } else {
    // 没有传入 riskId 时默认选中一条示例数据，确保可视化面板正常展示
    const first = Object.values(mockCloudRiskData)[0]
    if (first) selectedRisk.value = first
  }

  // 从localStorage加载溯源记录
  const savedRecords = localStorage.getItem('traceRecords')
  if (savedRecords) {
    try {
      const all = JSON.parse(savedRecords)
      // 仅展示云端记录
      traceRecords.value = Array.isArray(all)
        ? all.filter((r: any) => r?.traceType === 'cloud')
        : []
    } catch (e) {
      console.error('Failed to parse trace records:', e)
    }
  }

  // 若无任何记录，则注入一批 mock 溯源记录（仅云端）
  if (!savedRecords) {
    const now = new Date()
    const pad = (n: number) => (n < 10 ? '0' + n : '' + n)
    const ts = (offsetMin: number) => {
      const d = new Date(now.getTime() - offsetMin * 60000)
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
    }

    type TraceRecordLocal = TraceRecord & { packetInfo: any }

    const mkVehicle = (i: number): TraceRecordLocal => ({
      packetInfo: {
        id: `PKT-V-${i.toString().padStart(3, '0')}`,
        vin: `LSGWB54E8KS12${(300 + i).toString()}`,
        dataType: i % 2 === 0 ? '驾驶行为' : '位置轨迹',
      },
      traceSequence: [
        { step: 1, timestamp: ts(60 + i), node: '车载终端', action: '采集数据', location: '现场' },
        { step: 2, timestamp: ts(58 + i), node: '边缘网关', action: '预处理', location: '边缘' },
        { step: 3, timestamp: ts(55 + i), node: '云端接入', action: '上传', location: '云端' },
        { step: 4, timestamp: ts(52 + i), node: '云端存储', action: '入库', location: '云端' },
      ],
      timestamp: ts(50 + i),
      traceType: 'vehicle',
    })

    const mkCloud = (i: number): TraceRecordLocal => ({
      packetInfo: {
        id: `PKT-C-${i.toString().padStart(3, '0')}`,
        enterprise: i % 2 === 0 ? '北京数据科技有限公司' : '华北云服务商',
        dataType: i % 2 === 0 ? '脱敏坐标' : '原始坐标',
      },
      traceSequence: [
        { step: 1, timestamp: ts(40 + i), node: '收集', action: '汇聚', location: '云端入口' },
        { step: 2, timestamp: ts(38 + i), node: '存储', action: '分级存储', location: '对象存储' },
        { step: 3, timestamp: ts(35 + i), node: '加工', action: '模型训练', location: '计算集群' },
        { step: 4, timestamp: ts(32 + i), node: '提供', action: 'API提供', location: 'API网关' },
      ],
      timestamp: ts(30 + i),
      traceType: 'cloud',
    })

    const seed: TraceRecordLocal[] = [...Array.from({ length: 16 }, (_, i) => mkCloud(i + 1))]

    traceRecords.value = seed
    localStorage.setItem('traceRecords', JSON.stringify(traceRecords.value))
  }
})

// 返回风险管理页面
const goToRiskManagement = () => {
  router.push('/gov/risk/cloud')
}

// 显示处理流程图
const showProcessFlow = () => {
  flowDialogOpen.value = true
}

// 显示操作日志
const showOperationLog = (item: CloudViolationItem) => {
  selectedViolation.value = item
  logDialogOpen.value = true
}

// 深度分析
const drillDown = (item: CloudViolationItem) => {
  console.log('深度分析:', item)
}

// 查看实体详情
const viewEntityDetail = (entity: ResponsibleEntity) => {
  console.log('查看实体详情:', entity)
}

// 导出溯源报告
const exportTraceReport = () => {
  if (!selectedRisk.value) return

  const reportData = {
    riskInfo: selectedRisk.value,
    violationData: cloudViolationData.value,
    responsibleEntities: responsibleEntities.value,
    exportTime: new Date().toISOString(),
    reportType: '云端应急溯源报告',
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], {
    type: 'application/json;charset=utf-8;',
  })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `云端溯源报告_${selectedRisk.value.id}_${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  URL.revokeObjectURL(url)
}

// 导出流程图
const exportFlowChart = () => {
  const flowData = {
    riskId: selectedRisk.value?.id,
    stages: cloudViolationData.value.map((item) => ({
      stage: item.stage,
      activity: item.activity,
      timestamp: item.timestamp,
    })),
    exportTime: new Date().toISOString(),
  }

  const blob = new Blob([JSON.stringify(flowData, null, 2)], {
    type: 'application/json;charset=utf-8;',
  })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `云端处理流程_${selectedRisk.value?.id}_${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  URL.revokeObjectURL(url)

  flowDialogOpen.value = false
}

// 处理溯源记录保存
const handleSaveRecord = async (recordData: TraceRecord) => {
  traceRecords.value.unshift(recordData)

  // 限制记录数量，最多保存50条
  if (traceRecords.value.length > 50) {
    traceRecords.value = traceRecords.value.slice(0, 50)
  }

  // 保存到localStorage
  localStorage.setItem('traceRecords', JSON.stringify(traceRecords.value))
  // 保存成功提示（shadcn-vue sonner）
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const { toast } = await import('vue-sonner')
  toast.success('溯源信息已保存')
}

// 右上角按钮：触发保存（调用子组件暴露的方法）
const triggerSaveRecord = () => {
  traceVisRef.value?.saveRecord()
}

// 接收溯源会话ID用于卡片徽章显示
const onTraceStarted = (id: string) => {
  currentTraceId.value = id
}

// 查看溯源记录详情（点击左侧列表回放）
const viewRecord = (record: TraceRecord) => {
  traceVisRef.value?.replayRecord?.(record)
  currentTraceId.value = record.packetInfo.id
}

// 删除溯源记录
const deleteRecord = (index: number) => {
  traceRecords.value.splice(index, 1)
  localStorage.setItem('traceRecords', JSON.stringify(traceRecords.value))
}

// Badge 样式函数
const levelVariant = (lv: RiskLevel) => {
  switch (lv) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}

const statusVariant = (st: HandleStatus) => {
  switch (st) {
    case '未处理':
      return 'outline'
    case '处理中':
      return 'secondary'
    case '已处理':
      return 'default'
    default:
      return 'outline'
  }
}

import type { BadgeVariants } from '@/components/ui/badge'
const getStageVariant = (stage: CloudStage): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<CloudStage, NonNullable<BadgeVariants['variant']>> = {
    收集: 'default',
    存储: 'secondary',
    传输: 'outline',
    加工: 'default',
    提供: 'secondary',
    公开: 'outline',
    销毁: 'destructive',
  }
  return variants[stage] ?? 'outline'
}

const getEntityVariant = (type: ResponsibleEntity['type']) => {
  switch (type) {
    case '主体企业':
      return 'default'
    case '支撑单位':
      return 'secondary'
    case '第三方服务商':
      return 'outline'
    case '监管机构':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
