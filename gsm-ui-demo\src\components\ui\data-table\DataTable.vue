<template>
  <div class="space-y-4">
    <!-- 表格 -->
    <div class="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              v-for="column in columns"
              :key="column.key"
              :style="{ width: column.width + 'px' }"
            >
              {{ column.title }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="loading">
            <TableCell :colspan="columns.length" class="h-24 text-center">
              <div class="flex items-center justify-center space-x-2">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span>加载中...</span>
              </div>
            </TableCell>
          </TableRow>
          <TableRow v-else-if="data.length === 0">
            <TableCell :colspan="columns.length" class="h-24 text-center text-muted-foreground">
              暂无数据
            </TableCell>
          </TableRow>
          <TableRow v-else v-for="(item, index) in data" :key="item.id" class="hover:bg-muted/50">
            <TableCell v-for="column in columns" :key="column.key">
              <!-- 序号列 -->
              <template v-if="column.key === 'index'">
                {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
              </template>

              <!-- 企业名称列 -->
              <template v-else-if="column.key === 'name'">
                <div class="space-y-1">
                  <div class="font-medium">{{ item.name }}</div>
                  <div class="text-xs text-muted-foreground">{{ item.creditCode }}</div>
                </div>
              </template>

              <!-- 注册状态列 -->
              <template v-else-if="column.key === 'registrationStatus'">
                <Badge :variant="getStatusVariant(item.registrationStatus)">
                  {{ item.registrationStatus }}
                </Badge>
              </template>

              <!-- 车辆数量列 -->
              <template v-else-if="column.key === 'vehicleCount'">
                <div class="flex items-center space-x-2">
                  <Car class="h-4 w-4 text-muted-foreground" />
                  <span>{{ item.vehicleCount.toLocaleString() }}</span>
                </div>
              </template>

              <!-- 联系人列 -->
              <template v-else-if="column.key === 'contactPerson'">
                <div class="space-y-1">
                  <div>{{ item.contactPerson }}</div>
                  <div class="text-xs text-muted-foreground">{{ item.contactPhone }}</div>
                </div>
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <div class="flex items-center space-x-2">
                  <Button size="sm" variant="outline" @click="$emit('view-details', item)">
                    查看详情
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="sm" variant="ghost" class="h-8 w-8 p-0">
                        <MoreHorizontal class="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleQuickAction(item, 'view-basic')">
                        企业基本信息
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleQuickAction(item, 'view-qualification')">
                        测绘资质信息
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleQuickAction(item, 'view-vehicles')">
                        车辆信息
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleQuickAction(item, 'view-security')">
                        安全防护措施
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleQuickAction(item, 'view-activities')">
                        处理活动信息
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        @click="handleQuickAction(item, 'urge')"
                        v-if="canUrge(item)"
                      >
                        催办审批
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </template>

              <!-- 其他列 -->
              <template v-else>
                {{ item[column.key] }}
              </template>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <!-- 分页（统一使用 PaginationBar 组件） -->
    <PaginationBar
      :page="pagination.current"
      :total="pagination.total"
      :page-size="pagination.pageSize"
      @update:page="handlePageChange"
      @update:pageSize="handlePageSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Car, MoreHorizontal } from 'lucide-vue-next'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { PaginationBar } from '@/components/ui/pagination'

interface Column {
  key: string
  title: string
  width?: number
}

interface Enterprise {
  id: string
  name: string
  type: string
  creditCode: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  contactPerson: string
  contactPhone: string
  address: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

interface Pagination {
  current: number
  pageSize: number
  total: number
}

interface Props {
  columns: Column[]
  data: Enterprise[]
  loading?: boolean
  pagination: Pagination
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<{
  'view-details': [item: Enterprise]
  'page-change': [page: number]
  'page-size-change': [pageSize: number]
  'quick-action': [item: Enterprise, action: string]
}>()

// 计算属性
const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.pageSize))

// 方法
const getStatusVariant = (status: string) => {
  switch (status) {
    case '通过':
      return 'default'
    case '注册中':
      return 'secondary'
    case '待审核':
      return 'outline'
    case '未通过':
      return 'destructive'
    default:
      return 'outline'
  }
}

const canUrge = (item: Enterprise) => {
  // 判断是否可以催办：待审核状态且超过3天未审批
  if (item.registrationStatus !== '待审核') return false

  const registrationDate = new Date(item.registrationTime)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - registrationDate.getTime()) / (1000 * 60 * 60 * 24))

  return diffDays >= 3
}

const handlePageChange = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    emit('page-change', page)
  }
}

const handlePageSizeChange = (pageSize: number) => {
  emit('page-size-change', pageSize)
}

const handleQuickAction = (item: Enterprise, action: string) => {
  emit('quick-action', item, action)
}

// 统一使用官方 Pagination 组件后，不再需要本地页码计算逻辑
</script>
