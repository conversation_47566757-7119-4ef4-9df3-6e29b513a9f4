<template>
  <section class="gsm-external-module hero-section">
    <!-- 背景图片容器 -->
    <div class="hero-backgrounds">
      <div
        v-for="(item, index) in backgroundImages"
        :key="index"
        class="hero-background"
        :class="{
          active: index === currentImageIndex,
          loaded: isImageLoaded,
        }"
        :style="{
          background: useGradients ? item.gradient : `url(${item.url})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }"
        :title="item.name"
      ></div>
    </div>

    <div class="external-container">
      <div class="hero-content">
        <h1 class="hero-title">
          贯彻落实国家地理信息安全防控要求 <br />构建地理信息安全风险防控新体系
        </h1>
        <p class="hero-subtitle">多级联动 · 智慧监管 · 合规护航</p>
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">100%</span>
            <span class="stat-label">政策合规覆盖</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">50万+</span>
            <span class="stat-label">监管车辆规模</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">30+</span>
            <span class="stat-label">重点企业接入</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">7×24</span>
            <span class="stat-label">全天候监管</span>
          </div>
        </div>
        <div class="hero-actions">
          <button class="external-btn external-btn-primary btn-large" @click="handleGetStarted">
            企业接入
          </button>
          <button class="external-btn btn-outline btn-large" @click="handleExplore">
            政策解读
          </button>
        </div>
      </div>
    </div>

    <!-- 图片指示器 -->
    <div class="hero-indicators">
      <button
        v-for="(item, index) in backgroundImages"
        :key="index"
        class="indicator"
        :class="{ active: index === currentImageIndex }"
        @click="currentImageIndex = index"
        :title="item.name"
      ></button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  onExplore?: () => void
  onGetStarted?: () => void
  onNavigate?: (path: string) => void
}

const props = defineProps<Props>()

// 背景图片轮播功能 - 使用渐变背景作为备选
const backgroundImages = [
  {
    url: '/external/assets/images/hero-bg-1-new.jpg',
    gradient: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
    name: '地理信息测绘监管',
  },
  {
    url: '/external/assets/images/hero-bg-2-new.jpg',
    gradient: 'linear-gradient(135deg, #34495e 0%, #2c3e50 100%)',
    name: '车辆数据安全监测',
  },
  {
    url: '/external/assets/images/hero-bg-3.jpg',
    gradient: 'linear-gradient(135deg, #16a085 0%, #1abc9c 100%)',
    name: '政策法规落地',
  },
  {
    url: '/external/assets/images/hero-bg-4.jpg',
    gradient: 'linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%)',
    name: '企业合规服务',
  },
  {
    url: '/external/assets/images/hero-bg-5-new.jpg',
    gradient: 'linear-gradient(135deg, #2c3e50 0%, #1a252f 100%)',
    name: '时空数据安全监管',
  },
]

const currentImageIndex = ref(0)
const isImageLoaded = ref(false)
const useGradients = ref(false) // 控制是否使用渐变背景
let intervalId: number | null = null

// 预加载所有图片，失败则切换到渐变背景
const preloadImages = () => {
  let errorCount = 0

  backgroundImages.forEach((item, index) => {
    const img = new Image()
    img.onload = () => {
      if (index === 0 && !useGradients.value) {
        isImageLoaded.value = true
      }
    }
    img.onerror = () => {
      errorCount++
      // 如果所有图片都加载失败，切换到渐变背景
      if (errorCount === backgroundImages.length) {
        useGradients.value = true
        isImageLoaded.value = true
        console.log('图片加载失败，使用渐变背景')
      }
    }
    img.src = item.url
  })

  // 3秒后如果还没加载完成，切换到渐变背景
  setTimeout(() => {
    if (!isImageLoaded.value) {
      useGradients.value = true
      isImageLoaded.value = true
      console.log('图片加载超时，使用渐变背景')
    }
  }, 3000)
}

// 切换到下一张图片
const nextImage = () => {
  currentImageIndex.value = (currentImageIndex.value + 1) % backgroundImages.length
}

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 处理按钮点击事件
const handleGetStarted = () => {
  if (props.onGetStarted) {
    props.onGetStarted()
  } else {
    // Fallback to scroll to features section
    scrollToSection('features')
  }
}

const handleExplore = () => {
  if (props.onExplore) {
    props.onExplore()
  } else {
    // Fallback to internal navigation via parent app if available
    if (props.onNavigate) {
      props.onNavigate('/content/compliance/default')
    } else {
      // final fallback: open in same tab
      window.location.href = '/content/compliance/default'
    }
  }
}

// 组件挂载时开始预加载和轮播
onMounted(() => {
  preloadImages()

  // 每10秒切换一次背景图片
  intervalId = window.setInterval(nextImage, 10000)
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<style scoped>
/* 英雄区域样式 */
.hero-section {
  padding: 8rem 0;
  text-align: center;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
  color: var(--external-text-color);
  overflow: hidden;
  min-height: 80vh;
}

/* 背景图片容器 */
.hero-backgrounds {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* 单个背景图片 */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  animation: kenBurns 24s infinite;
}

.hero-background.active {
  opacity: 1;
}

.hero-background.loaded {
  background-color: var(--external-surface-color);
}

/* Ken Burns 效果 */
@keyframes kenBurns {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 背景遮罩 */
.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(44, 62, 80, 0.7) 0%, rgba(52, 73, 94, 0.8) 100%);
  z-index: 1;
}

/* Hero内容 */
.hero-content {
  position: relative;
  z-index: 2;
  max-width: 900px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--external-text-color-secondary);
  margin-bottom: 3rem;
  font-weight: 500;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* 统计数据 */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-item {
  background: var(--external-card-bg);
  border: 1px solid var(--external-border-color);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 128, 204, 0.2);
  border-color: var(--external-primary-color);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--external-primary-color);
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 8px rgba(0, 128, 204, 0.3);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--external-text-color-regular);
  font-weight: 500;
}

/* 操作按钮 */
.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.btn-outline {
  background: rgba(255, 255, 255, 0.1);
  color: var(--external-text-color);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--external-primary-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 128, 204, 0.2);
}

/* 图片指示器 */
.hero-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.75rem;
  z-index: 3;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.2);
}

.indicator.active {
  background: var(--external-primary-color);
  box-shadow: 0 0 16px var(--external-glow-color);
  transform: scale(1.3);
}

/* Light theme adjustments */
.gsm-external-module[data-theme='light'] .hero-section::before {
  background: linear-gradient(135deg, rgba(44, 62, 80, 0.4) 0%, rgba(68, 84, 106, 0.6) 100%);
}

.gsm-external-module[data-theme='light'] .hero-title {
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.gsm-external-module[data-theme='light'] .hero-subtitle {
  color: var(--external-text-color-secondary);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.gsm-external-module[data-theme='light'] .stat-item {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.gsm-external-module[data-theme='light'] .stat-item:hover {
  box-shadow: 0 16px 40px rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
}

.gsm-external-module[data-theme='light'] .stat-number {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.gsm-external-module[data-theme='light'] .btn-outline {
  background: rgba(255, 255, 255, 0.8);
  color: var(--external-text-color);
  border-color: rgba(203, 213, 225, 0.6);
}

.gsm-external-module[data-theme='light'] .btn-outline:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 16px 40px rgba(59, 130, 246, 0.12);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 6rem 0 4rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    grid-template-columns: 1fr;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }
}
</style>
