/**
 * Ocean Depths Theme System - 深海主题系统
 * 将深海主题应用到整个系统的配色方案
 */

// Ocean Depths 基础配色
export const OCEAN_DEPTHS_COLORS = {
  // 主色调 - 深海蓝系
  primary: {
    50: '#bbe1fa', // 浅蓝 - 最浅色调
    100: '#87ceeb', // 天空蓝
    200: '#5f9ea0', // 青灰 - 辅助色
    300: '#4682b4', // 钢蓝 - 中等色调
    400: '#3282b8', // 海洋蓝 - 常用色调
    500: '#0f4c75', // 深海蓝 - 主色调
    600: '#0d3f63', // 更深的蓝
    700: '#0a334f', // 深蓝
    800: '#08263b', // 极深蓝
    900: '#051a27', // 最深蓝
  },

  // 辅助色 - 海绿系
  secondary: {
    50: '#20b2aa', // 浅海绿 - 最浅色调
    100: '#2e8b57', // 海绿 - 中等色调
    200: '#228b22', // 森林绿
    300: '#1e7e34', // 深绿
    400: '#1a6d2e', // 更深绿
    500: '#155724', // 深森林绿
    600: '#0f4419', // 极深绿
    700: '#0a2f0f', // 最深绿
    800: '#051a08', // 极暗绿
    900: '#020c03', // 黑绿
  },

  // 中性色 - 深灰蓝系
  neutral: {
    50: '#f8fafc', // 白色
    100: '#f1f5f9', // 极浅灰
    200: '#e2e8f0', // 浅灰
    300: '#cbd5e1', // 中浅灰
    400: '#94a3b8', // 中灰
    500: '#64748b', // 标准灰
    600: '#475569', // 深灰
    700: '#334155', // 更深灰
    800: '#1e293b', // 极深灰
    900: '#1b262c', // 深灰蓝 - 主题特色
  },

  // 功能色系
  success: {
    light: '#20b2aa', // 浅海绿
    main: '#2e8b57', // 海绿
    dark: '#155724', // 深绿
  },

  warning: {
    light: '#ffd700', // 金黄
    main: '#ffa500', // 橙色
    dark: '#ff8c00', // 深橙
  },

  error: {
    light: '#ff6b6b', // 珊瑚红
    main: '#dc3545', // 红色
    dark: '#c82333', // 深红
  },

  info: {
    light: '#87ceeb', // 天空蓝
    main: '#4682b4', // 钢蓝
    dark: '#0f4c75', // 深海蓝
  },
} as const

// 语义化颜色映射
export const SEMANTIC_COLORS = {
  // 风险等级配色 - 优化的浅色高对比度配色
  risk: {
    critical: '#ffcdd2', // 严重 - 浅粉红红
    high: '#ffcdd2', // 高风险 - 浅粉红红
    medium: '#ffe0b2', // 中风险 - 浅柔橙
    low: '#c8e6c9', // 低风险 - 浅柔绿
    minimal: '#c8e6c9', // 极低 - 浅柔绿
  },

  // 状态配色
  status: {
    active: OCEAN_DEPTHS_COLORS.success.main, // 激活/正常
    inactive: OCEAN_DEPTHS_COLORS.neutral[500], // 非激活
    pending: OCEAN_DEPTHS_COLORS.warning.main, // 待处理
    error: OCEAN_DEPTHS_COLORS.error.main, // 错误
    processing: OCEAN_DEPTHS_COLORS.info.main, // 处理中
  },

  // 企业类型配色
  enterprise: {
    manufacturer: OCEAN_DEPTHS_COLORS.primary[500], // 整车生产企业 - 深海蓝
    platform: OCEAN_DEPTHS_COLORS.primary[400], // 平台运营商 - 海洋蓝
    solution: OCEAN_DEPTHS_COLORS.secondary[100], // 智驾方案提供商 - 海绿
    map: OCEAN_DEPTHS_COLORS.secondary[50], // 地图服务商 - 浅海绿
    other: OCEAN_DEPTHS_COLORS.neutral[600], // 其他 - 深灰
  },

  // 数据处理阶段配色 (7阶段)
  stages: {
    collection: OCEAN_DEPTHS_COLORS.primary[500], // 收集 - 深海蓝
    storage: OCEAN_DEPTHS_COLORS.primary[400], // 存储 - 海洋蓝
    transmission: OCEAN_DEPTHS_COLORS.primary[300], // 传输 - 钢蓝
    processing: OCEAN_DEPTHS_COLORS.secondary[100], // 加工 - 海绿
    provision: OCEAN_DEPTHS_COLORS.secondary[50], // 提供 - 浅海绿
    publication: OCEAN_DEPTHS_COLORS.warning.main, // 公开 - 橙色
    destruction: OCEAN_DEPTHS_COLORS.neutral[600], // 销毁 - 深灰
  },
} as const

// 图表配色方案 - Ocean Depths 主题
export const OCEAN_DEPTHS_CHART_COLORS = {
  // 主要配色方案 - 8色渐进
  primary: [
    OCEAN_DEPTHS_COLORS.primary[500], // 深海蓝
    OCEAN_DEPTHS_COLORS.primary[400], // 海洋蓝
    OCEAN_DEPTHS_COLORS.primary[50], // 浅蓝
    OCEAN_DEPTHS_COLORS.neutral[900], // 深灰蓝
    OCEAN_DEPTHS_COLORS.secondary[100], // 海绿
    OCEAN_DEPTHS_COLORS.secondary[50], // 浅海绿
    OCEAN_DEPTHS_COLORS.primary[300], // 钢蓝
    OCEAN_DEPTHS_COLORS.primary[200], // 青灰
  ],

  // 风险等级专用配色（高/中/低）- 优化的浅色高对比度配色
  risk: [
    '#ffcdd2', // 高风险 - 更浅的粉红红（lighter pastel red）
    '#ffe0b2', // 中风险 - 更浅的柔橙（lighter pastel orange）
    '#c8e6c9', // 低风险 - 更浅的柔绿（lighter pastel green）
  ],

  // 状态配色
  status: [
    SEMANTIC_COLORS.status.active, // 正常/已完成
    SEMANTIC_COLORS.status.processing, // 处理中/进行中
    SEMANTIC_COLORS.status.pending, // 待处理/警告
    SEMANTIC_COLORS.status.error, // 异常/失败
    SEMANTIC_COLORS.status.inactive, // 未知/其他
  ],

  // 企业类型配色
  enterprise: Object.values(SEMANTIC_COLORS.enterprise),

  // 处理阶段配色
  stages: Object.values(SEMANTIC_COLORS.stages),

  // 渐变配色
  gradients: {
    ocean: {
      start: OCEAN_DEPTHS_COLORS.primary[500],
      end: OCEAN_DEPTHS_COLORS.primary[50],
    },
    seaweed: {
      start: OCEAN_DEPTHS_COLORS.secondary[100],
      end: OCEAN_DEPTHS_COLORS.secondary[50],
    },
    depth: {
      start: OCEAN_DEPTHS_COLORS.neutral[900],
      end: OCEAN_DEPTHS_COLORS.neutral[600],
    },
  },
} as const

// CSS 变量生成
export const generateOceanDepthsCSSVariables = () => {
  const cssVars: Record<string, string> = {}

  // 主色调变量
  Object.entries(OCEAN_DEPTHS_COLORS.primary).forEach(([key, value]) => {
    cssVars[`--ocean-primary-${key}`] = value
  })

  // 辅助色变量
  Object.entries(OCEAN_DEPTHS_COLORS.secondary).forEach(([key, value]) => {
    cssVars[`--ocean-secondary-${key}`] = value
  })

  // 中性色变量
  Object.entries(OCEAN_DEPTHS_COLORS.neutral).forEach(([key, value]) => {
    cssVars[`--ocean-neutral-${key}`] = value
  })

  // 功能色变量
  Object.entries(OCEAN_DEPTHS_COLORS.success).forEach(([key, value]) => {
    cssVars[`--ocean-success-${key}`] = value
  })

  Object.entries(OCEAN_DEPTHS_COLORS.warning).forEach(([key, value]) => {
    cssVars[`--ocean-warning-${key}`] = value
  })

  Object.entries(OCEAN_DEPTHS_COLORS.error).forEach(([key, value]) => {
    cssVars[`--ocean-error-${key}`] = value
  })

  Object.entries(OCEAN_DEPTHS_COLORS.info).forEach(([key, value]) => {
    cssVars[`--ocean-info-${key}`] = value
  })

  // 语义化颜色变量
  Object.entries(SEMANTIC_COLORS.risk).forEach(([key, value]) => {
    cssVars[`--ocean-risk-${key}`] = value
  })

  Object.entries(SEMANTIC_COLORS.status).forEach(([key, value]) => {
    cssVars[`--ocean-status-${key}`] = value
  })

  return cssVars
}

// Tailwind CSS 扩展配置
export const oceanDepthsTailwindExtend = {
  colors: {
    'ocean-primary': OCEAN_DEPTHS_COLORS.primary,
    'ocean-secondary': OCEAN_DEPTHS_COLORS.secondary,
    'ocean-neutral': OCEAN_DEPTHS_COLORS.neutral,
    'ocean-success': OCEAN_DEPTHS_COLORS.success,
    'ocean-warning': OCEAN_DEPTHS_COLORS.warning,
    'ocean-error': OCEAN_DEPTHS_COLORS.error,
    'ocean-info': OCEAN_DEPTHS_COLORS.info,
    'ocean-risk': SEMANTIC_COLORS.risk,
    'ocean-status': SEMANTIC_COLORS.status,
    'ocean-enterprise': SEMANTIC_COLORS.enterprise,
    'ocean-stages': SEMANTIC_COLORS.stages,
  },
}

// ECharts 主题配置 - Ocean Depths
export const createOceanDepthsEChartsTheme = () => {
  return {
    color: OCEAN_DEPTHS_CHART_COLORS.primary,
    backgroundColor: 'transparent',

    textStyle: {
      fontFamily:
        'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      color: OCEAN_DEPTHS_COLORS.neutral[700],
      fontSize: 12,
    },

    title: {
      textStyle: {
        color: OCEAN_DEPTHS_COLORS.neutral[800],
        fontSize: 16,
        fontWeight: '600',
      },
      subtextStyle: {
        color: OCEAN_DEPTHS_COLORS.neutral[600],
      },
    },

    legend: {
      textStyle: {
        color: OCEAN_DEPTHS_COLORS.neutral[600],
        fontSize: 12,
      },
      itemGap: 16,
      itemWidth: 14,
      itemHeight: 14,
    },

    tooltip: {
      backgroundColor: 'rgba(15, 76, 117, 0.95)', // 使用深海蓝作为背景
      borderColor: OCEAN_DEPTHS_COLORS.primary[400],
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#ffffff',
        fontSize: 12,
      },
      shadowBlur: 16,
      shadowColor: 'rgba(15, 76, 117, 0.3)',
      padding: [8, 12],
    },

    categoryAxis: {
      axisLine: {
        lineStyle: {
          color: OCEAN_DEPTHS_COLORS.neutral[300],
        },
      },
      axisTick: {
        lineStyle: {
          color: OCEAN_DEPTHS_COLORS.neutral[300],
        },
      },
      axisLabel: {
        color: OCEAN_DEPTHS_COLORS.neutral[600],
      },
      splitLine: {
        lineStyle: {
          color: OCEAN_DEPTHS_COLORS.neutral[200],
        },
      },
    },

    valueAxis: {
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: OCEAN_DEPTHS_COLORS.neutral[600],
      },
      splitLine: {
        lineStyle: {
          color: OCEAN_DEPTHS_COLORS.neutral[200],
        },
      },
    },

    // 特定图表类型样式
    pie: {
      itemStyle: {
        borderWidth: 2,
        borderColor: '#ffffff',
      },
      label: {
        color: OCEAN_DEPTHS_COLORS.neutral[700],
      },
      labelLine: {
        lineStyle: {
          color: OCEAN_DEPTHS_COLORS.neutral[400],
        },
      },
    },

    bar: {
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
      },
    },

    line: {
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
      },
    },
  }
}

// 辅助函数：获取语义化颜色
export const getSemanticColor = (
  type: 'risk' | 'status' | 'enterprise' | 'stages',
  key: string,
) => {
  return SEMANTIC_COLORS[type][key as keyof (typeof SEMANTIC_COLORS)[typeof type]]
}

// 辅助函数：获取图表颜色数组
export const getChartColors = (scheme: keyof typeof OCEAN_DEPTHS_CHART_COLORS) => {
  return OCEAN_DEPTHS_CHART_COLORS[scheme]
}

// 辅助函数：创建渐变色
export const createOceanGradient = (
  gradientKey: keyof typeof OCEAN_DEPTHS_CHART_COLORS.gradients,
  direction: 'vertical' | 'horizontal' = 'vertical',
) => {
  const gradient = OCEAN_DEPTHS_CHART_COLORS.gradients[gradientKey]

  return {
    type: 'linear',
    x: 0,
    y: direction === 'vertical' ? 0 : 0,
    x2: direction === 'horizontal' ? 1 : 0,
    y2: direction === 'vertical' ? 1 : 0,
    colorStops: [
      { offset: 0, color: gradient.start + 'CC' }, // 添加透明度
      { offset: 1, color: gradient.end + '66' }, // 渐变到更透明
    ],
  }
}
