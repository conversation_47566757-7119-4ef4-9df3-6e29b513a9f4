﻿表名: data_processing_activity,,,,,,,
,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,enterprise_id,所属企业ID,VARCHAR,32,○,,外键关联 enterprise_info.id
3,activity_name,活动名称,VARCHAR,255,○,,
4,activity_description,活动描述,TEXT,,○,,
5,data_types_involved,涉及数据类型,JSONB,,,,列表形式存储
6,processing_methods,处理方式描述,TEXT,,,,
7,start_date,开始日期,DATE,,,,
8,end_date,结束日期,DATE,,,,
9,filing_status,备案状态,SMALLINT,,○,,"1: 待审核, 2: 已备案, 3: 驳回"
10,filing_date,备案时间,TIMESTAMP,,,,
11,create_time,创建时间,TIMESTAMP,,○,,
