<template>
  <footer class="gsm-external-module footer">
    <div class="external-container">
      <div class="footer-content">
        <!-- 平台信息 -->
        <div class="footer-section">
          <h3 class="footer-title">地理信息安全监测平台</h3>
          <p class="footer-description">地理信息安全监测平台，助力构建车联网产业安全发展新生态。</p>
          <div class="footer-meta">
            <p>© 2024 地理信息安全监测平台</p>
            <p>版权所有 · 保留所有权利</p>
          </div>
        </div>

        <!-- 服务导航 -->
        <div class="footer-section">
          <h4 class="section-title">平台服务</h4>
          <ul class="footer-links">
            <li><a href="#compliance" @click="scrollToSection('compliance')">合规接入</a></li>
            <li><a href="#policy" @click="scrollToSection('policy')">政策解读</a></li>
            <li><a href="#monitoring" @click="scrollToSection('monitoring')">实时监控</a></li>
            <li><a href="#enterprise" @click="scrollToSection('enterprise')">企业展示</a></li>
          </ul>
        </div>

        <!-- 政府链接 -->
        <div class="footer-section">
          <h4 class="section-title">相关部门</h4>
          <ul class="footer-links">
            <li>
              <a
                href="https://www.miit.gov.cn"
                target="_blank"
                rel="noopener noreferrer"
                class="external-link"
              >
                工业和信息化部
                <ExternalLinkIcon />
              </a>
            </li>
            <li>
              <a
                href="https://www.mot.gov.cn"
                target="_blank"
                rel="noopener noreferrer"
                class="external-link"
              >
                交通运输部
                <ExternalLinkIcon />
              </a>
            </li>
            <li>
              <a
                href="https://www.mnr.gov.cn"
                target="_blank"
                rel="noopener noreferrer"
                class="external-link"
              >
                自然资源部
                <ExternalLinkIcon />
              </a>
            </li>
          </ul>
        </div>

        <!-- 联系支持 -->
        <div class="footer-section">
          <h4 class="section-title">技术支持</h4>
          <ul class="footer-links">
            <li>技术咨询：400-123-4567</li>
            <li>政策咨询：<EMAIL></li>
            <li>运维支持：<EMAIL></li>
            <li>应急响应：7×24小时</li>
          </ul>
        </div>
      </div>

      <!-- 底部信息栏 -->
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p class="copyright">© 2024 地理信息安全监测平台 · 智能网联汽车时空数据安全监管</p>
          <div class="footer-badges">
            <span class="badge">安全合规</span>
            <span class="badge">政府认证</span>
            <span class="badge">7×24服务</span>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { h, markRaw } from 'vue'

// 自定义外部链接图标组件
const ExternalLinkIcon = markRaw(() =>
  h(
    'svg',
    {
      width: '14',
      height: '14',
      viewBox: '0 0 24 24',
      fill: 'none',
      stroke: 'currentColor',
      strokeWidth: '2',
      strokeLinecap: 'round',
      strokeLinejoin: 'round',
      class: 'external-icon',
    },
    [h('path', { d: 'M7 17L17 7' }), h('path', { d: 'M7 7h10v10' })],
  ),
)

interface Props {
  onSectionClick?: (section: string) => void
}

const props = defineProps<Props>()

const scrollToSection = (sectionId: string) => {
  if (props.onSectionClick) {
    props.onSectionClick(sectionId)
  } else {
    // Fallback to smooth scrolling
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }
}
</script>

<style scoped>
/* Footer样式 */
.footer {
  background: var(--external-surface-color);
  border-top: 1px solid var(--external-border-color);
  padding: 3rem 0 0;
  color: var(--external-text-color);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem;
  margin-bottom: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--external-primary-color);
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.footer-description {
  color: var(--external-text-color-regular);
  line-height: 1.6;
  margin: 0;
}

.footer-meta {
  margin-top: 1rem;
}

.footer-meta p {
  color: var(--external-text-color-secondary);
  font-size: 0.875rem;
  margin: 0.25rem 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--external-text-color);
  margin: 0 0 1rem 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-links li {
  font-size: 0.875rem;
}

.footer-links a {
  color: var(--external-text-color-regular);
  text-decoration: none;
  transition: color 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-links a:hover {
  color: var(--external-primary-color);
}

.external-link {
  position: relative;
}

.external-link:hover .external-icon {
  transform: translate(2px, -2px);
}

.external-icon {
  transition: transform 0.2s ease;
  opacity: 0.7;
}

/* 底部信息栏 */
.footer-bottom {
  border-top: 1px solid var(--external-border-color);
  padding: 1.5rem 0;
  margin-top: 2rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: var(--external-text-color-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.footer-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.badge {
  background: var(--external-primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Light theme adjustments */
.gsm-external-module[data-theme='light'] .footer {
  background: var(--external-surface-color);
  border-top-color: var(--external-border-color);
}

.gsm-external-module[data-theme='light'] .footer-title {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gsm-external-module[data-theme='light'] .section-title {
  color: var(--external-text-color);
}

.gsm-external-module[data-theme='light'] .footer-description {
  color: var(--external-text-color-regular);
}

.gsm-external-module[data-theme='light'] .footer-links a {
  color: var(--external-text-color-secondary);
}

.gsm-external-module[data-theme='light'] .footer-links a:hover {
  color: var(--external-primary-color);
}

.gsm-external-module[data-theme='light'] .footer-bottom {
  border-top-color: var(--external-border-color);
}

.gsm-external-module[data-theme='light'] .copyright {
  color: var(--external-text-color-secondary);
}

.gsm-external-module[data-theme='light'] .badge {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-badges {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2rem 0 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-title {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1rem;
  }
}
</style>
