<template>
  <div class="space-y-6">
    <!-- 数据处理活动概览 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Database class="h-5 w-5" />
            <span>数据处理活动概览</span>
          </div>
          <Badge variant="outline">7阶段全流程管理</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ totalActivities }}</div>
            <div class="text-sm text-blue-600">活动总数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ filedActivities }}</div>
            <div class="text-sm text-green-600">已备案</div>
          </div>
          <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-2xl font-bold text-yellow-600">{{ pendingActivities }}</div>
            <div class="text-sm text-yellow-600">待审核</div>
          </div>
          <div class="text-center p-4 bg-red-50 rounded-lg">
            <div class="text-2xl font-bold text-red-600">{{ rejectedActivities }}</div>
            <div class="text-sm text-red-600">已驳回</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 七阶段处理详情 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Workflow class="h-5 w-5" />
          <span>数据处理七阶段详情</span>
        </CardTitle>
        <CardDescription>
          按照数据处理生命周期的七个阶段：收集、存储、传输、加工、提供、公开、销毁
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs v-model="activeStage" class="w-full">
          <TabsList class="grid w-full grid-cols-7">
            <TabsTrigger value="collect">收集</TabsTrigger>
            <TabsTrigger value="store">存储</TabsTrigger>
            <TabsTrigger value="transfer">传输</TabsTrigger>
            <TabsTrigger value="process">加工</TabsTrigger>
            <TabsTrigger value="provide">提供</TabsTrigger>
            <TabsTrigger value="publish">公开</TabsTrigger>
            <TabsTrigger value="destroy">销毁</TabsTrigger>
          </TabsList>

          <!-- 收集阶段 -->
          <TabsContent value="collect" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Download class="w-5 h-5" />
                  数据收集详情
                </CardTitle>
                <CardDescription>企业端数据收集活动的详细信息和安全措施</CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <!-- 收集基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">数据重要程度</Label>
                    <Badge
                      :variant="
                        collectDetails.dataImportanceLevel === 2 ? 'destructive' : 'secondary'
                      "
                    >
                      {{ collectDetails.dataImportanceLevel === 2 ? '重要数据' : '一般数据' }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">数据来源类型</Label>
                    <div class="text-sm">
                      {{ getDataSourceType(collectDetails.dataSourceType) }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">数据来源标识</Label>
                    <div class="text-sm font-mono">{{ collectDetails.dataSourceId }}</div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">数据用途</Label>
                    <div class="text-sm">{{ getDataPurpose(collectDetails.dataPurpose) }}</div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">操作员身份</Label>
                    <Badge variant="outline">
                      {{
                        collectDetails.operatorIdentity === 1
                          ? '重要数据操作人员'
                          : '一般数据操作人员'
                      }}
                    </Badge>
                  </div>
                </div>

                <!-- 收集活动列表 -->
                <div class="space-y-3">
                  <Label class="text-sm font-medium">最近收集活动记录</Label>
                  <div class="table-elevated rounded-md border">
                    <Table class="data-table">
                      <TableHeader>
                        <TableRow class="data-table-header">
                          <TableHead class="data-table-cell">时间</TableHead>
                          <TableHead class="data-table-cell">数据来源</TableHead>
                          <TableHead class="data-table-cell">收集量</TableHead>
                          <TableHead class="data-table-cell">重要程度</TableHead>
                          <TableHead class="data-table-cell">操作员</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow
                          v-for="record in collectRecords"
                          :key="record.id"
                          class="data-table-row"
                        >
                          <TableCell class="data-table-cell">{{
                            formatDateTime(record.timestamp)
                          }}</TableCell>
                          <TableCell class="data-table-cell">{{ record.sourceId }}</TableCell>
                          <TableCell class="data-table-cell">{{ record.dataSize }}</TableCell>
                          <TableCell class="data-table-cell">
                            <Badge :variant="record.importance === 2 ? 'destructive' : 'secondary'">
                              {{ record.importance === 2 ? '重要' : '一般' }}
                            </Badge>
                          </TableCell>
                          <TableCell class="data-table-cell">{{ record.operator }}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 存储阶段 -->
          <TabsContent value="store" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <HardDrive class="w-5 h-5" />
                  数据存储详情
                </CardTitle>
                <CardDescription>企业端数据存储配置和安全保障措施</CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <!-- 存储配置信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">存储设备类型</Label>
                    <div class="text-sm">
                      {{ getStorageDeviceType(storeDetails.storageDeviceType) }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">存储区域</Label>
                    <Badge
                      :variant="storeDetails.storageRegionFlag === 1 ? 'default' : 'destructive'"
                    >
                      {{ storeDetails.storageRegionFlag === 1 ? '境内存储' : '境外存储' }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">存储设备IP</Label>
                    <div class="text-sm font-mono">{{ storeDetails.storageDeviceIp }}</div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">存储区类型</Label>
                    <div class="text-sm">
                      {{ getStorageAreaType(storeDetails.storageAreaType) }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">数据分区</Label>
                    <Badge
                      :variant="storeDetails.dataPartitionFlag === 1 ? 'default' : 'secondary'"
                    >
                      {{ storeDetails.dataPartitionFlag === 1 ? '已分区存储' : '未分区存储' }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">脱敏状态</Label>
                    <Badge
                      :variant="storeDetails.desensitizationFlag === 1 ? 'default' : 'destructive'"
                    >
                      {{ storeDetails.desensitizationFlag === 1 ? '已脱敏' : '未脱敏' }}
                    </Badge>
                  </div>
                </div>

                <!-- 存储保障措施 -->
                <div class="space-y-3">
                  <Label class="text-sm font-medium">存储保障措施</Label>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center space-x-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          storeDetails.storageSafeguardBitmap & 1 ? 'bg-green-500' : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">完整性保障</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          storeDetails.storageSafeguardBitmap & 2 ? 'bg-green-500' : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">真实性保障</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          storeDetails.storageSafeguardBitmap & 4 ? 'bg-green-500' : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">可用性保障</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 传输阶段 -->
          <TabsContent value="transfer" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Send class="w-5 h-5" />
                  数据传输详情
                </CardTitle>
                <CardDescription>企业端数据传输安全配置和协议信息</CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <!-- 传输配置 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">传输目的地</Label>
                    <div class="text-sm font-mono">{{ transferDetails.targetEnterpriseCode }}</div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">通信通道类型</Label>
                    <Badge
                      :variant="
                        transferDetails.communicationChannelType === 1 ? 'default' : 'secondary'
                      "
                    >
                      {{
                        transferDetails.communicationChannelType === 1 ? '国密通道' : '非国密通道'
                      }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">公共网络使用</Label>
                    <Badge
                      :variant="
                        transferDetails.publicNetworkUsageFlag === 1 ? 'destructive' : 'default'
                      "
                    >
                      {{
                        transferDetails.publicNetworkUsageFlag === 1
                          ? '使用公共网络'
                          : '不使用公共网络'
                      }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">传输方式</Label>
                    <div class="text-sm">
                      {{ getTransmissionMethod(transferDetails.transmissionMethod) }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">网络类型</Label>
                    <div class="text-sm">{{ getNetworkType(transferDetails.networkType) }}</div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">安全协议</Label>
                    <div class="text-sm">
                      {{ getSecurityProtocol(transferDetails.securityProtocol) }}
                    </div>
                  </div>
                </div>

                <!-- 传输保障措施 -->
                <div class="space-y-3">
                  <Label class="text-sm font-medium">传输保障措施</Label>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center space-x-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          transferDetails.transmissionSafeguardBitmap & 1
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">完整性保障</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          transferDetails.transmissionSafeguardBitmap & 2
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">真实性保障</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          transferDetails.transmissionSafeguardBitmap & 4
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">可用性保障</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 加工阶段 -->
          <TabsContent value="process" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Settings class="w-5 h-5" />
                  数据加工详情
                </CardTitle>
                <CardDescription>数据加工处理类型和新数据包生成信息</CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <!-- 加工基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">加工类型</Label>
                    <div class="text-sm">
                      {{ getProcessingType(processDetails.processingType) }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">位置精度合规性</Label>
                    <Badge
                      :variant="
                        processDetails.positionAccuracyCompliance === 1 ? 'default' : 'destructive'
                      "
                    >
                      {{
                        processDetails.positionAccuracyCompliance === 1 ? '符合要求' : '不符合要求'
                      }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">脱敏状态</Label>
                    <Badge
                      :variant="
                        processDetails.desensitizationStatus === 1 ? 'default' : 'destructive'
                      "
                    >
                      {{ processDetails.desensitizationStatus === 1 ? '已脱敏' : '未脱敏' }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">新数据包生成</Label>
                    <Badge
                      :variant="
                        processDetails.isNewPackageGenerated === 1 ? 'default' : 'secondary'
                      "
                    >
                      {{ processDetails.isNewPackageGenerated === 1 ? '是' : '否' }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">新数据包数量</Label>
                    <div class="text-sm font-mono">{{ processDetails.newPackageCount }}</div>
                  </div>
                </div>

                <!-- 新数据包信息 -->
                <div v-if="processDetails.isNewPackageGenerated === 1" class="space-y-3">
                  <Label class="text-sm font-medium">新生成数据包详情</Label>
                  <div class="table-elevated rounded-md border">
                    <Table class="data-table">
                      <TableHeader>
                        <TableRow class="data-table-header">
                          <TableHead class="data-table-cell">数据包ID</TableHead>
                          <TableHead class="data-table-cell">数据类型</TableHead>
                          <TableHead class="data-table-cell">生成时间</TableHead>
                          <TableHead class="data-table-cell">大小</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow
                          v-for="pkg in newDataPackages"
                          :key="pkg.id"
                          class="data-table-row"
                        >
                          <TableCell class="data-table-cell font-mono">{{ pkg.id }}</TableCell>
                          <TableCell class="data-table-cell">{{ pkg.dataType }}</TableCell>
                          <TableCell class="data-table-cell">{{
                            formatDateTime(pkg.createdAt)
                          }}</TableCell>
                          <TableCell class="data-table-cell">{{ pkg.size }}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 提供阶段 -->
          <TabsContent value="provide" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Share class="w-5 h-5" />
                  数据提供详情
                </CardTitle>
                <CardDescription>数据对外提供的接收方信息和安全措施</CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <!-- 提供基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">数据包总数</Label>
                    <div class="text-sm font-mono">
                      {{ provideDetails.totalPackagesInOperation }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">接收方类型</Label>
                    <div class="text-sm">{{ getRecipientType(provideDetails.recipientType) }}</div>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">接收方安全能力</Label>
                    <Badge
                      :variant="
                        provideDetails.recipientSecurityCapability === 1 ? 'default' : 'destructive'
                      "
                    >
                      {{
                        provideDetails.recipientSecurityCapability === 1
                          ? '安全能力满足'
                          : '安全能力不足'
                      }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">豁免条件</Label>
                    <Badge :variant="provideDetails.isExemptionMet === 1 ? 'default' : 'secondary'">
                      {{ provideDetails.isExemptionMet === 1 ? '满足豁免条件' : '不满足豁免条件' }}
                    </Badge>
                  </div>
                  <div class="space-y-2">
                    <Label class="text-sm font-medium">合同状态</Label>
                    <Badge
                      :variant="provideDetails.contractStatus === 2 ? 'default' : 'destructive'"
                    >
                      {{ provideDetails.contractStatus === 2 ? '有合同协议' : '无合同协议' }}
                    </Badge>
                  </div>
                </div>

                <!-- 安全处理技术 -->
                <div class="space-y-3">
                  <Label class="text-sm font-medium">采用的安全处理技术</Label>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <div
                      v-for="(tech, index) in securityTechnologies"
                      :key="index"
                      class="flex items-center space-x-2"
                    >
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="
                          provideDetails.securityMeasuresBitmap & Math.pow(2, index)
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        "
                      ></div>
                      <span class="text-sm">{{ tech }}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 公开阶段 -->
          <TabsContent value="publish" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Globe class="w-5 h-5" />
                  数据公开详情
                </CardTitle>
                <CardDescription>数据公开发布的范围和安全控制措施</CardDescription>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="text-center py-12 text-muted-foreground">
                  <Globe class="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
                  <p>该企业暂无数据公开活动</p>
                  <p class="text-sm">数据公开需要经过严格的安全评估和审批流程</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 销毁阶段 -->
          <TabsContent value="destroy" class="space-y-4">
            <Card class="relative">
              <div
                class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
              ></div>
              <CardHeader>
                <CardTitle class="text-lg flex items-center gap-2">
                  <Trash2 class="w-5 h-5" />
                  数据销毁详情
                </CardTitle>
                <CardDescription>数据销毁记录和安全销毁措施</CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <!-- 销毁统计 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div class="text-center p-4 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">156</div>
                    <div class="text-sm text-red-600">已销毁数据包</div>
                  </div>
                  <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">23</div>
                    <div class="text-sm text-yellow-600">待销毁数据包</div>
                  </div>
                  <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">12.5TB</div>
                    <div class="text-sm text-blue-600">累计销毁数据量</div>
                  </div>
                </div>

                <!-- 销毁记录 -->
                <div class="space-y-3">
                  <Label class="text-sm font-medium">最近销毁记录</Label>
                  <div class="table-elevated rounded-md border">
                    <Table class="data-table">
                      <TableHeader>
                        <TableRow class="data-table-header">
                          <TableHead class="data-table-cell">销毁时间</TableHead>
                          <TableHead class="data-table-cell">数据类型</TableHead>
                          <TableHead class="data-table-cell">数据量</TableHead>
                          <TableHead class="data-table-cell">销毁方式</TableHead>
                          <TableHead class="data-table-cell">操作员</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow
                          v-for="record in destroyRecords"
                          :key="record.id"
                          class="data-table-row"
                        >
                          <TableCell class="data-table-cell">{{
                            formatDateTime(record.timestamp)
                          }}</TableCell>
                          <TableCell class="data-table-cell">{{ record.dataType }}</TableCell>
                          <TableCell class="data-table-cell">{{ record.dataSize }}</TableCell>
                          <TableCell class="data-table-cell">{{ record.method }}</TableCell>
                          <TableCell class="data-table-cell">{{ record.operator }}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Database,
  Workflow,
  Download,
  HardDrive,
  Send,
  Settings,
  Share,
  Globe,
  Trash2,
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from '@/components/ui/table'

interface Props {
  enterpriseId: string
}

const props = defineProps<Props>()

// 活动状态
const activeStage = ref('collect')

// 活动概览统计
const totalActivities = computed(() => 15)
const filedActivities = computed(() => 12)
const pendingActivities = computed(() => 2)
const rejectedActivities = computed(() => 1)

// 数据收集阶段详情
const collectDetails = ref({
  dataImportanceLevel: 2, // 0x01:一般数据; 0x02:重要数据
  dataSourceType: 1, // 0x01:本企业研采车; 0x02:本企业量产车; 0x03:第三方企业提供; 0x04:其他
  dataSourceId: 'LSGJ8A12X34567890', // 车端VIN或第三方企业统一社会信用代码
  dataPurpose: 1, // 0x01:数据汇聚及脱敏处理; 0x02:导航电子地图制作; 0x03:场景库制作及服务
  operatorIdentity: 1, // 0x01:重要数据操作人员; 0x02:一般数据操作人员
})

// 数据存储阶段详情
const storeDetails = ref({
  storageDeviceType: 1, // 0x01:服务器; 0x02:存储阵列; 0x03:网络存储; 0x04:硬盘; 0x05:其他
  storageRegionFlag: 1, // 0x01:境内存储; 0x02:境外存储
  storageDeviceIp: '*************',
  storageAreaType: 1, // 0x01:原始数据处理区; 0x02:数据共享区; 0x03:数据中转区; 0x04:其他区域
  dataPartitionFlag: 1, // 0x01:已分区存储; 0x02:未分区存储
  storageSafeguardBitmap: 7, // BitMap。Bit0:完整性, Bit1:真实性, Bit2:可用性
  desensitizationFlag: 1, // 0x01:已脱敏; 0x02:未脱敏
})

// 数据传输阶段详情
const transferDetails = ref({
  targetEnterpriseCode: '91440300567890123Z',
  communicationChannelType: 1, // 0x01:国密通道; 0x02:非国密通道
  publicNetworkUsageFlag: 2, // 0x01:使用公共信息网络; 0x02:不使用
  transmissionMethod: 1, // 0x01:网络传输; 0x02:硬盘拷贝; 0x03:其他
  networkType: 2, // 0x01:公共网络; 0x02:专用网络; 0x03:国家认定网络; 0x04:其他
  securityProtocol: 3, // 0x01:HTTP; 0x02:HTTPS; 0x03:国密通道; 0x04:其他
  transmissionSafeguardBitmap: 7, // BitMap。Bit0:完整性 Bit1:真实性 Bit2:可用性
})

// 数据加工阶段详情
const processDetails = ref({
  processingType: 1, // 0x01:数据汇聚及脱敏处理; 0x02:导航电子地图制作; 0x03:场景库制作及服务; 0x04:互联网地图服务; 0x05:其他
  positionAccuracyCompliance: 1, // 0x01:符合要求; 0x02:不符合要求
  desensitizationStatus: 1, // 0x01:已脱敏; 0x02:未脱敏
  isNewPackageGenerated: 1, // 0x01:是; 0x02:否
  newPackageCount: 3,
})

// 数据提供阶段详情
const provideDetails = ref({
  totalPackagesInOperation: 5,
  recipientType: 1, // 0x01:境内非外商投资企业; 0x02:境内外商投资企业; 0x03:境外接收方; 0x04:个人
  recipientSecurityCapability: 1, // 0x01:安全能力满足; 0x02:不足
  isExemptionMet: 2, // 0x01:是; 0x02:否
  securityMeasuresBitmap: 15, // BitMap of security technologies
  contractStatus: 2, // 0x01:无合同协议; 0x02:有合同协议
})

// 模拟数据
const collectRecords = ref([
  {
    id: 1,
    timestamp: '2024-01-20T10:30:00',
    sourceId: 'LSGJ8A12X34567890',
    dataSize: '2.5GB',
    importance: 2,
    operator: '张工程师',
  },
  {
    id: 2,
    timestamp: '2024-01-20T14:15:00',
    sourceId: 'WBAVC31060A123456',
    dataSize: '1.8GB',
    importance: 1,
    operator: '李技术员',
  },
  {
    id: 3,
    timestamp: '2024-01-20T16:45:00',
    sourceId: 'LFPH3ACC9J1234567',
    dataSize: '3.2GB',
    importance: 2,
    operator: '王专员',
  },
])

const newDataPackages = ref([
  { id: 'PKG-001', dataType: '脱敏地图数据', createdAt: '2024-01-20T11:00:00', size: '1.2GB' },
  { id: 'PKG-002', dataType: '导航路径数据', createdAt: '2024-01-20T11:15:00', size: '0.8GB' },
  { id: 'PKG-003', dataType: '场景库数据', createdAt: '2024-01-20T11:30:00', size: '2.1GB' },
])

const destroyRecords = ref([
  {
    id: 1,
    timestamp: '2024-01-15T18:00:00',
    dataType: '过期缓存数据',
    dataSize: '500MB',
    method: '安全擦除',
    operator: '系统管理员',
  },
  {
    id: 2,
    timestamp: '2024-01-10T20:30:00',
    dataType: '临时处理数据',
    dataSize: '1.2GB',
    method: '物理销毁',
    operator: '数据管理员',
  },
  {
    id: 3,
    timestamp: '2024-01-05T16:45:00',
    dataType: '测试数据',
    dataSize: '800MB',
    method: '逻辑删除',
    operator: '测试工程师',
  },
])

const securityTechnologies = [
  '数据加密',
  '访问控制',
  '传输加密',
  '数字签名',
  '审计日志',
  '完整性校验',
  '脱敏处理',
  '安全传输',
]

// 工具方法
const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getDataSourceType = (type: number) => {
  const types = ['', '本企业研采车', '本企业量产车', '第三方企业提供', '其他']
  return types[type] || '未知'
}

const getDataPurpose = (purpose: number) => {
  const purposes = ['', '数据汇聚及脱敏处理', '导航电子地图制作', '场景库制作及服务']
  return purposes[purpose] || '未知'
}

const getStorageDeviceType = (type: number) => {
  const types = ['', '服务器', '存储阵列', '网络存储', '硬盘', '其他']
  return types[type] || '未知'
}

const getStorageAreaType = (type: number) => {
  const types = ['', '原始数据处理区', '数据共享区', '数据中转区', '其他区域']
  return types[type] || '未知'
}

const getTransmissionMethod = (method: number) => {
  const methods = ['', '网络传输', '硬盘拷贝', '其他']
  return methods[method] || '未知'
}

const getNetworkType = (type: number) => {
  const types = ['', '公共网络', '专用网络', '国家认定网络', '其他']
  return types[type] || '未知'
}

const getSecurityProtocol = (protocol: number) => {
  const protocols = ['', 'HTTP', 'HTTPS', '国密通道', '其他']
  return protocols[protocol] || '未知'
}

const getProcessingType = (type: number) => {
  const types = [
    '',
    '数据汇聚及脱敏处理',
    '导航电子地图制作',
    '场景库制作及服务',
    '互联网地图服务',
    '其他',
  ]
  return types[type] || '未知'
}

const getRecipientType = (type: number) => {
  const types = ['', '境内非外商投资企业', '境内外商投资企业', '境外接收方', '个人']
  return types[type] || '未知'
}
</script>
