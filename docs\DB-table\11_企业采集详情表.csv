﻿表名: log_ent_collect_detail,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id"""
3,data_importance_level,数据重要程度,SMALLINT,,○,,"""(BYTE) 0x01:一般数据; 0x02:重要数据"""
4,data_source_type,数据来源类型,SMALLINT,,○,,"""(BYTE) 0x01:本企业研采车; 0x02:本企业量产车; 0x03:第三方企业提供; 0x04:其他"""
5,data_source_id,数据来源方标识,VARCHAR,32,○,,根据“数据来源类型”填写车端VIN或第三方企业统一社会信用代码。
6,data_purpose,数据用途,SMALLINT,,○,,"""(BYTE) 0x01:数据汇聚及脱敏处理; 0x02:导航电子地图制作; 0x03:场景库制作及服务"""
7,operator_identity,操作员身份,SMALLINT,,○,,"""(BYTE) 0x01:重要数据操作人员; 0x02:一般数据操作人员"""
,,,,,,,
,,,,,,,
,,,,,,,
,,,,,,,
