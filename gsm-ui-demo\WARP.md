# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

Geographic Information Security Monitoring Platform (时空数据安全监测平台) - A Vue 3 + TypeScript + Vite web application for spatiotemporal data security monitoring in intelligent connected vehicles.

**Important**: This is a static demo prototype for UI demonstration purposes. Form validation and backend integration are intentionally minimal.

## Common Development Commands

All commands should be run from the `gsm-ui-demo/` directory:

```bash
# Development
npm run dev          # Start Vite dev server on port 5173

# Building & Type Checking  
npm run build        # Production build with TypeScript type checking
npm run build-only   # Build without type checking (faster)
npm run type-check   # Run TypeScript type checking only

# Code Quality
npm run lint         # Run ESLint with auto-fix
npm run format       # Format code with Prettier

# Preview
npm run preview      # Preview production build locally
```

**Requirements**: Node.js ^20.19.0 || >=22.12.0

## High-Level Architecture

The platform implements a four-tier distributed regulatory framework:

1. **National Level** - Central monitoring and policy management
2. **Regional Level** - Regional data aggregation and supervision  
3. **Enterprise Level** - Company compliance and data reporting
4. **Terminal Level** - Vehicle/device data collection (future integration)

### Frontend Routing Structure

- `/gov/*` - Government regulatory interface (国家/区域监管端)
  - Dashboard, registration approval, real-time monitoring, risk management
- `/corp/*` - Enterprise management interface (企业端)  
  - Dashboard, compliance filing, risk events, notifications
- `/` - Public landing page and authentication

The Vue Router configuration in `src/router/index.ts` maps directly to these regulatory tiers, with role-based access control.

## Critical Development Standards

### [!] NON-NEGOTIABLE Component Rules

- **ONLY** use official shadcn-vue components via: `npx shadcn-vue@latest add [component]`
- **NEVER** create manual/simplified/copied UI components
- **ALWAYS** fix root causes - no temporary workarounds or technical debt
- **STRICTLY** follow the official shadcn-vue documentation

### Required Technology Stack

- **UI Components**: shadcn-vue (official only) + Radix Vue
- **Styling**: Tailwind CSS with HSL-based theme variables
- **Icons**: Lucide Vue Next
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Build Tool**: Vite 7.0

### File Structure Conventions

```
src/
├── components/
│   ├── ui/           # Official shadcn-vue components ONLY
│   └── *.vue         # Custom app components
├── views/            # Page components
│   ├── government/   # Government module pages
│   └── enterprise/   # Enterprise module pages  
├── router/           # Vue Router configuration
├── stores/           # Pinia state stores
├── lib/utils.ts      # Utility functions
└── assets/index.css  # Tailwind + theme CSS variables
```

### Coding Patterns

- Vue 3 Composition API with `<script setup lang="ts">`
- TypeScript strict mode enabled
- HSL-based theme system with light/dark mode toggle
- Responsive design using Tailwind CSS utilities
- Component props and emits must be properly typed

### Development Workflow

1. Check if shadcn-vue has the required component
2. Add via official CLI command
3. Implement features using official patterns
4. Fix any issues at the root cause
5. Test thoroughly before committing

## Important Context from CLAUDE.md

- This is a government regulatory platform with enterprise integration
- Two primary user types: Government (监管方) and Enterprise (企业方)
- Currently implementing government-side functionality first
- 53+ pages planned across both user modules
- Static demo prototype - buttons can submit/navigate directly without validation

## Quick Component Reference

Common shadcn-vue components already installed:
- Alert, Avatar, Badge, Breadcrumb, Button, Card, Chart
- Checkbox, Collapsible, Dropdown Menu, Form, Input, Label
- Separator, Sheet, Sidebar, Table, Tabs, Tooltip

Add new components only via: `npx shadcn-vue@latest add [component-name]`
