// 风险类型编码映射工具
export const riskTypeMapping = {
  // 车端风险类型
  '0x01': '车端数据泄露/丢失',
  '0x04': '核心安全策略异常',
  'RT-001': '违规区域采集',
  'RT-021': '未授权跨境传输',
  'RT-015': '核心安全策略失效',
  'RT-008': '数据采集权限异常',
  
  // 云端风险类型
  '0x10': '数据泄露/丢失',
  'RE-021': '未脱敏对外提供',
  'RE-011': '境外存储核心数据',
  'RE-031': '数据泄露/丢失',
  'RE-041': '数据备份异常'
}

// 获取风险类型标签
export const getRiskTypeLabel = (eventType: string): string => {
  return riskTypeMapping[eventType] || eventType
}

// 数据重要性等级映射
export const dataImportanceMapping = {
  '0x01': '一般',
  '0x02': '重要',
  '0x03': '核心'
}

export const getDataImportanceLabel = (importance: string | number): string => {
  const key = typeof importance === 'number' ? `0x0${importance}` : importance.toString()
  return dataImportanceMapping[key] || importance.toString()
}

// 处理阶段映射
export const processingStageMapping = {
  '0x01': '数据收集',
  '0x02': '数据存储', 
  '0x03': '数据处理',
  '0x04': '数据传输',
  '0x05': '数据提供',
  '0x06': '数据销毁'
}

export const getProcessingStageLabel = (stage: string | number): string => {
  const key = typeof stage === 'number' ? `0x0${stage}` : stage.toString()
  return processingStageMapping[key] || stage.toString()
}

// 存储区域映射
export const storageAreaMapping = {
  '0x01': '境内存储',
  '0x02': '境外存储'
}

export const getStorageAreaLabel = (area: string | number): string => {
  const key = typeof area === 'number' ? `0x0${area}` : area.toString()
  return storageAreaMapping[key] || area.toString()
}

// 安全处理技术映射
export const securityTechMapping = {
  '0x0000': '未采用安全处理技术',
  '0x0001': '仅启用地理围栏',
  '0x0002': '位置数据保密处理',
  '0x0004': '属性脱敏技术',
  '0x0008': '里程限制技术'
}

export const getSecurityTechLabel = (tech: string | number): string => {
  const key = typeof tech === 'number' ? `0x${tech.toString(16).padStart(4, '0')}` : tech.toString()
  return securityTechMapping[key] || tech.toString()
}