<template>
  <LabelRoot
    :class="
      cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        props.class,
      )
    "
    v-bind="forwarded"
  >
    <slot />
  </LabelRoot>
</template>

<script setup lang="ts">
import { type HTMLAttributes, computed } from 'vue'
import { Label as LabelRoot, type LabelProps as LabelRootProps, useForwardProps } from 'radix-vue'
import { cn } from '@/lib/utils'

const props = defineProps<LabelRootProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props
  return delegated
})

const forwarded = useForwardProps(delegatedProps)
</script>
