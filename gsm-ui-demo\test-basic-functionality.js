/**
 * 基本功能测试脚本
 * 测试应用程序的基本功能是否正常工作
 */

const http = require('http');
const https = require('https');

// 测试配置
const DEV_SERVER = 'http://localhost:5174';
const PREVIEW_SERVER = 'http://localhost:4173';

// 测试用例
const testCases = [
  { name: '首页', path: '/' },
  { name: '登录页面', path: '/login' },
  { name: '政府登录', path: '/login/government' },
  { name: '企业登录', path: '/login/enterprise' },
  { name: '找回密码', path: '/password/reset' },
  { name: '个人实名认证', path: '/auth/activate' },
];

// HTTP 请求函数
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    }).on('error', reject);
  });
}

// 测试单个端点
async function testEndpoint(server, testCase) {
  const url = `${server}${testCase.path}`;
  
  try {
    console.log(`测试: ${testCase.name} (${url})`);
    const response = await makeRequest(url);
    
    if (response.statusCode === 200) {
      console.log(`✅ ${testCase.name}: 成功 (状态码: ${response.statusCode})`);
      
      // 检查响应内容
      if (response.body.includes('<!DOCTYPE html>') || response.body.includes('<html')) {
        console.log(`   📄 返回了有效的 HTML 内容`);
      }
      
      return true;
    } else {
      console.log(`❌ ${testCase.name}: 失败 (状态码: ${response.statusCode})`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${testCase.name}: 错误 - ${error.message}`);
    return false;
  }
}

// 测试服务器
async function testServer(serverUrl, serverName) {
  console.log(`\n🚀 测试 ${serverName} (${serverUrl})`);
  console.log('='.repeat(50));
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    const success = await testEndpoint(serverUrl, testCase);
    if (success) passedTests++;
    
    // 添加小延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n📊 ${serverName} 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log(`🎉 ${serverName} 所有测试通过！`);
  } else {
    console.log(`⚠️  ${serverName} 有 ${totalTests - passedTests} 个测试失败`);
  }
  
  return passedTests === totalTests;
}

// 主测试函数
async function runTests() {
  console.log('🧪 开始基本功能测试...');
  console.log('测试时间:', new Date().toLocaleString());
  
  try {
    // 测试开发服务器
    const devSuccess = await testServer(DEV_SERVER, '开发服务器');
    
    // 测试预览服务器
    const previewSuccess = await testServer(PREVIEW_SERVER, '预览服务器');
    
    // 总结
    console.log('\n' + '='.repeat(60));
    console.log('📋 测试总结:');
    console.log(`   开发服务器: ${devSuccess ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   预览服务器: ${previewSuccess ? '✅ 通过' : '❌ 失败'}`);
    
    if (devSuccess && previewSuccess) {
      console.log('\n🎊 所有测试通过！应用程序运行正常。');
      process.exit(0);
    } else {
      console.log('\n⚠️  部分测试失败，请检查服务器状态。');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testServer, testEndpoint };
