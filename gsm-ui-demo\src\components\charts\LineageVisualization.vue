<template>
  <div
    class="lineage-container"
    :style="{ minHeight: height + 'px' }"
    :class="{
      'bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950': theme === 'dark',
      // 明色主题略加深层次，避免强对比
      'bg-gradient-to-b from-slate-100 via-white to-slate-100': theme === 'light',
    }"
  >
    <div class="p-6 space-y-4">
      <!-- 线性（单列）布局：保留连接线与箭头 -->
      <template v-if="layout === 'linear'">
        <div
          v-for="(node, index) in data"
          :key="node.id"
          class="relative flex flex-col items-center"
        >
          <!-- 连接线 (除了第一个节点) -->
          <div
            v-if="index > 0"
            class="w-1 h-6 mb-2 flex items-center justify-center relative"
            :class="{
              'bg-gradient-to-b from-blue-600 to-blue-700': !node.isRisk,
              'bg-gradient-to-b from-red-600 to-red-700': node.isRisk,
            }"
          >
            <ArrowDown
              :class="{
                'w-4 h-4 text-blue-200': !node.isRisk,
                'w-4 h-4 text-red-200': node.isRisk,
              }"
            />
            <div
              class="absolute inset-0 w-1"
              :class="{
                'shadow-md shadow-blue-500/20': !node.isRisk,
                'shadow-md shadow-red-500/20': node.isRisk,
              }"
            ></div>
          </div>

          <!-- 节点卡片（线性布局） -->
          <Card
            class="w-full max-w-2xl transition-all duration-300 hover:scale-[1.01] relative overflow-hidden"
            :class="{
              'bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800 border-slate-600 hover:border-slate-500':
                !node.isRisk && theme === 'dark',
              'bg-gradient-to-br from-red-950 via-red-900 to-red-950 border-red-700 hover:border-red-600':
                node.isRisk && theme === 'dark',
              'bg-gradient-to-br from-slate-50 via-white to-slate-50 border-slate-200 hover:border-slate-300 shadow-sm':
                !node.isRisk && theme === 'light',
              'bg-gradient-to-br from-red-50 via-white to-red-50 border-red-200 hover:border-red-300':
                node.isRisk && theme === 'light',
            }"
          >
            <div
              class="absolute top-0 left-0 w-full h-1"
              :class="{
                'bg-gradient-to-r from-blue-600 to-blue-700': !node.isRisk,
                'bg-gradient-to-r from-red-600 to-red-700': node.isRisk,
              }"
            ></div>
            <CardHeader class="pb-3">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div
                    class="w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg"
                    :class="{
                      'bg-blue-600 text-white': !node.isRisk,
                      'bg-red-600 text-white': node.isRisk,
                    }"
                  >
                    {{ index + 1 }}
                  </div>
                  <div>
                    <CardTitle
                      class="text-lg font-bold"
                      :class="{
                        'text-slate-100': theme === 'dark',
                        'text-slate-900': theme === 'light',
                      }"
                    >
                      {{ node.stage }}
                    </CardTitle>
                    <p
                      class="text-sm"
                      :class="{
                        'text-slate-300': theme === 'dark',
                        'text-slate-600': theme === 'light',
                      }"
                    >
                      {{ node.name }}
                    </p>
                  </div>
                </div>
                <Badge :variant="node.isRisk ? 'destructive' : 'secondary'" class="font-medium">
                  <AlertTriangle v-if="node.isRisk" class="w-3 h-3 mr-1" />
                  <CheckCircle v-else class="w-3 h-3 mr-1" />
                  {{ node.isRisk ? '异常' : '正常' }}
                </Badge>
              </div>
            </CardHeader>
            <CardContent class="space-y-3">
              <div class="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span
                    class="font-medium"
                    :class="{
                      'text-slate-300': theme === 'dark',
                      'text-slate-600': theme === 'light',
                    }"
                    >日志ID:</span
                  >
                  <p
                    class="font-mono text-xs mt-1 px-2 py-1 rounded"
                    :class="{
                      'bg-slate-700 text-slate-200': theme === 'dark',
                      'bg-gray-100 text-gray-800': theme === 'light',
                    }"
                  >
                    {{ node.logId }}
                  </p>
                </div>
                <div>
                  <span
                    class="font-medium"
                    :class="{
                      'text-slate-300': theme === 'dark',
                      'text-slate-600': theme === 'light',
                    }"
                    >时间:</span
                  >
                  <p
                    class="text-xs mt-1"
                    :class="{
                      'text-slate-200': theme === 'dark',
                      'text-gray-700': theme === 'light',
                    }"
                  >
                    {{ formatTime(node.timestamp) }}
                  </p>
                </div>
              </div>
              <Alert
                v-if="node.isRisk && node.riskDescription"
                variant="destructive"
                :class="[
                  'mt-3 border-2 shadow-md shadow-red-500/20',
                  theme === 'dark'
                    ? 'border-red-500/60 bg-red-900/40 text-red-100'
                    : 'border-red-600/40 bg-red-50 text-red-800',
                ]"
              >
                <AlertTriangle class="h-4 w-4" />
                <AlertTitle class="font-semibold">风险详情</AlertTitle>
                <AlertDescription class="text-sm leading-relaxed">{{
                  node.riskDescription
                }}</AlertDescription>
              </Alert>
              <div v-if="node.details" class="mt-3">
                <div
                  class="text-sm font-medium mb-2"
                  :class="{
                    'text-slate-300': theme === 'dark',
                    'text-slate-600': theme === 'light',
                  }"
                >
                  详细信息:
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                  <div
                    v-for="[key, value] in Object.entries(node.details)"
                    :key="key"
                    class="flex justify-between items-center gap-2 text-xs px-2 py-1 rounded"
                    :class="{
                      'bg-slate-800/50 text-slate-300': theme === 'dark',
                      'bg-gray-50 text-gray-700': theme === 'light',
                    }"
                  >
                    <span class="font-medium whitespace-nowrap">{{ key }}:</span>
                    <span class="truncate">{{ value }}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        <!-- 结束节点 -->
        <div v-if="data.length > 0" class="relative flex flex-col items-center mt-6">
          <div
            class="w-1 h-6 mb-2 bg-gradient-to-b from-emerald-600 to-emerald-700 shadow-md shadow-emerald-500/20 flex items-center justify-center"
          >
            <CheckCircle class="w-4 h-4 text-emerald-200" />
          </div>
          <div
            :class="[
              'w-20 h-20 rounded-full flex items-center justify-center font-bold text-lg',
              theme === 'dark'
                ? 'bg-gradient-to-br from-emerald-600 to-emerald-700 text-white'
                : 'bg-gradient-to-br from-emerald-500 to-emerald-600 text-white',
            ]"
          >
            <CheckCircle class="w-8 h-8" />
          </div>
          <p
            :class="[
              'mt-2 text-sm font-medium',
              theme === 'dark' ? 'text-slate-300' : 'text-slate-600',
            ]"
          >
            溯源完成
          </p>
        </div>
      </template>

      <!-- 网格（多列）布局：撑满宽度，移除连接线 -->
      <template v-else>
        <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
          <Card
            v-for="(node, index) in data"
            :key="node.id"
            class="w-full transition-all duration-300 hover:scale-[1.01] relative overflow-hidden"
            :class="{
              'bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800 border-slate-600 hover:border-slate-500':
                !node.isRisk && theme === 'dark',
              'bg-gradient-to-br from-red-950 via-red-900 to-red-950 border-red-700 hover:border-red-600':
                node.isRisk && theme === 'dark',
              'bg-gradient-to-br from-slate-50 via-white to-slate-50 border-slate-200 hover:border-slate-300 shadow-sm':
                !node.isRisk && theme === 'light',
              'bg-gradient-to-br from-red-50 via-white to-red-50 border-red-200 hover:border-red-300':
                node.isRisk && theme === 'light',
            }"
          >
            <div
              class="absolute top-0 left-0 w-full h-1"
              :class="{
                'bg-gradient-to-r from-blue-600 to-blue-700': !node.isRisk,
                'bg-gradient-to-r from-red-600 to-red-700': node.isRisk,
              }"
            ></div>
            <CardHeader class="pb-3">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div
                    :class="[
                      'w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg',
                      node.isRisk ? 'bg-red-600 text-white' : 'bg-blue-600 text-white',
                    ]"
                  >
                    {{ index + 1 }}
                  </div>
                  <div>
                    <CardTitle
                      :class="[
                        'text-lg font-bold',
                        theme === 'dark' ? 'text-slate-100' : 'text-slate-900',
                      ]"
                      >{{ node.stage }}</CardTitle
                    >
                    <p :class="['text-sm', theme === 'dark' ? 'text-slate-300' : 'text-slate-600']">
                      {{ node.name }}
                    </p>
                  </div>
                </div>
                <Badge :variant="node.isRisk ? 'destructive' : 'secondary'" class="font-medium">
                  <AlertTriangle v-if="node.isRisk" class="w-3 h-3 mr-1" />
                  <CheckCircle v-else class="w-3 h-3 mr-1" />
                  {{ node.isRisk ? '异常' : '正常' }}
                </Badge>
              </div>
            </CardHeader>
            <CardContent class="space-y-3">
              <div class="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span
                    :class="['font-medium', theme === 'dark' ? 'text-slate-300' : 'text-slate-600']"
                    >日志ID:</span
                  >
                  <p
                    :class="[
                      'font-mono text-xs mt-1 px-2 py-1 rounded',
                      theme === 'dark'
                        ? 'bg-slate-700 text-slate-200'
                        : 'bg-gray-100 text-gray-800',
                    ]"
                  >
                    {{ node.logId }}
                  </p>
                </div>
                <div>
                  <span
                    :class="['font-medium', theme === 'dark' ? 'text-slate-300' : 'text-slate-600']"
                    >时间:</span
                  >
                  <p
                    :class="['text-xs mt-1', theme === 'dark' ? 'text-slate-200' : 'text-gray-700']"
                  >
                    {{ formatTime(node.timestamp) }}
                  </p>
                </div>
              </div>
              <Alert
                v-if="node.isRisk && node.riskDescription"
                variant="destructive"
                :class="[
                  'mt-3 border-2 shadow-md shadow-red-500/20',
                  theme === 'dark'
                    ? 'border-red-500/60 bg-red-900/40 text-red-100'
                    : 'border-red-600/40 bg-red-50 text-red-800',
                ]"
              >
                <AlertTriangle class="h-4 w-4" />
                <AlertTitle class="font-semibold">风险详情</AlertTitle>
                <AlertDescription class="text-sm leading-relaxed">{{
                  node.riskDescription
                }}</AlertDescription>
              </Alert>
              <div v-if="node.details" class="mt-3">
                <div
                  :class="[
                    'text-sm font-medium mb-2',
                    theme === 'dark' ? 'text-slate-300' : 'text-slate-600',
                  ]"
                >
                  详细信息:
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                  <div
                    v-for="[key, value] in Object.entries(node.details)"
                    :key="key"
                    :class="[
                      'flex justify-between items-center gap-2 text-xs px-2 py-1 rounded',
                      theme === 'dark'
                        ? 'bg-slate-800/50 text-slate-300'
                        : 'bg-gray-50 text-gray-700',
                    ]"
                  >
                    <span class="font-medium whitespace-nowrap">{{ key }}:</span>
                    <span class="truncate">{{ value }}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ArrowDown, CheckCircle, AlertTriangle } from 'lucide-vue-next'

interface LineageNode {
  id: string
  name: string
  stage: string
  timestamp: number
  isRisk: boolean
  riskDescription?: string
  logId: string
  details?: Record<string, any>
}

interface Props {
  data: LineageNode[]
  height?: number
  theme?: 'dark' | 'light'
  layout?: 'linear' | 'grid'
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  theme: 'light',
  layout: 'grid',
})

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}
</script>

<style scoped>
.lineage-container {
  width: 100%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    inset 0 1px 0 rgba(56, 189, 248, 0.1),
    0 4px 20px rgba(15, 23, 42, 0.5);
}

.lineage-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(56, 189, 248, 0.6), transparent);
  pointer-events: none;
  z-index: 10;
}

.lineage-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.4), transparent);
  pointer-events: none;
  z-index: 10;
}
</style>
