name: gitleaks (secret scan)

on:
  push:
    branches: ['**']
  pull_request:
    branches: ['**']

jobs:
  scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: <PERSON><PERSON> with gitleaks
        uses: gitleaks/gitleaks-action@v2
        with:
          # 全历史扫描：扫描所有分支与历史记录
          args: --verbose --redact --source="." --log-opts="--all"
