<template>
  <ChartContainer
    :option="barOption"
    :height="height"
    :color-scheme="derivedScheme"
    :loading="loading"
    :enable-toolbox="enableToolbox"
    :enable-data-zoom="enableDataZoom"
    @click="handleClick"
    @mouseover="handleMouseOver"
    @mouseout="handleMouseOut"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ChartContainer from './ChartContainer.vue'
import { createTooltipFormatter, createLinearGradient } from '@/lib/chart-themes'
import { getUnifiedColorScheme, isRiskChart } from '@/lib/unified-chart-colors'

interface DataItem {
  name: string
  value: number
  color?: string
  description?: string
}

interface SeriesItem {
  name: string
  data: DataItem[]
  color?: string
  stack?: string
  barGap?: string
  barCategoryGap?: string
  showBackground?: boolean
}

interface Props {
  data?: DataItem[]
  series?: SeriesItem[]
  title?: string
  height?: string | number
  direction?: 'horizontal' | 'vertical'
  showLegend?: boolean
  colorScheme?:
    | 'primary'
    | 'risk'
    | 'enterprise'
    | 'stages'
    | 'status'
    | 'vehicleTypes'
    | 'barChart'
    | 'disposalStatus'
    | 'processingStages'
    | 'eventTypes'
    | 'approvalStatus'
  loading?: boolean
  enableToolbox?: boolean
  enableDataZoom?: boolean
  yAxisName?: string
  xAxisName?: string
  gridTop?: string | number
  gridBottom?: string | number
  showValues?: boolean
  barWidth?: string | number
  showBackground?: boolean
  stackType?: 'normal' | 'percentage'
  autoColor?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  direction: 'vertical',
  showLegend: false,
  colorScheme: 'primary',
  loading: false,
  enableToolbox: false,
  enableDataZoom: false,
  showValues: false,
  barWidth: '60%',
  showBackground: false,
  stackType: 'normal',
  autoColor: true,
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
}>()

// 处理数据：支持单系列和多系列
const processedData = computed(() => {
  if (props.data) {
    // 单系列模式
    return {
      categories: props.data.map((item) => item.name),
      series: [
        {
          name: props.title || '数据统计',
          data: props.data,
          stack: undefined,
        },
      ],
    }
  } else if (props.series) {
    // 多系列模式
    const allCategories = new Set<string>()
    props.series.forEach((s) => {
      s.data.forEach((d) => allCategories.add(d.name))
    })

    return {
      categories: Array.from(allCategories).sort(),
      series: props.series,
    }
  }

  return { categories: [], series: [] }
})

// 推断配色方案（仅当 autoColor 为 true 且未明确使用非 primary 方案时）
const derivedScheme = computed(() => {
  if (!props.autoColor) return props.colorScheme
  if (props.colorScheme && props.colorScheme !== 'primary') return props.colorScheme

  // 收集所有数据用于推断
  const allData: any[] = []
  if (props.data) allData.push(...props.data)
  if (props.series) {
    props.series.forEach((s) => allData.push(...s.data))
  }

  // 对于柱状图，如果是风险类数据，使用风险配色；否则使用barChart配色
  if (isRiskChart(allData)) return 'risk'
  return 'barChart'
})

const barOption = computed(() => {
  const { categories, series } = processedData.value
  const colors = getUnifiedColorScheme(derivedScheme.value || 'primary')

  return {
    title: props.title
      ? {
          text: props.title,
          left: 'center',
          top: 16,
          textStyle: {
            fontSize: 18,
            fontWeight: '600',
          },
        }
      : undefined,

    legend:
      props.showLegend && series.length > 1
        ? {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 15,
            left: 'center',
            itemGap: 20,
            itemWidth: 14,
            itemHeight: 14,
            pageButtonItemGap: 8,
            pageIconColor: '#64748b',
            pageIconInactiveColor: '#cbd5e1',
            animation: true,
            animationDurationUpdate: 600,
          }
        : undefined,

    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(139, 21, 56, 0.1)',
        },
      },
      formatter: createTooltipFormatter('bar'),
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        fontSize: 12,
      },
      padding: [12, 16],
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); backdrop-filter: blur(8px);',
    },

    grid: {
      left: '5%',
      right: '5%',
      bottom: props.showLegend && series.length > 1 ? '22%' : props.gridBottom || '12%',
      top: props.title ? '22%' : props.gridTop || '12%',
      containLabel: true,
    },

    xAxis: {
      type: props.direction === 'horizontal' ? 'value' : 'category',
      data: props.direction === 'vertical' ? categories : undefined,
      name: props.xAxisName,
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        fontSize: 12,
        fontWeight: '500',
      },
      axisLine: {
        show: props.direction === 'vertical',
        lineStyle: {
          color: 'rgba(148, 163, 184, 0.3)',
          width: 1,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#64748b',
        fontSize: 13,
        fontWeight: '500',
        margin: 10,
        rotate: props.direction === 'vertical' && categories.length > 6 ? 45 : 0,
      },
      splitLine: {
        show: props.direction === 'horizontal',
        lineStyle: {
          color: 'rgba(148, 163, 184, 0.1)',
          type: 'dashed',
        },
      },
    },

    yAxis: {
      type: props.direction === 'horizontal' ? 'category' : 'value',
      data: props.direction === 'horizontal' ? categories : undefined,
      name: props.yAxisName,
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        fontSize: 12,
        fontWeight: '500',
      },
      axisLine: {
        show: props.direction === 'horizontal',
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#64748b',
        fontSize: 13,
        fontWeight: '500',
        margin: 10,
      },
      splitLine: {
        show: props.direction === 'vertical',
        lineStyle: {
          color: 'rgba(148, 163, 184, 0.1)',
          width: 1,
          type: 'dashed',
        },
      },
    },

    series: series.map((seriesItem, seriesIndex) => {
      const baseColor = colors[seriesIndex % colors.length]

      return {
        name: seriesItem.name,
        type: 'bar',
        stack: props.stackType === 'percentage' ? 'total' : seriesItem.stack,
        barWidth: props.barWidth || '50%',
        barGap: seriesItem.barGap || (series.length > 1 ? '20%' : '40%'),
        barCategoryGap: seriesItem.barCategoryGap || '30%',

        // 数据处理
        data: categories.map((category, categoryIndex) => {
          const dataItem = seriesItem.data.find((d) => d.name === category)
          if (!dataItem) return 0

          const itemColor = dataItem.color || baseColor

          return {
            name: dataItem.name,
            value: dataItem.value,
            description: dataItem.description,
            itemStyle: {
              color: props.showBackground
                ? createLinearGradient(
                    'blue2',
                    props.direction === 'horizontal' ? 'horizontal' : 'vertical',
                  )
                : itemColor,
              borderRadius: props.direction === 'horizontal' ? [0, 6, 6, 0] : [6, 6, 0, 0],
              shadowBlur: 4,
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowOffsetY: 2,
            },
          }
        }),

        // 显示数值标签
        label: props.showValues
          ? {
              show: true,
              position: props.direction === 'horizontal' ? 'right' : 'top',
              fontSize: 13,
              fontWeight: '600',
              color: '#475569',
              formatter: '{c}',
              distance: 5,
            }
          : undefined,

        // 背景柱子
        showBackground: props.showBackground || seriesItem.showBackground,
        backgroundStyle: {
          color: 'rgba(148, 163, 184, 0.1)',
          borderRadius: props.direction === 'horizontal' ? [0, 4, 4, 0] : [4, 4, 0, 0],
        },

        // 悬浮效果
        emphasis: {
          focus: 'series',
          blurScope: 'coordinateSystem',
          itemStyle: {
            borderColor: baseColor,
            borderWidth: 2,
            shadowBlur: 16,
            shadowColor: baseColor + '60',
            opacity: 0.95,
          },
          label: props.showValues
            ? {
                fontSize: 14,
                fontWeight: '700',
                color: '#1e293b',
              }
            : undefined,
        },

        // 选中效果
        select: {
          itemStyle: {
            borderColor: '#8b1538',
            borderWidth: 3,
            shadowBlur: 8,
            shadowColor: '#8b1538' + '40',
          },
        },

        // 动画配置
        animationDuration: 1000,
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => idx * 80,
      }
    }),

    // 数据缩放配置（仅垂直柱状图支持）
    dataZoom:
      props.enableDataZoom && props.direction === 'vertical'
        ? [
            {
              type: 'slider',
              show: true,
              bottom: 5,
              start: 0,
              end: 100,
              height: 20,
            },
            {
              type: 'inside',
              zoomOnMouseWheel: 'shift',
              moveOnMouseMove: 'ctrl',
            },
          ]
        : undefined,

    // 全局动画配置
    animationDuration: 1000,
    animationEasing: 'cubicOut',

    // 媒体查询配置
    media: [
      {
        query: {
          maxWidth: 768,
        },
        option: {
          grid: {
            left: '5%',
            right: '5%',
            bottom: props.showLegend && series.length > 1 ? '25%' : '12%',
            top: props.title ? '25%' : '12%',
          },
          legend:
            props.showLegend && series.length > 1
              ? {
                  orient: 'horizontal',
                  bottom: 10,
                  left: 'center',
                }
              : undefined,
          xAxis:
            props.direction === 'vertical'
              ? {
                  axisLabel: {
                    rotate: 45,
                    fontSize: 10,
                  },
                }
              : {
                  axisLabel: {
                    fontSize: 10,
                  },
                },
          yAxis: {
            axisLabel: {
              fontSize: 10,
            },
          },
          series: series.map(() => ({
            label: props.showValues
              ? {
                  fontSize: 10,
                }
              : undefined,
          })),
        },
      },
    ],
  }
})

const handleClick = (params: any) => {
  emit('click', params)
}

const handleMouseOver = (params: any) => {
  emit('mouseover', params)
}

const handleMouseOut = (params: any) => {
  emit('mouseout', params)
}
</script>
