<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">请填写企业持有的测绘资质信息，支持添加多个资质证书</p>
      </div>
      <Badge variant="outline" class="text-sm"> 步骤 2 / 5 </Badge>
    </div>

    <!-- 进度条 -->
    <div class="w-full bg-muted rounded-full h-2">
      <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 40%"></div>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Award class="w-5 h-5" />
          测绘资质信息
        </CardTitle>
        <CardDescription> 企业从事地理信息测绘相关业务的资质证书信息 </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 资质列表 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium">资质证书列表</h3>
              <Button type="button" @click="addQualification" class="flex items-center gap-2">
                <Plus class="w-4 h-4" />
                添加资质
              </Button>
            </div>

            <div v-if="qualifications.length === 0" class="text-center py-12 text-muted-foreground">
              <FileSearch class="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
              <p>暂无资质信息</p>
              <p class="text-sm">点击"添加资质"按钮开始填写</p>
            </div>

            <!-- 资质卡片列表 -->
            <div
              v-for="(qualification, index) in qualifications"
              :key="qualification.id"
              class="space-y-4"
            >
              <Card class="relative">
                <div class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"></div>
                <CardHeader class="pb-3">
                  <div class="flex items-center justify-between">
                    <CardTitle class="text-lg font-semibold">资质证书 {{ index + 1 }}</CardTitle>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeQualification(index)"
                      class="text-red-600 hover:text-red-700"
                    >
                      <Trash2 class="w-4 h-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent class="space-y-4">
                  <!-- 资质类别与等级 -->
                  <div class="space-y-3">
                    <Label class="text-base font-semibold">
                      资质类别与等级 <span class="text-red-500">*</span>
                    </Label>
                    <div class="space-y-3">
                      <!-- 互联网地图 -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`internet-map-${index}`"
                            v-model:checked="qualification.categories.internetMap.enabled"
                          />
                          <Label :for="`internet-map-${index}`" class="text-sm">互联网地图</Label>
                        </div>
                        <div
                          v-if="qualification.categories.internetMap.enabled"
                          class="ml-6 space-x-4"
                        >
                          <Label class="inline-flex items-center">
                            <Checkbox
                              v-model:checked="qualification.categories.internetMap.levelA"
                              class="mr-2"
                            />
                            甲级
                          </Label>
                          <Label class="inline-flex items-center">
                            <Checkbox
                              v-model:checked="qualification.categories.internetMap.levelB"
                              class="mr-2"
                            />
                            乙级
                          </Label>
                        </div>
                      </div>

                      <!-- 导航电子地图制作 -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`navigation-map-${index}`"
                            v-model:checked="qualification.categories.navigationMap.enabled"
                          />
                          <Label :for="`navigation-map-${index}`" class="text-sm"
                            >导航电子地图制作</Label
                          >
                        </div>
                        <div
                          v-if="qualification.categories.navigationMap.enabled"
                          class="ml-6 space-x-4"
                        >
                          <Label class="inline-flex items-center">
                            <Checkbox
                              v-model:checked="qualification.categories.navigationMap.levelA"
                              class="mr-2"
                            />
                            甲级
                          </Label>
                          <Label class="inline-flex items-center">
                            <Checkbox
                              v-model:checked="qualification.categories.navigationMap.levelB"
                              class="mr-2"
                            />
                            乙级
                          </Label>
                        </div>
                      </div>

                      <!-- 地理信息系统工程 -->
                      <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                          <Checkbox
                            :id="`gis-engineering-${index}`"
                            v-model:checked="qualification.categories.gisEngineering.enabled"
                          />
                          <Label :for="`gis-engineering-${index}`" class="text-sm"
                            >地理信息系统工程</Label
                          >
                        </div>
                        <div
                          v-if="qualification.categories.gisEngineering.enabled"
                          class="ml-6 space-x-4"
                        >
                          <Label class="inline-flex items-center">
                            <Checkbox
                              v-model:checked="qualification.categories.gisEngineering.levelA"
                              class="mr-2"
                            />
                            甲级
                          </Label>
                          <Label class="inline-flex items-center">
                            <Checkbox
                              v-model:checked="qualification.categories.gisEngineering.levelB"
                              class="mr-2"
                            />
                            乙级
                          </Label>
                        </div>
                      </div>
                    </div>
                    <p
                      v-if="errors[`qualification_${index}_categories`]"
                      class="text-sm text-red-500"
                    >
                      {{ errors[`qualification_${index}_categories`] }}
                    </p>
                  </div>

                  <!-- 证书基本信息 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label :for="`certificate-number-${index}`" class="text-base font-semibold">
                        证书编号 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`certificate-number-${index}`"
                        v-model="qualification.certificateNumber"
                        placeholder="请输入证书编号"
                        :class="{
                          'border-red-500': errors[`qualification_${index}_certificateNumber`],
                        }"
                      />
                      <p
                        v-if="errors[`qualification_${index}_certificateNumber`]"
                        class="text-sm text-red-500"
                      >
                        {{ errors[`qualification_${index}_certificateNumber`] }}
                      </p>
                    </div>
                    <div class="space-y-2">
                      <Label :for="`issuing-authority-${index}`" class="text-base font-semibold">
                        发证机关 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`issuing-authority-${index}`"
                        v-model="qualification.issuingAuthority"
                        placeholder="请输入发证机关"
                        :class="{
                          'border-red-500': errors[`qualification_${index}_issuingAuthority`],
                        }"
                      />
                      <p
                        v-if="errors[`qualification_${index}_issuingAuthority`]"
                        class="text-sm text-red-500"
                      >
                        {{ errors[`qualification_${index}_issuingAuthority`] }}
                      </p>
                    </div>
                  </div>

                  <!-- 有效期 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label :for="`valid-from-${index}`" class="text-base font-semibold">
                        有效期开始 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`valid-from-${index}`"
                        v-model="qualification.validFrom"
                        type="date"
                        :class="{ 'border-red-500': errors[`qualification_${index}_validFrom`] }"
                      />
                      <p
                        v-if="errors[`qualification_${index}_validFrom`]"
                        class="text-sm text-red-500"
                      >
                        {{ errors[`qualification_${index}_validFrom`] }}
                      </p>
                    </div>
                    <div class="space-y-2">
                      <Label :for="`valid-to-${index}`" class="text-base font-semibold">
                        有效期结束 <span class="text-red-500">*</span>
                      </Label>
                      <Input
                        :id="`valid-to-${index}`"
                        v-model="qualification.validTo"
                        type="date"
                        :class="{ 'border-red-500': errors[`qualification_${index}_validTo`] }"
                      />
                      <p
                        v-if="errors[`qualification_${index}_validTo`]"
                        class="text-sm text-red-500"
                      >
                        {{ errors[`qualification_${index}_validTo`] }}
                      </p>
                    </div>
                  </div>

                  <!-- 状态 -->
                  <div class="space-y-2">
                    <Label :for="`status-${index}`" class="text-base font-semibold">
                      状态 <span class="text-red-500">*</span>
                    </Label>
                    <Select v-model="qualification.status">
                      <SelectTrigger
                        :id="`status-${index}`"
                        :class="{ 'border-red-500': errors[`qualification_${index}_status`] }"
                      >
                        <SelectValue placeholder="请选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="有效">有效</SelectItem>
                        <SelectItem value="即将过期">即将过期</SelectItem>
                        <SelectItem value="已过期">已过期</SelectItem>
                        <SelectItem value="暂停">暂停</SelectItem>
                      </SelectContent>
                    </Select>
                    <p v-if="errors[`qualification_${index}_status`]" class="text-sm text-red-500">
                      {{ errors[`qualification_${index}_status`] }}
                    </p>
                  </div>

                  <!-- 证书扫描件上传 -->
                  <div class="space-y-2">
                    <Label class="text-base font-semibold">
                      证书扫描件 <span class="text-red-500">*</span>
                    </Label>
                    <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                      <div v-if="!qualification.uploadedFile" class="text-center">
                        <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                        <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                        <p class="text-xs text-muted-foreground mb-3">
                          支持 JPG、PNG、PDF 格式，文件大小不超过 10MB
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          @click="triggerFileUpload(index)"
                        >
                          选择文件
                        </Button>
                        <input
                          :ref="(el) => (fileInputs[index] = el as HTMLInputElement | null)"
                          type="file"
                          accept=".jpg,.jpeg,.png,.pdf"
                          class="hidden"
                          @change="handleFileUpload(index, $event)"
                        />
                      </div>
                      <div v-else class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <FileText class="w-6 h-6 text-blue-500" />
                          <div>
                            <p class="text-base font-semibold">{{ qualification.uploadedFile.name }}</p>
                            <p class="text-xs text-muted-foreground">
                              {{ formatFileSize(qualification.uploadedFile.size) }}
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          @click="removeFile(index)"
                        >
                          <X class="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p v-if="errors[`qualification_${index}_file`]" class="text-sm text-red-500">
                      {{ errors[`qualification_${index}_file`] }}
                    </p>
                  </div>

                  <!-- 特殊情况说明 -->
                  <div class="space-y-2">
                    <Label :for="`special-notes-${index}`" class="text-base font-semibold">
                      特殊情况说明
                    </Label>
                    <Textarea
                      :id="`special-notes-${index}`"
                      v-model="qualification.specialNotes"
                      placeholder="如有特殊情况需要说明，请在此填写"
                      rows="3"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" @click="handlePrevious">
              <ChevronLeft class="w-4 h-4 mr-2" />
              上一步
            </Button>
            <div class="flex gap-2">
              <Button type="button" variant="outline" @click="handleSave">
                <Save class="w-4 h-4 mr-2" />
                保存（暂存）
              </Button>
              <Button type="submit" @click="handleNext">
                下一步
                <ChevronRight class="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 保存提示弹窗 -->
    <Dialog v-model:open="saveDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>保存成功</DialogTitle>
          <DialogDescription> 您的测绘资质信息已保存，可稍后继续填报。 </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end">
          <Button @click="saveDialogOpen = false">确定</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Award,
  ChevronLeft,
  ChevronRight,
  FileSearch,
  FileText,
  Plus,
  Save,
  Trash2,
  Upload,
  X,
} from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const router = useRouter()

interface QualificationCategories {
  internetMap: {
    enabled: boolean
    levelA: boolean
    levelB: boolean
  }
  navigationMap: {
    enabled: boolean
    levelA: boolean
    levelB: boolean
  }
  gisEngineering: {
    enabled: boolean
    levelA: boolean
    levelB: boolean
  }
}

interface Qualification {
  id: string
  categories: QualificationCategories
  certificateNumber: string
  issuingAuthority: string
  validFrom: string
  validTo: string
  status: string
  uploadedFile: File | null
  specialNotes: string
}

// 资质列表
const qualifications = ref<Qualification[]>([])

// 表单验证错误
const errors = reactive<Record<string, string>>({})

// 其他状态
const fileInputs = ref<(HTMLInputElement | null)[]>([])
const saveDialogOpen = ref(false)

// 创建新资质对象
const createNewQualification = (): Qualification => ({
  id: `qual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  categories: {
    internetMap: { enabled: false, levelA: false, levelB: false },
    navigationMap: { enabled: false, levelA: false, levelB: false },
    gisEngineering: { enabled: false, levelA: false, levelB: false },
  },
  certificateNumber: '',
  issuingAuthority: '',
  validFrom: '',
  validTo: '',
  status: '',
  uploadedFile: null,
  specialNotes: '',
})

// 添加资质
const addQualification = () => {
  qualifications.value.push(createNewQualification())
}

// 删除资质
const removeQualification = (index: number) => {
  qualifications.value.splice(index, 1)
  // 清理对应的文件输入引用
  fileInputs.value.splice(index, 1)
}

// 文件上传处理
const triggerFileUpload = (index: number) => {
  fileInputs.value[index]?.click()
}

const handleFileUpload = (index: number, event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      errors[`qualification_${index}_file`] = '文件格式不支持，请上传 JPG、PNG 或 PDF 格式的文件'
      return
    }

    if (file.size > maxSize) {
      errors[`qualification_${index}_file`] = '文件大小超过限制，请上传小于 10MB 的文件'
      return
    }

    qualifications.value[index].uploadedFile = file
    delete errors[`qualification_${index}_file`]
  }
}

const removeFile = (index: number) => {
  qualifications.value[index].uploadedFile = null
  if (fileInputs.value[index]) {
    fileInputs.value[index]!.value = ''
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表单验证（原型演示版本 - 校验已禁用）
const validateForm = () => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => {
    delete errors[key]
  })

  // 原型演示模式：直接返回true，跳过所有校验
  console.log('原型演示模式：测绘资质信息表单校验已禁用，直接通过')
  return true

  /* 原始校验逻辑（原型完成后可恢复）
  let isValid = true

  if (qualifications.value.length === 0) {
    errors.general = '请至少添加一项测绘资质'
    isValid = false
    return isValid
  }

  qualifications.value.forEach((qualification, index) => {
    // 验证资质类别
    const hasCategory =
      qualification.categories.internetMap.enabled ||
      qualification.categories.navigationMap.enabled ||
      qualification.categories.gisEngineering.enabled

    if (!hasCategory) {
      errors[`qualification_${index}_categories`] = '请至少选择一个资质类别'
      isValid = false
    }

    // 验证证书编号
    if (!qualification.certificateNumber.trim()) {
      errors[`qualification_${index}_certificateNumber`] = '请输入证书编号'
      isValid = false
    }

    // 验证发证机关
    if (!qualification.issuingAuthority.trim()) {
      errors[`qualification_${index}_issuingAuthority`] = '请输入发证机关'
      isValid = false
    }

    // 验证有效期
    if (!qualification.validFrom) {
      errors[`qualification_${index}_validFrom`] = '请选择有效期开始日期'
      isValid = false
    }

    if (!qualification.validTo) {
      errors[`qualification_${index}_validTo`] = '请选择有效期结束日期'
      isValid = false
    }

    if (
      qualification.validFrom &&
      qualification.validTo &&
      qualification.validFrom >= qualification.validTo
    ) {
      errors[`qualification_${index}_validTo`] = '结束日期必须晚于开始日期'
      isValid = false
    }

    // 验证状态
    if (!qualification.status) {
      errors[`qualification_${index}_status`] = '请选择资质状态'
      isValid = false
    }

    // 验证文件上传
    if (!qualification.uploadedFile) {
      errors[`qualification_${index}_file`] = '请上传证书扫描件'
      isValid = false
    }
  })

  return isValid
  */
}

// 上一步
const handlePrevious = () => {
  router.push('/corp/filing/form/basic-info')
}

// 保存（暂存）
const handleSave = () => {
  console.log('保存测绘资质信息:', qualifications.value)

  // 保存到本地存储
  localStorage.setItem(
    'enterprise_qualification_info',
    JSON.stringify({
      qualifications: qualifications.value.map((q) => ({
        ...q,
        uploadedFileName: q.uploadedFile?.name,
      })),
    }),
  )

  saveDialogOpen.value = true
}

// 下一步
const handleNext = () => {
  if (validateForm()) {
    handleSave()
    // 跳转到下一步（数据安全防控措施-制度）
    router.push('/corp/filing/form/security-policy')
  }
}

// 表单提交
const handleSubmit = () => {
  // 阻止默认提交行为，由handleNext处理
}

// 页面加载时尝试恢复数据
const loadSavedData = () => {
  const saved = localStorage.getItem('enterprise_qualification_info')
  if (saved) {
    try {
      const data = JSON.parse(saved)
      qualifications.value = data.qualifications || []
    } catch (e) {
      console.warn('Failed to load saved data:', e)
    }
  }
}

// 组件挂载时加载保存的数据
onMounted(() => {
  loadSavedData()
  
  // 如果没有保存的数据，默认添加一项资质
  if (qualifications.value.length === 0) {
    addQualification()
  }
})
</script>

<style scoped>
/* 自定义样式 */
.border-red-500 {
  border-color: rgb(239 68 68);
}
</style>
