<template>
  <ChartContainer :option="finalOption" :height="height" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ChartContainer from '@/components/charts/ChartContainer.vue'

interface Props {
  data: {
    data: [number, number, number][]
  }
  height?: number
  options?: Record<string, unknown> & { series?: Array<Record<string, unknown>> }
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  options: () => ({}),
})

const defaultOptions = {
  grid: { left: 16, right: 16, top: 24, bottom: 24, containLabel: true },
  backgroundColor: 'transparent',
  tooltip: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: '#374151',
    textStyle: { color: '#f3f4f6' },
  },
  series: [
    {
      name: '热力图',
      type: 'heatmap' as const,
      data: [] as [number, number, number][],
      label: { show: false },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
}

const finalOption = computed(() => {
  const optionsSeries = props.options?.series
  const userSeries0 = Array.isArray(optionsSeries)
    ? (optionsSeries[0] as Record<string, unknown>)
    : undefined
  return {
    ...defaultOptions,
    ...props.options,
    series: [
      {
        ...defaultOptions.series[0],
        ...(userSeries0 || {}),
        data: props.data?.data ?? [],
      },
    ],
  }
})
</script>
