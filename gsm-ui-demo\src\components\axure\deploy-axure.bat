@echo off
REM deploy-axure.bat - 部署Axure原型到public目录

echo 开始部署Axure原型文件...

REM 创建目标目录
if not exist "..\..\public\axure" mkdir "..\..\public\axure"

REM 复制所有文件
xcopy /E /Y /I "data" "..\..\public\axure\data"
xcopy /E /Y /I "files" "..\..\public\axure\files"
xcopy /E /Y /I "images" "..\..\public\axure\images"
xcopy /E /Y /I "resources" "..\..\public\axure\resources"
copy /Y "GovernmentDashboard.html" "..\..\public\axure\"

echo.
echo ✅ Axure原型文件部署完成!
echo 📁 文件位置: public/axure/
echo 🌐 访问地址: http://localhost:5173/axure/GovernmentDashboard.html
echo.
pause