<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          查看政策法规动态、公告通知等重要信息，支持分类筛选与详情查看
        </p>
      </div>
    </div>

    <!-- 通知列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>通知列表</span>
          <Badge variant="outline"> 共 {{ filteredNotifications.length }} 条通知 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="false"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
        />
        <div class="flex justify-end my-4">
          <Button variant="outline" @click="markAllRead" class="flex items-center gap-2">
            <CheckCircle2 class="w-4 h-4" />
            全部标记已读
          </Button>
        </div>
        <!-- 置顶通知 -->
        <div v-if="pinnedNotifications.length > 0" class="mb-6">
          <h4 class="text-base font-semibold text-muted-foreground mb-3 flex items-center gap-2">
            <Pin class="w-4 h-4" />
            置顶通知
          </h4>
          <div class="space-y-2">
            <div
              v-for="item in pinnedNotifications"
              :key="item.id"
              class="flex items-center justify-between p-4 border rounded-lg bg-amber-50 border-amber-200 hover:bg-amber-100 cursor-pointer transition-colors"
              @click="viewDetail(item)"
            >
              <div class="flex-1">
                <div class="flex items-center gap-3">
                  <Badge :variant="categoryVariant(item.category)">{{ item.category }}</Badge>
                  <Badge :variant="priorityVariant(item.priority)">{{ item.priority }}</Badge>
                  <div v-if="item.status === '未读'" class="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
                <h3 class="font-medium mt-2 text-foreground">{{ item.title }}</h3>
                <p class="text-sm text-muted-foreground mt-1 line-clamp-2">{{ item.summary }}</p>
                <div class="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                  <span>发布时间：{{ item.publishAt }}</span>
                  <span>发布部门：{{ item.publisher }}</span>
                </div>
              </div>
              <ChevronRight class="w-4 h-4 text-muted-foreground" />
            </div>
          </div>
        </div>

        <!-- 普通通知 -->
        <div class="space-y-2">
          <div
            v-if="pagedNotifications.length === 0"
            class="text-center py-12 text-muted-foreground"
          >
            暂无通知数据
          </div>
          <div
            v-for="item in pagedNotifications"
            :key="item.id"
            class="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/40 cursor-pointer transition-colors"
            :class="{ 'bg-blue-50 border-blue-200': item.status === '未读' }"
            @click="viewDetail(item)"
          >
            <div class="flex-1">
              <div class="flex items-center gap-3">
                <Badge :variant="categoryVariant(item.category)">{{ item.category }}</Badge>
                <Badge :variant="priorityVariant(item.priority)">{{ item.priority }}</Badge>
                <div v-if="item.status === '未读'" class="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
              <h3
                class="font-medium mt-2"
                :class="{
                  'text-foreground': item.status === '已读',
                  'text-blue-900': item.status === '未读',
                }"
              >
                {{ item.title }}
              </h3>
              <p class="text-sm text-muted-foreground mt-1 line-clamp-2">{{ item.summary }}</p>
              <div class="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                <span>发布时间：{{ item.publishAt }}</span>
                <span>发布部门：{{ item.publisher }}</span>
              </div>
            </div>
            <ChevronRight class="w-4 h-4 text-muted-foreground" />
          </div>
        </div>

        <!-- 分页：统一 PaginationBar -->
        <PaginationBar v-model:page="currentPage" v-model:pageSize="pageSize" :total="filteredNotifications.length" class="mt-6 px-0 pb-0" />
      </CardContent>
    </Card>

    <!-- 通知详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{{ selectedNotification?.title }}</DialogTitle>
          <DialogDescription>
            {{ selectedNotification?.category }} | {{ selectedNotification?.publisher }} |
            {{ selectedNotification?.publishAt }}
          </DialogDescription>
        </DialogHeader>

        <div v-if="selectedNotification" class="space-y-4">
          <!-- 基本信息 -->
          <div class="flex items-center gap-3">
            <Badge :variant="categoryVariant(selectedNotification.category)">
              {{ selectedNotification.category }}
            </Badge>
            <Badge :variant="priorityVariant(selectedNotification.priority)">
              {{ selectedNotification.priority }}
            </Badge>
            <Badge :variant="statusVariant(selectedNotification.status)">
              {{ selectedNotification.status }}
            </Badge>
          </div>

          <!-- 摘要 -->
          <div class="p-4 bg-muted rounded-lg">
            <p class="text-sm">{{ selectedNotification.summary }}</p>
          </div>

          <!-- 正文内容 -->
          <div class="prose max-w-none">
            <div class="whitespace-pre-line text-sm">{{ selectedNotification.content }}</div>
          </div>

          <!-- 附件 -->
          <div v-if="selectedNotification.attachments?.length" class="space-y-2">
            <h4 class="font-medium">相关附件</h4>
            <div class="space-y-2">
              <div
                v-for="attachment in selectedNotification.attachments"
                :key="attachment.id"
                class="flex items-center gap-2 p-3 border rounded-lg"
              >
                <FileText class="w-4 h-4" />
                <span class="flex-1 text-sm">{{ attachment.name }}</span>
                <Button size="sm" variant="outline">下载</Button>
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-2">
            <Button variant="outline" @click="detailOpen = false">关闭</Button>
            <Button
              v-if="selectedNotification.status === '未读'"
              @click="markAsRead(selectedNotification)"
            >
              标记已读
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { CheckCircle2, ChevronRight, FileText, Pin } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { PaginationBar } from '@/components/ui/pagination'
import DatePickerWithRange from '@/components/ui/date-picker/DatePickerWithRange.vue'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type NotificationCategory = '法规' | '政策' | '公告' | '通用信息'
type Priority = '高' | '中' | '低'
type ReadStatus = '未读' | '已读'

interface NotificationItem {
  id: string
  title: string
  category: NotificationCategory
  priority: Priority
  status: ReadStatus
  summary: string
  content: string
  publisher: string
  publishAt: string
  isPinned: boolean
  attachments?: {
    id: string
    name: string
    url: string
  }[]
}

// 筛选条件
const filters = ref({
  category: 'ALL' as 'ALL' | NotificationCategory,
  priority: 'ALL' as 'ALL' | Priority,
  status: 'ALL' as 'ALL' | ReadStatus,
  timeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'category',
    label: '信息类型',
    type: 'select',
    placeholder: '请选择类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '法规', value: '法规' },
      { label: '政策', value: '政策' },
      { label: '公告', value: '公告' },
      { label: '通用信息', value: '通用信息' },
    ],
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'select',
    placeholder: '请选择优先级',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' },
    ],
  },
  {
    key: 'status',
    label: '阅读状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '未读', value: '未读' },
      { label: '已读', value: '已读' },
    ],
  },
  {
    key: 'timeRange',
    label: '发布时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 弹窗状态
const detailOpen = ref(false)
const selectedNotification = ref<NotificationItem | null>(null)

// Mock数据
const notifications = ref<NotificationItem[]>([
  {
    id: 'N-2025-001',
    title: '关于加强智能网联汽车地理信息安全管理的通知',
    category: '法规',
    priority: '高',
    status: '未读',
    summary:
      '为进一步规范智能网联汽车地理信息数据处理活动，切实维护国家地理信息安全，现就有关事项通知如下...',
    content: `进一步规范智能网联汽车地理信息数据处理活动，切实维护国家地理信息安全，现就有关事项通知如下：

一、严格按照《数据安全法》《个人信息保护法》等法律法规要求，规范地理信息数据收集、存储、传输、加工、提供、公开、销毁等处理活动。

二、建立健全地理信息安全管理制度，明确数据安全负责部门和负责人，定期开展风险评估和安全审计。

三、对涉及敏感地理信息的数据处理活动，应当采取必要的技术和管理措施，确保数据安全。

四、发生地理信息安全事件时，应当立即启动应急预案，及时报告相关部门。

本通知自发布之日起施行。

特此通知。`,
    publisher: '国家测绘地理信息局',
    publishAt: '2025-08-21 10:00',
    isPinned: true,
    attachments: [{ id: 'att-001', name: '智能网联汽车地理信息安全管理办法.pdf', url: '#' }],
  },
  {
    id: 'N-2025-002',
    title: '智能网联汽车数据安全管理若干规定（试行）',
    category: '政策',
    priority: '高',
    status: '未读',
    summary:
      '为了规范汽车数据处理活动，保护个人、组织的合法权益，维护国家安全和社会公共利益，促进汽车数据合理开发利用...',
    content: `第一条 为了规范汽车数据处理活动，保护个人、组织的合法权益，维护国家安全和社会公共利益，促进汽车数据合理开发利用，根据《中华人民共和国网络安全法》《中华人民共和国数据安全法》《中华人民共和国个人信息保护法》等法律法规，制定本规定。

第二条 在中华人民共和国境内设计、生产、销售、运行、维护汽车过程中的个人信息和重要数据处理活动，适用本规定。

第三条 国家支持汽车数据依法合理有效利用，保障汽车数据依法有序自由流动，促进汽车行业蓬勃发展。

第四条 汽车数据处理者应当履行数据安全保护义务，实施数据分类分级管理，加强个人信息和重要数据保护。`,
    publisher: '国家互联网信息办公室',
    publishAt: '2025-08-20 15:30',
    isPinned: true,
  },
  {
    id: 'N-2025-003',
    title: '关于开展智能网联汽车地理信息安全专项检查的公告',
    category: '公告',
    priority: '中',
    status: '已读',
    summary: '为确保智能网联汽车地理信息安全，维护国家地理信息安全，决定开展专项检查工作...',
    content: `根据《数据安全法》《地理信息保护条例》等相关法律法规，以确保智能网联汽车地理信息安全，维护国家地理信息安全，决定开展专项检查工作。

检查重点：
1. 地理信息数据处理活动的合规性
2. 数据安全防护措施的有效性
3. 涉密地理信息的保护情况
4. 数据出境安全管理情况

检查时间：2025年9月1日至2025年11月30日

请各相关企业配合检查工作，如实提供相关材料。`,
    publisher: '国家测绘地理信息局',
    publishAt: '2025-08-19 09:15',
    isPinned: false,
  },
  {
    id: 'N-2025-004',
    title: '智能网联汽车地图数据采集技术规范更新说明',
    category: '通用信息',
    priority: '低',
    status: '已读',
    summary:
      '为适应技术发展需要，对智能网联汽车地图数据采集技术规范进行更新，主要涉及数据格式、采集精度等方面...',
    content: `根据智能网联汽车技术发展现状，对《智能网联汽车地图数据采集技术规范》进行如下更新：

1. 数据格式标准化：统一采用国际通用的数据格式标准
2. 采集精度要求：提高定位精度要求至厘米级
3. 数据安全要求：增加数据加密和脱敏处理要求
4. 质量控制：建立多级质量检验机制

本规范自2025年10月1日起实施，请相关企业按照新规范要求执行。`,
    publisher: '中国测绘学会',
    publishAt: '2025-08-18 14:20',
    isPinned: false,
  },
])

// 置顶通知
const pinnedNotifications = computed(() => notifications.value.filter((n) => n.isPinned))

// 过滤后的通知（不含置顶）
const filteredNotifications = computed(() => {
  const { category, priority, status, timeRange } = filters.value
  return notifications.value.filter((n) => {
    if (n.isPinned) return false // 置顶通知单独显示
    if (category !== 'ALL' && n.category !== category) return false
    if (priority !== 'ALL' && n.priority !== priority) return false
    if (status !== 'ALL' && n.status !== status) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const publish = new Date(n.publishAt.replace(/-/g, '/')).getTime()
      if (publish < start || publish > end) return false
    }
    return true
  })
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredNotifications.value.length / pageSize.value)),
)
const pagedNotifications = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredNotifications.value.slice(start, start + pageSize.value)
})

// 处理函数
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    category: 'ALL',
    priority: 'ALL',
    status: 'ALL',
    timeRange: null,
  }
  currentPage.value = 1
}

const viewDetail = (notification: NotificationItem) => {
  selectedNotification.value = notification
  detailOpen.value = true
  if (notification.status === '未读') {
    markAsRead(notification)
  }
}

const markAsRead = (notification: NotificationItem) => {
  notification.status = '已读'
}

const markAllRead = () => {
  notifications.value.forEach((n) => {
    if (n.status === '未读') {
      n.status = '已读'
    }
  })
}

// Badge 样式
const categoryVariant = (category: NotificationCategory) => {
  switch (category) {
    case '法规':
      return 'destructive'
    case '政策':
      return 'default'
    case '公告':
      return 'secondary'
    case '通用信息':
      return 'outline'
    default:
      return 'outline'
  }
}

const priorityVariant = (priority: Priority) => {
  switch (priority) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}

const statusVariant = (status: ReadStatus) => {
  switch (status) {
    case '未读':
      return 'default'
    case '已读':
      return 'secondary'
    default:
      return 'outline'
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
