﻿表名: log_ent_destroy_detail,,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment),
1,id,主键ID,BIGINT,,○,●,自增主键,
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id""",
3,total_packages_in_operation,销毁的数据包总数,INTEGER,,○,,"""(WORD)。本次销毁操作涉及的数据包总数。（注：log_main中记录的是拆分后的单个包）""", 
4,destruction_method,销毁方式,SMALLINT,,○,,"""(BYTE) 0x01:逻辑删除; 0x02:文件覆盖/低级格式化; 0x03:物理销毁""",
5,approval_status,审批状态,SMALLINT,,○,,"""(BYTE) 0x01:未经审批; 0x02:已经审批""",
6,destruction_process_status,销毁流程状态,SMALLINT,,○,,"""(BYTE) 0x01:未完成; 0x02:已完成""",
7,operator_identity,操作员身份,SMALLINT,,○,,"""(BYTE) 0x01:重要数据操作人员; 0x02:一般数据操作人员""",
,,,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
