# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Geographic Information Security Monitoring Platform** built with Vue 3 + TypeScript + Vite. The application provides spatiotemporal data security monitoring for intelligent connected vehicles with a four-tier distributed regulatory framework (National-Regional-Enterprise-Terminal).

## 我们本次项目开发的是完全静态演示原型可以不做任何表单校验测试，目的就是为了展示界面内容和模块的业务跳转交互，按钮都可以直接提交或跳转

## Technology Stack

- **Frontend**: Vue 3.5 with Composition API and TypeScript
- **Build Tool**: Vite 7.0
- **UI Components**: shadcn-vue (strict official components only)
- **Styling**: Tailwind CSS with HSL-based theme system
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Testing**: Vitest (unit) + Playwright (e2e)
- **Node Version**: ^20.19.0 || >=22.12.0

## Essential Commands

Navigate to `gsm-ui-demo/` directory for all commands:

```bash
# Development
npm run dev                    # Start development server (port 5173)

# Building & Type Checking
npm run build                  # Full production build with type checking
npm run type-check            # TypeScript type checking only
npm run build-only            # Build without type check

# Code Quality
npm run lint                  # ESLint with auto-fix
npm run format               # Prettier formatting

# Preview
npm run preview              # Preview production build

# Deployment
npm run deploy               # Deploy to Tencent CloudBase
npm run deploy:cloudbase     # Alternative CloudBase deployment
```

## Architecture & Code Organization

### Directory Structure

```
gsm-ui-demo/src/
├── components/
│   ├── ui/                  # shadcn-vue official components only
│   ├── charts/              # Chart components (BarChart, LineChart, PieChart)
│   ├── layout/              # Layout components (GlobalHeader, TopUserMenu)
│   ├── enterprise-info/     # Enterprise-specific components
│   ├── AppSidebar.vue       # Main app sidebar (Sidebar08 pattern)
│   ├── Nav*.vue             # Navigation components
│   └── ThemeToggle.vue      # Dark/light theme switcher
├── views/
│   ├── Home.vue
│   ├── government/          # Government user pages
│   │   └── Dashboard.vue    # Main dashboard (P-006)
│   └── enterprise/          # Enterprise user pages
├── modules/
│   ├── public/              # Public module (landing pages, login)
│   └── external/            # External module (public-facing pages)
├── router/index.ts          # Vue Router configuration
├── stores/                  # Pinia stores
├── services/                # API services (districtService.ts)
├── lib/utils.ts             # Utility functions
└── assets/index.css         # Tailwind + CSS variables
```

## CRITICAL: Development Standards & Rules

### shadcn-vue Official Standards (NON-NEGOTIABLE)

**Core Principles:**

- Never create simplified component versions
- Strictly use official shadcn-vue.com methods
- Address root causes with systematic fixes
- Avoid technical debt

**Component Usage Rules:**

- [✓] Only use `npx shadcn-vue@latest add [component]` to add components
- [✓] Only use `npx shadcn-vue@latest add [Block]` to add blocks
- [✗] Prohibited: Manual creation, copy-paste, or mimicking official components
- [✗] All components must be from official shadcn-vue (unless explicitly stated)

**Problem-Solving Approach:**

- [✓] Diagnose root cause and implement systematic fixes
- [✗] Create simplified versions or bypass problems
- [✗] Use temporary solutions

**Technology Stack Constraints:**

- UI: Official shadcn-vue components + Radix Vue
- Styling: Official Tailwind CSS configuration
- Icons: Lucide Vue Next
- Theming: Official theme color schemes

### Development Workflow Standards

When encountering component requirements, strictly follow these steps:

1. **First Check**: Does shadcn-vue.com have the corresponding official component?
2. **Use Official CLI**: Add components via CLI commands, never manual creation
3. **Fix Configuration Issues**: Address root causes with systematic repairs
4. **Avoid Any Form**: Of simplification, mimicking, or bypassing official processes
5. **Ensure All Code**: Meets official standards for long-term project maintainability

**Core Philosophy**: Quality over speed, official over custom, systematic solutions over temporary fixes

### Theme System

The app uses a sophisticated HSL-based color system with CSS variables:

- **Theme Toggle**: Implemented with @vueuse/core
- **Color Variables**: Defined in `assets/index.css` with light/dark variants
- **Sidebar Colors**: Dedicated sidebar-specific color variables
- **Component Integration**: All shadcn-vue components inherit theme automatically

## Development Patterns

### Vue 3 Composition API

All components use `<script setup lang="ts">` pattern with TypeScript.

### State Management

- **Pinia**: For complex application state
- **Composables**: VueUse for common functionality

### Routing Structure

- **Role-based**: `/government/*` for government users, `/enterprise/*` for enterprise users
- **Modular**: `/public/*` for landing pages, login, and public content
- **Page Naming**: Follow P-XXX pattern from business requirements

### Module System

The application uses a modular architecture with three main modules:

- **Core Application**: Main authenticated application views
- **Public Module**: Landing pages, login, and public-facing content
- **External Module**: Alternative public-facing components

### Chart Integration

Uses multiple charting solutions:
- **ECharts** via vue-echarts for complex visualizations
- **Custom Chart Components** in `components/charts/` for standardized styling
- **D3.js** for specialized trace visualization in `TraceVisualization.vue`

## Business Context

This platform serves two primary user types:

1. **Government**: Registration approval, monitoring, risk management
2. **Enterprise**: Data reporting, compliance tracking, risk alerts

The current implementation focuses on government-side functionality with a comprehensive dashboard showing statistics, monitoring status, and risk alerts.

## Key Implementation Notes

- **Responsive Design**: Mobile-first with Tailwind CSS
- **TypeScript**: Strict typing enabled, use proper type definitions
- **Performance**: Vite for fast development and optimized builds
- **Development Server**: Runs on port 5173 with Amap API proxy for CORS handling
- **Deployment**: Configured for Tencent CloudBase via multiple deploy commands
- **Static Demo**: This is a fully static demonstration prototype - no form validation testing needed, focused on UI display and module interaction

## Important Development Context

This is specifically a **static demonstration prototype** designed to showcase:
- Interface content and visual design
- Business module navigation and interaction flows
- UI components and user experience patterns
- Form submissions and navigation can be direct without validation

The primary goal is demonstrating the interface and business logic flow rather than full backend integration.

## Page Development Status

Reference `docs/page-tasks.md` for detailed task breakdown. Currently implemented:

- [✓] Basic layout with sidebar navigation
- [✓] Theme switching functionality
- [✓] Government dashboard (P-006)
- 🔄 53 additional pages planned across government and enterprise modules

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.