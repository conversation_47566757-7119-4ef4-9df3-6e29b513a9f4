<template>
  <ChartContainer 
    :option="lineOption" 
    :height="height"
    :color-scheme="colorScheme"
    :loading="loading"
    :enable-toolbox="enableToolbox"
    :enable-data-zoom="enableDataZoom"
    @click="handleClick"
    @mouseover="handleMouseOver"
    @mouseout="handleMouseOut"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ChartContainer from './ChartContainer.vue'
import { createTooltipFormatter, createLinearGradient, CHART_COLOR_SCHEMES } from '@/lib/chart-themes'

interface DataPoint {
  name: string
  value: number
  timestamp?: number
}

interface SeriesItem {
  name: string
  data: DataPoint[]
  color?: string
  lineType?: 'solid' | 'dashed' | 'dotted'
  smooth?: boolean
  showArea?: boolean
  showSymbol?: boolean
  symbolSize?: number
  stack?: string
}

interface Props {
  series: SeriesItem[]
  title?: string
  height?: string | number
  showLegend?: boolean
  smooth?: boolean
  showArea?: boolean
  colorScheme?: 'primary' | 'risk' | 'enterprise' | 'stages' | 'status' | 'oceanDepths' | 'sunsetWarmth' | 'forestGrove' | 'modernCorporate' | 'pastelDreams' | 'neonTech' | 'vintageEarth' | 'arcticAurora' | 'floralPalette' | 'summerAfternoon' | 'retroFuturistic' | 'resilience'
  loading?: boolean
  enableToolbox?: boolean
  enableDataZoom?: boolean
  xAxisType?: 'category' | 'time' | 'value'
  yAxisName?: string
  xAxisName?: string
  gridTop?: string | number
  gridBottom?: string | number
  connectNulls?: boolean
  showMarkLine?: boolean
  markLineData?: Array<{value: number, name: string}>
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  showLegend: true,
  smooth: true,
  showArea: false,
  colorScheme: 'primary',
  loading: false,
  enableToolbox: false,
  enableDataZoom: false,
  xAxisType: 'category',
  connectNulls: false,
  showMarkLine: false
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
}>()

// 获取所有唯一的x轴数据
const xAxisData = computed(() => {
  if (!props.series || !Array.isArray(props.series)) {
    return []
  }
  const allNames = new Set<string>()
  props.series.forEach(s => {
    if (s && s.data && Array.isArray(s.data)) {
      s.data.forEach(d => allNames.add(d.name))
    }
  })
  return Array.from(allNames).sort()
})

const lineOption = computed(() => ({
  title: props.title ? {
    text: props.title,
    left: 'center',
    top: 16,
    textStyle: {
      fontSize: 18,
      fontWeight: '600'
    }
  } : undefined,
  
  legend: props.showLegend && props.series && props.series.length > 1 ? {
    type: 'scroll',
    orient: 'horizontal',
    bottom: 15,
    left: 'center',
    itemGap: 20,
    itemWidth: 25,
    itemHeight: 14,
    icon: 'roundRect',
    pageButtonItemGap: 8,
    pageIconColor: '#64748b',
    pageIconInactiveColor: '#cbd5e1',
    animation: true,
    animationDurationUpdate: 600
  } : undefined,
  
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#8b1538',
        width: 1,
        opacity: 0.6
      },
      lineStyle: {
        color: '#8b1538',
        width: 1,
        opacity: 0.4,
        type: 'dashed'
      }
    },
    formatter: createTooltipFormatter('line'),
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderWidth: 1,
    borderRadius: 8,
    textStyle: {
      fontSize: 12
    },
    padding: [12, 16],
    extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); backdrop-filter: blur(8px);'
  },
  
  grid: {
    left: '5%',
    right: '5%',
    bottom: props.showLegend && props.series.length > 1 ? '22%' : props.gridBottom || '12%',
    top: props.title ? '22%' : props.gridTop || '12%',
    containLabel: true
  },
  
  xAxis: {
    type: props.xAxisType,
    data: props.xAxisType === 'category' ? xAxisData.value : undefined,
    name: props.xAxisName,
    nameLocation: 'middle',
    nameGap: 30,
    nameTextStyle: {
      fontSize: 12,
      fontWeight: '500'
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(148, 163, 184, 0.3)',
        width: 1
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#64748b',
      fontSize: 13,
      fontWeight: '500',
      margin: 10,
      rotate: xAxisData.value.length > 6 ? 45 : 0
    },
    splitLine: {
      show: false
    }
  },
  
  yAxis: {
    type: 'value',
    name: props.yAxisName,
    nameLocation: 'middle',
    nameGap: 50,
    nameTextStyle: {
      fontSize: 12,
      fontWeight: '500'
    },
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#64748b',
      fontSize: 13,
      fontWeight: '500',
      margin: 10
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: 'rgba(148, 163, 184, 0.15)',
        width: 1.5,
        type: 'dashed',
        dashOffset: 5
      }
    }
  },
  
  series: (props.series || []).map((seriesItem, index) => {
    const colors = CHART_COLOR_SCHEMES[props.colorScheme] || CHART_COLOR_SCHEMES.primary
    const baseColor = seriesItem.color || colors[index % colors.length]
    
    const seriesConfig: any = {
      name: seriesItem.name,
      type: 'line',
      smooth: seriesItem.smooth !== undefined ? seriesItem.smooth : props.smooth,
      connectNulls: props.connectNulls,
      stack: seriesItem.stack,
      
      // 数据处理
      data: props.xAxisType === 'category' 
        ? xAxisData.value.map(name => {
            const item = (seriesItem.data || []).find(d => d && d.name === name)
            return item ? item.value : null
          })
        : (seriesItem.data || []).map(d => d ? [d.name, d.value] : [null, null]),
      
      // 线条样式
      lineStyle: {
        width: 3.5,
        color: baseColor,
        shadowBlur: 8,
        shadowColor: baseColor + '50',
        cap: 'round',
        join: 'round',
        type: seriesItem.lineType || 'solid'
      },
      
      // 数据点样式
      symbol: seriesItem.showSymbol !== false ? 'circle' : 'none',
      symbolSize: seriesItem.symbolSize || 8,
      itemStyle: {
        color: baseColor,
        borderColor: '#ffffff',
        borderWidth: 2.5,
        shadowBlur: 6,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      },
      
      // 区域填充
      areaStyle: (seriesItem.showArea || props.showArea) ? {
        color: createLinearGradient('blue2', 'vertical'),
        opacity: 0.25,
        shadowBlur: 10,
        shadowColor: baseColor + '20'
      } : undefined,
      
      // 悬浮效果
      emphasis: {
        focus: 'series',
        blurScope: 'coordinateSystem',
        lineStyle: {
          width: 4,
          shadowBlur: 12,
          shadowColor: baseColor + '60'
        },
        itemStyle: {
          color: baseColor,
          borderColor: '#ffffff',
          borderWidth: 3,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      
      // 动画配置
      animationDuration: 1200,
      animationEasing: 'elasticOut',
      animationDelay: (idx: number) => idx * 60
    }
    
    // 标线配置
    if (props.showMarkLine && props.markLineData && index === 0) {
      seriesConfig.markLine = {
        silent: true,
        lineStyle: {
          color: '#ef4444',
          type: 'dashed',
          width: 2,
          opacity: 0.7
        },
        label: {
          show: true,
          position: 'end',
          fontSize: 11,
          color: '#ef4444',
          fontWeight: '500'
        },
        data: props.markLineData.map(item => ({
          yAxis: item.value,
          name: item.name
        }))
      }
    }
    
    return seriesConfig
  }),
  
  // 数据缩放配置
  dataZoom: props.enableDataZoom ? [
    {
      type: 'slider',
      show: true,
      bottom: 5,
      start: 0,
      end: 100,
      height: 20
    },
    {
      type: 'inside',
      zoomOnMouseWheel: 'shift',
      moveOnMouseMove: 'ctrl'
    }
  ] : undefined,
  
  // 全局动画配置
  animationDuration: 1200,
  animationEasing: 'cubicOut',
  
  // 媒体查询配置
  media: [
    {
      query: {
        maxWidth: 768
      },
      option: {
        grid: {
          left: '5%',
          right: '5%',
          bottom: props.showLegend && props.series.length > 1 ? '25%' : '12%',
          top: props.title ? '25%' : '12%'
        },
        legend: props.showLegend && props.series && props.series.length > 1 ? {
          orient: 'horizontal',
          bottom: 10,
          left: 'center'
        } : undefined,
        xAxis: {
          axisLabel: {
            rotate: 45,
            fontSize: 10
          }
        },
        yAxis: {
          axisLabel: {
            fontSize: 10
          }
        }
      }
    }
  ]
}))

const handleClick = (params: any) => {
  emit('click', params)
}

const handleMouseOver = (params: any) => {
  emit('mouseover', params)
}

const handleMouseOut = (params: any) => {
  emit('mouseout', params)
}
</script>