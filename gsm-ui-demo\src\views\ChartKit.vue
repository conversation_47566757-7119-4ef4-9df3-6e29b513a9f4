<template>
  <div class="min-h-screen bg-background">
    <!-- Header -->
    <div class="border-b bg-card">
      <div class="container mx-auto px-6 py-8">
        <div class="flex flex-col gap-4">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-foreground">{{ $route.meta.title }}</h1>
              <p class="text-muted-foreground mt-2">
                横向对比不同主题的相同图表类型，纵向查看不同类型的图表展示
              </p>
            </div>
            <div class="flex items-center gap-4">
              <div class="text-sm text-muted-foreground">
                共 {{ themeList.length }} 个主题 · {{ chartTypes.length }} 种图表类型
              </div>
              <Button @click="exportColors" variant="outline">
                <Download class="w-4 h-4 mr-2" />
                导出配色
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chart Types Grid -->
    <div class="container mx-auto px-6 py-8">
      <div class="space-y-16">
        <!-- Chart Type Row -->
        <template v-for="chartType in chartTypes" :key="chartType.type">
          <div class="space-y-6">
            <!-- Chart Type Header -->
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-3">
                <Badge variant="secondary" class="text-lg px-4 py-2">{{ chartType.name }}</Badge>
                <span class="text-sm text-muted-foreground">{{ chartType.description }}</span>
              </div>
            </div>

            <!-- Horizontal scroll container for themes -->
            <div class="overflow-x-auto pb-4 scrollbar-custom">
              <div class="flex gap-6" style="width: max-content">
                <template v-for="theme in themeList" :key="`${chartType.type}-${theme.key}`">
                  <!-- Theme Chart Card -->
                  <Card class="flex-shrink-0 w-80 h-96">
                    <div class="p-6 h-full flex flex-col">
                      <!-- Theme Header -->
                      <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                          <h3 class="font-semibold text-foreground">{{ theme.name }}</h3>
                          <Badge variant="outline" class="text-xs"
                            >{{ theme.colors.length }}色</Badge
                          >
                        </div>
                      </div>

                      <!-- Color Palette Preview -->
                      <div class="flex items-center gap-1 mb-4">
                        <div
                          v-for="(color, index) in theme.colors.slice(0, 8)"
                          :key="index"
                          :style="{ backgroundColor: color }"
                          class="w-4 h-4 rounded-sm border shadow-sm dark:border-black border-white"
                          :title="color"
                        />
                      </div>

                      <!-- Chart Container -->
                      <div class="flex-1 min-h-0">
                        <!-- Pie Chart -->
                        <PieChart
                          v-if="chartType.type === 'pie'"
                          :data="pieChartData"
                          :color-scheme="theme.key as any"
                          :height="240"
                          chart-type="doughnut"
                          :show-legend="true"
                          :show-values="false"
                        />

                        <!-- Bar Chart -->
                        <BarChart
                          v-else-if="chartType.type === 'bar'"
                          :data="barChartData"
                          :color-scheme="theme.key as any"
                          :height="240"
                          :show-legend="false"
                          :show-values="true"
                        />

                        <!-- Line Chart -->
                        <LineChart
                          v-else-if="chartType.type === 'line'"
                          :series="lineChartSeries"
                          :color-scheme="theme.key as any"
                          :height="240"
                          :show-legend="false"
                          :smooth="true"
                        />

                        <!-- Multi Bar Chart -->
                        <BarChart
                          v-else-if="chartType.type === 'multiBar'"
                          :series="multiBarSeries"
                          :color-scheme="theme.key as any"
                          :height="240"
                          :show-legend="true"
                        />

                        <!-- Area Chart -->
                        <LineChart
                          v-else-if="chartType.type === 'area'"
                          :series="areaChartSeries"
                          :color-scheme="theme.key as any"
                          :height="240"
                          :show-legend="false"
                          :show-area="true"
                          :smooth="true"
                        />

                        <!-- Ring Chart -->
                        <PieChart
                          v-else-if="chartType.type === 'ring'"
                          :data="ringChartData"
                          :color-scheme="theme.key as any"
                          :height="240"
                          chart-type="doughnut"
                          center-text="总计"
                          center-sub-text="12,345"
                          :show-legend="true"
                        />

                        <!-- Stats Grid -->
                        <div
                          v-else-if="chartType.type === 'stats'"
                          class="grid grid-cols-2 gap-3 h-full"
                        >
                          <div
                            v-for="(stat, index) in statsData"
                            :key="index"
                            class="text-center p-4 rounded-lg border flex flex-col justify-center items-center"
                            :style="{
                              backgroundColor: theme.colors[index % theme.colors.length] + '10',
                              borderLeft: `4px solid ${theme.colors[index % theme.colors.length]}`,
                              borderColor: theme.colors[index % theme.colors.length] + '20',
                            }"
                          >
                            <!-- Icon -->
                            <div
                              class="mb-2"
                              :style="{ color: theme.colors[index % theme.colors.length] }"
                            >
                              <Users v-if="index === 0" class="w-6 h-6 mx-auto" />
                              <Activity v-else-if="index === 1" class="w-6 h-6 mx-auto" />
                              <TrendingUp v-else-if="index === 2" class="w-6 h-6 mx-auto" />
                              <Eye v-else class="w-6 h-6 mx-auto" />
                            </div>
                            <!-- Value -->
                            <div
                              class="text-2xl font-bold mb-1"
                              :style="{ color: theme.colors[index % theme.colors.length] }"
                            >
                              {{ stat.value }}
                            </div>
                            <!-- Label -->
                            <div class="text-sm text-muted-foreground font-medium">
                              {{ stat.label }}
                            </div>
                          </div>
                        </div>

                        <!-- Horizontal Bar Chart -->
                        <BarChart
                          v-else-if="chartType.type === 'horizontalBar'"
                          :data="barChartData.slice(0, 5)"
                          :color-scheme="theme.key as any"
                          :height="240"
                          direction="horizontal"
                          :show-legend="false"
                          :show-values="true"
                        />
                      </div>
                    </div>
                  </Card>
                </template>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Download, Users, Activity, TrendingUp, Eye } from 'lucide-vue-next'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import PieChart from '@/components/charts/PieChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import LineChart from '@/components/charts/LineChart.vue'
import { CHART_COLOR_SCHEMES } from '@/lib/chart-themes'

// Chart types configuration
const chartTypes = [
  { type: 'pie', name: '饼图', description: '展示数据占比关系' },
  { type: 'bar', name: '柱状图', description: '对比不同类别的数值大小' },
  { type: 'line', name: '折线图', description: '展示数据随时间的变化趋势' },
  { type: 'multiBar', name: '多系列柱状图', description: '对比多个系列的数据' },
  { type: 'area', name: '面积图', description: '强调数据量的累积效果' },
  { type: 'ring', name: '环形图', description: '带中心信息的数据占比展示' },
  { type: 'stats', name: '统计数字', description: '关键指标的数值展示' },
  { type: 'horizontalBar', name: '横向柱状图', description: '适合长标签的数据对比' },
]

// Theme list with Chinese names
const themeList = [
  { key: 'primary', name: '主要配色' },
  { key: 'oceanDepths', name: '深海主题' },
  { key: 'sunsetWarmth', name: '暖夕阳' },
  { key: 'forestGrove', name: '森林主题' },
  { key: 'modernCorporate', name: '现代商务' },
  { key: 'pastelDreams', name: '柔和梦幻' },
  { key: 'neonTech', name: '科技霓虹' },
  { key: 'vintageEarth', name: '复古大地' },
  { key: 'arcticAurora', name: '极光主题' },
  { key: 'floralPalette', name: '花卉主题' },
  { key: 'summerAfternoon', name: '夏日午后' },
  { key: 'retroFuturistic', name: '复古未来' },
  { key: 'resilience', name: '韧性色彩' },
].map((theme) => ({
  ...theme,
  colors: [
    ...(CHART_COLOR_SCHEMES[theme.key as keyof typeof CHART_COLOR_SCHEMES] as readonly string[]),
  ] as string[],
}))

// Sample data for charts
const pieChartData = [
  { name: '企业A', value: 335 },
  { name: '企业B', value: 310 },
  { name: '企业C', value: 234 },
  { name: '企业D', value: 135 },
  { name: '企业E', value: 548 },
]

const barChartData = [
  { name: '1月', value: 320 },
  { name: '2月', value: 302 },
  { name: '3月', value: 341 },
  { name: '4月', value: 374 },
  { name: '5月', value: 390 },
  { name: '6月', value: 450 },
]

const lineChartSeries = [
  {
    name: '访问量',
    data: [
      { name: '周一', value: 820 },
      { name: '周二', value: 932 },
      { name: '周三', value: 901 },
      { name: '周四', value: 934 },
      { name: '周五', value: 1290 },
      { name: '周六', value: 1330 },
      { name: '周日', value: 1320 },
    ],
  },
]

const statsData = [
  { value: '1,234', label: '总用户' },
  { value: '5,678', label: '活跃数' },
  { value: '89%', label: '增长率' },
  { value: '12.3K', label: '访问量' },
]

const multiBarSeries = [
  {
    name: '产品A',
    data: [
      { name: 'Q1', value: 150 },
      { name: 'Q2', value: 200 },
      { name: 'Q3', value: 180 },
      { name: 'Q4', value: 220 },
    ],
  },
  {
    name: '产品B',
    data: [
      { name: 'Q1', value: 120 },
      { name: 'Q2', value: 160 },
      { name: 'Q3', value: 140 },
      { name: 'Q4', value: 180 },
    ],
  },
  {
    name: '产品C',
    data: [
      { name: 'Q1', value: 100 },
      { name: 'Q2', value: 140 },
      { name: 'Q3', value: 120 },
      { name: 'Q4', value: 160 },
    ],
  },
]

const areaChartSeries = [
  {
    name: '销售额',
    data: [
      { name: '1月', value: 400 },
      { name: '2月', value: 300 },
      { name: '3月', value: 300 },
      { name: '4月', value: 500 },
      { name: '5月', value: 420 },
      { name: '6月', value: 600 },
    ],
    showArea: true,
  },
]

const ringChartData = [
  { name: '已完成', value: 60 },
  { name: '进行中', value: 30 },
  { name: '待开始', value: 10 },
]

// Export colors functionality
const exportColors = () => {
  let cssContent = '/* 图表主题配色导出 */\n\n'

  themeList.forEach((theme) => {
    cssContent += `/* ${theme.name} */\n`
    theme.colors.forEach((color, index) => {
      cssContent += `.${theme.key}-${index + 1} { color: ${color}; }\n`
    })
    cssContent += '\n'
  })

  // Create and download file
  const blob = new Blob([cssContent], { type: 'text/css' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'chart-themes.css'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
/* 自定义横向滚动条样式 */
.scrollbar-custom {
  scrollbar-width: thin;
  scrollbar-color: rgb(148 163 184) transparent;
}

/* Webkit 浏览器滚动条样式 */
.scrollbar-custom::-webkit-scrollbar {
  height: 8px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgb(148 163 184);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgb(100 116 139);
}

/* 暗色主题下的滚动条 */
.dark .scrollbar-custom {
  scrollbar-color: rgb(71 85 105) transparent;
}

.dark .scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgb(71 85 105);
}

.dark .scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgb(51 65 85);
}

/* 滚动条角落 */
.scrollbar-custom::-webkit-scrollbar-corner {
  background: transparent;
}
</style>
