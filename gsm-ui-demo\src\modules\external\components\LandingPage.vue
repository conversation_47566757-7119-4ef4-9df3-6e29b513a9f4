<template>
  <div class="gsm-external-module landing-page">
    <!-- 导航栏 -->
    <LandingHeader
      :onLogin="handleLogin"
      :onRegister="handleRegister"
      :onThemeToggle="handleThemeToggle"
    />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <HeroSection :onExplore="handleExplore" :onGetStarted="handleGetStarted" />

      <!-- 政策动态 -->
      <NewsSection :onViewMore="handleViewMore" :onNewsClick="handleNewsClick" />

      <!-- 实时数据监控面板 -->
      <RealTimeStatsSection />

      <!-- 平台特色功能 -->
      <FeaturesSection />

      <!-- 运营数据展示 -->
      <StatsSection />

      <!-- 服务企业展示 -->
      <EnterprisesSection />
    </main>

    <!-- 页脚 -->
    <LandingFooter :onLinkClick="handleFooterLink" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, provide } from 'vue'
import LandingHeader from './LandingHeader.vue'
import HeroSection from './HeroSection.vue'
import NewsSection from './NewsSection.vue'
import RealTimeStatsSection from './RealTimeStatsSection.vue'
import FeaturesSection from './FeaturesSection.vue'
import StatsSection from './StatsSection.vue'
import EnterprisesSection from './EnterprisesSection.vue'
import LandingFooter from './LandingFooter.vue'

// Props for callback functions from parent component
interface Props {
  onNavigate?: (route: string) => void
  onAction?: (action: string, data?: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  onNavigate: (route: string) => console.log('Navigate to:', route),
  onAction: (action: string, data?: any) => console.log('Action:', action, data),
})

// Theme management
const currentTheme = ref<'light' | 'dark'>('light')

// Initialize theme
onMounted(() => {
  // Detect system theme
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  currentTheme.value = prefersDark ? 'dark' : 'light'

  // Apply theme to gsm-external-module
  const moduleElement = document.querySelector('.gsm-external-module') as HTMLElement
  if (moduleElement) {
    moduleElement.setAttribute('data-theme', currentTheme.value)
  }

  // Listen for system theme changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    currentTheme.value = e.matches ? 'dark' : 'light'
    if (moduleElement) {
      moduleElement.setAttribute('data-theme', currentTheme.value)
    }
  })
})

// Provide theme to child components
provide('theme', currentTheme)

// Event handlers
const handleLogin = () => {
  props.onAction?.('login')
  // Navigate to login page in parent application
  props.onNavigate?.('/login/government')
}

const handleRegister = () => {
  props.onAction?.('register')
  // Navigate to register page in parent application
  props.onNavigate?.('/auth/register')
}

const handleThemeToggle = (isDark: boolean) => {
  currentTheme.value = isDark ? 'dark' : 'light'
  const moduleElement = document.querySelector('.gsm-external-module') as HTMLElement
  if (moduleElement) {
    moduleElement.setAttribute('data-theme', currentTheme.value)
  }
  props.onAction?.('theme-change', { theme: currentTheme.value })
}

const handleExplore = () => {
  props.onAction?.('explore')
  // Navigate to main platform
  props.onNavigate?.('/app/government/dashboard')
}

const handleGetStarted = () => {
  props.onAction?.('get-started')
  // Navigate to onboarding
  props.onNavigate?.('/onboarding')
}

const handleViewMore = () => {
  props.onAction?.('view-more-news')
  // Navigate to news page
  props.onNavigate?.('/news')
}

const handleNewsClick = (newsItem: any) => {
  props.onAction?.('news-click', newsItem)
  // Navigate to specific news article
  props.onNavigate?.(`/news/${newsItem.id}`)
}

const handleFooterLink = (link: string) => {
  props.onAction?.('footer-link', link)
  // Handle external links or internal navigation
  if (link.startsWith('http')) {
    window.open(link, '_blank', 'noopener,noreferrer')
  } else {
    // prefer content paths mapping to public routes
    if (link.startsWith('/content/')) {
      props.onNavigate?.(link.replace('/content', ''))
    } else {
      props.onNavigate?.(link)
    }
  }
}
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background: var(--external-background-color);
  width: 100%;
  position: relative;
  overflow-x: hidden;
}

.main-content {
  position: relative;
  z-index: 1;
}

/* Global theme variables for external module */
.gsm-external-module {
  /* Default (dark) theme */
  --external-background-color: #0f172a;
  --external-surface-color: #1e293b;
  --external-card-bg: rgba(30, 41, 59, 0.8);
  --external-text-color: #f8fafc;
  --external-text-color-secondary: #94a3b8;
  --external-text-color-regular: #cbd5e1;
  --external-border-color: rgba(51, 65, 85, 0.6);
  --external-border-light: rgba(71, 85, 105, 0.4);
  --external-primary-color: #0080cc;
  --external-primary-hover: #60a5fa;
  --external-primary-bg: rgba(0, 128, 204, 0.05);
  --external-success-color: #00ff88;
  --external-warning-color: #faad14;
  --external-danger-color: #ff4444;
  --external-glow-color: rgba(255, 255, 255, 0.1);
}

/* Light theme overrides */
.gsm-external-module[data-theme='light'] {
  --external-background-color: #ffffff;
  --external-surface-color: #f8fafc;
  --external-card-bg: rgba(255, 255, 255, 0.9);
  --external-text-color: #1e293b;
  --external-text-color-secondary: #64748b;
  --external-text-color-regular: #475569;
  --external-border-color: rgba(203, 213, 225, 0.6);
  --external-border-light: rgba(203, 213, 225, 0.4);
  --external-primary-color: #3b82f6;
  --external-primary-hover: #2563eb;
  --external-primary-bg: rgba(59, 130, 246, 0.05);
  --external-success-color: #10b981;
  --external-warning-color: #f59e0b;
  --external-danger-color: #ef4444;
  --external-glow-color: rgba(59, 130, 246, 0.1);
}

/* Container for external components */
.external-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

/* Smooth transitions for theme changes */
.gsm-external-module,
.gsm-external-module * {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* Responsive container adjustments */
@media (max-width: 768px) {
  .external-container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .external-container {
    padding: 0 0.75rem;
  }
}

/* Prevent horizontal scroll issues */
.landing-page {
  overflow-x: hidden;
}

.landing-page * {
  box-sizing: border-box;
}

/* Ensure proper stacking context */
.main-content > * {
  position: relative;
  z-index: 1;
}

/* Full width sections reset */
.gsm-external-module section {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

/* Performance optimizations */
.gsm-external-module {
  contain: layout style paint;
  will-change: background-color;
}

/* Accessibility improvements */
.gsm-external-module {
  color-scheme: light dark;
}

.gsm-external-module[data-theme='light'] {
  color-scheme: light;
}

.gsm-external-module[data-theme='dark'] {
  color-scheme: dark;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gsm-external-module {
    --external-border-color: rgba(255, 255, 255, 0.8);
    --external-text-color-secondary: rgba(255, 255, 255, 0.9);
  }

  .gsm-external-module[data-theme='light'] {
    --external-border-color: rgba(0, 0, 0, 0.8);
    --external-text-color-secondary: rgba(0, 0, 0, 0.9);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .gsm-external-module,
  .gsm-external-module * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .gsm-external-module {
    background: white !important;
    color: black !important;
  }

  .gsm-external-module * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
</style>
