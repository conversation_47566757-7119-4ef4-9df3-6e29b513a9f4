<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">发布和管理政策法规、标准规范、公告通知等信息内容</p>
      </div>
      <Button @click="handleCreate" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新建发布
      </Button>
    </div>

    <!-- 信息列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>信息列表</span>
          <Badge variant="outline"> 共 {{ filteredItems.length }} 条记录 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 筛选条件 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="false"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead class="min-w-[300px]">标题</TableHead>
                <TableHead class="w-[120px]">信息类型</TableHead>
                <TableHead class="w-[100px]">发布状态</TableHead>
                <TableHead class="w-[180px]">发布时间</TableHead>
                <TableHead class="w-[120px]">发布人</TableHead>
                <TableHead class="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedItems.length === 0">
                <TableCell :colspan="7" class="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedItems"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.title }}</div>
                    <div class="text-sm text-muted-foreground line-clamp-1">{{ item.summary }}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getTypeVariant(item.type)">{{ item.type }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap">
                  {{ item.publishTime || '-' }}
                </TableCell>
                <TableCell class="whitespace-nowrap">{{ item.publisher }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleEdit(item.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '待发布'"
                        @click="handlePublish(item.id)"
                      >
                        <Send class="w-4 h-4 mr-2" />
                        发布
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '已发布'"
                        @click="handleOffline(item.id)"
                      >
                        <EyeOff class="w-4 h-4 mr-2" />
                        下线
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '已下线'"
                        @click="handleRepublish(item.id)"
                      >
                        <RefreshCw class="w-4 h-4 mr-2" />
                        重新发布
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDelete(item.id)" class="text-red-600">
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页（统一使用 shadcn-vue Pagination） -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredItems.length) }} 条， 共
            {{ filteredItems.length }} 条记录
          </div>
          <div class="flex items-center gap-4">
            <div class="pagination-size-control">
              <span>每页显示</span>
              <Select
                :model-value="pageSize.toString()"
                @update:model-value="
                  (v: string) => {
                    const n = +v
                    if (!Number.isNaN(n) && n > 0) {
                      pageSize = n
                      currentPage = 1
                    }
                  }
                "
              >
                <SelectTrigger class="w-20"><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span>条</span>
            </div>
            <Pagination
              v-model:page="currentPage"
              :total="filteredItems.length"
              :items-per-page="pageSize"
              :sibling-count="1"
              :show-edges="true"
            >
              <PaginationContent v-slot="{ items }">
                <PaginationFirst />
                <PaginationPrevious />
                <template v-for="(item, idx) in items" :key="idx">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === currentPage"
                  />
                  <PaginationEllipsis v-else />
                </template>
                <PaginationNext />
                <PaginationLast />
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 发布确认弹窗 -->
    <Dialog v-model:open="publishDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>确认发布</DialogTitle>
          <DialogDescription>
            确认发布该信息？发布后将对外公开，企业用户可以查看。
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="publishDialogOpen = false">取消</Button>
          <Button @click="confirmPublish">确认发布</Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 下线确认弹窗 -->
    <Dialog v-model:open="offlineDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>确认下线</DialogTitle>
          <DialogDescription>
            确认下线该信息？下线后将不再对外显示，但可以重新发布。
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="offlineDialogOpen = false">取消</Button>
          <Button variant="destructive" @click="confirmOffline">确认下线</Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 删除确认弹窗 -->
    <Dialog v-model:open="deleteDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>确认删除</DialogTitle>
          <DialogDescription> 确认删除该信息？删除后不可恢复，请谨慎操作。 </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="deleteDialogOpen = false">取消</Button>
          <Button variant="destructive" @click="confirmDelete">确认删除</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Edit, EyeOff, MoreHorizontal, Plus, RefreshCw, Trash2 } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

const router = useRouter()

type InformationType = '政策法规' | '标准规范' | '公告通知' | '新闻动态' | '通知公告'
type PublishStatus = '待发布' | '已发布' | '已下线'

interface InformationItem {
  id: string
  title: string
  summary: string
  type: InformationType
  status: PublishStatus
  content: string
  publishTime: string | null
  publisher: string
  createdAt: string
  updatedAt: string
}

// 筛选条件
const filters = reactive({
  title: '',
  type: 'ALL' as 'ALL' | InformationType,
  status: 'ALL' as 'ALL' | PublishStatus,
  publishTimeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'title',
    label: '标题关键词',
    type: 'input',
    placeholder: '请输入标题关键词',
  },
  {
    key: 'type',
    label: '信息类型',
    type: 'select',
    placeholder: '请选择信息类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '政策法规', value: '政策法规' },
      { label: '标准规范', value: '标准规范' },
      { label: '公告通知', value: '公告通知' },
      { label: '新闻动态', value: '新闻动态' },
      { label: '通知公告', value: '通知公告' },
    ],
  },
  {
    key: 'status',
    label: '发布状态',
    type: 'select',
    placeholder: '请选择发布状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '待发布', value: '待发布' },
      { label: '已发布', value: '已发布' },
      { label: '已下线', value: '已下线' },
    ],
  },
  {
    key: 'publishTimeRange',
    label: '发布时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 弹窗状态
const publishDialogOpen = ref(false)
const offlineDialogOpen = ref(false)
const deleteDialogOpen = ref(false)
const selectedItemId = ref<string | null>(null)

// Mock数据
const items = ref<InformationItem[]>([
  {
    id: 'INFO-2025-001',
    title: '关于加强智能网联汽车地理信息安全管理的通知',
    summary:
      '为进一步规范智能网联汽车地理信息采集、处理和使用行为，保障国家地理信息安全，现就有关事项通知如下...',
    type: '政策法规',
    status: '已发布',
    content: '详细内容...',
    publishTime: '2025-08-20 10:30',
    publisher: '管理员',
    createdAt: '2025-08-19 14:20',
    updatedAt: '2025-08-20 10:30',
  },
  {
    id: 'INFO-2025-002',
    title: '智能网联汽车地理信息安全技术规范（试行）',
    summary:
      '本规范规定了智能网联汽车地理信息安全技术要求、测试方法和评估流程，适用于智能网联汽车相关企业...',
    type: '标准规范',
    status: '已发布',
    content: '详细内容...',
    publishTime: '2025-08-18 16:45',
    publisher: '技术专家',
    createdAt: '2025-08-17 09:15',
    updatedAt: '2025-08-18 16:45',
  },
  {
    id: 'INFO-2025-003',
    title: '关于开展智能网联汽车地理信息安全专项检查的公告',
    summary:
      '为贯彻相关法律法规，保障地理信息安全，决定在全国范围内开展智能网联汽车地理信息安全专项检查...',
    type: '公告通知',
    status: '待发布',
    content: '详细内容...',
    publishTime: null,
    publisher: '监管部门',
    createdAt: '2025-08-21 11:20',
    updatedAt: '2025-08-21 11:20',
  },
  {
    id: 'INFO-2025-004',
    title: '智能网联汽车地理信息安全监测平台正式上线',
    summary:
      '经过为期半年的建设和测试，智能网联汽车地理信息安全监测平台今日正式上线运行，为行业提供统一的监测服务...',
    type: '新闻动态',
    status: '已发布',
    content: '详细内容...',
    publishTime: '2025-08-15 09:00',
    publisher: '新闻发言人',
    createdAt: '2025-08-14 16:30',
    updatedAt: '2025-08-15 09:00',
  },
  {
    id: 'INFO-2025-005',
    title: '关于系统维护升级的通知',
    summary:
      '为提升系统服务质量，计划于本周末进行系统维护升级，维护期间可能影响正常使用，请提前做好相关准备...',
    type: '通知公告',
    status: '已下线',
    content: '详细内容...',
    publishTime: '2025-08-16 14:20',
    publisher: '系统管理员',
    createdAt: '2025-08-16 14:00',
    updatedAt: '2025-08-17 10:00',
  },
])

// 过滤后的数据
const filteredItems = computed(() => {
  return items.value.filter((item) => {
    // 标题过滤
    if (filters.title && !item.title.toLowerCase().includes(filters.title.toLowerCase())) {
      return false
    }

    // 类型过滤
    if (filters.type !== 'ALL' && item.type !== filters.type) {
      return false
    }

    // 状态过滤
    if (filters.status !== 'ALL' && item.status !== filters.status) {
      return false
    }

    // 时间范围过滤
    if (filters.publishTimeRange && filters.publishTimeRange[0] && filters.publishTimeRange[1]) {
      if (!item.publishTime) return false
      const publishDate = new Date(item.publishTime.replace(/-/g, '/'))
      const startDate = new Date(
        filters.publishTimeRange[0].getFullYear(),
        filters.publishTimeRange[0].getMonth(),
        filters.publishTimeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.publishTimeRange[1].getFullYear(),
        filters.publishTimeRange[1].getMonth(),
        filters.publishTimeRange[1].getDate(),
        23,
        59,
        59,
      )
      if (publishDate < startDate || publishDate > endDate) return false
    }

    return true
  })
})

// 分页数据
const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredItems.value.length / pageSize.value)),
)
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredItems.value.slice(start, start + pageSize.value)
})

// 搜索处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters, searchFilters)
  }
  currentPage.value = 1
  console.log('搜索条件:', filters)
}

// 重置筛选
const resetFilters = () => {
  filters.title = ''
  filters.type = 'ALL'
  filters.status = 'ALL'
  filters.publishTimeRange = null
  currentPage.value = 1
}

// 新建发布
const handleCreate = () => {
  router.push('/gov/system/information/edit')
}

// 编辑
const handleEdit = (id: string) => {
  router.push(`/gov/system/information/edit/${id}`)
}

// 发布
const handlePublish = (id: string) => {
  selectedItemId.value = id
  publishDialogOpen.value = true
}

const confirmPublish = () => {
  if (selectedItemId.value) {
    const item = items.value.find((item) => item.id === selectedItemId.value)
    if (item) {
      item.status = '已发布'
      item.publishTime = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
        .replace(/\//g, '-')
      item.updatedAt = item.publishTime
    }
  }
  publishDialogOpen.value = false
  selectedItemId.value = null
}

// 下线
const handleOffline = (id: string) => {
  selectedItemId.value = id
  offlineDialogOpen.value = true
}

const confirmOffline = () => {
  if (selectedItemId.value) {
    const item = items.value.find((item) => item.id === selectedItemId.value)
    if (item) {
      item.status = '已下线'
      item.updatedAt = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
        .replace(/\//g, '-')
    }
  }
  offlineDialogOpen.value = false
  selectedItemId.value = null
}

// 重新发布
const handleRepublish = (id: string) => {
  const item = items.value.find((item) => item.id === id)
  if (item) {
    item.status = '已发布'
    item.publishTime = new Date()
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/\//g, '-')
    item.updatedAt = item.publishTime
  }
}

// 删除
const handleDelete = (id: string) => {
  selectedItemId.value = id
  deleteDialogOpen.value = true
}

const confirmDelete = () => {
  if (selectedItemId.value) {
    const index = items.value.findIndex((item) => item.id === selectedItemId.value)
    if (index > -1) {
      items.value.splice(index, 1)
    }
  }
  deleteDialogOpen.value = false
  selectedItemId.value = null
}

// Badge 样式
const getTypeVariant = (type: InformationType) => {
  switch (type) {
    case '政策法规':
      return 'default'
    case '标准规范':
      return 'secondary'
    case '公告通知':
      return 'outline'
    case '新闻动态':
      return 'default'
    case '通知公告':
      return 'secondary'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: PublishStatus) => {
  switch (status) {
    case '待发布':
      return 'secondary'
    case '已发布':
      return 'default'
    case '已下线':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
