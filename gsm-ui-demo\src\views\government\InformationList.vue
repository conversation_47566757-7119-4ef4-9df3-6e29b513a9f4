<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">发布和管理政策法规、标准规范、公告通知等信息内容</p>
      </div>
      <Button @click="handleCreate" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新建发布
      </Button>
    </div>

    <!-- 信息列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>信息列表</span>
          <Badge variant="outline"> 共 {{ filteredItems.length }} 条记录 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <!-- 筛选条件 - 紧贴表格 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="false"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
        />
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead class="min-w-[300px]">标题</TableHead>
                <TableHead class="w-[120px]">信息类型</TableHead>
                <TableHead class="w-[100px]">发布状态</TableHead>
                <TableHead class="w-[180px]">发布时间</TableHead>
                <TableHead class="w-[120px]">发布人</TableHead>
                <TableHead class="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedItems.length === 0">
                <TableCell :colspan="7" class="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedItems"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.title }}</div>
                    <div class="text-sm text-muted-foreground line-clamp-1">{{ item.summary }}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getTypeVariant(item.type)">{{ item.type }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap">
                  {{ item.publishTime || '-' }}
                </TableCell>
                <TableCell class="whitespace-nowrap">{{ item.publisher }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleEdit(item.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '待发布'"
                        @click="handlePublish(item.id)"
                      >
                        <Send class="w-4 h-4 mr-2" />
                        发布
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '已发布'"
                        @click="handleOffline(item.id)"
                      >
                        <EyeOff class="w-4 h-4 mr-2" />
                        下线
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '已下线'"
                        @click="handleRepublish(item.id)"
                      >
                        <RefreshCw class="w-4 h-4 mr-2" />
                        重新发布
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDelete(item.id)" class="text-red-600">
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页（统一使用 shadcn-vue Pagination） -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredItems.length) }} 条， 共
            {{ filteredItems.length }} 条记录
          </div>
          <div class="flex items-center gap-4">
            <div class="pagination-size-control">
              <span>每页显示</span>
              <Select
                :model-value="pageSize.toString()"
                @update:model-value="
                  (v: string) => {
                    const n = +v
                    if (!Number.isNaN(n) && n > 0) {
                      pageSize = n
                      currentPage = 1
                    }
                  }
                "
              >
                <SelectTrigger class="w-20"><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span>条</span>
            </div>
            <Pagination
              v-model:page="currentPage"
              :total="filteredItems.length"
              :items-per-page="pageSize"
              :sibling-count="1"
              :show-edges="true"
            >
              <PaginationContent v-slot="{ items }">
                <PaginationFirst />
                <PaginationPrevious />
                <template v-for="(item, idx) in items" :key="idx">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === currentPage"
                  />
                  <PaginationEllipsis v-else />
                </template>
                <PaginationNext />
                <PaginationLast />
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 发布确认弹窗 -->
    <Sheet v-model:open="publishDialogOpen">
      <SheetContent side="right" class="z-[60] w-[33vw] min-w-[380px] max-w-[640px]">
        <SheetHeader>
          <SheetTitle>确认发布</SheetTitle>
          <SheetDescription>
            确认发布该信息？发布后将对外公开，企业用户可以查看。
          </SheetDescription>
        </SheetHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="publishDialogOpen = false">取消</Button>
          <Button @click="confirmPublish">确认发布</Button>
        </div>
      </SheetContent>
    </Sheet>

    <!-- 下线确认弹窗 -->
    <Sheet v-model:open="offlineDialogOpen">
      <SheetContent side="right" class="z-[60] w-[33vw] min-w-[380px] max-w-[640px]">
        <SheetHeader>
          <SheetTitle>确认下线</SheetTitle>
          <SheetDescription>
            确认下线该信息？下线后将不再对外显示，但可以重新发布。
          </SheetDescription>
        </SheetHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="offlineDialogOpen = false">取消</Button>
          <Button variant="destructive" @click="confirmOffline">确认下线</Button>
        </div>
      </SheetContent>
    </Sheet>

    <!-- 删除确认弹窗 -->
    <Sheet v-model:open="deleteDialogOpen">
      <SheetContent side="right" class="z-[60] w-[33vw] min-w-[380px] max-w-[640px]">
        <SheetHeader>
          <SheetTitle>确认删除</SheetTitle>
          <SheetDescription> 确认删除该信息？删除后不可恢复，请谨慎操作。 </SheetDescription>
        </SheetHeader>
        <div class="flex justify-end gap-2">
          <Button variant="outline" @click="deleteDialogOpen = false">取消</Button>
          <Button variant="destructive" @click="confirmDelete">确认删除</Button>
        </div>
      </SheetContent>
    </Sheet>

    <!-- 新建/编辑 抽屉 -->
    <Sheet v-model:open="editDrawerOpen">
      <SheetContent
        side="right"
        class="z-[60] w-[66vw] min-w-[640px] max-w-[1100px] max-h-[100vh] overflow-y-auto"
      >
        <SheetHeader>
          <SheetTitle>{{ editMode === 'create' ? '新建信息' : '编辑信息' }}</SheetTitle>
          <SheetDescription>编辑政策法规、标准规范、公告通知等信息内容</SheetDescription>
        </SheetHeader>

        <div class="space-y-6 mt-2">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <Label for="title" class="required">文章标题</Label>
              <Input
                id="title"
                v-model="formData.title"
                placeholder="请输入文章标题"
                :class="{ 'border-red-500': errors.title }"
                required
              />
              <p v-if="errors.title" class="text-sm text-red-500">{{ errors.title }}</p>
            </div>

            <div class="space-y-2">
              <Label for="type" class="required">信息类型</Label>
              <Select v-model="formData.type" required>
                <SelectTrigger id="type" :class="{ 'border-red-500': errors.type }">
                  <SelectValue placeholder="请选择信息类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="政策法规">政策法规</SelectItem>
                  <SelectItem value="标准规范">标准规范</SelectItem>
                  <SelectItem value="公告通知">公告通知</SelectItem>
                  <SelectItem value="新闻动态">新闻动态</SelectItem>
                  <SelectItem value="通知公告">通知公告</SelectItem>
                </SelectContent>
              </Select>
              <p v-if="errors.type" class="text-sm text-red-500">{{ errors.type }}</p>
            </div>
          </div>

          <!-- 摘要 -->
          <div class="space-y-2">
            <Label for="summary" class="required">文章摘要</Label>
            <Textarea
              id="summary"
              v-model="formData.summary"
              placeholder="请输入文章摘要（建议100-200字）"
              :class="{ 'border-red-500': errors.summary }"
              rows="3"
              maxlength="500"
              required
            />
            <div class="flex justify-between">
              <p v-if="errors.summary" class="text-sm text-red-500">{{ errors.summary }}</p>
              <p class="text-sm text-muted-foreground">{{ formData.summary.length }}/500</p>
            </div>
          </div>

          <!-- 正文（富文本，与编辑页一致） -->
          <RichTextEditor
            id="content"
            label="正文内容"
            v-model="formData.content"
            :error="errors.content"
            placeholder="请输入正文内容..."
          />

          <!-- 发布设置 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <Label for="publishMode">发布模式</Label>
              <Select v-model="formData.publishMode">
                <SelectTrigger id="publishMode">
                  <SelectValue placeholder="请选择发布模式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">立即发布</SelectItem>
                  <SelectItem value="draft">保存为草稿</SelectItem>
                  <SelectItem value="scheduled">定时发布</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div v-if="formData.publishMode === 'scheduled'" class="space-y-2">
              <Label for="publishTime">发布时间</Label>
              <Input
                id="publishTime"
                v-model="formData.publishTime"
                type="datetime-local"
                :min="minDateTime"
              />
            </div>
          </div>

          <!-- 标签与优先级 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <Label for="tags">标签</Label>
              <div class="space-y-2">
                <Input
                  v-model="tagInput"
                  placeholder="输入标签后按回车添加"
                  @keydown.enter.prevent="addTag"
                />
                <div v-if="formData.tags.length > 0" class="flex flex-wrap gap-2">
                  <Badge
                    v-for="(tag, index) in formData.tags"
                    :key="index"
                    variant="secondary"
                    class="flex items-center gap-1"
                  >
                    {{ tag }}
                    <button type="button" class="inline-flex" @click="removeTag(index)">×</button>
                  </Badge>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <Label for="priority">优先级</Label>

              <Select v-model="formData.priority">
                <SelectTrigger id="priority">
                  <SelectValue placeholder="请选择优先级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">高</SelectItem>
                  <SelectItem value="normal">普通</SelectItem>
                  <SelectItem value="low">低</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-end gap-4 pt-4 border-t">
            <Button type="button" variant="outline" @click="resetForm">重置</Button>
            <Button type="button" variant="outline" @click="saveDraft">保存草稿</Button>
            <Button type="button" :disabled="isSubmitting" @click="handleSubmit">
              <Loader2 v-if="isSubmitting" class="w-4 h-4 mr-2 animate-spin" />
              {{ editMode === 'create' ? '发布信息' : '更新信息' }}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Edit, EyeOff, MoreHorizontal, Plus, RefreshCw, Trash2, Loader2 } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import RichTextEditor from '@/components/information/RichTextEditor.vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

const router = useRouter()

type InformationType = '政策法规' | '标准规范' | '公告通知' | '新闻动态' | '通知公告'
type PublishStatus = '待发布' | '已发布' | '已下线'

interface InformationItem {
  id: string
  title: string
  summary: string
  type: InformationType
  status: PublishStatus
  content: string
  publishTime: string | null
  publisher: string
  createdAt: string
  updatedAt: string
}

// 筛选条件
const filters = reactive({
  title: '',
  type: 'ALL' as 'ALL' | InformationType,
  status: 'ALL' as 'ALL' | PublishStatus,
  publishTimeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'title',
    label: '标题关键词',
    type: 'input',
    placeholder: '请输入标题关键词',
  },
  {
    key: 'type',
    label: '信息类型',
    type: 'select',
    placeholder: '请选择信息类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '政策法规', value: '政策法规' },
      { label: '标准规范', value: '标准规范' },
      { label: '公告通知', value: '公告通知' },
      { label: '新闻动态', value: '新闻动态' },
      { label: '通知公告', value: '通知公告' },
    ],
  },
  {
    key: 'status',
    label: '发布状态',
    type: 'select',
    placeholder: '请选择发布状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '待发布', value: '待发布' },
      { label: '已发布', value: '已发布' },
      { label: '已下线', value: '已下线' },
    ],
  },
  {
    key: 'publishTimeRange',
    label: '发布时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 弹窗状态
const publishDialogOpen = ref(false)
const offlineDialogOpen = ref(false)
const deleteDialogOpen = ref(false)
const selectedItemId = ref<string | null>(null)

// 新建/编辑抽屉状态与表单（脚本区）
const editDrawerOpen = ref(false)
const editMode = ref<'create' | 'edit'>('create')
const editingId = ref<string | null>(null)

interface EditFormData {
  title: string
  type: InformationType | ''
  summary: string
  content: string
  publishMode: 'immediate' | 'draft' | 'scheduled'
  publishTime: string
  tags: string[]
  priority: 'high' | 'normal' | 'low'
}

const formData = reactive<EditFormData>({
  title: '',
  type: '',
  summary: '',
  content: '',
  publishMode: 'immediate',
  publishTime: '',
  tags: [],
  priority: 'normal',
})

const errors = reactive({
  title: '',
  type: '',
  summary: '',
  content: '',
})

const isSubmitting = ref(false)
const tagInput = ref('')

const minDateTime = computed(() => new Date().toISOString().slice(0, 16))

const resetForm = () => {
  Object.assign(formData, {
    title: '',
    type: '',
    summary: '',
    content: '',
    publishMode: 'immediate',
    publishTime: '',
    tags: [],
    priority: 'normal',
  })
  Object.assign(errors, { title: '', type: '', summary: '', content: '' })
}

const validateForm = () => {
  errors.title = formData.title ? '' : '请输入文章标题'
  errors.type = formData.type ? '' : '请选择信息类型'
  errors.summary = formData.summary ? '' : '请输入文章摘要'
  errors.content = formData.content ? '' : '请输入正文内容'
  return !Object.values(errors).some(Boolean)
}

const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !formData.tags.includes(tag)) {
    formData.tags.push(tag)
    tagInput.value = ''
  }
}

const removeTag = (index: number) => {
  formData.tags.splice(index, 1)
}

const route = useRoute()

const clearEditQuery = () => {
  const q = { ...route.query }
  delete q.edit
  router.replace({ query: q })
}

const openCreateDrawer = () => {
  editMode.value = 'create'
  editingId.value = null
  resetForm()
  editDrawerOpen.value = true
  router.replace({ query: { ...route.query, edit: 'new' } })
}

const openEditDrawer = (id: string) => {
  editMode.value = 'edit'
  editingId.value = id
  const item = items.value.find((it) => it.id === id)
  if (item) {
    Object.assign(formData, {
      title: item.title,
      type: item.type,
      summary: item.summary,
      content: item.content,
      publishMode: item.status === '已发布' ? 'immediate' : 'draft',
      publishTime: typeof item.publishTime === 'string' ? item.publishTime : '',
      tags: [],
      priority: 'normal',
    })
  }
  editDrawerOpen.value = true
  router.replace({ query: { ...route.query, edit: id } })
}

const saveDraft = async () => {
  console.log('保存草稿:', { ...formData, id: editingId.value })
}

const handleSubmit = async () => {
  if (!validateForm()) return
  isSubmitting.value = true
  try {
    await new Promise((r) => setTimeout(r, 600))
    const nowStr = new Date()
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/\//g, '-')

    if (editMode.value === 'create') {
      const newId = `INFO-${Date.now()}`
      const status: PublishStatus = formData.publishMode === 'immediate' ? '已发布' : '待发布'
      const publishTime = formData.publishMode === 'immediate' ? nowStr : null
      items.value.unshift({
        id: newId,
        title: formData.title,
        summary: formData.summary,
        type: formData.type as InformationType,
        status,
        content: formData.content,
        publishTime,
        publisher: '管理员',
        createdAt: nowStr,
        updatedAt: nowStr,
      })
    } else if (editMode.value === 'edit' && editingId.value) {
      const idx = items.value.findIndex((it) => it.id === editingId.value)
      if (idx > -1) {
        const target = items.value[idx]
        target.title = formData.title
        target.summary = formData.summary
        target.type = formData.type as InformationType
        target.content = formData.content
        if (formData.publishMode === 'immediate') {
          target.status = '已发布'
          target.publishTime = nowStr
        }
        target.updatedAt = nowStr
      }
    }

    editDrawerOpen.value = false
    clearEditQuery()
  } catch (e) {
    console.error('提交失败', e)
  } finally {
    isSubmitting.value = false
  }
}

// Mock数据
const items = ref<InformationItem[]>([
  {
    id: 'INFO-2025-001',
    title: '关于加强智能网联汽车地理信息安全管理的通知',
    summary:
      '为进一步规范智能网联汽车地理信息采集、处理和使用行为，保障国家地理信息安全，现就有关事项通知如下...',
    type: '政策法规',
    status: '已发布',
    content: '详细内容...',
    publishTime: '2025-08-20 10:30',
    publisher: '管理员',
    createdAt: '2025-08-19 14:20',
    updatedAt: '2025-08-20 10:30',
  },
  {
    id: 'INFO-2025-002',
    title: '智能网联汽车地理信息安全技术规范（试行）',
    summary:
      '本规范规定了智能网联汽车地理信息安全技术要求、测试方法和评估流程，适用于智能网联汽车相关企业...',
    type: '标准规范',
    status: '已发布',
    content: '详细内容...',
    publishTime: '2025-08-18 16:45',
    publisher: '技术专家',
    createdAt: '2025-08-17 09:15',
    updatedAt: '2025-08-18 16:45',
  },
  {
    id: 'INFO-2025-003',
    title: '关于开展智能网联汽车地理信息安全专项检查的公告',
    summary:
      '为贯彻相关法律法规，保障地理信息安全，决定在全国范围内开展智能网联汽车地理信息安全专项检查...',
    type: '公告通知',
    status: '待发布',
    content: '详细内容...',
    publishTime: null,
    publisher: '监管部门',
    createdAt: '2025-08-21 11:20',
    updatedAt: '2025-08-21 11:20',
  },
  {
    id: 'INFO-2025-004',
    title: '智能网联汽车地理信息安全监测平台正式上线',
    summary:
      '经过为期半年的建设和测试，智能网联汽车地理信息安全监测平台今日正式上线运行，为行业提供统一的监测服务...',
    type: '新闻动态',
    status: '已发布',
    content: '详细内容...',
    publishTime: '2025-08-15 09:00',
    publisher: '新闻发言人',
    createdAt: '2025-08-14 16:30',
    updatedAt: '2025-08-15 09:00',
  },
  {
    id: 'INFO-2025-005',
    title: '关于系统维护升级的通知',
    summary:
      '为提升系统服务质量，计划于本周末进行系统维护升级，维护期间可能影响正常使用，请提前做好相关准备...',
    type: '通知公告',
    status: '已下线',
    content: '详细内容...',
    publishTime: '2025-08-16 14:20',
    publisher: '系统管理员',
    createdAt: '2025-08-16 14:00',
    updatedAt: '2025-08-17 10:00',
  },
])

// 路由深链支持（放在 items 定义之后，避免 TDZ 问题）
if (route.query.edit) {
  const val = route.query.edit as string
  if (val === 'new') openCreateDrawer()
  else openEditDrawer(val)
}

// 关闭抽屉时清理 URL 上的 edit 参数
watch(editDrawerOpen, (val) => {
  if (!val) clearEditQuery()
})

// 过滤后的数据
const filteredItems = computed(() => {
  return items.value.filter((item) => {
    // 标题过滤
    if (filters.title && !item.title.toLowerCase().includes(filters.title.toLowerCase())) {
      return false
    }

    // 类型过滤
    if (filters.type !== 'ALL' && item.type !== filters.type) {
      return false
    }

    // 状态过滤
    if (filters.status !== 'ALL' && item.status !== filters.status) {
      return false
    }

    // 时间范围过滤
    if (filters.publishTimeRange && filters.publishTimeRange[0] && filters.publishTimeRange[1]) {
      if (!item.publishTime) return false
      const publishDate = new Date(item.publishTime.replace(/-/g, '/'))
      const startDate = new Date(
        filters.publishTimeRange[0].getFullYear(),
        filters.publishTimeRange[0].getMonth(),
        filters.publishTimeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.publishTimeRange[1].getFullYear(),
        filters.publishTimeRange[1].getMonth(),
        filters.publishTimeRange[1].getDate(),
        23,
        59,
        59,
      )
      if (publishDate < startDate || publishDate > endDate) return false
    }

    return true
  })
})

// 分页数据
const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredItems.value.length / pageSize.value)),
)
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredItems.value.slice(start, start + pageSize.value)
})

// 搜索处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters, searchFilters)
  }
  currentPage.value = 1
  console.log('搜索条件:', filters)
}

// 重置筛选
const resetFilters = () => {
  filters.title = ''
  filters.type = 'ALL'
  filters.status = 'ALL'
  filters.publishTimeRange = null
  currentPage.value = 1
}

// 新建发布
const handleCreate = () => {
  openCreateDrawer()
}

// 编辑
const handleEdit = (id: string) => {
  openEditDrawer(id)
}

// 发布
const handlePublish = (id: string) => {
  selectedItemId.value = id
  publishDialogOpen.value = true
}

const confirmPublish = () => {
  if (selectedItemId.value) {
    const item = items.value.find((item) => item.id === selectedItemId.value)
    if (item) {
      item.status = '已发布'
      item.publishTime = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
        .replace(/\//g, '-')
      item.updatedAt = item.publishTime
    }
  }
  publishDialogOpen.value = false
  selectedItemId.value = null
}

// 下线
const handleOffline = (id: string) => {
  selectedItemId.value = id
  offlineDialogOpen.value = true
}

const confirmOffline = () => {
  if (selectedItemId.value) {
    const item = items.value.find((item) => item.id === selectedItemId.value)
    if (item) {
      item.status = '已下线'
      item.updatedAt = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
        .replace(/\//g, '-')
    }
  }
  offlineDialogOpen.value = false
  selectedItemId.value = null
}

// 重新发布
const handleRepublish = (id: string) => {
  const item = items.value.find((item) => item.id === id)
  if (item) {
    item.status = '已发布'
    item.publishTime = new Date()
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/\//g, '-')
    item.updatedAt = item.publishTime
  }
}

// 删除
const handleDelete = (id: string) => {
  selectedItemId.value = id
  deleteDialogOpen.value = true
}

const confirmDelete = () => {
  if (selectedItemId.value) {
    const index = items.value.findIndex((item) => item.id === selectedItemId.value)
    if (index > -1) {
      items.value.splice(index, 1)
    }
  }
  deleteDialogOpen.value = false
  selectedItemId.value = null
}

// Badge 样式
const getTypeVariant = (type: InformationType) => {
  switch (type) {
    case '政策法规':
      return 'default'
    case '标准规范':
      return 'secondary'
    case '公告通知':
      return 'outline'
    case '新闻动态':
      return 'default'
    case '通知公告':
      return 'secondary'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: PublishStatus) => {
  switch (status) {
    case '待发布':
      return 'secondary'
    case '已发布':
      return 'default'
    case '已下线':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
