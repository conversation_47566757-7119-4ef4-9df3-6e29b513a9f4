import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import './assets/index.css'
import './modules/public/assets/styles/theme.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 处理 404 页面重定向
const redirect = sessionStorage.getItem('redirect')
if (redirect) {
  sessionStorage.removeItem('redirect')
  router.push(redirect)
}

app.mount('#app')
