import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import './assets/main.css'
import './modules/public/assets/styles/theme.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 在应用启动时检查是否有 404 重定向遗留的路径
const pendingRedirect = sessionStorage.getItem('redirect')
if (pendingRedirect) {
  sessionStorage.removeItem('redirect')
  // 使用 router.replace 避免历史记录污染
  router.replace(pendingRedirect)
}

app.mount('#app')
