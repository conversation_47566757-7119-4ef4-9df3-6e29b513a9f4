// 组件类型声明

// Chart 相关类型
export interface ChartDataItem {
  name: string
  value: number
}

export interface ChartData {
  [key: string]: number[] | ChartDataItem[]
}

// 颜色数组类型 - 解决 readonly 数组问题
export type ColorArray = string[]
export type ReadonlyColorArray = readonly string[]

// Badge 变体类型扩展
declare module '@/components/ui/badge/Badge.vue' {
  interface BadgeVariants {
    variant: 'default' | 'outline' | 'destructive' | 'secondary' | string
  }
}

// Select 组件类型扩展
export type AcceptableValue = string | number | boolean | null | undefined

// Textarea 组件 class 属性类型扩展
export interface TextareaProps {
  class?: string | Record<string, boolean | string> | Array<string | Record<string, boolean>>
  [key: string]: any
}

// 企业类型扩展 - 解决索引签名问题
export interface Enterprise {
  [key: string]: any
  id?: string
  name?: string
  type?: string
  status?: string
}

// Task 管理类型扩展
export interface BaseTask {
  id: string
  category: string
  title: string
  description: string
  receivedAt: string
  details?: Record<string, any>
}

export interface PendingTask extends BaseTask {
  priority: string
  deadline: string
}

export interface CompletedTask extends BaseTask {
  completedAt: string
  result: string
}

export type Task = PendingTask | CompletedTask

// 文件类型扩展
export interface FileWithMetadata extends File {
  readonly lastModified: number
  readonly name: string
  readonly webkitRelativePath: string
  readonly size: number
  readonly type: string
}

// 追溯记录类型
export interface TraceRecord {
  riskId: string
  enterprise: string
  vin: string
  timestamp: string
  traceSequence: any[]
  packetInfo: {
    id: string
    enterprise?: string
    vin?: string
  }
  traceType: 'cloud' | 'vehicle'
}

// Vue 组件实例类型扩展
declare module 'vue' {
  interface ComponentCustomProperties {
    // 添加全局属性
  }
  
  // Vue Chart 实例类型
  interface ChartInstance {
    getChart?(): any
    chart?: any
  }
}

// ECharts 相关类型
export interface EChartsInstance {
  getChart?(): any
}

// 地图相关类型
export interface LeafletInstance {
  leafletObject?: any
}

// 文件策略类型
export interface PolicyFiles {
  collection: File | null
  storage: File | null
  processing: File | null
  provision: File | null
  disclosure: File | null
  destruction: File | null
  logging: File | null
  [key: string]: File | null
}

export {}
