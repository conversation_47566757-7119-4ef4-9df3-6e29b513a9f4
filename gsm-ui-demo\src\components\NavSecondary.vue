<script setup lang="ts">
import type { LucideIcon } from "lucide-vue-next"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'

const props = defineProps<{
  items: {
    title: string
    url: string
    icon: LucideIcon
  }[]
}>()
</script>

<template>
  <SidebarGroup data-sidebar="group">
    <SidebarGroupContent data-sidebar="group-content">
      <SidebarMenu data-sidebar="menu" class="space-y-2">
        <SidebarMenuItem 
          v-for="item in items" 
          :key="item.title"
          data-sidebar="menu-item"
        >
          <SidebarMenuButton 
            as-child 
            data-sidebar="menu-button"
            size="default"
          >
            <a :href="item.url" class="flex items-center gap-3 px-3 py-2.5">
              <component :is="item.icon" class="h-4 w-4 shrink-0" />
              <span class="font-medium">{{ item.title }}</span>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroupContent>
  </SidebarGroup>
</template>
