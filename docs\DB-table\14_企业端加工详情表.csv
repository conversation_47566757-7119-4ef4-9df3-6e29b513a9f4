﻿表名: log_ent_process_detail,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id"""
3,processing_type,加工类型,SMALLINT,,○,,"""(BYTE) 0x01:数据汇聚及脱敏处理; 0x02:导航电子地图制作; 0x03:场景库制作及服务; 0x04:互联网地图服务; 0x05:其他"""
4,position_accuracy_compliance,平面位置精度合规性,SMALLINT,,○,,"""(BYTE) 0x01:符合要求; 0x02:不符合要求"""
5,desensitization_status,脱敏状态,SMALLINT,,○,,"""(BYTE) 0x01:已脱敏; 0x02:未脱敏"""
6,is_new_package_generated,是否生成新数据包,SMALLINT,,○,,"""(BYTE) 0x01:是; 0x02:否"""
7,new_package_count,新数据包数量,INTEGER,,○,,"""(WORD)。若生成新数据包，此处填写生成的新数据包个数。否则填0。"""
8,output_data_package_ids,新数据包ID列表,JSONB,,,,若生成了新数据包，此处填写所有新包的唯一标识ID。用于数据血缘。
9,new_package_data_types_bitmap,新数据包数据类型,BIGINT,,,,(DWORD) BitMap。标识新数据包中包含的数据类型。
10,operator_identity,操作员身份,SMALLINT,,○,,"""(BYTE) 0x01:重要数据操作人员; 0x02:一般数据操作人员"""
