<template>
  <div class="space-y-2">
    <Label :for="id" class="required" v-if="label">{{ label }}</Label>
    <div class="border rounded-lg overflow-hidden">
      <!-- 工具栏 -->
      <div class="border-b bg-muted/30 p-3 flex flex-wrap gap-2">
        <div class="flex items-center gap-1 border-r pr-2 mr-2">
          <Button type="button" variant="ghost" size="sm" @click="exec('bold')" :class="{ 'bg-accent': state.bold }">
            <Bold class="w-4 h-4" />
          </Button>
          <Button type="button" variant="ghost" size="sm" @click="exec('italic')" :class="{ 'bg-accent': state.italic }">
            <Italic class="w-4 h-4" />
          </Button>
          <Button type="button" variant="ghost" size="sm" @click="exec('underline')" :class="{ 'bg-accent': state.underline }">
            <Underline class="w-4 h-4" />
          </Button>
        </div>

        <div class="flex items-center gap-1 border-r pr-2 mr-2">
          <Button type="button" variant="ghost" size="sm" @click="exec('justifyLeft')">
            <AlignLeft class="w-4 h-4" />
          </Button>
          <Button type="button" variant="ghost" size="sm" @click="exec('justifyCenter')">
            <AlignCenter class="w-4 h-4" />
          </Button>
          <Button type="button" variant="ghost" size="sm" @click="exec('justifyRight')">
            <AlignRight class="w-4 h-4" />
          </Button>
        </div>

        <div class="flex items-center gap-1 border-r pr-2 mr-2">
          <Button type="button" variant="ghost" size="sm" @click="exec('insertOrderedList')">
            <List class="w-4 h-4" />
          </Button>
          <Button type="button" variant="ghost" size="sm" @click="exec('insertUnorderedList')">
            <ListChecks class="w-4 h-4" />
          </Button>
        </div>

        <div class="flex items-center gap-1">
          <Button type="button" variant="ghost" size="sm" @click="openLinkDialog">
            <Link class="w-4 h-4" />
          </Button>
          <Button type="button" variant="ghost" size="sm" @click="insertImage">
            <ImageIcon class="w-4 h-4" />
          </Button>
        </div>
      </div>

      <!-- 编辑区域 -->
      <div
        ref="editor"
        contenteditable="true"
        class="min-h-[300px] p-4 prose max-w-none focus:outline-none"
        :class="{ 'border-red-500': error }"
        :placeholder="placeholder"
        @input="update"
        @paste="onPaste"
        @keydown="onKeydown"
      ></div>
    </div>
    <p v-if="error" class="text-sm text-red-500">{{ error }}</p>

    <!-- 插入链接 -->
    <Sheet v-model:open="linkOpen">
      <SheetContent side="right" class="w-[33vw] min-w-[380px] max-w-[640px]">
        <SheetHeader>
          <SheetTitle>插入链接</SheetTitle>
          <SheetDescription>请输入链接信息</SheetDescription>
        </SheetHeader>
        <div class="space-y-4">
          <div class="space-y-2">
            <Label for="linkText">链接文字</Label>
            <Input id="linkText" v-model="linkData.text" placeholder="请输入链接文字" />
          </div>
          <div class="space-y-2">
            <Label for="linkUrl">链接地址</Label>
            <Input id="linkUrl" v-model="linkData.url" placeholder="https://example.com" />
          </div>
        </div>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="linkOpen = false">取消</Button>
          <Button @click="confirmInsertLink">确认</Button>
        </div>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue'
import { AlignCenter, AlignLeft, AlignRight, Bold, ImageIcon, Italic, Link, List, ListChecks, Underline } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet'

const props = defineProps<{
  modelValue: string
  label?: string
  id?: string
  placeholder?: string
  error?: string
}>()

const emit = defineEmits(['update:modelValue'])

const editor = ref<HTMLDivElement>()
const linkOpen = ref(false)
const linkData = reactive({ text: '', url: '' })

const state = reactive({ bold: false, italic: false, underline: false })

watch(
  () => props.modelValue,
  (val) => {
    if (editor.value && editor.value.innerHTML !== val) {
      editor.value.innerHTML = val || ''
    }
  },
  { immediate: true },
)

const update = () => {
  if (editor.value) emit('update:modelValue', editor.value.innerHTML)
}

const exec = (cmd: string, value?: string) => {
  document.execCommand(cmd, false, value)
  editor.value?.focus()
  refreshState()
}

const refreshState = () => {
  state.bold = document.queryCommandState('bold')
  state.italic = document.queryCommandState('italic')
  state.underline = document.queryCommandState('underline')
}

const onPaste = (e: ClipboardEvent) => {
  e.preventDefault()
  const text = e.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const onKeydown = (e: KeyboardEvent) => {
  if (e.ctrlKey || e.metaKey) {
    switch (e.key) {
      case 'b':
        e.preventDefault(); exec('bold'); break
      case 'i':
        e.preventDefault(); exec('italic'); break
      case 'u':
        e.preventDefault(); exec('underline'); break
    }
  }
}

const openLinkDialog = () => {
  const sel = window.getSelection()
  if (sel && sel.toString()) linkData.text = sel.toString()
  linkOpen.value = true
}

const confirmInsertLink = () => {
  if (linkData.url) {
    const html = `<a href="${linkData.url}" target="_blank">${linkData.text || linkData.url}</a>`
    document.execCommand('insertHTML', false, html)
  }
  linkOpen.value = false
  linkData.text = ''
  linkData.url = ''
  editor.value?.focus()
}

const insertImage = () => {
  const url = prompt('请输入图片URL：')
  if (url) {
    const img = `<img src="${url}" alt="插入的图片" style="max-width: 100%; height: auto;" />`
    document.execCommand('insertHTML', false, img)
  }
  editor.value?.focus()
}
</script>

<style scoped>
.required::after {
  content: ' *';
  color: rgb(239 68 68);
}
.prose { line-height: 1.6; }
.prose p { margin-bottom: 1em; }
.prose img { max-width: 100%; height: auto; margin: 1em 0; border-radius: 0.5rem; }
[contenteditable='true']:focus { outline: none; }
[contenteditable='true']:empty:before { content: attr(placeholder); color: rgb(156 163 175); font-style: italic; }
</style>

