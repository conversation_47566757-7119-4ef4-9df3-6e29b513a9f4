#!/bin/bash
# deploy-axure.sh - 部署Axure原型到public目录

echo "开始部署Axure原型文件..."

# 创建目标目录
mkdir -p ../../public/axure

# 复制所有文件
cp -r ./* ../../public/axure/

# 移除不需要的文件
rm -f ../../public/axure/deploy-axure.sh
rm -f ../../public/axure/deploy-axure.bat
rm -f ../../public/axure/README.md
rm -f ../../public/axure/AxureGovernmentDashboard.vue

echo "✅ Axure原型文件部署完成!"
echo "📁 文件位置: public/axure/"
echo "🌐 访问地址: http://localhost:5173/axure/GovernmentDashboard.html"