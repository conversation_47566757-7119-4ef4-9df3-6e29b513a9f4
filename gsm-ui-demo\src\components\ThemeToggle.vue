<script setup lang="ts">
import { computed, onMounted, watchEffect } from 'vue'
import { Icon } from '@iconify/vue'
import { useColorMode, usePreferredDark } from '@vueuse/core'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

// System dark mode preference
const prefersDark = usePreferredDark()

// Configure useColorMode with proper system detection
const mode = useColorMode({
  selector: 'html',
  attribute: 'class',
  initialValue: 'auto',
  storageKey: 'vueuse-color-scheme',
  emitAuto: true,
  disableTransition: false
})

// Watch for system preference changes and update accordingly
watchEffect(() => {
  if (mode.value === 'auto') {
    // Force update the class when system preference changes
    const html = document.documentElement
    if (prefersDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }
})

onMounted(() => {
  // Ensure proper initial theme application
  if (mode.value === 'auto') {
    const html = document.documentElement
    if (prefersDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }
})

// 计算当前主题的显示文本
const currentThemeText = computed(() => {
  switch (mode.value) {
    case 'light':
      return '浅色'
    case 'dark':
      return '深色'
    case 'auto':
      return `跟随系统 (${prefersDark.value ? '深色' : '浅色'})`
    default:
      return '主题'
  }
})
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="outline" size="icon" class="relative">
        <Icon
          icon="radix-icons:moon"
          class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0"
        />
        <Icon
          icon="radix-icons:sun"
          class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100"
        />
        <span class="sr-only">切换主题 - 当前: {{ currentThemeText }}</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-40">
      <DropdownMenuItem
        @click="mode = 'light'"
        class="cursor-pointer"
        :class="{ 'bg-accent': mode === 'light' }"
      >
        <Icon icon="radix-icons:sun" class="mr-2 h-4 w-4" />
        <span>浅色模式</span>
        <Icon
          v-if="mode === 'light'"
          icon="radix-icons:check"
          class="ml-auto h-4 w-4"
        />
      </DropdownMenuItem>
      <DropdownMenuItem
        @click="mode = 'dark'"
        class="cursor-pointer"
        :class="{ 'bg-accent': mode === 'dark' }"
      >
        <Icon icon="radix-icons:moon" class="mr-2 h-4 w-4" />
        <span>深色模式</span>
        <Icon
          v-if="mode === 'dark'"
          icon="radix-icons:check"
          class="ml-auto h-4 w-4"
        />
      </DropdownMenuItem>
      <DropdownMenuItem
        @click="mode = 'auto'"
        class="cursor-pointer"
        :class="{ 'bg-accent': mode === 'auto' }"
      >
        <Icon icon="radix-icons:desktop" class="mr-2 h-4 w-4" />
        <span>跟随系统</span>
        <Icon
          v-if="mode === 'auto'"
          icon="radix-icons:check"
          class="ml-auto h-4 w-4"
        />
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
