# Axure原型集成指南

## 部署步骤

### 1. 复制Axure文件到public目录

将Axure文件从 `src/components/axure/` 复制到 `public/axure/` 目录：

```bash
# Windows命令
xcopy /E /I "src\components\axure" "public\axure"

# Linux/Mac命令
cp -r src/components/axure/* public/axure/
```

### 2. 文件结构

确保public目录下有以下结构：
```
public/
  axure/
    GovernmentDashboard.html
    data/
    files/
    images/
    resources/
```

### 3. 访问方式

部署完成后，Axure原型将通过以下方式访问：

- **开发环境**：http://localhost:5173/gov/dashboard
- **生产环境**：https://your-domain.com/gov/dashboard

### 4. 三种显示模式

组件提供了三种显示模式：

1. **iframe模式**（默认）
   - 加载本地Axure文件
   - 适合开发环境
   - 需要将文件部署到public目录

2. **embed模式**
   - 直接嵌入HTML内容
   - 适合需要修改Axure内容的场景

3. **external模式**
   - 加载外部URL
   - 适合已部署的Axure原型

### 5. 配置外部URL

如果你的Axure原型已部署到外部服务器，可以这样配置：

```vue
<AxureGovernmentDashboard 
  :external-url="'https://your-axure-server.com/prototype.html'"
  :initial-mode="'external'"
/>
```

### 6. 响应式缩放

组件会自动根据窗口大小进行缩放，确保在不同分辨率下都能正常显示。

### 7. 性能优化

- 使用iframe沙盒模式提高安全性
- 自动检测文件可访问性
- 延迟加载提升首屏性能

## 注意事项

1. **跨域问题**：如果使用external模式，确保外部服务器允许跨域访问
2. **文件大小**：Axure原型文件较大，首次加载可能需要时间
3. **浏览器兼容**：建议使用Chrome、Firefox等现代浏览器

## 故障排除

### 问题1：Axure文件无法加载
- 检查文件是否已复制到public/axure目录
- 检查浏览器控制台是否有404错误
- 尝试直接访问 http://localhost:5173/axure/GovernmentDashboard.html

### 问题2：样式显示异常
- 确保所有资源文件（CSS、JS、图片）都已复制
- 检查是否有样式冲突

### 问题3：缩放效果不理想
- 调整浏览器窗口大小触发重新计算
- 使用开发者工具检查transform属性