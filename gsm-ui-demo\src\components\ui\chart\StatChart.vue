<template>
  <div class="flex items-center gap-4">
    <!-- 左侧文字区域 -->
    <div class="flex-1 space-y-2">
      <div class="text-2xl font-bold" :class="valueColorClass">
        {{ value }}
      </div>
      <p class="text-xs text-muted-foreground">
        {{ description }}
      </p>
    </div>

    <!-- 右侧图表区域 -->
    <div class="w-20 h-16 flex-shrink-0">
      <div class="stat-chart-container">
        <v-chart
          :class="['w-full h-full']"
          :option="chartOption"
          :autoresize="true"
          @mouseover="handleMouseOver"
          @mouseout="handleMouseOut"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { getUnifiedColorScheme, inferColorScheme } from '@/lib/unified-chart-colors'

use([
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  BarChart,
  LineChart,
])

interface Props {
  type: 'pie' | 'bar' | 'line'
  data: any[]
  value: string | number
  description: string
  valueType?: 'success' | 'warning' | 'danger' | 'info' | 'default'
  colorScheme?:
    | 'primary'
    | 'risk'
    | 'enterprise'
    | 'stages'
    | 'status'
    | 'vehicleTypes'
    | 'barChart'
    | 'disposalStatus'
    | 'processingStages'
    | 'eventTypes'
    | 'approvalStatus'
  autoColor?: boolean
  series?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  valueType: 'default',
  colorScheme: 'primary',
  autoColor: true,
})

const emit = defineEmits<{
  mouseover: [params: any]
  mouseout: [params: any]
}>()

// 数值颜色类
const valueColorClass = computed(() => {
  switch (props.valueType) {
    case 'success':
      return 'text-green-600'
    case 'warning':
      return 'text-orange-600'
    case 'danger':
      return 'text-red-600'
    case 'info':
      return 'text-blue-600'
    default:
      return 'text-foreground'
  }
})

// 智能推断配色方案
const inferredScheme = computed(() => {
  if (!props.autoColor) return props.colorScheme
  if (props.colorScheme && props.colorScheme !== 'primary') return props.colorScheme

  // 根据图表类型和数据推断配色
  if (props.type === 'bar') {
    return inferColorScheme(props.data) === 'risk' ? 'risk' : 'barChart'
  }
  return inferColorScheme(props.data)
})

// 获取配色方案
const getColors = computed(() => {
  return getUnifiedColorScheme(inferredScheme.value || 'primary')
})

// 图表配置
const chartOption = computed(() => {
  const baseOptions = {
    animation: false, // 禁用动画提高小图表性能
    backgroundColor: 'transparent',
    tooltip: {
      show: false, // 小图表不显示tooltip
    },
    color: [...getColors.value] as string[],
  }

  switch (props.type) {
    case 'pie':
      return {
        ...baseOptions,
        series: [
          {
            type: 'pie',
            radius: ['30%', '80%'],
            center: ['50%', '50%'],
            data: props.data,
            label: { show: false },
            labelLine: { show: false },
            itemStyle: {
              borderWidth: 0,
              shadowBlur: 0,
            },
            emphasis: {
              scale: false,
              itemStyle: {
                shadowBlur: 0,
              },
            },
          },
        ],
      }

    case 'bar':
      return {
        ...baseOptions,
        grid: {
          left: 0,
          right: 0,
          top: 2,
          bottom: 2,
          containLabel: false,
        },
        xAxis: {
          type: 'category',
          data: props.data.map((item) => item.name || item.category),
          show: false,
        },
        yAxis: {
          type: 'value',
          show: false,
        },
        series: [
          {
            type: 'bar',
            data: props.data.map((item) => item.value),
            itemStyle: {
              borderRadius: [2, 2, 0, 0],
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 0,
              },
            },
          },
        ],
      }

    case 'line':
      return {
        ...baseOptions,
        grid: {
          left: 0,
          right: 0,
          top: 2,
          bottom: 2,
          containLabel: false,
        },
        xAxis: {
          type: 'category',
          data: props.series?.[0]?.data?.map((_: any, index: number) => index) || [],
          show: false,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          show: false,
        },
        series:
          props.series?.map((serie) => ({
            type: 'line',
            data: serie.data,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              width: 2,
            },
            areaStyle: {
              opacity: 0.3,
            },
          })) || [],
      }

    default:
      return baseOptions
  }
})

const handleMouseOver = (params: any) => {
  emit('mouseover', params)
}

const handleMouseOut = (params: any) => {
  emit('mouseout', params)
}
</script>

<style scoped>
.stat-chart-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.stat-chart-container :deep(canvas) {
  width: 100% !important;
  height: 100% !important;
}

/* 确保图表不会超出容器 */
.stat-chart-container :deep(.vue-echarts) {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden;
}
</style>
