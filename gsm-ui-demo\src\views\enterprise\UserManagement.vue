<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理企业内部用户账户，支持用户创建、权限分配和状态管理
        </p>
      </div>
      <Button @click="handleCreateUser" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增用户
      </Button>
    </div>

    <!-- 用户列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>用户列表</span>
          <Badge variant="outline"> 共 {{ users.length }} 个用户 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>用户名称</TableHead>
                <TableHead>角色</TableHead>
                <TableHead>部门</TableHead>
                <TableHead>邮箱</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead class="w-[120px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="users.length === 0">
                <TableCell :colspan="8" class="h-24 text-center text-muted-foreground">
                  暂无用户数据
                </TableCell>
              </TableRow>
              <TableRow v-for="(user, index) in users" :key="user.id">
                <TableCell>{{ index + 1 }}</TableCell>
                <TableCell>{{ user.name }}</TableCell>
                <TableCell>
                  <Badge :variant="getRoleVariant(user.role)">{{ user.role }}</Badge>
                </TableCell>
                <TableCell>{{ user.department }}</TableCell>
                <TableCell>{{ user.email }}</TableCell>
                <TableCell>
                  <Badge :variant="user.status === 'active' ? 'default' : 'secondary'">
                    {{ user.status === 'active' ? '正常' : '禁用' }}
                  </Badge>
                </TableCell>
                <TableCell>{{ user.createdAt }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <Button size="sm" variant="outline" @click="handleEdit(user)">编辑</Button>
                    <Button size="sm" variant="outline" @click="handleDelete(user)">删除</Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface User {
  id: string
  name: string
  role: string
  department: string
  email: string
  status: 'active' | 'inactive'
  createdAt: string
}

const users = ref<User[]>([
  {
    id: '1',
    name: '张三',
    role: '管理员',
    department: '技术部',
    email: '<EMAIL>',
    status: 'active',
    createdAt: '2025-01-15',
  },
  {
    id: '2',
    name: '李四',
    role: '操作员',
    department: '运营部',
    email: '<EMAIL>',
    status: 'active',
    createdAt: '2025-02-20',
  },
  {
    id: '3',
    name: '王五',
    role: '查看员',
    department: '市场部',
    email: '<EMAIL>',
    status: 'inactive',
    createdAt: '2025-03-10',
  },
])

const getRoleVariant = (role: string) => {
  switch (role) {
    case '管理员':
      return 'destructive'
    case '操作员':
      return 'default'
    case '查看员':
      return 'secondary'
    default:
      return 'outline'
  }
}

const handleCreateUser = () => {
  console.log('创建新用户')
}

const handleEdit = (user: User) => {
  console.log('编辑用户:', user)
}

const handleDelete = (user: User) => {
  console.log('删除用户:', user)
}
</script>