<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">管理系统数据字典，包括类型定义、键值对配置和显示顺序等</p>
      </div>
      <Button @click="handleCreateDict" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增字典
      </Button>
    </div>

    <!-- 字典分类 -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 左侧字典类型列表 -->
      <Card class="lg:col-span-1">
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <span>字典类型</span>
            <Badge variant="outline">{{ dictTypes.length }}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <Button
              :variant="selectedType === 'ALL' ? 'default' : 'ghost'"
              size="sm"
              class="w-full justify-start"
              @click="selectedType = 'ALL'"
            >
              <BookOpen class="w-4 h-4 mr-2" />
              全部类型
            </Button>
            <Button
              v-for="type in dictTypes"
              :key="type.key"
              :variant="selectedType === type.key ? 'default' : 'ghost'"
              size="sm"
              class="w-full justify-start"
              @click="selectedType = type.key"
            >
              <component :is="getTypeIcon(type.key)" class="w-4 h-4 mr-2" />
              {{ type.label }}
              <Badge variant="outline" class="ml-auto text-xs">
                {{ getDictCount(type.key) }}
              </Badge>
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 右侧字典条目列表 -->
      <Card class="lg:col-span-3">
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <span>
              字典条目
              <span
                v-if="selectedType !== 'ALL'"
                class="text-base font-normal text-muted-foreground"
              >
                - {{ getTypeLabel(selectedType) }}
              </span>
            </span>
            <Badge variant="outline"> 共 {{ filteredDictItems.length }} 条记录 </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent class="p-0">
          <!-- 筛选条件 - 紧贴表格 -->
          <CompactFilterForm
            :filter-fields="filterFields"
            :show-export="true"
            :initial-values="filters"
            @search="handleSearch"
            @reset="resetFilters"
            @export="exportData"
          />
          <div class="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-[80px]">序号</TableHead>
                  <TableHead>字典类型</TableHead>
                  <TableHead>字典键</TableHead>
                  <TableHead>字典值</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead class="w-[100px]">排序</TableHead>
                  <TableHead class="w-[100px]">状态</TableHead>
                  <TableHead class="w-[140px]">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="pagedDictItems.length === 0">
                  <TableCell :colspan="8" class="h-24 text-center text-muted-foreground">
                    暂无数据
                  </TableCell>
                </TableRow>
                <TableRow
                  v-for="(item, index) in pagedDictItems"
                  :key="item.id"
                  class="hover:bg-muted/40"
                >
                  <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                  <TableCell>
                    <div class="flex items-center gap-2">
                      <component :is="getTypeIcon(item.dictType)" class="w-4 h-4 text-primary" />
                      <Badge variant="outline" class="text-xs">
                        {{ getTypeLabel(item.dictType) }}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <code class="bg-muted px-2 py-1 rounded text-sm font-mono">
                      {{ item.dictKey }}
                    </code>
                  </TableCell>
                  <TableCell>
                    <span class="font-medium">{{ item.dictValue }}</span>
                  </TableCell>
                  <TableCell>
                    <span class="text-sm text-muted-foreground">{{ item.description || '-' }}</span>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        @click="handleSortUp(item.id)"
                        :disabled="index === 0"
                      >
                        <ChevronUp class="w-3 h-3" />
                      </Button>
                      <span class="text-sm font-mono w-8 text-center">{{ item.sortOrder }}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        @click="handleSortDown(item.id)"
                        :disabled="index === pagedDictItems.length - 1"
                      >
                        <ChevronDown class="w-3 h-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="item.isActive ? 'default' : 'destructive'">
                      {{ item.isActive ? '启用' : '禁用' }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem @click="handleEdit(item.id)">
                          <Edit class="w-4 h-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem @click="handleDuplicate(item.id)">
                          <Copy class="w-4 h-4 mr-2" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          v-if="item.isActive"
                          @click="handleDisable(item.id)"
                          class="text-orange-600"
                        >
                          <XCircle class="w-4 h-4 mr-2" />
                          禁用
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          v-if="!item.isActive"
                          @click="handleEnable(item.id)"
                          class="text-green-600"
                        >
                          <CheckCircle class="w-4 h-4 mr-2" />
                          启用
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem @click="handleDelete(item.id)" class="text-red-600">
                          <Trash2 class="w-4 h-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- 分页 -->
          <div class="flex items-center justify-between mt-4 px-4 pb-4">
            <div class="text-sm text-muted-foreground">
              显示第 {{ (currentPage - 1) * pageSize + 1 }} -
              {{ Math.min(currentPage * pageSize, filteredDictItems.length) }} 条， 共
              {{ filteredDictItems.length }} 条记录
            </div>
            <div class="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                @click="currentPage = Math.max(1, currentPage - 1)"
                :disabled="currentPage === 1"
              >
                上一页
              </Button>
              <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
              <Button
                variant="outline"
                size="sm"
                @click="currentPage = Math.min(totalPages, currentPage + 1)"
                :disabled="currentPage === totalPages"
              >
                下一页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 编辑对话框 -->
    <Dialog :open="showEditDialog" @update:open="showEditDialog = $event">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>{{ editMode === 'create' ? '新增字典' : '编辑字典' }}</DialogTitle>
          <DialogDescription>
            {{ editMode === 'create' ? '创建新的字典条目' : '修改现有字典条目信息' }}
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="dict-type">字典类型</Label>
              <Select v-model="editForm.dictType">
                <SelectTrigger>
                  <SelectValue placeholder="选择字典类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="type in dictTypes" :key="type.key" :value="type.key">
                    {{ type.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="dict-key">字典键</Label>
              <Input id="dict-key" v-model="editForm.dictKey" placeholder="请输入字典键" />
            </div>
          </div>
          <div>
            <Label for="dict-value">字典值</Label>
            <Input
              id="dict-value"
              v-model="editForm.dictValue"
              placeholder="请输入字典值（显示名称）"
            />
          </div>
          <div>
            <Label for="description">描述</Label>
            <Textarea
              id="description"
              v-model="editForm.description"
              placeholder="请输入描述（可选）"
              rows="2"
            />
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="sort-order">排序</Label>
              <Input
                id="sort-order"
                v-model="editForm.sortOrder"
                type="number"
                placeholder="排序值"
                min="0"
              />
            </div>
            <div class="flex items-center space-x-2 pt-6">
              <Checkbox
                id="is-active"
                :checked="editForm.isActive"
                @update:checked="editForm.isActive = $event"
              />
              <Label for="is-active">启用状态</Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showEditDialog = false">取消</Button>
          <Button @click="handleSave">保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  BookOpen,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Copy,
  Edit,
  MoreHorizontal,
  Plus,
  Settings,
  Shield,
  Tag,
  Trash2,
  Users,
  XCircle,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

interface DictType {
  key: string
  label: string
}

interface DictItem {
  id: number
  dictType: string
  dictKey: string
  dictValue: string
  description?: string
  sortOrder: number
  isActive: boolean
}

interface EditForm {
  dictType: string
  dictKey: string
  dictValue: string
  description: string
  sortOrder: number
  isActive: boolean
}

// 筛选条件
const filters = ref({
  dictType: 'ALL' as string,
  keyword: '',
  isActive: 'ALL' as 'ALL' | 'true' | 'false',
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'dictType',
    label: '字典类型',
    type: 'select',
    placeholder: '请选择类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '用户类型', value: 'user_type' },
      { label: '企业类型', value: 'enterprise_type' },
      { label: '风险等级', value: 'risk_level' },
      { label: '事件类型', value: 'event_type' },
      { label: '处理状态', value: 'process_status' },
    ],
  },
  {
    key: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '搜索字典键或值',
  },
  {
    key: 'isActive',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '启用', value: 'true' },
      { label: '禁用', value: 'false' },
    ],
  },
]

// 字典类型
const dictTypes = ref<DictType[]>([
  { key: 'user_type', label: '用户类型' },
  { key: 'enterprise_type', label: '企业类型' },
  { key: 'risk_level', label: '风险等级' },
  { key: 'event_type', label: '事件类型' },
  { key: 'process_status', label: '处理状态' },
  { key: 'vehicle_brand', label: '车辆品牌' },
  { key: 'region_level', label: '区域级别' },
  { key: 'approval_status', label: '审批状态' },
])

// 分页和选择状态
const currentPage = ref(1)
const pageSize = ref(10)
const selectedType = ref<string>('ALL')

// 对话框状态
const showEditDialog = ref(false)
const editMode = ref<'create' | 'edit'>('create')
const currentEditId = ref<number | null>(null)
const editForm = ref<EditForm>({
  dictType: '',
  dictKey: '',
  dictValue: '',
  description: '',
  sortOrder: 0,
  isActive: true,
})

// Mock 数据
const dictItems = ref<DictItem[]>([
  // 用户类型
  {
    id: 1,
    dictType: 'user_type',
    dictKey: 'gov_admin',
    dictValue: '政府管理员',
    description: '政府端最高权限管理员',
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 2,
    dictType: 'user_type',
    dictKey: 'gov_supervisor',
    dictValue: '政府监管员',
    description: '政府端监管人员',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 3,
    dictType: 'user_type',
    dictKey: 'ent_admin',
    dictValue: '企业管理员',
    description: '企业端管理员',
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 4,
    dictType: 'user_type',
    dictKey: 'ent_operator',
    dictValue: '企业操作员',
    description: '企业端普通操作员',
    sortOrder: 4,
    isActive: true,
  },

  // 企业类型
  {
    id: 11,
    dictType: 'enterprise_type',
    dictKey: 'vehicle_manufacturer',
    dictValue: '整车生产企业',
    description: '汽车整车制造企业',
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 12,
    dictType: 'enterprise_type',
    dictKey: 'platform_operator',
    dictValue: '平台运营商',
    description: '数据平台运营企业',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 13,
    dictType: 'enterprise_type',
    dictKey: 'tech_provider',
    dictValue: '智驾方案提供商',
    description: '智能驾驶技术提供商',
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 14,
    dictType: 'enterprise_type',
    dictKey: 'map_service',
    dictValue: '地图服务商',
    description: '地图数据服务提供商',
    sortOrder: 4,
    isActive: true,
  },
  {
    id: 15,
    dictType: 'enterprise_type',
    dictKey: 'other',
    dictValue: '其他',
    description: '其他类型企业',
    sortOrder: 5,
    isActive: true,
  },

  // 风险等级
  {
    id: 21,
    dictType: 'risk_level',
    dictKey: 'high',
    dictValue: '高风险',
    description: '需要立即处理的高等级风险',
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 22,
    dictType: 'risk_level',
    dictKey: 'medium',
    dictValue: '中风险',
    description: '需要及时关注的中等级风险',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 23,
    dictType: 'risk_level',
    dictKey: 'low',
    dictValue: '低风险',
    description: '一般性风险',
    sortOrder: 3,
    isActive: true,
  },

  // 事件类型
  {
    id: 31,
    dictType: 'event_type',
    dictKey: 'data_breach',
    dictValue: '数据泄露',
    description: '数据安全泄露事件',
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 32,
    dictType: 'event_type',
    dictKey: 'illegal_access',
    dictValue: '非法访问',
    description: '非授权访问事件',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 33,
    dictType: 'event_type',
    dictKey: 'system_fault',
    dictValue: '系统故障',
    description: '系统技术故障',
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 34,
    dictType: 'event_type',
    dictKey: 'violation',
    dictValue: '违规操作',
    description: '违反操作规程',
    sortOrder: 4,
    isActive: true,
  },

  // 处理状态
  {
    id: 41,
    dictType: 'process_status',
    dictKey: 'pending',
    dictValue: '待处理',
    description: '等待处理的状态',
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 42,
    dictType: 'process_status',
    dictKey: 'processing',
    dictValue: '处理中',
    description: '正在处理中',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 43,
    dictType: 'process_status',
    dictKey: 'completed',
    dictValue: '已完成',
    description: '处理完成',
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 44,
    dictType: 'process_status',
    dictKey: 'rejected',
    dictValue: '已驳回',
    description: '处理驳回',
    sortOrder: 4,
    isActive: false,
  },
])

// 计算属性
const filteredDictItems = computed(() => {
  let items = dictItems.value

  // 按选中的类型过滤
  if (selectedType.value !== 'ALL') {
    items = items.filter((item) => item.dictType === selectedType.value)
  }

  // 按搜索条件过滤
  if (filters.value.dictType !== 'ALL') {
    items = items.filter((item) => item.dictType === filters.value.dictType)
  }

  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    items = items.filter(
      (item) =>
        item.dictKey.toLowerCase().includes(keyword) ||
        item.dictValue.toLowerCase().includes(keyword),
    )
  }

  if (filters.value.isActive !== 'ALL') {
    const isActive = filters.value.isActive === 'true'
    items = items.filter((item) => item.isActive === isActive)
  }

  return items.sort((a, b) => {
    if (a.dictType !== b.dictType) {
      return a.dictType.localeCompare(b.dictType)
    }
    return a.sortOrder - b.sortOrder
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredDictItems.value.length / pageSize.value)),
)

const pagedDictItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredDictItems.value.slice(start, start + pageSize.value)
})

// 辅助函数
const getDictCount = (type: string) => {
  return dictItems.value.filter((item) => item.dictType === type).length
}

const getTypeLabel = (typeKey: string) => {
  const type = dictTypes.value.find((t) => t.key === typeKey)
  return type?.label || typeKey
}

const getTypeIcon = (typeKey: string) => {
  const iconMap: Record<string, any> = {
    user_type: Users,
    enterprise_type: BookOpen,
    risk_level: Shield,
    event_type: Tag,
    process_status: Settings,
    vehicle_brand: Settings,
    region_level: Settings,
    approval_status: Settings,
  }
  return iconMap[typeKey] || Settings
}

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    dictType: 'ALL',
    keyword: '',
    isActive: 'ALL',
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = ['序号', '字典类型', '字典键', '字典值', '描述', '排序', '状态']
  const rows = filteredDictItems.value.map((item, index) => [
    (index + 1).toString(),
    getTypeLabel(item.dictType),
    item.dictKey,
    item.dictValue,
    item.description || '',
    item.sortOrder.toString(),
    item.isActive ? '启用' : '禁用',
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `字典管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleCreateDict = () => {
  editMode.value = 'create'
  currentEditId.value = null
  editForm.value = {
    dictType: selectedType.value !== 'ALL' ? selectedType.value : '',
    dictKey: '',
    dictValue: '',
    description: '',
    sortOrder: 0,
    isActive: true,
  }
  showEditDialog.value = true
}

const handleEdit = (id: number) => {
  const item = dictItems.value.find((d) => d.id === id)
  if (item) {
    editMode.value = 'edit'
    currentEditId.value = id
    editForm.value = {
      dictType: item.dictType,
      dictKey: item.dictKey,
      dictValue: item.dictValue,
      description: item.description || '',
      sortOrder: item.sortOrder,
      isActive: item.isActive,
    }
    showEditDialog.value = true
  }
}

const handleDuplicate = (id: number) => {
  const item = dictItems.value.find((d) => d.id === id)
  if (item) {
    editMode.value = 'create'
    currentEditId.value = null
    editForm.value = {
      dictType: item.dictType,
      dictKey: item.dictKey + '_copy',
      dictValue: item.dictValue + '（副本）',
      description: item.description || '',
      sortOrder: item.sortOrder,
      isActive: item.isActive,
    }
    showEditDialog.value = true
  }
}

const handleSave = () => {
  if (editMode.value === 'create') {
    const newItem: DictItem = {
      id: Math.max(...dictItems.value.map((d) => d.id)) + 1,
      ...editForm.value,
    }
    dictItems.value.push(newItem)
  } else if (currentEditId.value) {
    const index = dictItems.value.findIndex((d) => d.id === currentEditId.value)
    if (index > -1) {
      Object.assign(dictItems.value[index], editForm.value)
    }
  }
  showEditDialog.value = false
}

const handleSortUp = (id: number) => {
  const index = dictItems.value.findIndex((d) => d.id === id)
  if (index > 0) {
    const temp = dictItems.value[index].sortOrder
    dictItems.value[index].sortOrder = dictItems.value[index - 1].sortOrder
    dictItems.value[index - 1].sortOrder = temp
  }
}

const handleSortDown = (id: number) => {
  const index = dictItems.value.findIndex((d) => d.id === id)
  if (index < dictItems.value.length - 1) {
    const temp = dictItems.value[index].sortOrder
    dictItems.value[index].sortOrder = dictItems.value[index + 1].sortOrder
    dictItems.value[index + 1].sortOrder = temp
  }
}

const handleEnable = (id: number) => {
  const item = dictItems.value.find((d) => d.id === id)
  if (item) {
    item.isActive = true
  }
}

const handleDisable = (id: number) => {
  const item = dictItems.value.find((d) => d.id === id)
  if (item) {
    item.isActive = false
  }
}

const handleDelete = (id: number) => {
  const index = dictItems.value.findIndex((d) => d.id === id)
  if (index > -1) {
    dictItems.value.splice(index, 1)
  }
}
</script>
