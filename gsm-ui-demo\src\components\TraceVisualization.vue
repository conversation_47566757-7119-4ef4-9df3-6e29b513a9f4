<template>
  <div class="w-full min-h-[720px] bg-background rounded-lg border relative overflow-hidden">
    <!-- 标题栏 -->
    <div class="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
      <div class="text-center">
        <h2 class="text-xl font-bold text-primary mb-1">{{ title }}</h2>
        <p class="text-sm text-muted-foreground">{{ subtitle }}</p>
      </div>
    </div>

    <!-- 数据包信息面板 -->
    <Card class="absolute top-4 left-4 w-80 z-10 backdrop-blur-sm bg-background/95">
      <CardHeader class="pb-3">
        <CardTitle class="text-sm text-primary text-center flex items-center justify-center gap-1">
          <Package class="w-4 h-4" />
          数据包溯源追踪
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-2 text-xs">
        <div class="flex justify-between">
          <span class="text-muted-foreground">数据包ID:</span>
          <span class="text-primary font-mono">{{ currentPacket.id }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">车辆VIN码:</span>
          <span class="text-orange-600 font-mono">{{ currentPacket.vin }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">企业信用代码:</span>
          <span class="text-red-600 font-mono">{{ currentPacket.creditCode }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">数据类型:</span>
          <span>{{ currentPacket.dataType }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">采集时间:</span>
          <span>{{ currentPacket.collectTime }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-foreground">数据大小:</span>
          <span>{{ currentPacket.dataSize }}</span>
        </div>
        <div class="mt-3 p-2 border rounded bg-muted/20">
          <div class="text-xs text-center mb-2 text-primary">{{ traceStatus }}</div>
          <div class="w-full bg-muted rounded-full h-2">
            <div
              class="bg-primary h-2 rounded-full transition-all duration-1000"
              :style="{ width: `${traceProgress}%` }"
            ></div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 时间轴面板 -->
    <Card
      v-if="false"
      class="absolute bottom-4 left-4 w-80 max-h-80 z-10 backdrop-blur-sm bg-background/95"
    >
      <CardHeader class="pb-3">
        <CardTitle class="text-sm text-primary text-center flex items-center justify-center gap-1">
          <Clipboard class="w-4 h-4" />
          数据包传播路径时间轴
        </CardTitle>
        <div class="text-xs text-muted-foreground text-center space-y-1">
          <div>数据包ID: {{ currentPacket.id }}</div>
          <div>车辆: {{ currentPacket.vehicleModel }} ({{ currentPacket.vin }})</div>
        </div>
      </CardHeader>
      <CardContent class="max-h-60 overflow-y-auto">
        <div class="space-y-3">
          <div
            v-for="(step, index) in traceSequence"
            :key="step.step"
            :class="[
              'flex items-start gap-3 relative transition-all duration-500',
              step.active ? 'opacity-100' : 'opacity-30',
            ]"
          >
            <div
              v-if="index < traceSequence.length - 1"
              class="absolute left-4 top-8 bottom-0 w-0.5 bg-primary/30"
            ></div>
            <div
              :class="[
                'w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold z-10',
                step.active
                  ? 'bg-primary text-primary-foreground shadow-lg'
                  : 'bg-muted text-muted-foreground',
              ]"
            >
              {{ step.step }}
            </div>
            <div class="flex-1 pt-1">
              <div class="text-sm font-medium text-primary">
                {{ step.action }} - {{ step.location }}
              </div>
              <div class="text-xs text-orange-600 font-mono">{{ step.timestamp }}</div>
              <div class="text-xs text-muted-foreground mt-1">{{ step.details }}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- D3可视化容器 -->
    <div ref="visualizationRef" class="w-full h-full min-h-[720px]"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Package, Clipboard } from 'lucide-vue-next'
import * as d3 from 'd3'

interface Props {
  title?: string
  subtitle?: string
  traceType?: 'vehicle' | 'cloud'
}

const props = withDefaults(defineProps<Props>(), {
  title: '智能网联汽车数据溯源追踪看板',
  subtitle: 'Vehicle Data Lineage & Traceability Dashboard',
  traceType: 'vehicle',
})

const emit = defineEmits<{
  saveRecord: [data: unknown]
  traceStarted: [id: string]
}>()

// 组件状态
const visualizationRef = ref<HTMLElement>()
const traceRunning = ref(false)
const traceCompleted = ref(false)
const traceProgress = ref(0)
const traceStatus = ref('准备开始溯源...')
const showTimeline = ref(false)

interface PacketInfo {
  id: string
  vin: string
  creditCode: string
  dataType: string
  collectTime: string
  dataSize: string
  vehicleModel: string
  manufacturer: string
}

interface Metrics {
  throughput: string
  latency: string
  successRate: string
  activeNodes: string
  traceDepth: string
}

// 当前数据包信息
const currentPacket = ref<PacketInfo>({
  id: 'PKT-20250821-001847',
  vin: 'LSGWB54E8KS123456',
  creditCode: '91110000MA01234567',
  dataType: '位置轨迹+驾驶行为',
  collectTime: '2025-08-21 14:18:47',
  dataSize: '2.34 MB',
  vehicleModel: '理想L9',
  manufacturer: '北京理想汽车有限公司',
})

// 实时指标
const metrics = ref<Metrics>({
  throughput: '2.3 GB/s',
  latency: '8ms',
  successRate: '99.97%',
  activeNodes: '11',
  traceDepth: '5层',
})

// 溯源序列（按类型动态构建）
interface TraceStep {
  step: number
  timestamp: string
  node: string // 对应 links 的 source/target（如 stage-0, stage-1）
  action: string
  location: string
  details?: string
  active?: boolean
}
const traceSequence = ref<TraceStep[]>([])

// D3可视化相关变量
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
let nodeGroup: d3.Selection<SVGGElement, unknown, null, undefined>
let linkGroup: d3.Selection<SVGGElement, unknown, null, undefined>
interface StageNode {
  id: string
  name: string
  x: number
  y: number
  stage: string
  type: 'main' | 'backup' | 'security' | 'normal'
}
interface StageLink {
  source: string
  target: string
  type: 'main' | 'backup' | 'security' | 'normal'
  strength: number
}
let nodeElements: d3.Selection<SVGGElement, StageNode, SVGGElement, unknown>
let linkElements: d3.Selection<SVGPathElement, StageLink, SVGGElement, unknown>

// 数据血缘风格：按阶段分列布局的节点和连线
let nodes: StageNode[] = []
let links: StageLink[] = []

// 根据溯源类型构建不同的阶段布局
const buildStageLayout = () => {
  const containerWidth = 1200
  const containerHeight = 600
  const stageCount = props.traceType === 'cloud' ? 5 : 4
  const stageWidth = containerWidth / (stageCount + 1)

  if (props.traceType === 'cloud') {
    // 云端七阶段：收集/存储/传输/加工/提供/公开/销毁
    const stages = ['收集', '存储', '传输', '加工', '提供', '公开', '销毁']
    const stageCount = stages.length
    const stageWidthDyn = containerWidth / (stageCount + 1)

    nodes = stages.map((stage, i) => ({
      id: `stage-${i}`,
      name: stage,
      label: `${stage}阶段`,
      x: (i + 1) * stageWidthDyn,
      y: containerHeight / 2,
      stage,
      type: 'main',
    }))

    // 主路径连线
    links = stages.slice(0, -1).map((_, i) => ({
      source: `stage-${i}`,
      target: `stage-${i + 1}`,
      type: 'main',
      strength: 0.9,
    }))
  } else {
    // 车端四阶段：采集/预处理/传输/存储
    const stages = ['数据采集', '边缘预处理', '云端传输', '数据存储']
    nodes = stages.map((stage, i) => ({
      id: `stage-${i}`,
      name: stage,
      label: `${stage}节点`,
      x: (i + 1) * stageWidth,
      y: containerHeight / 2,
      stage: stage,
      type: 'main',
    }))

    // 主路径连线
    links = stages.slice(0, -1).map((_, i) => ({
      source: `stage-${i}`,
      target: `stage-${i + 1}`,
      type: 'main',
      strength: 0.9,
    }))
  }
}

// 根据阶段构建溯源序列（与列对齐）
const buildTraceSequenceFromStages = () => {
  const steps: TraceStep[] = nodes.map((n, idx) => {
    if (props.traceType === 'cloud') {
      const actionMap = [
        '接收数据',
        '入库存储',
        '内部传输',
        '加工处理',
        '对内/对外提供',
        '公开发布',
        '数据销毁',
      ]
      const locMap = [
        '汇聚入口',
        '对象存储',
        '内网通道',
        '计算集群',
        'API网关',
        '开放平台',
        '合规审计',
      ]
      return {
        step: idx + 1,
        timestamp: new Date().toISOString(),
        node: n.id,
        action: actionMap[idx] || '处理',
        location: locMap[idx] || '云端',
        active: false,
      }
    } else {
      const actionMap = ['数据采集', '边缘预处理', '上行传输', '入库存储']
      const locMap = ['车载终端', '边缘网关', '运营商/专线', '云端存储']
      return {
        step: idx + 1,
        timestamp: new Date().toISOString(),
        node: n.id,
        action: actionMap[idx] || '处理',
        location: locMap[idx] || '云端',
        active: false,
      }
    }
  })
  traceSequence.value = steps
}

// 初始化D3可视化
const initD3Visualization = async () => {
  await nextTick()
  if (!visualizationRef.value) return

  // 构建阶段布局
  buildStageLayout()
  // 根据阶段构建与列对齐的溯源序列

  buildTraceSequenceFromStages()

  // 容器尺寸 + 画布尺寸（视口小也能完整显示内容）
  const containerWidth = Math.max(visualizationRef.value.clientWidth || 0, 600)
  const containerHeight = Math.max(visualizationRef.value.clientHeight || 0, 400)
  console.debug('[TraceVisualization] container size:', { containerWidth, containerHeight })
  const artboardWidth = 1200
  const artboardHeight = 650

  // 清空容器
  d3.select(visualizationRef.value).selectAll('*').remove()

  // 创建SVG（使用 viewBox 适配不同容器尺寸）
  svg = d3
    .select(visualizationRef.value)
    .append('svg')
    .attr('width', containerWidth)
    .attr('height', containerHeight)
    .attr('viewBox', `0 0 ${artboardWidth} ${artboardHeight}`)
    .attr('preserveAspectRatio', 'xMidYMid meet')
    .style(
      'background',
      'radial-gradient(ellipse at center, hsl(var(--background)) 0%, hsl(var(--muted)) 100%)',
    )

  // 添加缩放功能
  const zoom = d3
    .zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.5, 3])
    .on('zoom', (event) => {
      const { transform } = event
      nodeGroup.attr('transform', transform)
      linkGroup.attr('transform', transform)
    })

  svg.call(zoom)

  // 创建渐变和滤镜
  const defs = svg.append('defs')

  // 发光滤镜
  const glowFilter = defs
    .append('filter')
    .attr('id', 'glow')
    .attr('x', '-50%')
    .attr('y', '-50%')
    .attr('width', '200%')
    .attr('height', '200%')

  glowFilter.append('feGaussianBlur').attr('stdDeviation', '4').attr('result', 'coloredBlur')

  const feMerge = glowFilter.append('feMerge')
  feMerge.append('feMergeNode').attr('in', 'coloredBlur')
  feMerge.append('feMergeNode').attr('in', 'SourceGraphic')

  // 箭头标记（统一样式与深浅）
  const arrowMarker = defs
    .append('marker')
    .attr('id', 'arrowhead')
    .attr('viewBox', '0 -5 10 10')
    .attr('refX', 8)
    .attr('refY', 0)
    .attr('markerWidth', 6)
    .attr('markerHeight', 6)
    .attr('orient', 'auto')

  arrowMarker
    .append('path')
    .attr('d', 'M0,-5L10,0L0,5')
    .attr('fill', 'hsl(var(--muted-foreground))')
    .attr('fill-opacity', 0.6)

  // 阶段列头与参考竖线（数据血缘风格）
  const headerGroup = svg.append('g').attr('class', 'headers')
  const uniqueStages = nodes.map((n) => ({ x: n.x, name: n.name }))
  interface HeaderDatum {
    x: number
    name: string
  }
  headerGroup
    .selectAll<SVGGElement, HeaderDatum>('.stage-header')
    .data(uniqueStages as HeaderDatum[])
    .enter()
    .append('g')
    .attr('class', 'stage-header')
    .each(function (d) {
      const g = d3.select(this)
      // 竖向参考线
      g.append('line')
        .attr('x1', d.x)
        .attr('y1', 100)
        .attr('x2', d.x)
        .attr('y2', 600)
        .attr('stroke', 'hsl(var(--muted-foreground))')
        .attr('stroke-opacity', 0.12)
        .attr('stroke-dasharray', '4 4')

      // 列头文字
      g.append('text')
        .attr('x', d.x)
        .attr('y', 70)
        .attr('text-anchor', 'middle')
        .attr('fill', 'hsl(var(--muted-foreground))')
        .attr('font-size', '12px')
        .attr('font-weight', '600')
        .text(d.name)
    })

  // 创建连线组和节点组
  linkGroup = svg.append('g').attr('class', 'links')
  nodeGroup = svg.append('g').attr('class', 'nodes')

  // 绘制连线
  linkElements = linkGroup
    .selectAll('.link-path')
    .data(links)
    .enter()
    .append('path')
    .attr('class', 'link-path')
    .attr('fill', 'none')
    .attr('stroke', (d) => {
      switch (d.type) {
        case 'main':
          return 'hsl(var(--primary))'
        case 'security':
          return 'hsl(var(--destructive))'
        case 'backup':
          return 'hsl(var(--secondary))'
        default:
          return 'hsl(var(--muted-foreground))'
      }
    })
    .attr('stroke-width', (d) => 2 + d.strength * 2)
    .attr('stroke-opacity', 0.6)
    .attr('marker-end', 'url(#arrowhead)')
    .attr('d', (d: StageLink) => {
      const source = nodes.find((n) => n.id === d.source)
      const target = nodes.find((n) => n.id === d.target)
      const sx = source?.x ?? 0
      const sy = source?.y ?? 0
      const tx = target?.x ?? 0
      const ty = target?.y ?? 0
      const midX = (sx + tx) / 2
      return `M ${sx} ${sy} H ${midX} V ${ty} H ${tx}`
    })

  // 绘制节点
  nodeElements = nodeGroup
    .selectAll('.node-group')
    .data(nodes)
    .enter()
    .append('g')
    .attr('class', 'node-group')
    .attr('transform', (d) => `translate(${d.x}, ${d.y})`)
    .style('cursor', 'pointer')

  // 简约流程图风格：矩形节点 + 居中文字
  nodeElements
    .append('rect')
    .attr('class', 'node-rect')
    .attr('x', -70)
    .attr('y', -22)
    .attr('rx', 6)
    .attr('ry', 6)
    .attr('width', 140)
    .attr('height', 44)
    .attr('fill', 'hsl(var(--background))')
    .attr('stroke', 'hsl(var(--foreground))')
    .attr('stroke-opacity', 0.2)

  nodeElements
    .append('text')
    .attr('class', 'node-text')
    .attr('text-anchor', 'middle')
    .attr('dominant-baseline', 'middle')
    .attr('fill', 'hsl(var(--foreground))')
    .attr('font-size', '12px')
    .attr('font-weight', '600')
    .text((d) => d.name)

  // 添加工具提示（悬浮显示，跟随鼠标，不冻结位置）
  nodeElements.on('mouseover', function (event, d: StageNode) {
    const tooltip = d3
      .select('body')
      .append('div')
      .attr('class', 'tooltip')
      .style('position', 'absolute')
      .style('background', 'hsl(var(--popover))')
      .style('border', '1px solid hsl(var(--border))')
      .style('border-radius', '6px')
      .style('padding', '8px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', '1000')
      .style('opacity', 0).html(`
          <strong>${d.name}</strong><br/>
          阶段: ${d.stage}<br/>
          类型: ${d.type}
        `)

    const move = (e: MouseEvent) => {
      tooltip.style('left', e.pageX + 10 + 'px').style('top', e.pageY - 10 + 'px')
    }

    move(event as unknown as MouseEvent)

    d3.select(this).on('mousemove', move)

    tooltip.transition().duration(200).style('opacity', 1)

    d3.select(this).on('mouseout', function () {
      d3.select(this).on('mousemove', null)
      tooltip.transition().duration(200).style('opacity', 0).remove()
    })
  })
}

// 开始溯源
const startTrace = (regeneratePacket: boolean = true) => {
  if (traceRunning.value) return

  traceRunning.value = true
  traceProgress.value = 0
  traceCompleted.value = false
  showTimeline.value = false

  // 重置所有步骤状态
  traceSequence.value.forEach((step) => (step.active = false))

  // 清除现有高亮
  if (nodeElements) nodeElements.classed('trace-active', false)
  if (linkElements) linkElements.classed('trace-active', false)

  // 生成新的数据包数据（回放时可跳过）
  if (regeneratePacket) {
    generateNewPacketData()
  } else {
    // 回放时也对外通知一次当前会话ID，便于父组件展示
    emit('traceStarted', currentPacket.value.id)
  }

  // 启动进度条
  const progressInterval = setInterval(() => {
    if (traceProgress.value < 100) {
      traceProgress.value += 100 / traceSequence.value.length
    } else {
      clearInterval(progressInterval)
    }
  }, 800)

  // 分步高亮追踪
  traceSequence.value.forEach((step, index) => {
    setTimeout(() => {
      // 激活时间轴步骤
      step.active = true

      // 高亮节点
      if (nodeElements) {
        nodeElements
          .filter((d: StageNode) => d.id === step.node)
          .classed('trace-active', true)
          .select('.node-rect')
          .style('stroke', 'hsl(var(--destructive))')
          .style('stroke-width', '4px')
          .style('filter', 'drop-shadow(0 0 15px hsl(var(--destructive)))')
      }

      // 高亮连线
      if (index > 0 && linkElements) {
        const prevStep = traceSequence.value[index - 1]
        linkElements
          .filter((d: StageLink) => d.source === prevStep.node && d.target === step.node)
          .classed('trace-active', true)
          .style('stroke', 'hsl(var(--destructive))')
          .style('stroke-width', '4px')
          .style('opacity', 1)
      }

      // 更新状态
      traceStatus.value = `Step ${step.step}: ${step.action} - ${step.location}`

      // 最后一步完成
      if (index === traceSequence.value.length - 1) {
        setTimeout(() => {
          traceRunning.value = false
          traceCompleted.value = true
          traceStatus.value = '数据溯源追踪完成'
          traceProgress.value = 100
        }, 1000)
      }
    }, index * 800)
  })

  // 清除高亮
  setTimeout(
    () => {
      if (nodeElements) {
        nodeElements
          .classed('trace-active', false)
          .select('.node-rect')
          .style('stroke', null)
          .style('stroke-width', null)
          .style('filter', null)
      }
      if (linkElements) {
        linkElements
          .classed('trace-active', false)
          .style('stroke', null)
          .style('stroke-width', null)
          .style('opacity', null)
      }
    },
    traceSequence.value.length * 800 + 3000,
  )
}

// 高亮路径
const highlightPath = () => {
  if (traceRunning.value) return

  // 高亮主要路径
  if (linkElements) {
    linkElements
      .filter((d: StageLink) => d.type === 'main')
      .style('stroke', 'hsl(var(--destructive))')
      .style('stroke-width', '3px')
      .style('opacity', 0.9)
      .style('filter', 'drop-shadow(0 0 5px hsl(var(--destructive)))')

    // 4秒后恢复
    setTimeout(() => {
      linkElements
        .style('stroke', null)
        .style('stroke-width', null)
        .style('opacity', null)
        .style('filter', null)
    }, 4000)
  }
}

// 生成新数据包数据
const generateNewPacketData = () => {
  const now = new Date()
  const packetId = `PKT-${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}-${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`

  currentPacket.value = {
    ...currentPacket.value,
    id: packetId,
    collectTime: now.toLocaleString('zh-CN'),
    dataSize: (Math.random() * 5 + 0.5).toFixed(2) + ' MB',
  }

  // 将本次溯源会话ID通知外部
  emit('traceStarted', currentPacket.value.id)

  // 更新时间轴时间戳
  traceSequence.value.forEach((step, index) => {
    step.timestamp = new Date(now.getTime() + index * 333).toISOString().slice(0, -1)
  })
}

// 保存溯源记录
const saveRecord = () => {
  const recordData = {
    packetInfo: currentPacket.value,
    traceSequence: traceSequence.value.map((step) => ({
      ...step,
      active: undefined, // 移除UI状态
    })),
    timestamp: new Date().toISOString(),
    traceType: props.traceType,
  }

  emit('saveRecord', recordData)

  // 简单反馈
  traceStatus.value = '溯源记录已保存'
  setTimeout(() => {
    traceStatus.value = '准备开始溯源...'
    traceCompleted.value = false
    showTimeline.value = false
  }, 2000)
}

// 记录回放（将保存的溯源记录加载到画布并重放）
const replayRecord = (recordData: { packetInfo: PacketInfo; traceSequence: TraceStep[] }) => {
  try {
    traceRunning.value = false
    traceCompleted.value = false
    showTimeline.value = true

    // 替换当前数据包与步骤
    currentPacket.value = {
      ...currentPacket.value,
      ...recordData.packetInfo,
    }
    traceSequence.value = recordData.traceSequence.map((step: TraceStep) => ({
      ...step,
      active: false,
    }))

    // 重新渲染并开始溯源
    initD3Visualization()
    setTimeout(() => startTrace(false), 300)
  } catch (e) {
    console.error('replayRecord failed:', e)
  }
}

// 对外暴露方法
defineExpose({ saveRecord, startTrace, highlightPath, replayRecord })

// 更新实时指标
const updateMetrics = () => {
  metrics.value = {
    throughput: (2.1 + Math.random() * 0.4).toFixed(1) + ' GB/s',
    latency: Math.floor(6 + Math.random() * 4) + 'ms',
    successRate: (99.95 + Math.random() * 0.04).toFixed(2) + '%',
    activeNodes: Math.floor(10 + Math.random() * 3).toString(),
    traceDepth: Math.floor(4 + Math.random() * 3) + '层',
  }
}

// 生命周期
let metricsInterval: ReturnType<typeof setInterval>

onMounted(() => {
  initD3Visualization()
  metricsInterval = setInterval(updateMetrics, 2000)

  // 初始化完成后自动开启溯源并高亮主路径
  setTimeout(() => {
    startTrace(true)
    setTimeout(() => highlightPath(), 0)
  }, 400)

  // 自适应容器尺寸变化（初次 0x0 时也能补渲染）
  let ro: ResizeObserver | null = null
  if (typeof ResizeObserver !== 'undefined' && visualizationRef.value) {
    ro = new ResizeObserver(() => {
      initD3Visualization()
    })
    ro.observe(visualizationRef.value)
  }

  // 窗口大小变化时重新初始化
  const handleResize = () => {
    initD3Visualization()
  }
  window.addEventListener('resize', handleResize)

  onUnmounted(() => {
    clearInterval(metricsInterval)
    window.removeEventListener('resize', handleResize)
    if (ro && visualizationRef.value) ro.disconnect()
  })
})
</script>

<style scoped>
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

:deep(.node-group:hover) {
  filter: brightness(1.3);
}

:deep(.link-path) {
  transition: all 0.3s ease;
}

:deep(.link-path:hover) {
  opacity: 1 !important;
  stroke-width: 3px !important;
}

:deep(.trace-active) {
  animation: traceGlow 1.5s ease-in-out infinite alternate;
}

@keyframes traceGlow {
  0% {
    filter: drop-shadow(0 0 15px currentColor);
  }
  100% {
    filter: drop-shadow(0 0 25px currentColor);
  }
}
</style>
