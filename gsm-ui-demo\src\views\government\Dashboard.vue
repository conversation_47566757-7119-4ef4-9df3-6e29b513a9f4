<template>
  <div class="government-dashboard axure-fullscreen">
    <h1 class="sr-only">{{ $route.meta.title }}</h1>
    <!-- 使用Axure原型作为主要内容 -->
    <AxureGovernmentDashboard />
  </div>
</template>

<script setup lang="ts">
import AxureGovernmentDashboard from '@/components/axure/AxureGovernmentDashboard.vue'

// 显式设置组件名称，满足多词命名规范
defineOptions({ name: 'GovernmentDashboardPage' })

// 在组件挂载时添加特殊类到父容器
import { onMounted, onUnmounted } from 'vue'

onMounted(() => {
  // 找到父容器并添加类以移除padding和gap
  const container = document.querySelector('.internal-page-wrapper .flex.flex-col')
  if (container) {
    container.classList.add('axure-dashboard-container')
  }
})

onUnmounted(() => {
  // 组件卸载时恢复原样
  const container = document.querySelector('.axure-dashboard-container')
  if (container) {
    container.classList.remove('axure-dashboard-container')
  }
})
</script>

<style>
/* 全局样式，仅在有axure-dashboard-container类时生效 */
.axure-dashboard-container {
  padding: 0 !important;
  gap: 0 !important;
}

/* 确保在此页面下的内容也无边距 */
.axure-dashboard-container > .government-dashboard {
  margin: 0 !important;
  padding: 0 !important;
}
</style>
