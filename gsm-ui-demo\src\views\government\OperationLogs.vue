<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">查询和管理系统操作日志，支持溯源定责，日志上链存证</p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="handleAnalyzeLog" variant="outline" class="flex items-center gap-2">
          <BarChart3 class="w-4 h-4" />
          日志分析
        </Button>
        <Button @click="handleArchive" variant="outline" class="flex items-center gap-2">
          <Archive class="w-4 h-4" />
          日志归档
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">今日操作</p>
              <p class="text-2xl font-bold">{{ stats.today }}</p>
            </div>
            <Calendar class="w-8 h-8 text-primary" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">本周操作</p>
              <p class="text-2xl font-bold">{{ stats.week }}</p>
            </div>
            <TrendingUp class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">异常操作</p>
              <p class="text-2xl font-bold text-red-600">{{ stats.abnormal }}</p>
            </div>
            <AlertTriangle class="w-8 h-8 text-red-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">活跃用户</p>
              <p class="text-2xl font-bold text-blue-600">{{ stats.activeUsers }}</p>
            </div>
            <Users class="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">上链存证</p>
              <p class="text-2xl font-bold text-green-600">{{ stats.onChain }}</p>
            </div>
            <Shield class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 操作日志列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>操作日志</span>
          <Badge variant="outline"> 共 {{ filteredLogs.length }} 条记录 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <!-- 筛选条件 - 紧贴表格 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>操作时间</TableHead>
                <TableHead>操作用户</TableHead>
                <TableHead>操作类型</TableHead>
                <TableHead>操作描述</TableHead>
                <TableHead>操作对象</TableHead>
                <TableHead>客户端IP</TableHead>
                <TableHead>状态</TableHead>
                <TableHead class="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedLogs.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无日志数据
                </TableCell>
              </TableRow>
              <TableRow v-for="(log, index) in pagedLogs" :key="log.id" class="hover:bg-muted/40">
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ log.actionTime }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component :is="getUserIcon(log.userType)" class="w-4 h-4 text-primary" />
                    <div>
                      <div class="font-medium text-sm">{{ log.userName }}</div>
                      <div class="text-xs text-muted-foreground">{{ log.userType }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getActionTypeVariant(log.actionType)">{{
                    log.actionType
                  }}</Badge>
                </TableCell>
                <TableCell>
                  <div class="max-w-[300px] text-sm">
                    {{ log.actionDescription }}
                  </div>
                </TableCell>
                <TableCell>
                  <div class="text-sm">
                    <div class="font-medium">{{ log.targetResource }}</div>
                    <div class="text-xs text-muted-foreground font-mono">{{ log.targetId }}</div>
                  </div>
                </TableCell>
                <TableCell class="font-mono text-xs">{{ log.clientIp }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <Badge :variant="log.isOnChain ? 'default' : 'outline'">
                      {{ log.isOnChain ? '已上链' : '待上链' }}
                    </Badge>
                    <component
                      :is="log.isOnChain ? Check : Clock"
                      :class="`w-3 h-3 ${log.isOnChain ? 'text-green-500' : 'text-orange-500'}`"
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleViewDetail(log.id)">
                        <Eye class="w-4 h-4 mr-2" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleViewTrace(log.id)">
                        <Route class="w-4 h-4 mr-2" />
                        操作链路
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="!log.isOnChain"
                        @click="handleUploadChain(log.id)"
                        class="text-blue-600"
                      >
                        <Upload class="w-4 h-4 mr-2" />
                        上链存证
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleExportLog(log.id)">
                        <Download class="w-4 h-4 mr-2" />
                        导出日志
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredLogs.length) }} 条， 共
            {{ filteredLogs.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 日志详情对话框 -->
    <Sheet :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <SheetContent side="right" class="w-[50vw] min-w-[520px] max-w-[900px]">
        <SheetHeader>
          <SheetTitle>操作日志详情</SheetTitle>
          <SheetDescription> 查看详细的操作日志信息和系统记录 </SheetDescription>
        </SheetHeader>

        <div v-if="currentLog" class="space-y-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-base font-semibold text-muted-foreground">操作时间</Label>
              <p class="text-sm">{{ currentLog.actionTime }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">操作用户</Label>
              <p class="text-sm">{{ currentLog.userName }} ({{ currentLog.userType }})</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">操作类型</Label>
              <Badge :variant="getActionTypeVariant(currentLog.actionType)">{{
                currentLog.actionType
              }}</Badge>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">客户端IP</Label>
              <p class="text-sm font-mono">{{ currentLog.clientIp }}</p>
            </div>
            <div class="col-span-2">
              <Label class="text-base font-semibold text-muted-foreground">操作描述</Label>
              <p class="text-sm">{{ currentLog.actionDescription }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">操作对象</Label>
              <p class="text-sm">{{ currentLog.targetResource }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">对象ID</Label>
              <p class="text-sm font-mono">{{ currentLog.targetId }}</p>
            </div>
          </div>

          <!-- 上链信息 -->
          <div
            v-if="currentLog.isOnChain"
            class="p-4 border rounded-md bg-green-50 dark:bg-green-900/20"
          >
            <div class="flex items-center gap-2 mb-2">
              <Shield class="w-4 h-4 text-green-600" />
              <span class="font-medium text-green-600">区块链存证信息</span>
            </div>
            <div class="space-y-2">
              <div>
                <Label class="text-xs font-medium text-muted-foreground">区块哈希</Label>
                <p class="text-xs font-mono">0x1234567890abcdef1234567890abcdef12345678</p>
              </div>
              <div>
                <Label class="text-xs font-medium text-muted-foreground">交易哈希</Label>
                <p class="text-xs font-mono">0xabcdef1234567890abcdef1234567890abcdef12</p>
              </div>
              <div>
                <Label class="text-xs font-medium text-muted-foreground">上链时间</Label>
                <p class="text-xs">2025-08-25 14:32:15</p>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showDetailDialog = false">关闭</Button>
          <Button
            v-if="currentLog && !currentLog.isOnChain"
            @click="handleUploadChain(currentLog.id)"
            variant="default"
          >
            <Upload class="w-4 h-4 mr-2" />
            上链存证
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>

    <!-- 操作链路对话框 -->
    <Sheet :open="showTraceDialog" @update:open="showTraceDialog = $event">
      <SheetContent side="right" class="w-[66vw] min-w-[640px] max-w-[1100px] max-h-[100vh] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>操作链路追踪</SheetTitle>
          <SheetDescription> 显示与该操作相关的完整操作链路和依赖关系 </SheetDescription>
        </SheetHeader>

        <div class="py-4">
          <div class="text-sm text-muted-foreground text-center py-8">
            操作链路图表将在此处显示，展示完整的操作时序和依赖关系
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showTraceDialog = false">关闭</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  AlertTriangle,
  Archive,
  BarChart3,
  Calendar,
  Check,
  Clock,
  Download,
  Eye,
  MoreHorizontal,
  Route,
  Shield,
  TrendingUp,
  Upload,
  User,
  Users,
  Building2,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type ActionType = '新增' | '修改' | '删除' | '登录' | '登出' | '查询' | '导出' | '审批'
type UserType = '政府管理员' | '政府监管员' | '企业管理员' | '企业操作员'

interface OperationLog {
  id: number
  actionTime: string
  userName: string
  userType: UserType
  actionType: ActionType
  actionDescription: string
  targetResource: string
  targetId: string
  clientIp: string
  isOnChain: boolean
}

// 统计数据
const stats = ref({
  today: 1247,
  week: 8965,
  abnormal: 23,
  activeUsers: 156,
  onChain: 7842,
})

// 筛选条件
const filters = ref({
  userName: '',
  userType: 'ALL' as 'ALL' | UserType,
  actionType: 'ALL' as 'ALL' | ActionType,
  targetResource: '',
  timeRange: null as [Date, Date] | null,
  isOnChain: 'ALL' as 'ALL' | 'true' | 'false',
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'userName',
    label: '操作用户',
    type: 'input',
    placeholder: '请输入用户名',
  },
  {
    key: 'userType',
    label: '用户类型',
    type: 'select',
    placeholder: '请选择用户类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '政府管理员', value: '政府管理员' },
      { label: '政府监管员', value: '政府监管员' },
      { label: '企业管理员', value: '企业管理员' },
      { label: '企业操作员', value: '企业操作员' },
    ],
  },
  {
    key: 'actionType',
    label: '操作类型',
    type: 'select',
    placeholder: '请选择操作类型',
    options: [
      { label: '全部操作', value: 'ALL' },
      { label: '新增', value: '新增' },
      { label: '修改', value: '修改' },
      { label: '删除', value: '删除' },
      { label: '登录', value: '登录' },
      { label: '登出', value: '登出' },
      { label: '查询', value: '查询' },
      { label: '导出', value: '导出' },
      { label: '审批', value: '审批' },
    ],
  },
  {
    key: 'targetResource',
    label: '操作对象',
    type: 'input',
    placeholder: '请输入操作对象',
  },
  {
    key: 'timeRange',
    label: '操作时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
  {
    key: 'isOnChain',
    label: '上链状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '已上链', value: 'true' },
      { label: '待上链', value: 'false' },
    ],
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(15)

// 对话框状态
const showDetailDialog = ref(false)
const showTraceDialog = ref(false)
const currentLog = ref<OperationLog | null>(null)

// Mock 数据
const logs = ref<OperationLog[]>([
  {
    id: 1,
    actionTime: '2025-08-25 14:30:25',
    userName: '李管理员',
    userType: '政府管理员',
    actionType: '审批',
    actionDescription: '审批通过企业"浙江极氪智能科技有限公司"的注册申请',
    targetResource: '企业注册申请',
    targetId: 'ENT-2025-001',
    clientIp: '*************',
    isOnChain: true,
  },
  {
    id: 2,
    actionTime: '2025-08-25 14:25:10',
    userName: '张总经理',
    userType: '企业管理员',
    actionType: '修改',
    actionDescription: '修改了企业基本信息中的联系方式',
    targetResource: '企业基本信息',
    targetId: 'ENT-2025-003',
    clientIp: '*************',
    isOnChain: true,
  },
  {
    id: 3,
    actionTime: '2025-08-25 14:20:33',
    userName: '王监管员',
    userType: '政府监管员',
    actionType: '查询',
    actionDescription: '查询了车端风险管理页面的统计数据',
    targetResource: '车端风险统计',
    targetId: 'RISK-STATS-VEHICLE',
    clientIp: '**********',
    isOnChain: false,
  },
  {
    id: 4,
    actionTime: '2025-08-25 14:15:47',
    userName: '刘操作员',
    userType: '企业操作员',
    actionType: '新增',
    actionDescription: '添加了新的车辆信息记录',
    targetResource: '车辆信息',
    targetId: 'VEH-2025-156',
    clientIp: '*************',
    isOnChain: true,
  },
  {
    id: 5,
    actionTime: '2025-08-25 14:10:15',
    userName: '赵审核员',
    userType: '政府监管员',
    actionType: '导出',
    actionDescription: '导出了用户管理页面的用户列表数据',
    targetResource: '用户列表',
    targetId: 'USER-LIST-EXPORT',
    clientIp: '**********',
    isOnChain: false,
  },
  {
    id: 6,
    actionTime: '2025-08-25 14:05:28',
    userName: '陈创始人',
    userType: '企业管理员',
    actionType: '登录',
    actionDescription: '用户成功登录系统',
    targetResource: '用户会话',
    targetId: 'SESSION-2025-8899',
    clientIp: '*************',
    isOnChain: true,
  },
  {
    id: 7,
    actionTime: '2025-08-25 13:58:42',
    userName: '李管理员',
    userType: '政府管理员',
    actionType: '删除',
    actionDescription: '删除了已注销的用户账户',
    targetResource: '用户账户',
    targetId: 'U-2025-099',
    clientIp: '*************',
    isOnChain: true,
  },
  {
    id: 8,
    actionTime: '2025-08-25 13:50:16',
    userName: '王监管员',
    userType: '政府监管员',
    actionType: '修改',
    actionDescription: '更新了风险规则"位置数据精度异常检测"的配置参数',
    targetResource: '风险规则',
    targetId: 'RULE-001',
    clientIp: '**********',
    isOnChain: false,
  },
])

// 计算属性
const filteredLogs = computed(() => {
  return logs.value.filter((log) => {
    if (filters.value.userName && !log.userName.includes(filters.value.userName)) {
      return false
    }
    if (filters.value.userType !== 'ALL' && log.userType !== filters.value.userType) {
      return false
    }
    if (filters.value.actionType !== 'ALL' && log.actionType !== filters.value.actionType) {
      return false
    }
    if (
      filters.value.targetResource &&
      !log.targetResource.includes(filters.value.targetResource)
    ) {
      return false
    }
    if (filters.value.isOnChain !== 'ALL') {
      const isOnChain = filters.value.isOnChain === 'true'
      if (log.isOnChain !== isOnChain) return false
    }
    if (filters.value.timeRange && filters.value.timeRange[0] && filters.value.timeRange[1]) {
      const startDate = new Date(
        filters.value.timeRange[0].getFullYear(),
        filters.value.timeRange[0].getMonth(),
        filters.value.timeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.value.timeRange[1].getFullYear(),
        filters.value.timeRange[1].getMonth(),
        filters.value.timeRange[1].getDate(),
        23,
        59,
        59,
      )
      const actionDate = new Date(log.actionTime.replace(/-/g, '/'))
      if (actionDate < startDate || actionDate > endDate) return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredLogs.value.length / pageSize.value)),
)

const pagedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredLogs.value.slice(start, start + pageSize.value)
})

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    userName: '',
    userType: 'ALL',
    actionType: 'ALL',
    targetResource: '',
    timeRange: null,
    isOnChain: 'ALL',
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = [
    '序号',
    '操作时间',
    '操作用户',
    '用户类型',
    '操作类型',
    '操作描述',
    '操作对象',
    '对象ID',
    '客户端IP',
    '上链状态',
  ]
  const rows = filteredLogs.value.map((log, index) => [
    (index + 1).toString(),
    log.actionTime,
    log.userName,
    log.userType,
    log.actionType,
    log.actionDescription,
    log.targetResource,
    log.targetId,
    log.clientIp,
    log.isOnChain ? '已上链' : '待上链',
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `操作日志_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleAnalyzeLog = () => {
  console.log('分析操作日志')
}

const handleArchive = () => {
  console.log('归档历史日志')
}

const handleViewDetail = (id: number) => {
  const log = logs.value.find((l) => l.id === id)
  if (log) {
    currentLog.value = log
    showDetailDialog.value = true
  }
}

const handleViewTrace = (id: number) => {
  const log = logs.value.find((l) => l.id === id)
  if (log) {
    currentLog.value = log
    showTraceDialog.value = true
  }
}

const handleUploadChain = (id: number) => {
  const log = logs.value.find((l) => l.id === id)
  if (log) {
    log.isOnChain = true
    console.log('上链存证:', id)
  }
  showDetailDialog.value = false
}

const handleExportLog = (id: number) => {
  console.log('导出单条日志:', id)
}

// 样式函数
import type { BadgeVariants } from '@/components/ui/badge'
const getActionTypeVariant = (type: ActionType): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<ActionType, NonNullable<BadgeVariants['variant']>> = {
    新增: 'default',
    修改: 'secondary',
    删除: 'destructive',
    登录: 'outline',
    登出: 'outline',
    查询: 'outline',
    导出: 'secondary',
    审批: 'default',
  }
  return variants[type] ?? 'outline'
}

const getUserIcon = (userType: UserType) => {
  return userType.includes('政府') ? Shield : Building2
}
</script>
