# 地理信息安全监测平台-系统前端原型页面开发任务明细 v2.0

## 0. 全局设计与开发要求

依据《修订-地理信息安全监测平台原型界面待优化问题汇总\_20250801.xls》整理的全局性要求如下：

- 界面风格与排版
  - 政府端与企业端的字体大小、排版、风格需严格参照统一示例标准执行，保持一致。
- 色彩搭配
  - 图表与界面配色建议采用暗色彩色色系（如暗蓝、暗红、暗绿色等等配合协调），避免亮艳色；柱状图等不使用渐变色；必要时可使用 AI 辅助配色。
- 对话框与弹窗
  - 所有弹窗的字段信息必须完整，不留空；弹窗的关闭按钮（×）必须正常工作。
- 交互体验
  - 鼠标悬停提示应尽量覆盖所有图标与图表数据点，显示明确的数值或信息。
- 组件使用
  - 严格使用 shadcn-vue 官方组件库进行开发，不得自制或简化替代。
- 操作确认
  - 删除、发布、提交审批等关键操作必须有二次确认提示框，包含 确定/取消/关闭 按钮，采用通用样式。
- 导出功能
  - 导出 Excel/报告等相关界面与弹窗采用统一样式。
- 系统结构
  - 顶部导航用于区分一级模块。
  - 左侧功能栏区分二级/三级子模块（随顶部导航切换动态变化）。
  - 导航栏与功能栏可见性通过账户权限管理配置。

## 1. 原型页面开发任务明细

以下各子章节采用统一格式组织：基础信息、描述/功能描述、核心字段与显示内容、设计与交互要求（如适用）、任务清单。

说明：

- 路由以“路由接入: /path”形式列出。
- 页面骨架统一采用“顶部头部组件 + shadcn-vue 布局容器/侧边栏 + 页面主内容”的模式（若需要侧边导航）。
- 组件开发优先使用 shadcn-vue 官方组件。
- 类型/状态建议定义 TS 类型，并在必要时使用 Pinia 进行状态管理。
- 交互联动包括与菜单/面包屑/标签页、地图或图表联动、导出与弹窗交互等。
- 基本测试覆盖渲染正确性与常用交互。

---

### P-001 首页门户落地页

- 模块: 首页门户落地 > 门户落地页
- 适用端: 通用
- 优先级-完成度: 高-已开发
- 描述: 展示平台的基本信息、功能介绍、运营情况、新闻动态与政策法规动态。

任务清单

- [ ] 路由接入: /平台门户首页/
- [ ] 页面骨架: 门户头部组件 + shadcn-vue 布局容器
- [ ] 组件开发: 表格/表单/图表/卡片（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 与同模块页面菜单/面包屑/标签页联动
- [ ] 基本测试: 渲染/交互用例

---

### P-002 企业接入申请页

- 模块: 首页门户落地 > 企业接入申请
- 适用端: 通用
- 优先级-完成度: 高-已开发
- 描述: 展示企业接入流程、申请信息说明，并提供盖章申请文件模板下载。

任务清单

- [ ] 路由接入: /企业接入申请/
- [ ] 页面骨架: 门户头部组件 + shadcn-vue 布局容器
- [ ] 组件开发: 表格/表单/图表/卡片（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 与同模块页面菜单/面包屑/标签页联动
- [ ] 基本测试: 渲染/交互用例

---

### P-003 政策法规列表页

任务清单

- [ ] 路由接入: /内容管理/内容管理/内容列表页
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件开发: 表格/表单/图表/卡片（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 菜单/面包屑/标签页联动
- [ ] 基本测试: 渲染/交互用例

---

### P-004 内容详情页面

任务清单

- [ ] 路由接入: /内容编辑页
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件开发: 表格/表单/图表/卡片（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 菜单/面包屑/标签页联动
- [ ] 基本测试: 渲染/交互用例

---

### P-005 大屏展示页

- 模块: 大屏展示页 > 监控大屏
- 适用端: 政府端
- 优先级-完成度: 暂不实现
- 描述: 基于综合概览（P-009）的主要信息与大屏风格设计。

---

### P-006 用户登录页

- 模块: 用户认证登录 > 认证登录 > 登录鉴权
- 适用端: 通用
- 优先级-完成度: 高-待移植
- 功能描述: 通过用户名、密码、校验码进行登录验证。
- 核心字段: 用户名、密码、校验码
- 设计与交互: 政府用户与企业用户登录界面统一为企业样式。

任务清单

- [ ] 路由接入: /login
- [ ] 页面骨架: 控制台头部组件 + shadcn-vue 布局容器
- [ ] 组件开发: 表单输入、按钮、校验码输入等（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 菜单/面包屑/标签页联动（如需）
- [ ] 基本测试: 渲染/交互用例

---

### P-007 账号认证激活页

- 模块: 用户认证登录 > 用户实名认证
- 适用端: 通用
- 优先级-完成度: 高-待开发
- 描述: 用户实名认证流程；个人实名认证包含身份证、手机号、姓名验证。个人用户实名认证后判断是企业身份账户，则出现企业实名认证引导按钮，导航至企业信息填报页面

任务清单

- [ ] 路由接入: /auth/activate
- [ ] 页面骨架: 控制台头部组件 + shadcn-vue 布局容器
- [ ] 组件开发: 表格/表单/卡片（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 菜单/面包屑/标签页联动（如需）
- [ ] 基本测试: 渲染/交互用例

---

### P-008 找回密码页

- 模块: 用户认证登录 > 密码找回
- 适用端: 通用
- 优先级-完成度: 高-待开发
- 功能描述: 通过企业邮箱、电话、身份证实名认证等方式找回密码。
- 设计与交互:
  - 流程采用 Tab 或分步（验证身份 -> 重置密码 -> 完成）。
  - Placeholder 美化。
  - 弹窗/页面关闭按钮（×）必须可用。

任务清单

- [ ] 路由接入: /password/reset
- [ ] 页面骨架: 控制台头部组件 + shadcn-vue 布局容器
- [ ] 组件开发: 表单、分步/标签页、弹窗（优先使用 shadcn-vue）
- [ ] 类型/状态: 定义 TS 类型与（如需）Pinia store
- [ ] 交互联动: 菜单/面包屑/标签页联动（如需）
- [ ] 基本测试: 渲染/交互用例

---

## 政府端模块

### P-009 首页控制台（政府端）

- 模块: 监测平台政府端 > 综合概览 > 政府端首页
- 适用端: 政府端
- 优先级-完成度: 高-待移植（需大量修改）
- 功能描述: 展示试点城市区域地图、地理围栏、车端/云端统计、处理活动信息、风险事件信息与实时预警。
- 核心布局
  - 顶端统计框：接入车辆总数、在线车辆总数、累计车端风险、车端累计上报事件、累计云端风险、云端累计上报事件（描述字体样式一致）。
  - 地图显示（主体）：高清底图、地理围栏展示、车端/云端节点标记；悬浮提示、图表联动定位（高级）、违规企业颜色标注、风险热力图。
  - 车端统计（左侧）：车辆类型/品牌占比（环/饼）、处理活动（收集/存储/传输折线，支持时间切换与悬浮数值）、风险事件（左：阶段高/中/低统计；右：事件类型饼图，支持时间切换）。
  - 云端统计（右侧）：企业类型占比；处理活动（收集/存储/传输/加工/提供/公开/销毁七阶段）；风险事件（七阶段统计与事件类型饼图），均支持时间切换。
  - 风险预警信息（中下）：左（车端）与右（云端）预警列表并列；支持按年/月/日过滤；告警提示级别：高（闪烁+强制弹窗）、中（呼吸灯+消息中心）、低（静态+当日汇总）。
- 任务清单
  - [ ] 路由接入: /gov/dashboard
  - [ ] 页面骨架: 自定义 AppHeader 组件 + shadcn-vue Sidebar08 布局 + 页面主内容
  - [ ] 组件开发: 地图组件、统计卡片、图表、信息列表（统一风格与交互）
  - [ ] 交互联动: 时间范围切换、图表-地图联动、悬浮交互、告警提示机制

---

### P-010 处理活动信息统计

- 模块: 监测平台政府端 > 综合概览
- 适用端: 政府端
- 优先级-完成度: 低-已取消不开发
- 描述: 原始定义已过时，相关图表已整合入 P-009。建议不再独立实现，或重定义为 P-009 图表详情页。

---

### P-011 注册信息管理页

- 模块: 监测平台政府端 > 备案审核 > 注册信息管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 管理已注册备案的企业与车辆信息库，提供统计、搜索与导出。
- 核心字段与显示内容
  - 顶端统计（可选）：企业占比（注册中/通过/未通过），车辆类型与品牌占比。
  - 查询条件：企业名称、企业类型、注册状态、注册时间范围。
  - 企业信息列表：序号、企业名称、企业类型、注册状态、企业基本信息（入口）、测绘资质信息（入口）、车辆信息（入口）、安全防护措施（入口）、处理活动信息（入口）、注册时间、操作（查看详情）。
  - 导出功能：导出 Excel（统一样式）。
- 设计与交互
  - 详情弹窗采用 Tab 布局（与 P-013 一致），包含企业基本信息、测绘资质、车辆信息、安全防护措施、处理活动信息；显示审批记录；提供 上一页/下一页 导航；审批状态使用 通过/驳回；关闭按钮有效；支持催办触发（≥3 天未审批）。
- 任务清单
  - [ ] 路由接入: /gov/registration/management
  - [ ] 组件开发: 查询表单、DataTable、Dialog、Tabs、环形图等
  - [ ] 交互联动: 查询、分页、导出、详情 Tab 交互

---

### P-012 测绘资质审核页

- 模块: 监测平台政府端 > 备案审核 > 注册信息管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 描述: 审核与管理测绘资质（作为 P-011 与 P-013 详情中的 Tab 呈现；若独立页面则展示全企业资质列表）。
- 列表字段（参考政府端注册审批-测绘资质信息）：
  - 序号、企业名称、资质类别、资质等级（甲/乙）、证书编号、资质有效期、资质状态（有效/过期）、操作（查看附件）。
- 任务清单
  - [ ] 路由接入: /测绘资质审核页
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件开发/类型/交互/测试：按统一规范

---

### P-013 审批任务管理页面

- 模块: 监测平台政府端 > 备案审核 > 备案审批管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 集中管理企业各类申请（新注册、更新、申诉、风险评估），含 待办任务/已办任务/发起跟踪 三个 Tab。
- 核心字段与显示内容
  - 待办任务：查询条件（企业名称、统一社会信用代码、申请类型、申请时间范围）；列表（序号、企业名称、申请类型、申请材料、申请时间、操作）。
  - 已办任务：列表（序号、企业名称、申请类型、申请材料、申请时间、审批时间、审批结果、操作人员、操作-审批记录）。
  - 发起跟踪：列表（序号、类型、标题、提交时间、当前待办人）。
- 设计与交互
  - Tab 精简：去掉 全部任务 与 待阅任务
  - 智能预审：自动审核填报完整性
  - 合规：审批日志上链存证；审批结果电子章并生成 PDF 回执（含二维码）
  - 审批详情与操作（点击待办“查看详情”后弹出）
    - 布局：5 类信息 Tab，与 P-011 对齐，信息只读
    - 导航：上一页/下一页
    - 审批操作：各 Tab 下提供 保存 按钮（保存审批意见，最终提交前可修改）
    - 审批意见输入：预设常见问题勾选 + 备注批注
    - Tab 内容要点：
      - 测绘资质信息：表格化呈现
      - 车辆信息：包含 车辆 VIN 清单（附件）、安全处理技术清单（附件）、自动驾驶系统信息（附件）
      - 数据安全防控措施信息：组织架构（数据安全）、制度名称精简、监测预警与应急管理拆分、防护技术细分车端/云端
      - 数据处理活动信息：活动区域及路线（部分区域需上传说明）、活动方式（独自/合作，合作需填合作单位）、活动目的（多选）、数据使用范围（多选，如提供需填提供对象）
  - 已办任务交互：审批记录弹窗仅保留审核记录；底部按钮改为 关闭 且包含 × 关闭
- 任务清单
  - [ ] 路由接入: /gov/approval/tasks
  - [ ] 组件开发: Tabs、DataTable、查询表单、Dialog、复杂表单（审批意见）
  - [ ] 交互联动: Tab 切换、查询、分页、多 Tab 保存与意见勾选

---

### P-014 车端安全风险

- 模块: 监测平台政府端 > 实时监测（原 风险监测）> 风险管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 监测与管理车端安全风险，含统计与列表。
- 核心字段与显示内容
  - 上端统计：按处理阶段（采集/存储/传输）、风险等级、处置情况；新增处置率统计（使用卡片和 Echarts 图表）
  - 下端检索：注册企业、车辆 VIN、品牌、车型、处理阶段、处理状态、风险等级、发生时间范围
  - 列表字段：注册企业、车辆 VIN、品牌、车型、处理阶段、风险等级、风险描述、当前状态、详细信息（入口）、风险溯源（入口）、发生时间、处置完成时间
  - 导出功能：风险信息导出
- 设计与交互
  - 详细信息弹窗：包含详细风险类型、危害、可能性、处置建议等（与业务专家确认）
  - 风险溯源：跳转 P-018
- 任务清单
  - [ ] 路由接入: /gov/risk/vehicle
  - [ ] 组件开发: DataTable、指定样式风险图表、筛选器、Dialog
  - [ ] 交互联动: 可视化、筛选查询、导出、详情查看、页面跳转

---

### P-015 云端安全风险

- 模块: 监测平台政府端 > 实时监测 > 风险管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 与 P-014 类似，按云端特点调整。
- 核心要点
  - 上端统计：收集/存储/传输/加工/提供/公开/销毁 七阶段；展示需更清晰
  - 下端检索：企业名称、企业类型、处理阶段（七阶段）、处理状态、风险等级、发生时间范围
  - 列表字段：企业名称、企业类型、处理阶段、风险等级、风险描述、当前状态、详细信息、风险溯源、发生时间、处置完成时间
  - 导出功能：风险信息导出
  - 详细信息弹窗：与车端一致（细节同业务确认）
  - 风险溯源：跳转 P-019
- 任务清单
  - [ ] 路由接入: /gov/risk/cloud
  - [ ] 其余内容与 P-014 保持一致

---

### P-016 车端事件管理

- 模块: 监测平台政府端 > 实时监测 > 事件管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 监测与管理车端安全事件，样式参照 P-014。
- 核心字段与显示内容
  - 上端统计：事件总数、处置状态（已处理/处理中/未处理）、分类占比、处置率
  - 下端检索：注册企业、车辆 VIN、品牌、车型、事件类型、处理状态、上报时间
  - 列表字段：注册企业、车辆 VIN、品牌、车型、事件类型、当前状态、上报时间、处置完成时间
  - 导出功能：事件信息导出
- 任务清单
  - [ ] 路由接入: /gov/event/vehicle
  - [ ] 组件开发: DataTable、统计图表、查询表单
  - [ ] 交互联动: 详情查看、筛选查询、导出

---

### P-017 云端事件管理

- 模块: 监测平台政府端 > 实时监测 > 事件管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 与 P-016 基本一致，面向云端。
- 核心字段与显示内容
  - 上端统计：同车端
  - 下端检索：企业名称、企业类型、事件类型、处理状态、上报时间
  - 列表字段：企业名称、企业类型、事件类型、当前状态、上报时间、处置完成时间
  - 导出功能：事件信息导出
- 任务清单
  - [ ] 路由接入: /gov/event/cloud
  - [ ] 其余内容与 P-016 保持一致

---

### P-018 车端应急溯源

- 模块: 监测平台政府端 > 实时监测 > 应急溯源管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 车端安全事件的应急响应与溯源分析，含违规环节追溯与历史轨迹回放。
- 核心字段与显示内容
  - 溯源信息展示（核心）：可视化溯源链/时间线，支持层层点击与深度链接
  - 溯源链示例：安全风险事件 -> 违规数据列表（处理环节/数据类型） -> 操作日志 -> 责任主体（企业信息） -> ...
  - 辅助功能：历史轨迹回放（地图）
- 设计与交互：需与业务专家深度协作设计
- 任务清单
  - [ ] 路由接入: /gov/trace/vehicle
  - [ ] 组件开发: 溯源可视化（流程/时序）、时间线、地图（轨迹）、数据表格
  - [ ] 交互联动: 溯源路径分析、数据钻取、轨迹回放控制

---

### P-019 云端应急溯源

- 模块: 监测平台政府端 > 实时监测 > 应急溯源管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 云端安全事件溯源，链条更复杂。
- 核心字段与显示内容
  - 溯源链示例：安全风险事件 -> 违规数据列表（处理活动：收集/存储/传输/加工使用/提供/公开/销毁/出境/转移/委托等）-> 操作日志 -> 责任主体 -> 支撑单位基本信息 -> ...
- 设计与交互：同车端（不含轨迹回放），重点设计
- 任务清单
  - [ ] 路由接入: /gov/trace/cloud
  - [ ] 其余内容与 P-018 保持一致（不含轨迹回放）

---

### P-020 车端操作信息

- 模块: 监测平台政府端 > 实时监测 > 操作信息管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 车端操作日志的记录、查询与分析，按协议字段全量展示。
- 核心字段与显示内容
  - 查询条件：注册企业、车辆 VIN、品牌、车型、处理阶段、操作时间范围
  - 列表字段：序号、注册企业、车辆 VIN、品牌、车型、处理阶段、操作时间（及其他协议字段）
  - 导出功能：导出操作信息
- 任务清单
  - [ ] 路由接入: /gov/log/vehicle
  - [ ] 组件开发: 日志查询表单、DataTable
  - [ ] 交互联动: 日志查询、导出

---

### P-021 云端操作信息

- 模块: 监测平台政府端 > 实时监测 > 操作信息管理
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 云端操作日志的记录、查询与分析。
- 核心字段与显示内容
  - 查询条件：企业名称、企业类型、处理阶段（收集至销毁）、操作时间范围
  - 列表字段：序号、企业名称、企业类型、处理阶段、操作时间（及其他协议字段）
  - 导出功能：导出操作信息
- 任务清单
  - [ ] 路由接入: /gov/log/cloud
  - [ ] 其余内容与 P-020 保持一致

---

### P-022 检查信息管理页

- 暂不实现

### P-023 检查计划制定页

- 暂不实现

---

## 企业端模块

### P-026 我的工作台（企业端）

- 模块: 监测平台企业端 > 综合概览（首页/我的门户）
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 功能描述: 企业端首页，展示我的填报、通知信息、所有任务（待办/已办）；是业务入口。
- 核心布局与模块
  - 左上 我的填报：包含注册信息填报、数据目录填报（待实现）、风险评估填报（待实现）的入口
    - 注册信息填报交互：进入注册信息管理（P-033）；首次填报时提供 信息填报 按钮触发 P-028 至 P-032；填报界面采用 Tab，每页含 保存（暂存）/上一页/下一页
  - 右上 通知：显示政策法规动态信息列表，最新置顶；需按法规/政策/公告/通用信息分类标记
  - 下部 所有任务：Tab 切换 待办任务/已办任务（删除 全部任务）
    - 待办任务列表：序号、类别（注册反馈/风险提醒/风险处置/事件处置）、标题、接收时间、操作（查看详情）
      - 注册反馈详情：类别、标题、申请人、申请时间、失败原因（若有）、截止填报时间；操作 查看填报信息（跳转 P-028 系列并标记失败项）
      - 风险提醒详情：风险 ID、类别（车端/云端）、标题、产生时间、阶段、等级、描述、处置建议、状态、截止处置日期（车端含 VIN 等）；操作：确认风险（弹出确认）、申诉风险（弹出表单：基本信息 + 申诉原因/材料/申诉人/时间）
      - 风险处置详情：基本信息 + 处置意见（通过/驳回及原因）、处理人、时间
      - 事件处置详情：基本信息 + 处置意见（通过/驳回及原因）、处理人、时间
    - 已办任务列表：序号、类别、标题、接收时间、处置时间、操作（查看详情）
- 设计与交互
  - 样式优化：边框圆角、阴影样式、渐变填充等
  - 内容布局：详情弹窗将同类内容聚合展示
- 任务清单
  - [ ] 路由接入: /corp/dashboard
  - [ ] 组件开发: 统计卡片、信息列表、Tabs、Dialog、复杂表单（申诉/确认）
  - [ ] 交互联动: 链接到对应功能页面、复杂待办处理（确认/申诉/反馈）

---

### P-027 风险评估处理

- 模块: 监测平台企业端 > 综合概览
- 适用端: 企业端
- 优先级-完成度: 中-待开发
- 描述: 风险评估相关信息填报（入口位于 P-026 我的填报；具体模板与流程后续设计）。

---

### 注册备案填报系列（P-028 至 P-032）

通用要求

- 布局：采用 Tab 或分步（Steps）形式。
- 导航：每页/每步提供 保存（暂存）/上一页/下一页。

#### P-028 企业基本信息（填报）

- 模块: 企业端 > 注册备案 > 备案填报
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 字段：企业名称（与营业执照一致）、统一社会信用代码、注册地址/经营地址、企业类型（整车生产企业/平台运营商/智驾方案提供商/地图服务商/其他）、成立日期、注册资本、法人代表信息、营业执照扫描件（附件）
- 任务清单
  - [ ] 路由接入: /corp/filing/basic-info
  - [ ] 组件开发: 表单、输入框、选择器、日期选择器、文件上传
  - [ ] 交互联动: 表单验证、保存（暂存）、步骤导航

#### P-029 测绘资质信息（填报）

- 模块: 企业端 > 注册备案 > 备案填报
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 字段：支持多条资质；类别与等级（互联网地图/导航电子地图制作/地理信息系统工程，支持甲/乙、多选、平铺展示）、证书编号、发证机关、有效期（范围）、状态、证书扫描件（附件）、特殊情况说明
- 任务清单
  - [ ] 路由接入: /corp/filing/qualification
  - [ ] 组件开发: 动态表单、多选框组、文件上传
  - [ ] 交互联动: 动态增删、保存（暂存）、步骤导航

#### P-030 数据安全防控措施-制度（填报）

- 模块: 企业端 > 注册备案 > 备案填报
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 字段：
  - 组织架构：数据安全负责部门、负责人、职位；组织架构管理制度（附件）
  - 数据分类分级管理：制度（附件）、重要核心数据目录（可选，附件）；显示法规提示语
  - 制度体系：收集/存储/加工/提供/公开/销毁阶段制度、日志管理制度（附件）
  - 安全风险评估：自评估/第三方；第三方需填机构名称/信用代码/资质；评估制度与报告（附件，报告可选）
  - 监测预警与应急管理：监测预警机制、应急预案及管理制度（附件）
- 任务清单
  - [ ] 路由接入: /corp/filing/security-policy
  - [ ] 组件开发: 表单、大量文件上传、条件渲染
  - [ ] 交互联动: 表单填写、保存（暂存）、步骤导航

#### P-031 数据安全防控措施-技术（填报）

- 模块: 企业端 > 注册备案 > 备案填报
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 字段（支持多选，每阶段含 其他 与 特殊情况说明）：
  - 车端技术：收集（属性脱敏、地理围栏）；存储（保密处理、里程限制、属性脱敏）；传输（保密处理、属性脱敏、里程限制、VMS 数据融合）
  - 云端技术：存储（网络与通信安全〈三级等保〉、设备与计算安全、应用与数据安全）；加工（属性脱敏、VMS 融合、访问控制、风险监测、应急溯源、应急处置）；传输（校验技术、密码技术、安全传输通道/协议）；审计（安全监测、日志审计、日志完整性）
  - 注：原界面的访问控制策略与数据脱敏规则可先保留
- 任务清单
  - [ ] 路由接入: /corp/filing/security-tech
  - [ ] 组件开发: 多选框组、表单、文本框
  - [ ] 交互联动: 表单填写、保存（暂存）、步骤导航

#### P-032 数据处理活动信息（填报）

- 模块: 企业端 > 注册备案 > 备案填报
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 字段：
  - 车辆产品信息：支持动态添加/模板导入；品牌、车型、VIN、数量；自动驾驶系统配置（名称版本、传感器数量与精度、地图数据应用）；所用地理信息安全处理技术（多选）；技术说明（插件/算法、自研/地方机构、认证情况等）
  - 数据采集区域与路线：全域/部分区域；部分区域需上传说明（附件）
  - 数据处理活动方式：自行/合作（合作需填写合作单位信息：名称/信用代码/资质）
  - 数据处理目的（多选）：数据脱敏、场景库制作与处理、导航电子地图制作、互联网地图服务
  - 数据使用范围（多选）：出境、提供、公开；若提供需填写提供对象信息（名称/信用代码/资质）
- 任务清单
  - [ ] 路由接入: /corp/filing/data-activity
  - [ ] 组件开发: 复杂表单、动态表单（车辆）、文件上传、条件渲染、数据导入
  - [ ] 交互联动: 动态增删、条件显示、保存（暂存）、提交

---

### P-033 备案更新记录管理页（注册信息管理）

- 模块: 企业端 > 注册备案 > 注册信息管理（原 备案记录）
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 功能描述: 管理企业的备案更新记录，查看注册状态，发起新备案、更新、申诉等操作。
- 核心字段与显示内容
  - 操作按钮：首次填报时显示 信息填报（跳转 P-028）
  - 查询条件：注册状态（成功/失败/审核中）、注册人、注册时间范围
  - 列表字段：序号、企业名称、企业类型、企业基本信息（状态）、测绘资质信息（状态）、车辆信息（状态）、安全防护措施（状态）、处理活动信息（状态）、注册时间、操作（查看详情）
- 设计与交互
  - 详情弹窗：Tab 展示五类信息，样式参照政府端 P-011；显示审批记录；上一页/下一页
  - 操作按钮：更新（跳转 P-028 至 P-032 回显数据并可重新填报）；申诉（参照 P-026 的申诉风险流程）
- 任务清单
  - [ ] 路由接入: /corp/filing/records
  - [ ] 组件开发: DataTable、查询表单、Dialog、Tabs
  - [ ] 交互联动: 详情查看、新备案/更新（跳转）、申诉流程（弹窗）

---

## 风险事件管理系列（企业端，P-034 至 P-035）

通用要求

- 导航名称调整：企业端“风险统计”改为“风险事件”；左侧功能栏分为 风险统计 与 事件统计；原 P-036 整合入 P-034。
- 样式：与政府端 P-014 至 P-017 保持风格一致。
- 布局：左侧 1/3 展示统计，右侧 2/3 展示详情列表。
- 切换：车端 与 云端 使用 Tab 切换。
- 内容：统计与详情弹窗内容参照政府端对应页面。

### P-034 风险统计页（企业端）

- 模块: 企业端 > 风险事件 > 风险统计
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 功能描述: 整合展示企业车端与云端的风险统计与详情。
- 要点
  - 车端风险：与政府端 P-014 对齐
  - 云端风险：与政府端 P-015 对齐
- 任务清单
  - [ ] 路由接入: /corp/risk/stats
  - [ ] 组件开发: 统计图表、Tabs、DataTable、查询表单
  - [ ] 交互联动: Tab 切换、可视化、查询、详情查看

### P-035 事件统计页（企业端）

- 模块: 企业端 > 风险事件 > 事件统计
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 功能描述: 整合展示企业车端与云端的事件统计与详情。
- 要点
  - 车端事件：与政府端 P-016 对齐
  - 云端事件：与政府端 P-017 对齐
- 任务清单
  - [ ] 路由接入: /corp/event/stats
  - [ ] 组件与交互：同 P-034

注：

- P-036 风险趋势分析页 功能已整合入 P-034
- P-037 工单列表页 与 P-038 工单处理页 功能已整合入 P-026 我的工作台

---

### P-039 通知信息页

- 模块: 企业端 > 通知待办
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 描述: 展示所有通知与最新消息，是 P-026 工作台“通知”模块的详情列表页。
- 任务清单
  - [ ] 路由接入: /通知待办/通知信息页
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-040 任务管理页

- 模块: 企业端 > 通知待办
- 适用端: 企业端
- 优先级-完成度: 高-待开发
- 描述: 展示所有待办任务与已办任务列表，是 P-026“所有任务”的详情页。
- 任务清单
  - [ ] 路由接入: /任务管理页
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-041 活动申请页

- 暂不实现

### P-042 实时数据流面板

- 暂不实现

### P-043 在线课程库页

- 暂不实现

---

## 系统管理模块

### P-044 角色权限管理页（企业端）

- 模块: 企业管理 > 系统管理 > 用户管理
- 适用端: 企业端
- 优先级-完成度: 中-待开发
- 功能描述: 企业内角色与权限配置管理。
- 预设角色（参考）：管理员、安全监测员、审核专员、风险处置专员、数据分析员
- 设计与交互：通用功能，需依据最终业务方案审核（与业务专家确认）
- 任务清单
  - [ ] 路由接入: /corp/management/roles
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-045 角色权限管理页（系统/政府端）

- 模块: 系统管理 > 用户管理
- 适用端: 政府端
- 优先级-完成度: 中-待开发
- 功能描述: 系统全局角色与权限配置管理（政府端）。
- 预设角色（参考）：超级管理员、安全监测员、审核专员、监督检查员、数据分析员
- 设计与交互：通用功能，需依据最终业务方案审核
- 任务清单
  - [ ] 路由接入: /admin/roles
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-046 组织架构管理页（政府端-企业管理）

- 模块: 系统管理 > 企业管理
- 适用端: 政府端
- 优先级-完成度: 中-待开发
- 功能描述: 更侧重“企业管理”，支持对注册备案企业的查询、新增（系统侧）、修改、删除、批量操作等。
- 任务清单
  - [ ] 路由接入: /admin/organization 或 /admin/enterprise
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-047 用户信息管理页

- 模块: 系统管理 > 用户管理
- 适用端: 政府端
- 优先级-完成度: 中-待开发
- 功能描述: 管理系统用户（政府端与企业端），支持查询、新增、密码重置、编辑、删除、批量操作等。
- 设计与交互：通用功能，需依据最终业务方案审核
- 任务清单
  - [ ] 路由接入: /admin/users
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-048 区域管理页

- 模块: 系统管理 > 区域管理 > 监测区域
- 适用端: 政府端
- 优先级-完成度: 中-待开发
- 功能描述: 电子信息围栏区域修正，提供预览，并同步至 P-009。
- 核心字段与显示内容：坐标输入（经纬度）、框选区域、预览、更新发布
- 任务清单
  - [ ] 路由接入: /admin/area/management
  - [ ] 组件开发: 地图组件、区域绘制工具、坐标输入表单
  - [ ] 交互联动: 围栏绘制、预览、保存/发布

---

### P-049 版本记录管理页

- 模块: 系统管理 > 区域管理 > 监测区域
- 适用端: 政府端
- 优先级-完成度:- 暂不实现
- 功能描述: 围栏版本管理与历史对比。
- 核心字段与显示内容：版本历史列表（范围描述/标识、更新时间、操作人员）、地图对比高亮差异
- 任务清单
  - [ ] 路由接入: /admin/area/versions
  - [ ] 组件开发: 版本列表（DataTable）、地图对比视图
  - [ ] 交互联动: 版本选择、历史对比展示

---

### P-050 风险规则库列表页

任务清单

- [ ] 路由接入: /风险规则管理/风险规则/风险规则库
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：按业务规则内容 增删改查列表，规则编辑打开 P-051

---

### P-051 规则引擎编辑

任务清单

- [ ] 路由接入: /规则引擎编辑
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：界面完全仿照规则引擎编辑器完整功能

---

### P-052 参数配置页

任务清单

- [ ] 路由接入: /系统配置/参数配置页
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：系统全局参数配置页面

---

### P-053 字典管理页

任务清单

- [ ] 路由接入: /字典管理页
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：系统数据字典管理

---

### P-054 操作日志页

- 模块: 系统管理 > 日志管理
- 适用端: 政府端
- 优先级-完成度: 中-待开发
- 功能描述: 查询与管理系统操作日志（备案审核、实时监测、监督检查、系统管理），支持溯源定责；企业端不可见。
- 核心要求：与业务数据全链路关联；日志上链存证；留存不少于 1 年（关键日志 ≥3 年）
- 任务清单
  - [ ] 路由接入: /admin/logs/operation
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-055 登录日志页

- 模块: 系统管理 > 日志管理
- 适用端: 政府端
- 优先级-完成度: 中-待开发
- 功能描述: 用户登录日志查询与管理；企业端不可见。
- 核心要求：同 P-054；日志可上链存证
- 任务清单
  - [ ] 路由接入: /admin/logs/login
  - [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
  - [ ] 组件/类型/交互/测试：按统一规范

---

### P-056 系统监控页

任务清单

- [ ] 路由接入: /监控运维/系统监控页
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：按统一规范
      样式模仿 Grafana 系统监控界面

---

### P-057 数据备份页

任务清单

- [ ] 路由接入: /数据备份页
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：按统一规范

---

### P-058 系统安全风险

任务清单

- [ ] 路由接入: /系统安全/系统安全风险
- [ ] 页面骨架: 控制台头部组件 + 左侧子菜单 + shadcn-vue 布局容器
- [ ] 组件/类型/交互/测试：按统一规范

---

### P-059 信息发布列表页（新增，原 P-022）

- 模块: 监测平台政府端 > 系统管理 > 信息发布
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 发布政策/标准/公告等，支持 CRUD 与发布；更新时自动通知企业。
- 核心字段与显示内容
  - 列表字段：序号、标题、信息类型（原 通知公告）、状态（待发布/已发布）、发布时间、操作（更多）
- 设计与交互
  - 操作列使用“更多”按钮，弹出 发布/编辑/删除
  - 发布/删除需二次确认弹窗（统一样式）
  - 编辑跳转 P-060
- 任务清单
  - [ ] 路由接入: /admin/information/list
  - [ ] 组件开发: DataTable、查询表单、下拉菜单（更多操作）、确认弹窗
  - [ ] 交互联动: 增删改查、发布/下线、确认弹窗

---

### P-060 信息发布编辑页（新增，原 P-023）

- 模块: 监测平台政府端 > 系统管理 > 信息发布
- 适用端: 政府端
- 优先级-完成度: 高-待开发
- 功能描述: 信息发布内容的详情编辑，使用富文本编辑器。
- 核心字段与显示内容
  - 基本信息：标题、信息类型（选择器）
  - 内容编辑：富文本编辑器
  - 操作按钮：保存、发布
- 任务清单
  - [ ] 路由接入: /admin/information/edit/:id
  - [ ] 组件开发: 富文本编辑器、表单、保存/发布按钮
  - [ ] 交互联动: 编辑、保存、发布

---

说明

- 文本已统一用词、标点、列表与复选框风格；尖括号组件名改为描述以避免语言构造冲突。
- 所有“任务清单”项遵循统一模板，便于后续开发与测试的批量执行与追踪。

请确认是否将上述内容保存为文件：docs/地理信息安全监测平台-系统前端原型页面开发任务明细-v2.0.md。若确认，我将写入文件并更新待办清单状态。
