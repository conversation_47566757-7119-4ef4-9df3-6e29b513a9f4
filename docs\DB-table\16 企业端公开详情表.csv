﻿表名: log_ent_publish_detail,,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment),
1,id,主键ID,BIGINT,,○,●,自增主键,
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id""",
3,publish_channel_and_scope,公开渠道与范围,VARCHAR,256,○,,填写公开渠道的URL或平台/媒介名称 并明确可访问范围。, 
4,map_review_executor,地图审核程序执行机构,SMALLINT,18,,,评估机构的18位统一社会信用代码。,
5,map_review_time,地图审核程序执行时间,TIMESTAMP,,,,时间戳,
6,desensitization_flag,脱敏处理标志,SMALLINT,,○,,"""(BYTE) 0x01:未经脱敏处理; 0x02:已经安全处理""",
7,risk_assessment_status,风险评估状态,SMALLINT,,○,,"""(BYTE) 0x01:未执行风险评估; 0x02:已执行""",
8,risk_assessment_org_code,安全风险评估机构代码,VARCHAR,18,,,评估机构的18位统一社会信用代码。,
9,risk_assessment_time,风险评估执行时间,TIMESTAMP,,,,时间戳,
10,data_review_status,数据审查状态,SMALLINT,,○,,"""(BYTE) 0x01:履行安全审查; 0x02:未履行""",
11,operator_identity,操作员身份,SMALLINT,,○,,"""(BYTE) 0x01:重要数据操作人员; 0x02:一般数据操作人员""",
,,,,,,,,
