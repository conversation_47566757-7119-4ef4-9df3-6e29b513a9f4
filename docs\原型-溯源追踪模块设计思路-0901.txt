风险溯源管理模块：设计原则：
1、分车端云端开展溯源追踪；
2、仅针对车端和云端的安全风险 和事件 进行溯源；
3、溯源的目的是找到与风险和事件相关的时间、地点、风险事件等信息，即只溯源出与风险事件相关的信息即可，其他信息无需溯源；
4、车端以VIN码，云端以数据包ID列表；
5、具体形式简单明了即可，可以采用流程图的形式，可以包含每个阶段，不涉及就不点亮；合规的就打钩，涉及风险的就打差并标记标红。并且也需要给出当前风险及事件的状态，处理的阶段，相关的附件等。
6、车端/云端安全风险-风险溯源的信息演示 种类选择：
（1）车端风险1：数据在车辆存储前，未按照国家认定的地理信息保密处理技术完成处理；
（2）云端风险1：数据未经安全处理直接提供数据；
7、http://localhost:5173/gov/monitor/event/ 风险溯源 事件种类选择
（1）车端事件1：核心安全策略失效：
（2）云端事件1：数据泄露/丢失：
8、需要考虑的问题：基于事前填报信息及制定的协议信息，针对未来可能产生的风险和事件，设计溯源信息应该包含的信息种类，数据对象、数据处理对象、数据关联对象相关的属性信息。


http://localhost:5173/gov/monitor/traceback/vehicle
http://localhost:5173/gov/monitor/traceback/cloud
界面规划：
左侧：溯源记录历史 mock更多数据，增加可查删的筛选操作表单
右侧：溯源看板包含的主要内容：

1.车端/云端风险基本信息卡片：= 云端/ 车端 安全风险事件信息， [开始溯源]按钮 人工触发“2溯源图谱”进行渲染动态出现。
2.溯源图谱卡片：纵向排列数据传播溯源流程图，重点标注风险点环境数据包信息。
3. 风险溯源分析：= 数据包（子数据包）信息（通信协议对照）、风险点描述（阶段、风险字段）、关联责任主体 （有关联实体信息时候可以点击调整）
4. 溯源报告：预览-可导出PDF

(右侧界面UI中对于1-2-3-4有 纵向时间轴效果，todo list 视觉效果）




溯源图谱要求 色彩高级（暗色调的深海主题）