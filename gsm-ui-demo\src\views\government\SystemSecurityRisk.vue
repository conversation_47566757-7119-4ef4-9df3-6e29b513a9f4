<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          实时监控系统安全风险，包括威胁检测、漏洞评估、安全事件响应和风险态势感知
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Badge :variant="getOverallRiskVariant(overallRiskLevel)" class="flex items-center gap-1">
          <component :is="getRiskIcon(overallRiskLevel)" class="w-3 h-3" />
          {{ overallRiskLevel }}
        </Badge>
        <Button @click="handleScanSecurity" :disabled="isScanning" variant="outline" size="sm">
          <Shield :class="`w-4 h-4 ${isScanning ? 'animate-spin' : ''}`" />
          {{ isScanning ? '扫描中...' : '安全扫描' }}
        </Button>
        <Button @click="refreshRiskData" variant="outline" size="sm">
          <RotateCcw class="w-4 h-4" />
        </Button>
      </div>
    </div>

    <!-- 安全态势总览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">总体风险等级</p>
              <div class="flex items-center gap-2 mt-1">
                <Badge :variant="getOverallRiskVariant(overallRiskLevel)" class="text-base">
                  {{ overallRiskLevel }}
                </Badge>
                <div class="text-sm text-muted-foreground">{{ riskScore }}/100</div>
              </div>
              <div class="w-full bg-muted rounded-full h-2 mt-2">
                <div
                  :class="`rounded-full h-2 transition-all duration-300 ${getRiskColor(overallRiskLevel)}`"
                  :style="{ width: `${riskScore}%` }"
                ></div>
              </div>
            </div>
            <component :is="getRiskIcon(overallRiskLevel)" class="w-8 h-8 text-risk-medium" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">活跃威胁</p>
              <p class="text-2xl font-bold text-risk-critical">{{ securityStats.activeThreats }}</p>
              <p class="text-xs text-risk-critical flex items-center gap-1 mt-1">
                <TrendingUp class="w-3 h-3" />
                +{{ securityStats.newThreatsToday }}个 今日新增
              </p>
            </div>
            <AlertTriangle class="w-8 h-8 text-risk-critical" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">安全漏洞</p>
              <p class="text-2xl font-bold text-risk-medium">{{ securityStats.vulnerabilities }}</p>
              <div class="text-xs text-muted-foreground mt-1">
                高危: {{ securityStats.criticalVulns }}个 | 中危: {{ securityStats.mediumVulns }}个
              </div>
            </div>
            <Bug class="w-8 h-8 text-risk-medium" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">攻击尝试</p>
              <p class="text-2xl font-bold" style="color: #4682b4">
                {{ securityStats.attackAttempts }}
              </p>
              <p class="text-xs flex items-center gap-1 mt-1" style="color: #4682b4">
                <Shield class="w-3 h-3" />
                防护率 {{ securityStats.blockRate }}%
              </p>
            </div>
            <Target class="w-8 h-8" style="color: #4682b4" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 安全风险详情 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 高危风险列表 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <AlertTriangle class="w-5 h-5 text-risk-critical" />
              高危风险项目
            </div>
            <Badge variant="destructive">{{ highRiskItems.length }}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea class="h-80">
            <div class="space-y-3">
              <div v-if="highRiskItems.length === 0" class="text-center py-8 text-muted-foreground">
                <CheckCircle class="w-12 h-12 mx-auto mb-2 text-green-500" />
                <p>暂无高危风险项目</p>
              </div>
              <div
                v-else
                v-for="item in highRiskItems"
                :key="item.id"
                class="flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/40 cursor-pointer"
                @click="handleViewRiskDetail(item.id)"
              >
                <component
                  :is="getRiskTypeIcon(item.type)"
                  class="w-5 h-5 text-risk-critical flex-shrink-0 mt-0.5"
                />
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2 mb-1">
                    <Badge variant="destructive" class="text-xs">{{ item.severity }}</Badge>
                    <span class="text-xs text-muted-foreground">{{ item.category }}</span>
                  </div>
                  <div class="font-medium text-sm">{{ item.title }}</div>
                  <div class="text-sm text-muted-foreground">{{ item.description }}</div>
                  <div class="flex items-center justify-between mt-2">
                    <span class="text-xs text-muted-foreground">{{ item.detectedAt }}</span>
                    <div class="flex items-center gap-1">
                      <Badge :variant="getStatusVariant(item.status)" class="text-xs">
                        {{ item.status }}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      <!-- 安全事件时间线 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Clock class="w-5 h-5" />
            安全事件时间线
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea class="h-80">
            <div class="space-y-4">
              <div v-for="event in securityEvents" :key="event.id" class="flex items-start gap-3">
                <div class="flex flex-col items-center">
                  <div
                    :class="`w-3 h-3 rounded-full flex-shrink-0 ${getEventColor(event.type)}`"
                  ></div>
                  <div
                    v-if="event !== securityEvents[securityEvents.length - 1]"
                    class="w-px h-8 bg-border mt-2"
                  ></div>
                </div>
                <div class="flex-1 min-w-0 pb-4">
                  <div class="flex items-center gap-2 mb-1">
                    <Badge :variant="getEventTypeVariant(event.type)" class="text-xs">
                      {{ event.type }}
                    </Badge>
                    <span class="text-xs text-muted-foreground">{{ event.timestamp }}</span>
                  </div>
                  <div class="font-medium text-sm">{{ event.title }}</div>
                  <div class="text-sm text-muted-foreground">{{ event.description }}</div>
                  <div class="flex items-center gap-2 mt-2">
                    <Badge :variant="getSeverityVariant(event.severity)" class="text-xs">
                      {{ event.severity }}
                    </Badge>
                    <span class="text-xs text-muted-foreground">来源: {{ event.source }}</span>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>

    <!-- 威胁情报与漏洞管理 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最新威胁情报 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Globe class="w-5 h-5" />
              最新威胁情报
            </div>
            <Button @click="handleRefreshThreatIntel" variant="ghost" size="sm">
              <RefreshCw class="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="threat in threatIntelligence"
              :key="threat.id"
              class="p-3 border rounded-lg"
            >
              <div class="flex items-center gap-2 mb-2">
                <Badge :variant="getThreatLevelVariant(threat.level)" class="text-xs">
                  {{ threat.level }}
                </Badge>
                <span class="text-xs text-muted-foreground">{{ threat.source }}</span>
                <span class="text-xs text-muted-foreground">{{ threat.publishedAt }}</span>
              </div>
              <div class="font-medium text-sm mb-1">{{ threat.title }}</div>
              <div class="text-sm text-muted-foreground">{{ threat.description }}</div>
              <div class="flex items-center gap-2 mt-2">
                <Badge variant="outline" class="text-xs">{{ threat.category }}</Badge>
                <Button
                  @click="handleViewThreatDetail(threat.id)"
                  variant="link"
                  class="text-xs h-auto p-0"
                >
                  查看详情 →
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 漏洞管理 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Bug class="w-5 h-5" />
              系统漏洞管理
            </div>
            <Button @click="handleScanVulnerabilities" variant="ghost" size="sm">
              <Search class="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div v-for="vuln in vulnerabilities" :key="vuln.id" class="p-3 border rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <Badge :variant="getSeverityVariant(vuln.severity)" class="text-xs">
                    {{ vuln.severity }}
                  </Badge>
                  <span class="text-base font-semibold">CVE-{{ vuln.cveId }}</span>
                </div>
                <span class="text-xs text-muted-foreground">CVSS: {{ vuln.cvssScore }}</span>
              </div>
              <div class="font-medium text-sm mb-1">{{ vuln.title }}</div>
              <div class="text-sm text-muted-foreground mb-2">影响组件: {{ vuln.component }}</div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <Badge :variant="getFixStatusVariant(vuln.fixStatus)" class="text-xs">
                    {{ vuln.fixStatus }}
                  </Badge>
                  <span class="text-xs text-muted-foreground"
                    >发现时间: {{ vuln.discoveredAt }}</span
                  >
                </div>
                <Button
                  @click="handleFixVulnerability(vuln.id)"
                  variant="link"
                  class="text-xs h-auto p-0"
                >
                  {{ vuln.fixStatus === '已修复' ? '查看修复' : '立即修复' }} →
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 安全防护策略 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Shield class="w-5 h-5" />
          安全防护策略
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="protection in protectionStrategies"
            :key="protection.id"
            class="p-4 border rounded-lg"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <component :is="protection.icon" class="w-5 h-5 text-primary" />
                <span class="font-medium">{{ protection.name }}</span>
              </div>
              <Switch
                :checked="protection.enabled"
                @update:checked="toggleProtection(protection.id, $event)"
              />
            </div>
            <div class="text-sm text-muted-foreground mb-3">{{ protection.description }}</div>
            <div class="space-y-2">
              <div class="flex items-center justify-between text-sm">
                <span class="text-muted-foreground">拦截次数:</span>
                <span class="font-medium">{{ protection.blockCount }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-muted-foreground">最后更新:</span>
                <span class="font-medium">{{ protection.lastUpdate }}</span>
              </div>
              <div class="flex items-center justify-between text-sm">
                <span class="text-muted-foreground">防护等级:</span>
                <Badge :variant="getProtectionLevelVariant(protection.level)" class="text-xs">
                  {{ protection.level }}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 风险详情对话框 -->
    <Dialog :open="showRiskDetailDialog" @update:open="showRiskDetailDialog = $event">
      <DialogContent class="max-w-3xl">
        <DialogHeader>
          <DialogTitle>风险详情 - {{ currentRisk?.title }}</DialogTitle>
          <DialogDescription> 查看风险项目的详细信息和处置建议 </DialogDescription>
        </DialogHeader>

        <div v-if="currentRisk" class="space-y-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-base font-semibold text-muted-foreground">风险编号</Label>
              <p class="text-sm font-mono">{{ currentRisk.id }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">检测时间</Label>
              <p class="text-sm">{{ currentRisk.detectedAt }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">风险等级</Label>
              <Badge :variant="getSeverityVariant(currentRisk.severity)">{{
                currentRisk.severity
              }}</Badge>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">风险类型</Label>
              <p class="text-sm">{{ currentRisk.category }}</p>
            </div>
          </div>

          <div>
            <Label class="text-base font-semibold text-muted-foreground">风险描述</Label>
            <p class="text-sm mt-1">{{ currentRisk.description }}</p>
          </div>

          <div>
            <Label class="text-base font-semibold text-muted-foreground">影响范围</Label>
            <p class="text-sm mt-1">
              {{ currentRisk.impact || '系统核心组件，可能影响数据安全性' }}
            </p>
          </div>

          <div>
            <Label class="text-base font-semibold text-muted-foreground">处置建议</Label>
            <div class="mt-1 space-y-2">
              <div class="flex items-start gap-2">
                <CheckCircle class="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                <span class="text-sm">立即更新相关安全补丁</span>
              </div>
              <div class="flex items-start gap-2">
                <CheckCircle class="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                <span class="text-sm">加强访问控制和权限管理</span>
              </div>
              <div class="flex items-start gap-2">
                <CheckCircle class="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                <span class="text-sm">启用相关安全防护策略</span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showRiskDetailDialog = false">关闭</Button>
          <Button @click="handleMarkResolved(currentRisk?.id)">
            <CheckCircle class="w-4 h-4 mr-2" />
            标记已处理
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  AlertTriangle,
  Bug,
  CheckCircle,
  Clock,
  Globe,
  RefreshCw,
  RotateCcw,
  Search,
  Shield,
  Target,
  TrendingUp,
  Database,
  Lock,
  Eye,
  Zap,
  Users,
} from 'lucide-vue-next'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

type RiskLevel = '极高' | '高' | '中' | '低'
type RiskStatus = '待处理' | '处理中' | '已处理' | '已忽略'
type ThreatLevel = '紧急' | '高危' | '中危' | '低危'
type FixStatus = '未修复' | '修复中' | '已修复' | '无需修复'

interface RiskItem {
  id: string
  title: string
  description: string
  severity: RiskLevel
  category: string
  type: string
  status: RiskStatus
  detectedAt: string
  impact?: string
}

interface SecurityEvent {
  id: string
  title: string
  description: string
  type: string
  severity: RiskLevel
  timestamp: string
  source: string
}

interface ThreatIntel {
  id: string
  title: string
  description: string
  level: ThreatLevel
  category: string
  source: string
  publishedAt: string
}

interface Vulnerability {
  id: string
  cveId: string
  title: string
  component: string
  severity: RiskLevel
  cvssScore: number
  fixStatus: FixStatus
  discoveredAt: string
}

interface ProtectionStrategy {
  id: string
  name: string
  description: string
  enabled: boolean
  level: RiskLevel
  blockCount: number
  lastUpdate: string
  icon: any
}

// 状态管理
const overallRiskLevel = ref<RiskLevel>('中')
const riskScore = ref(65)
const isScanning = ref(false)
const showRiskDetailDialog = ref(false)
const currentRisk = ref<RiskItem | null>(null)

// 安全统计
const securityStats = ref({
  activeThreats: 12,
  newThreatsToday: 3,
  vulnerabilities: 28,
  criticalVulns: 5,
  mediumVulns: 15,
  attackAttempts: 847,
  blockRate: 99.7,
})

// 高危风险项目
const highRiskItems = ref<RiskItem[]>([
  {
    id: 'risk-001',
    title: 'SQL注入漏洞检测',
    description: '在用户登录接口发现潜在的SQL注入风险点，可能导致数据泄露',
    severity: '极高',
    category: '代码安全',
    type: 'vulnerability',
    status: '待处理',
    detectedAt: '2025-08-25 14:32:15',
  },
  {
    id: 'risk-002',
    title: '异常登录行为检测',
    description: '检测到来自俄罗斯IP的多次失败登录尝试，疑似暴力破解攻击',
    severity: '高',
    category: '行为异常',
    type: 'intrusion',
    status: '处理中',
    detectedAt: '2025-08-25 13:58:42',
  },
  {
    id: 'risk-003',
    title: '未授权API调用',
    description: '发现多个未授权的API访问请求，可能存在权限绕过风险',
    severity: '高',
    category: '访问控制',
    type: 'access',
    status: '待处理',
    detectedAt: '2025-08-25 12:45:33',
  },
  {
    id: 'risk-004',
    title: '敏感数据传输未加密',
    description: '部分用户数据在传输过程中未使用HTTPS协议，存在数据泄露风险',
    severity: '中',
    category: '数据安全',
    type: 'data',
    status: '待处理',
    detectedAt: '2025-08-25 11:20:10',
  },
])

// 安全事件
const securityEvents = ref<SecurityEvent[]>([
  {
    id: 'event-001',
    title: '防火墙拦截攻击',
    description: '成功拦截来自恶意IP的端口扫描攻击',
    type: '防护事件',
    severity: '中',
    timestamp: '14:35:22',
    source: '防火墙系统',
  },
  {
    id: 'event-002',
    title: '异常文件上传阻止',
    description: '检测到恶意文件上传尝试，已自动阻止',
    type: '威胁阻断',
    severity: '高',
    timestamp: '14:28:15',
    source: '文件扫描',
  },
  {
    id: 'event-003',
    title: '权限提升尝试',
    description: '用户尝试提升系统权限，已记录并阻止',
    type: '权限异常',
    severity: '高',
    timestamp: '14:15:48',
    source: '权限管理',
  },
  {
    id: 'event-004',
    title: '数据库连接异常',
    description: '检测到数据库连接池耗尽，可能存在DOS攻击',
    type: '系统异常',
    severity: '中',
    timestamp: '13:52:33',
    source: '数据库监控',
  },
])

// 威胁情报
const threatIntelligence = ref<ThreatIntel[]>([
  {
    id: 'threat-001',
    title: 'CVE-2025-12345 严重漏洞预警',
    description: 'Node.js服务器存在远程代码执行漏洞，影响16.x-18.x版本',
    level: '紧急',
    category: '漏洞预警',
    source: '国家漏洞库',
    publishedAt: '2025-08-25',
  },
  {
    id: 'threat-002',
    title: 'APT组织新型攻击手法',
    description: '发现针对政府机构的新型网络钓鱼攻击，使用AI生成的虚假邮件',
    level: '高危',
    category: 'APT威胁',
    source: '威胁情报平台',
    publishedAt: '2025-08-24',
  },
  {
    id: 'threat-003',
    title: '勒索软件变种预警',
    description: 'LockBit勒索软件出现新变种，具有更强的横向移动能力',
    level: '高危',
    category: '恶意软件',
    source: '安全厂商',
    publishedAt: '2025-08-23',
  },
])

// 系统漏洞
const vulnerabilities = ref<Vulnerability[]>([
  {
    id: 'vuln-001',
    cveId: '2025-12345',
    title: 'Node.js远程代码执行漏洞',
    component: 'Node.js v16.20.1',
    severity: '极高',
    cvssScore: 9.8,
    fixStatus: '修复中',
    discoveredAt: '2025-08-25',
  },
  {
    id: 'vuln-002',
    cveId: '2025-11234',
    title: 'PostgreSQL权限提升漏洞',
    component: 'PostgreSQL 13.11',
    severity: '高',
    cvssScore: 8.2,
    fixStatus: '未修复',
    discoveredAt: '2025-08-24',
  },
  {
    id: 'vuln-003',
    cveId: '2025-10123',
    title: 'Nginx配置不当',
    component: 'Nginx 1.20.2',
    severity: '中',
    cvssScore: 6.5,
    fixStatus: '已修复',
    discoveredAt: '2025-08-23',
  },
])

// 防护策略
const protectionStrategies = ref<ProtectionStrategy[]>([
  {
    id: 'protection-001',
    name: 'Web应用防火墙',
    description: '保护Web应用免受OWASP Top 10攻击',
    enabled: true,
    level: '高',
    blockCount: 1247,
    lastUpdate: '2025-08-25',
    icon: Shield,
  },
  {
    id: 'protection-002',
    name: '入侵检测系统',
    description: '实时检测和响应网络入侵行为',
    enabled: true,
    level: '高',
    blockCount: 856,
    lastUpdate: '2025-08-25',
    icon: Eye,
  },
  {
    id: 'protection-003',
    name: 'DDoS防护',
    description: '防御分布式拒绝服务攻击',
    enabled: true,
    level: '中',
    blockCount: 234,
    lastUpdate: '2025-08-24',
    icon: Zap,
  },
  {
    id: 'protection-004',
    name: '数据加密',
    description: '保护静态和传输中的敏感数据',
    enabled: true,
    level: '极高',
    blockCount: 0,
    lastUpdate: '2025-08-20',
    icon: Lock,
  },
  {
    id: 'protection-005',
    name: '访问控制',
    description: '基于角色的细粒度权限管理',
    enabled: true,
    level: '高',
    blockCount: 67,
    lastUpdate: '2025-08-23',
    icon: Users,
  },
  {
    id: 'protection-006',
    name: '数据库防护',
    description: '防御SQL注入和数据库攻击',
    enabled: false,
    level: '中',
    blockCount: 45,
    lastUpdate: '2025-08-22',
    icon: Database,
  },
])

// 辅助函数
const getRiskIcon = (level: RiskLevel) => {
  const icons = {
    极高: AlertTriangle,
    高: AlertTriangle,
    中: AlertTriangle,
    低: CheckCircle,
  }
  return icons[level] || AlertTriangle
}

const getRiskColor = (level: RiskLevel) => {
  const colors = {
    极高: 'bg-red-500',
    高: 'bg-orange-500',
    中: 'bg-yellow-500',
    低: 'bg-green-500',
  }
  return colors[level] || 'bg-gray-500'
}

import type { BadgeVariants } from '@/components/ui/badge'
const getOverallRiskVariant = (level: RiskLevel): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<RiskLevel, NonNullable<BadgeVariants['variant']>> = {
    极高: 'destructive',
    高: 'destructive',
    中: 'secondary',
    低: 'default',
  }
  return variants[level] ?? 'outline'
}

const getRiskTypeIcon = (type: string) => {
  const icons = {
    vulnerability: Bug,
    intrusion: Target,
    access: Lock,
    data: Database,
  }
  return icons[type] || AlertTriangle
}

const getStatusVariant = (status: RiskStatus): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<RiskStatus, NonNullable<BadgeVariants['variant']>> = {
    待处理: 'destructive',
    处理中: 'secondary',
    已处理: 'default',
    已忽略: 'outline',
  }
  return variants[status] ?? 'outline'
}

const getSeverityVariant = (severity: RiskLevel): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<RiskLevel, NonNullable<BadgeVariants['variant']>> = {
    极高: 'destructive',
    高: 'destructive',
    中: 'secondary',
    低: 'outline',
  }
  return variants[severity] ?? 'outline'
}

const getEventColor = (type: string) => {
  const colors = {
    防护事件: 'bg-blue-500',
    威胁阻断: 'bg-red-500',
    权限异常: 'bg-orange-500',
    系统异常: 'bg-yellow-500',
  }
  return colors[type] || 'bg-gray-500'
}

const getEventTypeVariant = (type: string) => {
  const variants = {
    防护事件: 'default',
    威胁阻断: 'destructive',
    权限异常: 'secondary',
    系统异常: 'outline',
  }
  return variants[type] || 'outline'
}

const getThreatLevelVariant = (level: ThreatLevel): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<ThreatLevel, NonNullable<BadgeVariants['variant']>> = {
    紧急: 'destructive',
    高危: 'destructive',
    中危: 'secondary',
    低危: 'outline',
  }
  return variants[level] ?? 'outline'
}

const getFixStatusVariant = (status: FixStatus): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<FixStatus, NonNullable<BadgeVariants['variant']>> = {
    未修复: 'destructive',
    修复中: 'secondary',
    已修复: 'default',
    无需修复: 'outline',
  }
  return variants[status] ?? 'outline'
}

const getProtectionLevelVariant = (level: RiskLevel): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<RiskLevel, NonNullable<BadgeVariants['variant']>> = {
    极高: 'default',
    高: 'default',
    中: 'secondary',
    低: 'outline',
  }
  return variants[level] ?? 'outline'
}

// 事件处理
const refreshRiskData = () => {
  console.log('刷新风险数据')
}

const handleScanSecurity = () => {
  isScanning.value = true
  setTimeout(() => {
    isScanning.value = false
    console.log('安全扫描完成')
  }, 3000)
}

const handleViewRiskDetail = (id: string) => {
  const risk = highRiskItems.value.find((r) => r.id === id)
  if (risk) {
    currentRisk.value = risk
    showRiskDetailDialog.value = true
  }
}

const handleMarkResolved = (id?: string) => {
  if (id) {
    const risk = highRiskItems.value.find((r) => r.id === id)
    if (risk) {
      risk.status = '已处理'
    }
  }
  showRiskDetailDialog.value = false
}

const handleRefreshThreatIntel = () => {
  console.log('刷新威胁情报')
}

const handleViewThreatDetail = (id: string) => {
  console.log('查看威胁详情:', id)
}

const handleScanVulnerabilities = () => {
  console.log('扫描系统漏洞')
}

const handleFixVulnerability = (id: string) => {
  console.log('修复漏洞:', id)
}

const toggleProtection = (id: string, enabled: boolean) => {
  const protection = protectionStrategies.value.find((p) => p.id === id)
  if (protection) {
    protection.enabled = enabled
  }
}
</script>
