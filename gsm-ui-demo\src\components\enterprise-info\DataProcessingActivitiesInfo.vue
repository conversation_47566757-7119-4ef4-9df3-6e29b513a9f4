<template>
  <div class="space-y-6">
    <!-- 数据处理活动概览 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Activity class="h-5 w-5" />
            <span>数据处理活动概览</span>
          </div>
          <Badge variant="outline">
            {{ processingActivities.length }} 项活动
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ activityStats.regions }}</div>
            <div class="text-sm text-blue-600">覆盖区域</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ activityStats.partners }}</div>
            <div class="text-sm text-green-600">合作单位</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ activityStats.purposes }}</div>
            <div class="text-sm text-purple-600">处理目的</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 数据采集区域与路线 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <MapPin class="h-5 w-5" />
          <span>数据采集区域与路线</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="space-y-3">
                <Label class="text-sm font-medium">采集范围</Label>
                <div class="p-3 border rounded-lg">
                  <div class="flex items-center space-x-2">
                    <Badge :variant="collectionScope.type === '全域' ? 'default' : 'secondary'">
                      {{ collectionScope.type }}
                    </Badge>
                    <span class="text-sm">{{ collectionScope.description }}</span>
                  </div>
                </div>
              </div>

              <div class="space-y-3">
                <Label class="text-sm font-medium">主要区域</Label>
                <div class="space-y-2">
                  <div v-for="region in collectionScope.regions" :key="region" class="flex items-center space-x-2">
                    <MapPin class="w-4 h-4 text-blue-500" />
                    <span class="text-sm">{{ region }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div v-if="collectionScope.type === '部分区域'" class="space-y-3">
                <Label class="text-sm font-medium">区域说明文件</Label>
                <div class="space-y-2">
                  <div v-for="doc in collectionScope.documents" :key="doc.name" class="flex items-center justify-between p-2 border rounded">
                    <div class="flex items-center space-x-2">
                      <FileText class="w-4 h-4 text-blue-500" />
                      <div>
                        <div class="text-sm">{{ doc.name }}</div>
                        <div class="text-xs text-muted-foreground">{{ doc.size }}</div>
                      </div>
                    </div>
                    <Button size="sm" variant="ghost" @click="viewDocument(doc)">
                      <Eye class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 路线信息 -->
          <div class="space-y-4">
            <Label class="text-sm font-medium">主要采集路线</Label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="route in collectionRoutes" :key="route.name" class="p-3 border rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium">{{ route.name }}</h4>
                  <Badge :variant="route.status === '活跃' ? 'default' : 'secondary'">
                    {{ route.status }}
                  </Badge>
                </div>
                <div class="space-y-1 text-sm text-muted-foreground">
                  <div>起点：{{ route.startPoint }}</div>
                  <div>终点：{{ route.endPoint }}</div>
                  <div>距离：{{ route.distance }}</div>
                  <div>频次：{{ route.frequency }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 数据处理活动方式 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Users class="h-5 w-5" />
          <span>数据处理活动方式</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <div class="flex items-center space-x-4">
            <Label class="text-sm font-medium">处理方式：</Label>
            <Badge :variant="processingMode.type === '自行处理' ? 'default' : 'secondary'">
              {{ processingMode.type }}
            </Badge>
          </div>

          <div v-if="processingMode.type === '合作处理'" class="space-y-4">
            <Label class="text-sm font-medium">合作单位信息</Label>
            <div class="space-y-4">
              <div v-for="partner in processingMode.partners" :key="partner.name" class="p-4 border rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-3">
                    <div class="flex justify-between">
                      <span class="text-sm font-medium">单位名称：</span>
                      <span class="text-sm">{{ partner.name }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm font-medium">信用代码：</span>
                      <span class="text-sm font-mono">{{ partner.creditCode }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm font-medium">合作类型：</span>
                      <Badge variant="outline">{{ partner.cooperationType }}</Badge>
                    </div>
                  </div>
                  <div class="space-y-3">
                    <div class="flex justify-between">
                      <span class="text-sm font-medium">资质等级：</span>
                      <Badge variant="secondary">{{ partner.qualification }}</Badge>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm font-medium">合作范围：</span>
                      <span class="text-sm">{{ partner.scope }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm font-medium">合作期限：</span>
                      <span class="text-sm">{{ partner.duration }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 数据处理目的 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Target class="h-5 w-5" />
          <span>数据处理目的</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <Label class="text-sm font-medium">已选择的处理目的</Label>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div v-for="purpose in processingPurposes" :key="purpose.name" class="flex items-center justify-between p-3 border rounded-lg">
              <div class="flex items-center space-x-3">
                <Check class="w-5 h-5 text-green-500" />
                <div>
                  <div class="font-medium">{{ purpose.name }}</div>
                  <div class="text-sm text-muted-foreground">{{ purpose.description }}</div>
                </div>
              </div>
              <Badge variant="outline">{{ purpose.category }}</Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 数据使用范围 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Globe class="h-5 w-5" />
          <span>数据使用范围</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <div class="space-y-4">
            <Label class="text-sm font-medium">数据使用方式</Label>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div v-for="usage in dataUsageScope" :key="usage.type" class="p-3 border rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full" :class="usage.enabled ? 'bg-green-500' : 'bg-gray-300'"></div>
                    <span class="font-medium">{{ usage.type }}</span>
                  </div>
                  <Badge :variant="usage.enabled ? 'default' : 'secondary'">
                    {{ usage.enabled ? '已启用' : '未启用' }}
                  </Badge>
                </div>
                <div class="text-sm text-muted-foreground">{{ usage.description }}</div>
              </div>
            </div>
          </div>

          <!-- 数据提供对象 -->
          <div v-if="dataUsageScope.find(u => u.type === '提供' && u.enabled)" class="space-y-4">
            <Label class="text-sm font-medium">数据提供对象</Label>
            <div class="space-y-3">
              <div v-for="recipient in dataRecipients" :key="recipient.name" class="p-3 border rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <div class="font-medium">{{ recipient.name }}</div>
                    <div class="text-sm text-muted-foreground">{{ recipient.type }}</div>
                  </div>
                  <div class="space-y-2">
                    <div class="text-sm">
                      <span class="font-medium">信用代码：</span>
                      <span class="font-mono">{{ recipient.creditCode }}</span>
                    </div>
                    <div class="text-sm">
                      <span class="font-medium">资质等级：</span>
                      <Badge variant="outline">{{ recipient.qualification }}</Badge>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <div class="text-sm">
                      <span class="font-medium">提供范围：</span>
                      <span>{{ recipient.scope }}</span>
                    </div>
                    <div class="text-sm">
                      <span class="font-medium">提供期限：</span>
                      <span>{{ recipient.duration }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 技术应用信息 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Cpu class="h-5 w-5" />
          <span>地理信息安全处理技术</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-6">
          <div class="space-y-4">
            <Label class="text-sm font-medium">已应用的安全处理技术</Label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="tech in securityTechnologies" :key="tech.name" class="p-3 border rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium">{{ tech.name }}</h4>
                  <Badge :variant="tech.status === '已部署' ? 'default' : 'secondary'">
                    {{ tech.status }}
                  </Badge>
                </div>
                <div class="space-y-2 text-sm">
                  <div class="text-muted-foreground">{{ tech.description }}</div>
                  <div class="flex justify-between">
                    <span>技术来源：</span>
                    <span>{{ tech.source }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>认证状态：</span>
                    <Badge variant="outline">{{ tech.certification }}</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <Label class="text-sm font-medium">技术说明文档</Label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div v-for="doc in techDocuments" :key="doc.name" class="flex items-center justify-between p-2 border rounded">
                <div class="flex items-center space-x-2">
                  <FileText class="w-4 h-4 text-blue-500" />
                  <div>
                    <div class="text-sm">{{ doc.name }}</div>
                    <div class="text-xs text-muted-foreground">{{ doc.category }}</div>
                  </div>
                </div>
                <Button size="sm" variant="ghost" @click="viewDocument(doc)">
                  <Eye class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Activity, 
  MapPin, 
  Users, 
  Target, 
  Globe, 
  Cpu, 
  FileText, 
  Eye, 
  Check 
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

interface Enterprise {
  id: string
  name: string
  type: string
  creditCode: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  contactPerson: string
  contactPhone: string
  address: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

interface Props {
  enterprise: Enterprise
}

const props = defineProps<Props>()

// 数据处理活动列表
const processingActivities = computed(() => [
  '数据脱敏处理',
  '场景库制作与处理',
  '导航电子地图制作',
  '互联网地图服务',
  '车载导航服务',
  '位置定位服务'
])

// 活动统计
const activityStats = computed(() => ({
  regions: 15,    // 覆盖区域数量
  partners: 8,    // 合作单位数量
  purposes: 4     // 处理目的数量
}))

// 数据采集范围
const collectionScope = computed(() => ({
  type: '部分区域',
  description: '主要在试点城市指定区域内进行数据采集',
  regions: [
    '北京市朝阳区',
    '上海市浦东新区', 
    '深圳市南山区',
    '广州市天河区',
    '天津市滨海新区'
  ],
  documents: [
    { name: '数据采集区域说明.pdf', size: '2.1MB' },
    { name: '特殊区域采集授权.pdf', size: '1.5MB' },
    { name: '区域边界坐标文件.kml', size: '856KB' }
  ]
}))

// 采集路线
const collectionRoutes = computed(() => [
  {
    name: '城市环路采集',
    startPoint: '北京三环路起点',
    endPoint: '北京三环路终点',
    distance: '48.2公里',
    frequency: '每日2次',
    status: '活跃'
  },
  {
    name: '高速公路采集',
    startPoint: '京沪高速北京段',
    endPoint: '京沪高速天津段',
    distance: '137.5公里',
    frequency: '每周3次',
    status: '活跃'
  },
  {
    name: '城区道路采集',
    startPoint: '朝阳区CBD区域',
    endPoint: '海淀区中关村',
    distance: '25.8公里',
    frequency: '每日1次',
    status: '待启用'
  }
])

// 处理方式
const processingMode = computed(() => ({
  type: '合作处理',
  partners: [
    {
      name: '北京高精地图科技有限公司',
      creditCode: '91110000MA0123456X',
      cooperationType: '技术合作',
      qualification: '甲级测绘资质',
      scope: '高精度地图制作',
      duration: '2024.01.01 - 2026.12.31'
    },
    {
      name: '上海智能导航服务有限公司',
      creditCode: '91310000MA0789012Y',
      cooperationType: '数据共享',
      qualification: '乙级测绘资质',
      scope: '导航服务提供',
      duration: '2024.03.01 - 2025.02.28'
    }
  ]
}))

// 处理目的
const processingPurposes = computed(() => [
  {
    name: '数据脱敏',
    description: '对敏感地理信息进行脱敏处理',
    category: '安全处理'
  },
  {
    name: '场景库制作与处理',
    description: '构建自动驾驶场景数据库',
    category: '技术研发'
  },
  {
    name: '导航电子地图制作',
    description: '制作高精度导航电子地图',
    category: '产品开发'
  },
  {
    name: '互联网地图服务',
    description: '提供在线地图服务',
    category: '服务提供'
  }
])

// 数据使用范围
const dataUsageScope = computed(() => [
  {
    type: '出境',
    enabled: false,
    description: '数据不涉及出境传输'
  },
  {
    type: '提供',
    enabled: true,
    description: '向指定合作伙伴提供处理后数据'
  },
  {
    type: '公开',
    enabled: false,
    description: '暂不涉及数据公开发布'
  }
])

// 数据提供对象
const dataRecipients = computed(() => [
  {
    name: '国家智能网联汽车创新中心',
    type: '科研机构',
    creditCode: '91110000MA0111111A',
    qualification: '国家级研究机构',
    scope: '脱敏后测试数据',
    duration: '长期合作'
  },
  {
    name: '清华大学汽车工程系',
    type: '高等院校',
    creditCode: '91110000MA0222222B',
    qualification: '双一流高校',
    scope: '匿名化研究数据',
    duration: '2024.01-2025.12'
  }
])

// 安全处理技术
const securityTechnologies = computed(() => [
  {
    name: '多层级数据脱敏技术',
    description: '对地理坐标、车辆轨迹等敏感信息进行多层级脱敏',
    source: '自主研发',
    certification: '已认证',
    status: '已部署'
  },
  {
    name: 'VMS数据融合技术',
    description: '将车载传感器数据与地图数据进行安全融合',
    source: '合作开发',
    certification: '认证中',
    status: '已部署'
  },
  {
    name: '地理围栏技术',
    description: '基于地理位置的数据访问控制技术',
    source: '第三方技术',
    certification: '已认证',
    status: '已部署'
  },
  {
    name: '差分隐私技术',
    description: '保护个体隐私的数据发布技术',
    source: '自主研发',
    certification: '已认证',
    status: '测试中'
  }
])

// 技术文档
const techDocuments = computed(() => [
  { name: '数据脱敏技术规范.pdf', category: '技术规范' },
  { name: 'VMS融合算法说明.docx', category: '算法文档' },
  { name: '地理围栏配置手册.pdf', category: '配置文档' },
  { name: '隐私保护技术评估.pdf', category: '评估报告' },
  { name: '技术认证证书.pdf', category: '认证文件' },
  { name: '技术专利申请.pdf', category: '知识产权' }
])

// 方法
const viewDocument = (doc: any) => {
  console.log('查看文档:', doc.name)
  // 这里实现查看文档的逻辑
}
</script>