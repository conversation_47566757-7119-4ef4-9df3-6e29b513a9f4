name: Deploy to CloudBase (Static Hosting)

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: Cloudbase
    timeout-minutes: 30

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: |
            gsm-ui-demo/package-lock.json

      - name: Install dependencies
        working-directory: gsm-ui-demo
        run: npm ci

      - name: Build
        working-directory: gsm-ui-demo
        run: npm run build

      # 使用 CloudBase 官方 Action 部署静态网站
      - name: Deploy to CloudBase Hosting
        uses: TencentCloudBase/cloudbase-action@v2
        with:
          envId: ${{ secrets.TCB_ENVID }}
          secretId: ${{ secrets.TENCENT_SECRET_ID }}
          secretKey: ${{ secrets.TENCENT_SECRET_KEY }}

      - name: Print website url
        run: |
          echo "Deployed to CloudBase Hosting"
          echo "Check the environment console for the site URL."

