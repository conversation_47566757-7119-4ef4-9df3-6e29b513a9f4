name: Deploy to CloudBase (Static Hosting)

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: Cloudbase
    timeout-minutes: 30

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: |
            gsm-ui-demo/package-lock.json

      - name: Install dependencies
        working-directory: gsm-ui-demo
        run: npm ci

      - name: Build
        working-directory: gsm-ui-demo
        run: npm run build

      - name: Install CloudBase CLI
        run: npm i -g @cloudbase/cli

      - name: Deploy to CloudBase (Framework)
        env:
          TCB_ENVID: ${{ secrets.TCB_ENVID }}
          TENCENT_SECRET_ID: ${{ secrets.TENCENT_SECRET_ID }}
          TENCENT_SECRET_KEY: ${{ secrets.TENCENT_SECRET_KEY }}
        working-directory: gsm-ui-demo
        run: |
          tcb login --key "$TENCENT_SECRET_ID" --secret "$TENCENT_SECRET_KEY"
          tcb framework deploy

      - name: Print website url
        run: |
          echo "Deployed to CloudBase Hosting"
          echo "Check the environment console for the site URL."

