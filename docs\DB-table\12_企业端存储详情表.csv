﻿表名: log_ent_store_detail,,,,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment),,,
1,id,主键ID,BIGINT,,○,●,自增主键,,,
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id""",,,
3,data_importance_level,数据重要程度,SMALLINT,,○,,"""(BYTE) 0x01:一般数据; 0x02:重要数据""",,,
4,storage_device_type,存储设备类型,SMALLINT,,○,,"""(BYTE) 0x01:服务器; 0x02:存储阵列; 0x03:网络存储; 0x04:硬盘; 0x05:其他""",,,
5,storage_region_flag,存储区域标识,SMALLINT,,○,,"""(BYTE) 0x01:境内存储; 0x02:境外存储""",,,
6,storage_device_ip,存储设备IP,VARCHAR,45,,,用于识别是在境内还是境外,,,
7,storage_area_type,存储区类型,SMALLINT,,○,,"""(BYTE) 0x01:原始数据处理区; 0x02:数据共享区; 0x03:数据中转区; 0x04:其他区域""",,,
8,data_partition_flag,数据分区标识,SMALLINT,,○,,"""(BYTE) 0x01:已分区存储; 0x02:未分区存储""",,,
9,storage_safeguard_bitmap,存储保障标识,SMALLINT,,○,,"""(BYTE) BitMap。Bit0:完整性", Bit1:真实性, Bit2:可用性 (1=保障, 0=未保障)"
10,desensitization_flag,脱敏处理标识,SMALLINT,,○,,"""(BYTE) 0x01:已脱敏; 0x02:未脱敏""",,,
11,operator_identity,操作员身份,SMALLINT,,○,,"""(BYTE) 0x01:重要数据操作人员; 0x02:一般数据操作人员""",,,
