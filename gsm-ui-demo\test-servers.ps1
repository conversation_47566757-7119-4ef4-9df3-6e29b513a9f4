# 基本功能测试脚本 (PowerShell)
# 测试开发服务器和预览服务器是否正常运行

Write-Host "🧪 开始基本功能测试..." -ForegroundColor Green
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Gray

# 测试配置
$DevServer = "http://localhost:5174"
$PreviewServer = "http://localhost:4173"

# 测试路径
$TestPaths = @(
    @{ Name = "首页"; Path = "/" },
    @{ Name = "登录页面"; Path = "/login" },
    @{ Name = "政府登录"; Path = "/login/government" },
    @{ Name = "企业登录"; Path = "/login/enterprise" }
)

# 测试单个端点
function Test-Endpoint {
    param(
        [string]$Server,
        [hashtable]$TestCase
    )
    
    $Url = "$Server$($TestCase.Path)"
    
    try {
        Write-Host "测试: $($TestCase.Name) ($Url)" -ForegroundColor Cyan
        
        $Response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -ErrorAction Stop
        
        if ($Response.StatusCode -eq 200) {
            Write-Host "✅ $($TestCase.Name): 成功 (状态码: $($Response.StatusCode))" -ForegroundColor Green
            
            # 检查是否返回 HTML 内容
            if ($Response.Content -match "<!DOCTYPE html|<html") {
                Write-Host "   📄 返回了有效的 HTML 内容" -ForegroundColor Gray
            }
            
            return $true
        } else {
            Write-Host "❌ $($TestCase.Name): 失败 (状态码: $($Response.StatusCode))" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $($TestCase.Name): 错误 - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 测试服务器
function Test-Server {
    param(
        [string]$ServerUrl,
        [string]$ServerName
    )
    
    Write-Host "`n🚀 测试 $ServerName ($ServerUrl)" -ForegroundColor Yellow
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    $PassedTests = 0
    $TotalTests = $TestPaths.Count
    
    foreach ($TestCase in $TestPaths) {
        $Success = Test-Endpoint -Server $ServerUrl -TestCase $TestCase
        if ($Success) { $PassedTests++ }
        
        # 添加小延迟
        Start-Sleep -Milliseconds 200
    }
    
    Write-Host "`n📊 $ServerName 测试结果: $PassedTests/$TotalTests 通过" -ForegroundColor Magenta
    
    if ($PassedTests -eq $TotalTests) {
        Write-Host "🎉 $ServerName 所有测试通过！" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠️  $ServerName 有 $($TotalTests - $PassedTests) 个测试失败" -ForegroundColor Yellow
        return $false
    }
}

# 主测试流程
try {
    # 测试开发服务器
    $DevSuccess = Test-Server -ServerUrl $DevServer -ServerName "开发服务器"
    
    # 测试预览服务器
    $PreviewSuccess = Test-Server -ServerUrl $PreviewServer -ServerName "预览服务器"
    
    # 总结
    Write-Host "`n$("=" * 60)" -ForegroundColor Gray
    Write-Host "📋 测试总结:" -ForegroundColor Magenta
    Write-Host "   开发服务器: $(if ($DevSuccess) { "✅ 通过" } else { "❌ 失败" })" -ForegroundColor $(if ($DevSuccess) { "Green" } else { "Red" })
    Write-Host "   预览服务器: $(if ($PreviewSuccess) { "✅ 通过" } else { "❌ 失败" })" -ForegroundColor $(if ($PreviewSuccess) { "Green" } else { "Red" })
    
    if ($DevSuccess -and $PreviewSuccess) {
        Write-Host "`n🎊 所有测试通过！应用程序运行正常。" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "`n⚠️  部分测试失败，请检查服务器状态。" -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Host "❌ 测试过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
