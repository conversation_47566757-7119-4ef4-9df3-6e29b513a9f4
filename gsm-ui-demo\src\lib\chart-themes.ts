/**
 * 暗色系图表主题配置
 * 采用暗色调深浅多层次色系，避免过于鲜艳刺眼的颜色
 */

// 新的暗色调配色系统
export const DARK_COLORS = {
  // 暗蓝色系 - 主色调
  darkBlue1: '#1e3a5f', // 深海蓝
  darkBlue2: '#2c5282', // 钴蓝
  darkBlue3: '#3e6b9b', // 天青蓝
  darkBlue4: '#4f7fa6', // 雾蓝

  // 暗红色系 - 风险/警告
  darkRed1: '#7f1d1d', // 深酒红
  darkRed2: '#991b1b', // 暗红
  darkRed3: '#b91c1c', // 赤红
  darkRed4: '#dc2626', // 朱红

  // 暗绿色系 - 成功/安全
  darkGreen1: '#14532d', // 深森林绿
  darkGreen2: '#166534', // 暗绿
  darkGreen3: '#15803d', // 翠绿
  darkGreen4: '#16a34a', // 草绿

  // 暗橙色系 - 提醒/注意
  darkOrange1: '#7c2d12', // 深褐橙
  darkOrange2: '#9a3412', // 暗橙
  darkOrange3: '#c2410c', // 赤橙
  darkOrange4: '#ea580c', // 橘橙

  // 暗紫色系 - 特殊/重要
  darkPurple1: '#581c87', // 深紫
  darkPurple2: '#6b21a8', // 暗紫
  darkPurple3: '#7c3aed', // 蓝紫
  darkPurple4: '#8b5cf6', // 薰衣草紫

  // 暗灰色系 - 中性/辅助
  darkGrey1: '#1f2937', // 深灰
  darkGrey2: '#374151', // 暗灰
  darkGrey3: '#4b5563', // 中灰
  darkGrey4: '#6b7280', // 浅灰

  // 状态色 - 暗色调版本
  success: '#15803d', // 暗绿
  warning: '#c2410c', // 暗橙
  danger: '#991b1b', // 暗红
  info: '#2c5282', // 暗蓝
} as const

// 保留旧的蓝色系作为备用（已弃用）
export const BLUE_COLORS = DARK_COLORS

// 图表配色方案 - 多主题色彩系统
export const CHART_COLOR_SCHEMES = {
  // 主要配色方案 - Ocean Depths 深海主题 (系统默认主题，优化浅色版本)
  primary: [
    '#4a90e2', // 优化深海蓝 (主色，更浅)
    '#5ba3f5', // 优化海洋蓝（更浅）
    '#d1e7ff', // 优化浅蓝（更浅）
    '#6b7c93', // 优化深灰蓝（更浅）
    '#66bb6a', // 优化海绿（更浅）
    '#4dd0e1', // 优化浅海绿（更浅）
    '#7bb3d3', // 优化钢蓝（更浅）
    '#81c784', // 优化青灰（更浅）
  ],

  // Ocean Depths - 深海主题（优化浅色高对比度版本）
  oceanDepths: [
    '#4a90e2', // 优化深海蓝（更浅）
    '#5ba3f5', // 优化海洋蓝（更浅）
    '#d1e7ff', // 优化浅蓝（更浅）
    '#6b7c93', // 优化深灰蓝（更浅）
    '#66bb6a', // 优化海绿（更浅）
    '#4dd0e1', // 优化浅海绿（更浅）
    '#7bb3d3', // 优化钢蓝（更浅）
    '#81c784', // 优化青灰（更浅）
  ],

  // Sunset Warmth - 暖夕阳主题
  sunsetWarmth: [
    '#ff6b6b', // 珊瑚红
    '#ffa726', // 橙色
    '#ffcc02', // 金黄
    '#ff8a65', // 桃色
    '#ab47bc', // 紫色
    '#ec407a', // 粉红
    '#ff7043', // 深橙
    '#ffb74d', // 浅橙
  ],

  // Forest Grove - 森林主题
  forestGrove: [
    '#2d5016', // 深森林绿
    '#4a7c59', // 森林绿
    '#6a994e', // 橄榄绿
    '#a7c957', // 浅绿
    '#386641', // 暗绿
    '#6a4c93', // 森林紫
    '#bc6c25', // 棕橙
    '#dda15e', // 浅棕
  ],

  // Modern Corporate - 现代商务主题
  modernCorporate: [
    '#264653', // 深青
    '#2a9d8f', // 青绿
    '#e9c46a', // 金黄
    '#f4a261', // 橙黄
    '#e76f51', // 橙红
    '#6c757d', // 中性灰
    '#495057', // 深灰
    '#adb5bd', // 浅灰
  ],

  // Pastel Dreams - 柔和梦幻主题
  pastelDreams: [
    '#a8dadc', // 柔和青
    '#457b9d', // 中蓝
    '#f1faee', // 象牙白
    '#e63946', // 红色
    '#1d3557', // 深蓝
    '#ff9f1c', // 橙色
    '#2ec4b6', // 青绿
    '#cbf3f0', // 淡青
  ],

  // Neon Tech - 科技霓虹主题
  neonTech: [
    '#08fdd8', // 霓虹青
    '#fd1593', // 霓虹粉
    '#09fd02', // 霓虹绿
    '#fed102', // 霓虹黄
    '#fe9801', // 霓虹橙
    '#8b00ff', // 霓虹紫
    '#00d2ff', // 霓虹蓝
    '#ff0080', // 霓虹红
  ],

  // Vintage Earth - 复古大地主题
  vintageEarth: [
    '#8b4513', // 褐色
    '#a0522d', // 赤褐色
    '#cd853f', // 秘鲁色
    '#d2691e', // 橙红色
    '#bc8f8f', // 玫瑰褐
    '#f4a460', // 沙褐色
    '#daa520', // 金麒麟色
    '#b22222', // 火砖色
  ],

  // Arctic Aurora - 极光主题
  arcticAurora: [
    '#4c956c', // 极光绿
    '#2f3e46', // 深灰绿
    '#52b788', // 亮绿
    '#74c69d', // 中绿
    '#95d5b2', // 浅绿
    '#b7e4c7', // 极浅绿
    '#d8f3dc', // 淡绿
    '#40916c', // 深绿
  ],

  // Adobe Color Palettes - 来自Adobe Color的专业配色
  // Floral Palette - 花卉主题
  floralPalette: [
    '#023059', // 深蓝
    '#03658C', // 海蓝
    '#F29F05', // 金黄
    '#F28705', // 橙色
    '#F23005', // 红橙
    '#1a4d72', // 中蓝
    '#4a8bb8', // 浅蓝
    '#f5b935', // 浅黄
  ],

  // Summer Afternoon - 夏日午后
  summerAfternoon: [
    '#0CE87C', // 翠绿
    '#17FF0B', // 亮绿
    '#10F1FF', // 青色
    '#0366E8', // 蓝色
    '#1904FF', // 深蓝紫
    '#08c968', // 中绿
    '#0dd9eb', // 浅青
    '#355fe8', // 中蓝
  ],

  // Retro Futuristic - 复古未来
  retroFuturistic: [
    '#020659', // 深紫蓝
    '#010B40', // 深蓝
    '#03738C', // 青蓝
    '#05F2F2', // 霓虹青
    '#F2E205', // 霓虹黄
    '#1a1a6b', // 中紫蓝
    '#2a9db8', // 中青蓝
    '#d4c004', // 中黄
  ],

  // Colors of Resilience - 韧性色彩
  resilience: [
    '#1D2A73', // 深蓝紫
    '#8FADBF', // 蓝灰
    '#31403C', // 深绿灰
    '#D9734E', // 橙褐
    '#BFA59B', // 浅褐
    '#2e4087', // 中蓝紫
    '#5f7a8a', // 中蓝灰
    '#c48962', // 中橙褐
  ],

  // 风险等级配色 - 与“企业注册状态”环状图配色一致（深海主题主配色）
  risk: [
    '#0f4c75', // 深海蓝
    '#3282b8', // 海洋蓝
    '#bbe1fa', // 浅蓝
    '#1b262c', // 深灰蓝
    '#2e8b57', // 海绿
    '#20b2aa', // 浅海绿
    '#4682b4', // 钢蓝
    '#5f9ea0', // 青灰
  ],

  // 企业类型配色 - Ocean Depths 深海主题企业色
  enterprise: [
    '#0f4c75', // 整车生产企业 - 深海蓝
    '#3282b8', // 平台运营商 - 海洋蓝
    '#2e8b57', // 智驾方案提供商 - 海绿
    '#20b2aa', // 地图服务商 - 浅海绿
    '#5f9ea0', // 其他 - 青灰
  ],

  // 处理阶段配色 (7阶段) - Ocean Depths 深海主题阶段色
  stages: [
    '#0f4c75', // 收集 - 深海蓝
    '#3282b8', // 存储 - 海洋蓝
    '#4682b4', // 传输 - 钢蓝
    '#2e8b57', // 加工 - 海绿
    '#20b2aa', // 提供 - 浅海绿
    '#ffa500', // 公开 - 橙色
    '#5f9ea0', // 销毁 - 青灰
  ],

  // 状态配色 - 清新深海主题状态色
  status: [
    '#2ecc71', // 正常/已完成 - 清新绿色
    '#f39c12', // 警告/处理中 - 清新橙色
    '#e74c3c', // 异常/失败 - 清新红色
    '#3498db', // 信息/待处理 - 清新蓝色
    '#95a5a6', // 未知/其他 - 清新灰色
  ],

  // 渐变色配置 - 用于面积图等特殊场景
  gradients: {
    blue1: [DARK_COLORS.darkBlue1, DARK_COLORS.darkBlue3],
    blue2: [DARK_COLORS.darkBlue2, DARK_COLORS.darkBlue4],
    blue3: [DARK_COLORS.darkGreen1, DARK_COLORS.darkGreen3],
    blue4: [DARK_COLORS.darkPurple1, DARK_COLORS.darkPurple3],
    success: [DARK_COLORS.darkGreen1, DARK_COLORS.darkGreen3],
    risk: [DARK_COLORS.darkRed1, DARK_COLORS.darkRed3],
    warning: [DARK_COLORS.darkOrange1, DARK_COLORS.darkOrange3],
  },
} as const

// 旧的MTR色系保留作为备用
export const MTR_COLORS = {
  darkRed: '#8B1538',
  red: '#A91B47',
  lightRed: '#C22356',
  darkPurple: '#4A1A5C',
  purple: '#5D2375',
  lightPurple: '#702C8E',
  darkOrange: '#B85B00',
  orange: '#CC6600',
  lightOrange: '#E57300',
  darkGrey: '#2D3748',
  grey: '#4A5568',
  lightGrey: '#718096',
  success: '#2F855A',
  warning: '#D69E2E',
  danger: '#E53E3E',
  info: '#3182CE',
} as const

// 深浅主题适配
export const getThemeColors = (isDark: boolean) => ({
  background: isDark ? '#1a202c' : '#ffffff',
  surface: isDark ? '#2d3748' : '#f7fafc',
  text: {
    primary: isDark ? '#e2e8f0' : '#2d3748',
    secondary: isDark ? '#a0aec0' : '#4a5568',
    muted: isDark ? '#718096' : '#718096',
  },
  border: isDark ? '#4a5568' : '#e2e8f0',
  grid: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
})

// ECharts 全局主题配置
export const createEChartsTheme = (isDark: boolean) => {
  const themeColors = getThemeColors(isDark)

  return {
    // 背景色
    backgroundColor: 'transparent',

    // 全局文本样式
    textStyle: {
      fontFamily:
        'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      color: themeColors.text.primary,
      fontSize: 12,
    },

    // 标题样式
    title: {
      textStyle: {
        color: themeColors.text.primary,
        fontSize: 16,
        fontWeight: '600',
      },
    },

    // 图例样式
    legend: {
      textStyle: {
        color: themeColors.text.secondary,
        fontSize: 12,
      },
      itemGap: 16,
      itemWidth: 14,
      itemHeight: 14,
    },

    // 提示框样式
    tooltip: {
      backgroundColor: isDark ? 'rgba(26, 32, 44, 0.95)' : 'rgba(255, 255, 255, 0.95)',
      borderColor: themeColors.border,
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: themeColors.text.primary,
        fontSize: 12,
      },
      shadowBlur: 16,
      shadowColor: isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.1)',
      padding: [8, 12],
      extraCssText:
        'box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);',
    },

    // 坐标轴样式
    categoryAxis: {
      axisLine: {
        lineStyle: {
          color: themeColors.border,
        },
      },
      axisTick: {
        lineStyle: {
          color: themeColors.border,
        },
      },
      axisLabel: {
        color: themeColors.text.secondary,
      },
      splitLine: {
        lineStyle: {
          color: themeColors.grid,
        },
      },
    },

    valueAxis: {
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: themeColors.text.secondary,
      },
      splitLine: {
        lineStyle: {
          color: themeColors.grid,
        },
      },
    },

    // 默认配色 - 使用 Ocean Depths 深海主题
    color: CHART_COLOR_SCHEMES.oceanDepths,
  }
}

// 动画配置
export const CHART_ANIMATIONS = {
  // 入场动画
  animationDuration: 800,
  animationEasing: 'cubicOut',
  animationDelay: (idx: number) => idx * 100,

  // 更新动画
  animationDurationUpdate: 400,
  animationEasingUpdate: 'cubicOut',

  // 悬浮动画
  hoverAnimation: true,

  // 缩放动画
  animationThreshold: 2000,
} as const

// 获取渐变色配置
export const createLinearGradient = (
  colorKey: keyof typeof CHART_COLOR_SCHEMES.gradients,
  direction: 'vertical' | 'horizontal' = 'vertical',
) => {
  const colors = CHART_COLOR_SCHEMES.gradients[colorKey]

  return {
    type: 'linear',
    x: 0,
    y: direction === 'vertical' ? 0 : 0,
    x2: direction === 'horizontal' ? 1 : 0,
    y2: direction === 'vertical' ? 1 : 0,
    colorStops: [
      { offset: 0, color: colors[0] + 'CC' }, // 添加透明度
      { offset: 1, color: colors[1] + '33' }, // 渐变到更透明
    ],
  }
}

// 悬浮提示格式化函数
export const createTooltipFormatter = (type: 'pie' | 'bar' | 'line') => {
  switch (type) {
    case 'pie':
      return (params: any) => {
        if (Array.isArray(params)) {
          return params
            .map(
              (p) =>
                `<div style="display: flex; align-items: center; margin-bottom: 4px;">
              <div style="width: 12px; height: 12px; background: ${p.color}; border-radius: 2px; margin-right: 8px;"></div>
              <span style="flex: 1;">${p.seriesName}</span>
              <span style="font-weight: bold; margin-left: 8px;">${p.value} (${p.percent}%)</span>
            </div>`,
            )
            .join('')
        } else {
          return `<div style="display: flex; align-items: center;">
            <div style="width: 12px; height: 12px; background: ${params.color}; border-radius: 2px; margin-right: 8px;"></div>
            <span style="flex: 1;">${params.name}</span>
            <span style="font-weight: bold; margin-left: 8px;">${params.value} (${params.percent}%)</span>
          </div>`
        }
      }

    case 'bar':
      return (params: any) => {
        if (Array.isArray(params)) {
          const header = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValueLabel || params[0].name}</div>`
          const items = params
            .map(
              (p) =>
                `<div style="display: flex; align-items: center; margin-bottom: 4px;">
              <div style="width: 12px; height: 12px; background: ${p.color}; border-radius: 2px; margin-right: 8px;"></div>
              <span style="flex: 1;">${p.seriesName}</span>
              <span style="font-weight: bold; margin-left: 8px;">${p.value}</span>
            </div>`,
            )
            .join('')
          return header + items
        } else {
          return `<div style="display: flex; align-items: center;">
            <div style="width: 12px; height: 12px; background: ${params.color}; border-radius: 2px; margin-right: 8px;"></div>
            <span style="flex: 1;">${params.name}</span>
            <span style="font-weight: bold; margin-left: 8px;">${params.value}</span>
          </div>`
        }
      }

    case 'line':
      return (params: any) => {
        if (Array.isArray(params)) {
          const header = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValueLabel}</div>`
          const items = params
            .map(
              (p) =>
                `<div style="display: flex; align-items: center; margin-bottom: 4px;">
              <div style="width: 12px; height: 3px; background: ${p.color}; border-radius: 1px; margin-right: 8px;"></div>
              <span style="flex: 1;">${p.seriesName}</span>
              <span style="font-weight: bold; margin-left: 8px;">${p.value}</span>
            </div>`,
            )
            .join('')
          return header + items
        } else {
          return `<div style="display: flex; align-items: center;">
            <div style="width: 12px; height: 3px; background: ${params.color}; border-radius: 1px; margin-right: 8px;"></div>
            <span style="flex: 1;">${params.seriesName}</span>
            <span style="font-weight: bold; margin-left: 8px;">${params.value}</span>
          </div>`
        }
      }

    default:
      return undefined
  }
}
