<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          企业车端与云端安全事件统计分析，支持事件类型分布、处置状态跟踪与详情查看
        </p>
      </div>
    </div>

    <!-- Tab 切换 -->
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="vehicle" class="flex items-center gap-2">
          <Car class="w-4 h-4" />
          车端事件
        </TabsTrigger>
        <TabsTrigger value="cloud" class="flex items-center gap-2">
          <Cloud class="w-4 h-4" />
          云端事件
        </TabsTrigger>
      </TabsList>

      <!-- 车端事件 Tab -->
      <TabsContent value="vehicle" class="space-y-6">
        <!-- 统计卡片区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 总事件数 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">总事件数</CardTitle>
              <AlertCircle class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">{{ vehicleEventItems.length }}</div>
              <p class="text-xs text-muted-foreground">累计事件记录</p>
            </CardContent>
          </Card>

          <!-- 今日新增 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">今日新增</CardTitle>
              <TrendingUp class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-blue-600">
                {{ vehicleTodayEvents }}
              </div>
              <p class="text-xs text-muted-foreground">24小时内新增</p>
            </CardContent>
          </Card>

          <!-- 系统事件 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">系统事件</CardTitle>
              <Settings class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-orange-600">
                {{ vehicleEventItems.filter((e) => e.eventType === 1).length }}
              </div>
              <p class="text-xs text-muted-foreground">系统升级事件</p>
            </CardContent>
          </Card>

          <!-- 异常告警 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">异常告警</CardTitle>
              <AlertTriangle class="h-8 w-8 text-destructive" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-red-600">
                {{ vehicleEventItems.filter((e) => e.eventType === 3).length }}
              </div>
              <p class="text-xs text-muted-foreground">需要关注处理</p>
            </CardContent>
          </Card>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 事件类型分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">事件类型分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <PieChart
                  :data="vehicleEventTypeData"
                  :height="200"
                  chart-type="doughnut"
                  :show-legend="true"
                  color-scheme="eventTypes"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 来源模块分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">来源模块分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <BarChart
                  :data="vehicleSourceModuleData"
                  :height="200"
                  :show-values="true"
                  color-scheme="primary"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 时间趋势 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">7日事件趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <LineChart
                  :series="vehicleEventTrendData"
                  :height="200"
                  :smooth="true"
                  color-scheme="primary"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 事件列表 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>车端事件列表</span>
              <Badge variant="outline">共 {{ filteredVehicleEvents.length }} 条记录</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <!-- 筛选条件 -->
            <CompactFilterForm
              :filter-fields="vehicleFilterFields"
              :show-export="true"
              :initial-values="vehicleFilters"
              @search="handleVehicleSearch"
              @reset="resetVehicleFilters"
              @export="exportVehicleData"
            />

            <div class="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[60px]">序号</TableHead>
                    <TableHead>事件ID</TableHead>
                    <TableHead>事件类型</TableHead>
                    <TableHead>来源模块</TableHead>
                    <TableHead>事件描述</TableHead>
                    <TableHead>关联ID</TableHead>
                    <TableHead>事件时间</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead class="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="pagedVehicleEvents.length === 0">
                    <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                  <TableRow
                    v-for="(item, index) in pagedVehicleEvents"
                    :key="item.id"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{
                      (vehicleCurrentPage - 1) * vehiclePageSize + index + 1
                    }}</TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.eventId }}</span>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="getEventTypeVariant(item.eventType)">
                        {{ getEventTypeLabel(item.eventType) }}
                      </Badge>
                    </TableCell>
                    <TableCell>{{ item.sourceModule }}</TableCell>
                    <TableCell class="max-w-[200px]">
                      <div class="text-sm line-clamp-2" :title="item.eventDescription">
                        {{ item.eventDescription }}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.relatedId || '-' }}</span>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">
                      {{ formatTimestamp(item.eventTimestamp) }}
                    </TableCell>
                    <TableCell class="whitespace-nowrap">
                      {{ formatDate(item.createTime) }}
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" @click="viewEventDetail(item)">
                        详情
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <!-- 分页：统一 PaginationBar -->
            <PaginationBar
              v-model:page="vehicleCurrentPage"
              v-model:pageSize="vehiclePageSize"
              :total="filteredVehicleEvents.length"
            />
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 云端事件 Tab -->
      <TabsContent value="cloud" class="space-y-6">
        <!-- 统计卡片区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 总事件数 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">总事件数</CardTitle>
              <AlertCircle class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">{{ cloudEventItems.length }}</div>
              <p class="text-xs text-muted-foreground">累计事件记录</p>
            </CardContent>
          </Card>

          <!-- 今日新增 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">今日新增</CardTitle>
              <TrendingUp class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-blue-600">
                {{ cloudTodayEvents }}
              </div>
              <p class="text-xs text-muted-foreground">24小时内新增</p>
            </CardContent>
          </Card>

          <!-- 系统事件 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">系统事件</CardTitle>
              <Settings class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-orange-600">
                {{ cloudEventItems.filter((e) => e.eventType === 1).length }}
              </div>
              <p class="text-xs text-muted-foreground">系统升级事件</p>
            </CardContent>
          </Card>

          <!-- 异常告警 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">异常告警</CardTitle>
              <AlertTriangle class="h-8 w-8 text-destructive" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-red-600">
                {{ cloudEventItems.filter((e) => e.eventType === 3).length }}
              </div>
              <p class="text-xs text-muted-foreground">需要关注处理</p>
            </CardContent>
          </Card>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 事件类型分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">事件类型分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <PieChart
                  :data="cloudEventTypeData"
                  :height="200"
                  chart-type="doughnut"
                  :show-legend="true"
                  color-scheme="eventTypes"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 来源模块分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">来源模块分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <BarChart
                  :data="cloudSourceModuleData"
                  :height="200"
                  :show-values="true"
                  color-scheme="primary"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 时间趋势 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">7日事件趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <LineChart
                  :series="cloudEventTrendData"
                  :height="200"
                  :smooth="true"
                  color-scheme="primary"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 事件列表 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>云端事件列表</span>
              <Badge variant="outline">共 {{ filteredCloudEvents.length }} 条记录</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <!-- 筛选条件 -->
            <CompactFilterForm
              :filter-fields="cloudFilterFields"
              :show-export="true"
              :initial-values="cloudFilters"
              @search="handleCloudSearch"
              @reset="resetCloudFilters"
              @export="exportCloudData"
            />

            <div class="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[60px]">序号</TableHead>
                    <TableHead>事件ID</TableHead>
                    <TableHead>事件类型</TableHead>
                    <TableHead>来源模块</TableHead>
                    <TableHead>事件描述</TableHead>
                    <TableHead>关联ID</TableHead>
                    <TableHead>事件时间</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead class="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="pagedCloudEvents.length === 0">
                    <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                  <TableRow
                    v-for="(item, index) in pagedCloudEvents"
                    :key="item.id"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{ (cloudCurrentPage - 1) * cloudPageSize + index + 1 }}</TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.eventId }}</span>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="getEventTypeVariant(item.eventType)">
                        {{ getEventTypeLabel(item.eventType) }}
                      </Badge>
                    </TableCell>
                    <TableCell>{{ item.sourceModule }}</TableCell>
                    <TableCell class="max-w-[200px]">
                      <div class="text-sm line-clamp-2" :title="item.eventDescription">
                        {{ item.eventDescription }}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.relatedId || '-' }}</span>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">
                      {{ formatTimestamp(item.eventTimestamp) }}
                    </TableCell>
                    <TableCell class="whitespace-nowrap">
                      {{ formatDate(item.createTime) }}
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" @click="viewEventDetail(item)">
                        详情
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <!-- 分页：统一 PaginationBar -->
            <PaginationBar
              v-model:page="cloudCurrentPage"
              v-model:pageSize="cloudPageSize"
              :total="filteredCloudEvents.length"
            />
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>

    <!-- 事件详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>事件详细信息</DialogTitle>
          <DialogDescription> 事件ID: {{ selectedEvent?.eventId }} </DialogDescription>
        </DialogHeader>

        <div v-if="selectedEvent" class="space-y-4">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-muted-foreground">事件ID</div>
              <div class="font-medium">{{ selectedEvent.eventId }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">事件类型</div>
              <div class="font-medium">{{ getEventTypeLabel(selectedEvent.eventType) }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">来源模块</div>
              <div class="font-medium">{{ selectedEvent.sourceModule }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">关联ID</div>
              <div class="font-medium">{{ selectedEvent.relatedId || '-' }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">事件时间</div>
              <div class="font-medium">{{ formatTimestamp(selectedEvent.eventTimestamp) }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">创建时间</div>
              <div class="font-medium">{{ formatDate(selectedEvent.createTime) }}</div>
            </div>
          </div>

          <!-- 事件描述 -->
          <div>
            <div class="text-sm text-muted-foreground">事件描述</div>
            <div class="mt-1 p-3 bg-muted rounded-md">
              {{ selectedEvent.eventDescription }}
            </div>
          </div>

          <div class="flex items-center justify-end gap-2">
            <Button variant="outline" @click="detailOpen = false">关闭</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { AlertCircle, AlertTriangle, Car, Cloud, TrendingUp, Settings } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { PaginationBar } from '@/components/ui/pagination'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PieChart, BarChart, LineChart } from '@/components/charts'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

// 企业事件统计数据类型定义
interface EventRecord {
  id: number
  eventId: string
  eventType: number // 1-系统升级, 2-配置变更, 3-异常告警
  eventDescription: string
  eventTimestamp: number
  sourceModule: string
  relatedId?: string
  createTime: string
}

// 活动Tab
const activeTab = ref('vehicle')

// 弹窗状态
const detailOpen = ref(false)
const selectedEvent = ref<EventRecord | null>(null)

// 筛选条件
const vehicleFilters = ref({
  eventId: '',
  eventType: 0,
  sourceModule: '',
  timeRange: null as [Date, Date] | null,
})

const cloudFilters = ref({
  eventId: '',
  eventType: 0,
  sourceModule: '',
  timeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const vehicleFilterFields: FilterField[] = [
  {
    key: 'eventId',
    label: '事件ID',
    type: 'input',
    placeholder: '输入事件ID',
  },
  {
    key: 'eventType',
    label: '事件类型',
    type: 'select',
    placeholder: '选择类型',
    options: [
      { label: '全部', value: '0' },
      { label: '系统升级', value: '1' },
      { label: '配置变更', value: '2' },
      { label: '异常告警', value: '3' },
    ],
  },
  {
    key: 'sourceModule',
    label: '来源模块',
    type: 'input',
    placeholder: '输入模块名称',
  },
  {
    key: 'timeRange',
    label: '事件时间',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

const cloudFilterFields = vehicleFilterFields

// 生成Mock数据
const generateMockEvents = (count: number, type: 'vehicle' | 'cloud'): EventRecord[] => {
  const events: EventRecord[] = []
  const now = Date.now()

  const eventTypes = [1, 2, 3]
  const sourceModules = {
    vehicle: ['车端采集模块', '车端传输模块', '车端存储模块', '车端控制模块', '车端安全模块'],
    cloud: ['云端接收模块', '云端存储模块', '云端处理模块', '云端分析模块', '云端监控模块'],
  }

  const eventDescriptions = {
    1: [
      '系统版本升级到v2.3.5，增强了数据加密功能',
      '固件升级完成，修复了已知的安全漏洞',
      '核心模块更新，提升了数据处理效率',
      '安全补丁安装完成，加强了访问控制',
    ],
    2: [
      '数据采集频率调整为每5分钟一次',
      '更新了地理围栏配置参数',
      '修改了数据传输加密算法',
      '调整了存储策略，优化存储空间使用',
    ],
    3: [
      '检测到异常数据访问行为，已记录并告警',
      '数据传输异常中断，正在重试',
      '存储空间使用率超过90%，需要清理',
      '发现未授权的配置修改尝试',
    ],
  }

  const modules = sourceModules[type]

  for (let i = 1; i <= count; i++) {
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    const descriptions = eventDescriptions[eventType as keyof typeof eventDescriptions]

    const event: EventRecord = {
      id: i,
      eventId: `${type === 'vehicle' ? 'VE' : 'CE'}-2025-${String(i).padStart(4, '0')}`,
      eventType: eventType,
      eventDescription: descriptions[Math.floor(Math.random() * descriptions.length)],
      eventTimestamp: now - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000),
      sourceModule: modules[Math.floor(Math.random() * modules.length)],
      relatedId: Math.random() > 0.5 ? `REL-${Math.floor(Math.random() * 9999)}` : undefined,
      createTime: new Date(
        now - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000),
      ).toISOString(),
    }

    events.push(event)
  }

  return events
}

// Mock数据
const vehicleEventItems = ref<EventRecord[]>(generateMockEvents(38, 'vehicle'))
const cloudEventItems = ref<EventRecord[]>(generateMockEvents(42, 'cloud'))

// 今日新增
const vehicleTodayEvents = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return vehicleEventItems.value.filter((e) => e.eventTimestamp >= today.getTime()).length
})

const cloudTodayEvents = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return cloudEventItems.value.filter((e) => e.eventTimestamp >= today.getTime()).length
})

// 车端统计数据
const vehicleEventTypeData = computed(() => {
  const typeMap: Record<number, string> = {
    1: '系统升级',
    2: '配置变更',
    3: '异常告警',
  }
  const counts: Record<string, number> = {}

  vehicleEventItems.value.forEach((item) => {
    const label = typeMap[item.eventType]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const vehicleSourceModuleData = computed(() => {
  const counts: Record<string, number> = {}

  vehicleEventItems.value.forEach((item) => {
    counts[item.sourceModule] = (counts[item.sourceModule] || 0) + 1
  })

  return Object.entries(counts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([name, value]) => ({ name, value }))
})

const vehicleEventTrendData = computed(() => {
  const days = 7
  const data: { name: string; value: number }[] = []
  const now = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    date.setHours(0, 0, 0, 0)
    const nextDate = new Date(date)
    nextDate.setDate(nextDate.getDate() + 1)

    const count = vehicleEventItems.value.filter(
      (e) => e.eventTimestamp >= date.getTime() && e.eventTimestamp < nextDate.getTime(),
    ).length

    data.push({
      name: date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
      value: count,
    })
  }

  return [
    {
      name: '事件数量',
      data: data,
    },
  ]
})

// 云端统计数据
const cloudEventTypeData = computed(() => {
  const typeMap: Record<number, string> = {
    1: '系统升级',
    2: '配置变更',
    3: '异常告警',
  }
  const counts: Record<string, number> = {}

  cloudEventItems.value.forEach((item) => {
    const label = typeMap[item.eventType]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const cloudSourceModuleData = computed(() => {
  const counts: Record<string, number> = {}

  cloudEventItems.value.forEach((item) => {
    counts[item.sourceModule] = (counts[item.sourceModule] || 0) + 1
  })

  return Object.entries(counts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([name, value]) => ({ name, value }))
})

const cloudEventTrendData = computed(() => {
  const days = 7
  const data: { name: string; value: number }[] = []
  const now = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    date.setHours(0, 0, 0, 0)
    const nextDate = new Date(date)
    nextDate.setDate(nextDate.getDate() + 1)

    const count = cloudEventItems.value.filter(
      (e) => e.eventTimestamp >= date.getTime() && e.eventTimestamp < nextDate.getTime(),
    ).length

    data.push({
      name: date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
      value: count,
    })
  }

  return [
    {
      name: '事件数量',
      data: data,
    },
  ]
})

// 车端过滤与分页
const filteredVehicleEvents = computed(() => {
  const { eventId, eventType, sourceModule, timeRange } = vehicleFilters.value
  return vehicleEventItems.value.filter((e) => {
    if (eventId && !e.eventId.includes(eventId)) return false
    if (eventType && e.eventType !== eventType) return false
    if (sourceModule && !e.sourceModule.includes(sourceModule)) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = timeRange[0].getTime()
      const end = timeRange[1].getTime() + 24 * 60 * 60 * 1000 - 1
      if (e.eventTimestamp < start || e.eventTimestamp > end) return false
    }
    return true
  })
})

const vehicleCurrentPage = ref(1)
const vehiclePageSize = ref(10)
const vehicleTotalPages = computed(() =>
  Math.max(1, Math.ceil(filteredVehicleEvents.value.length / vehiclePageSize.value)),
)
const pagedVehicleEvents = computed(() => {
  const start = (vehicleCurrentPage.value - 1) * vehiclePageSize.value
  return filteredVehicleEvents.value.slice(start, start + vehiclePageSize.value)
})

// 云端过滤与分页
const filteredCloudEvents = computed(() => {
  const { eventId, eventType, sourceModule, timeRange } = cloudFilters.value
  return cloudEventItems.value.filter((e) => {
    if (eventId && !e.eventId.includes(eventId)) return false
    if (eventType && e.eventType !== eventType) return false
    if (sourceModule && !e.sourceModule.includes(sourceModule)) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = timeRange[0].getTime()
      const end = timeRange[1].getTime() + 24 * 60 * 60 * 1000 - 1
      if (e.eventTimestamp < start || e.eventTimestamp > end) return false
    }
    return true
  })
})

const cloudCurrentPage = ref(1)
const cloudPageSize = ref(10)
const cloudTotalPages = computed(() =>
  Math.max(1, Math.ceil(filteredCloudEvents.value.length / cloudPageSize.value)),
)
const pagedCloudEvents = computed(() => {
  const start = (cloudCurrentPage.value - 1) * cloudPageSize.value
  return filteredCloudEvents.value.slice(start, start + cloudPageSize.value)
})

// 事件处理函数
const handleVehicleSearch = () => {
  vehicleCurrentPage.value = 1
  console.log('车端搜索条件:', vehicleFilters.value)
}

const resetVehicleFilters = () => {
  vehicleFilters.value = {
    eventId: '',
    eventType: 0,
    sourceModule: '',
    timeRange: null,
  }
  vehicleCurrentPage.value = 1
}

const handleCloudSearch = () => {
  cloudCurrentPage.value = 1
  console.log('云端搜索条件:', cloudFilters.value)
}

const resetCloudFilters = () => {
  cloudFilters.value = {
    eventId: '',
    eventType: 0,
    sourceModule: '',
    timeRange: null,
  }
  cloudCurrentPage.value = 1
}

const viewEventDetail = (event: EventRecord) => {
  selectedEvent.value = event
  detailOpen.value = true
}

const exportVehicleData = () => {
  const headers = [
    '序号',
    '事件ID',
    '事件类型',
    '来源模块',
    '事件描述',
    '关联ID',
    '事件时间',
    '创建时间',
  ]
  const rows = filteredVehicleEvents.value.map((e, index) => [
    (index + 1).toString(),
    e.eventId,
    getEventTypeLabel(e.eventType),
    e.sourceModule,
    e.eventDescription.replace(/\n/g, ' '),
    e.relatedId || '',
    formatTimestamp(e.eventTimestamp),
    formatDate(e.createTime),
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  downloadCsv(content, `企业端车端事件统计_${new Date().toISOString().slice(0, 10)}.csv`)
}

const exportCloudData = () => {
  const headers = [
    '序号',
    '事件ID',
    '事件类型',
    '来源模块',
    '事件描述',
    '关联ID',
    '事件时间',
    '创建时间',
  ]
  const rows = filteredCloudEvents.value.map((e, index) => [
    (index + 1).toString(),
    e.eventId,
    getEventTypeLabel(e.eventType),
    e.sourceModule,
    e.eventDescription.replace(/\n/g, ' '),
    e.relatedId || '',
    formatTimestamp(e.eventTimestamp),
    formatDate(e.createTime),
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  downloadCsv(content, `企业端云端事件统计_${new Date().toISOString().slice(0, 10)}.csv`)
}

// 工具函数
const formatTimestamp = (ts: number) => {
  const date = new Date(ts)
  return date.toLocaleString('zh-CN')
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

const getEventTypeLabel = (type: number) => {
  const map: Record<number, string> = {
    1: '系统升级',
    2: '配置变更',
    3: '异常告警',
  }
  return map[type] || '未知'
}

const getEventTypeVariant = (type: number) => {
  switch (type) {
    case 1:
      return 'default'
    case 2:
      return 'secondary'
    case 3:
      return 'destructive'
    default:
      return 'outline'
  }
}

const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}

const downloadCsv = (content: string, filename: string) => {
  const blob = new Blob([`\ufeff${content}`], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.click()
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
