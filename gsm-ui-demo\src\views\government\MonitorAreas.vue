<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理地理围栏监测区域，包括敏感区域、禁行区域和重点监控区域的设定与维护
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="handleImportAreas" variant="outline" class="flex items-center gap-2">
          <Upload class="w-4 h-4" />
          导入区域
        </Button>
        <Button @click="handleCreateArea" class="flex items-center gap-2">
          <Plus class="w-4 h-4" />
          新增区域
        </Button>
      </div>
    </div>

    <!-- 区域列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>监测区域列表</span>
          <Badge variant="outline"> 共 {{ filteredAreas.length }} 个区域 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <!-- 筛选条件 - 紧贴表格 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>区域名称</TableHead>
                <TableHead>区域类型</TableHead>
                <TableHead>地理范围</TableHead>
                <TableHead>监控状态</TableHead>
                <TableHead>告警次数</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead class="w-[140px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedAreas.length === 0">
                <TableCell :colspan="8" class="h-24 text-center text-muted-foreground">
                  暂无监测区域数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(area, index) in pagedAreas"
                :key="area.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component
                      :is="getAreaTypeIcon(area.type)"
                      :class="`w-4 h-4 ${getAreaTypeColor(area.type)}`"
                    />
                    <div>
                      <div class="font-medium">{{ area.name }}</div>
                      <div class="text-xs text-muted-foreground">ID: {{ area.id }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getAreaTypeVariant(area.type)">{{ area.type }}</Badge>
                </TableCell>
                <TableCell>
                  <div class="text-sm">
                    <div>{{ area.location.region }}</div>
                    <div class="text-xs text-muted-foreground">
                      范围: {{ area.location.radius }}km
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <Badge :variant="area.isActive ? 'default' : 'secondary'">
                      {{ area.isActive ? '监控中' : '已停用' }}
                    </Badge>
                    <div
                      v-if="area.isActive"
                      class="w-2 h-2 bg-green-500 rounded-full animate-pulse"
                    ></div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-1">
                    <span class="font-medium">{{ area.alertCount }}</span>
                    <Bell class="w-3 h-3 text-muted-foreground" />
                    <span class="text-xs text-muted-foreground">次</span>
                  </div>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ area.createdAt }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleViewArea(area.id)">
                        <MapPin class="w-4 h-4 mr-2" />
                        查看地图
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleEditArea(area.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑区域
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleViewAlerts(area.id)">
                        <Bell class="w-4 h-4 mr-2" />
                        查看告警
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="area.isActive"
                        @click="handleToggleStatus(area.id)"
                        class="text-orange-600"
                      >
                        <Pause class="w-4 h-4 mr-2" />
                        停用监控
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="!area.isActive"
                        @click="handleToggleStatus(area.id)"
                        class="text-green-600"
                      >
                        <Play class="w-4 h-4 mr-2" />
                        启用监控
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDuplicate(area.id)">
                        <Copy class="w-4 h-4 mr-2" />
                        复制区域
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleDelete(area.id)" class="text-red-600">
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除区域
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页：统一 PaginationBar -->
        <PaginationBar
          v-model:page="currentPage"
          v-model:pageSize="pageSize"
          :total="filteredAreas.length"
        />
      </CardContent>
    </Card>

    <!-- 创建/编辑区域对话框 -->
    <Dialog :open="showEditDialog" @update:open="showEditDialog = $event">
      <DialogContent class="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{{ editMode === 'create' ? '新增' : '编辑' }}监测区域</DialogTitle>
          <DialogDescription>
            {{ editMode === 'create' ? '创建新的地理围栏监测区域' : '修改现有监测区域信息' }}
          </DialogDescription>
        </DialogHeader>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4">
          <!-- 左侧：表单配置 -->
          <div class="space-y-6">
            <!-- 基本信息 -->
            <div class="space-y-4">
              <h4 class="font-medium">基本信息</h4>
              <div class="grid grid-cols-1 gap-4">
                <div>
                  <Label>区域名称</Label>
                  <Input v-model="editForm.name" placeholder="请输入区域名称" />
                </div>
                <div>
                  <Label>区域类型</Label>
                  <Select v-model="editForm.type">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="敏感区域">敏感区域</SelectItem>
                      <SelectItem value="禁行区域">禁行区域</SelectItem>
                      <SelectItem value="重点区域">重点区域</SelectItem>
                      <SelectItem value="一般区域">一般区域</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <!-- 地理位置设置 -->
            <div class="space-y-4">
              <h4 class="font-medium">地理位置设置</h4>
              <div class="grid grid-cols-1 gap-4">
                <div>
                  <Label>所属地区</Label>
                  <Input v-model="editForm.location.region" placeholder="请输入地区名称" />
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <Label>中心经度</Label>
                    <Input
                      v-model.number="editForm.location.longitude"
                      type="number"
                      step="0.000001"
                      @input="updateMapCenter"
                    />
                  </div>
                  <div>
                    <Label>中心纬度</Label>
                    <Input
                      v-model.number="editForm.location.latitude"
                      type="number"
                      step="0.000001"
                      @input="updateMapCenter"
                    />
                  </div>
                </div>
                <div>
                  <Label>监控半径 (km)</Label>
                  <Input
                    v-model.number="editForm.location.radius"
                    type="number"
                    min="0.1"
                    max="100"
                    @input="updateMapRadius"
                  />
                </div>
              </div>
            </div>

            <!-- 监控配置 -->
            <div class="space-y-4">
              <h4 class="font-medium">监控配置</h4>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <Label>启用监控</Label>
                  <Switch v-model:checked="editForm.isActive" />
                </div>
                <div class="flex items-center justify-between">
                  <Label>启用告警</Label>
                  <Switch v-model:checked="editForm.alertEnabled" />
                </div>
                <div class="flex items-center justify-between">
                  <Label>自动处置</Label>
                  <Switch v-model:checked="editForm.autoAction" />
                </div>
                <div>
                  <Label>告警等级</Label>
                  <Select v-model="editForm.alertLevel">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="低">低</SelectItem>
                      <SelectItem value="中">中</SelectItem>
                      <SelectItem value="高">高</SelectItem>
                      <SelectItem value="紧急">紧急</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>监控频率</Label>
                  <Select v-model="editForm.monitorFrequency">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="实时">实时</SelectItem>
                      <SelectItem value="1分钟">1分钟</SelectItem>
                      <SelectItem value="5分钟">5分钟</SelectItem>
                      <SelectItem value="15分钟">15分钟</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div>
              <Label>区域描述</Label>
              <Textarea
                v-model="editForm.description"
                placeholder="请输入区域描述和监控要求"
                rows="3"
              />
            </div>
          </div>

          <!-- 右侧：地图预览 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h4 class="font-medium">地理围栏预览</h4>
              <Badge :variant="getAreaTypeVariant(editForm.type)" class="text-xs">
                {{ editForm.type }}
              </Badge>
            </div>

            <!-- 地图组件 -->
            <div class="h-[500px] border rounded-lg overflow-hidden">
              <GeofenceMap
                :area-data="{
                  name: editForm.name,
                  type: editForm.type,
                  longitude: editForm.location.longitude,
                  latitude: editForm.location.latitude,
                  radius: editForm.location.radius,
                }"
                :show-vehicles="true"
                :show-alerts="editMode === 'edit'"
                :editable="true"
                @center-changed="handleCenterChanged"
                @radius-changed="handleRadiusChanged"
              />
            </div>

            <!-- 坐标信息 -->
            <div class="bg-muted/50 rounded-lg p-3 space-y-2">
              <div class="text-base font-semibold">实时坐标信息</div>
              <div class="grid grid-cols-2 gap-4 text-xs">
                <div class="space-y-1">
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">经度:</span>
                    <span class="font-mono">{{ editForm.location.longitude?.toFixed(6) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">纬度:</span>
                    <span class="font-mono">{{ editForm.location.latitude?.toFixed(6) }}</span>
                  </div>
                </div>
                <div class="space-y-1">
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">半径:</span>
                    <span class="font-mono">{{ editForm.location.radius }} km</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-muted-foreground">面积:</span>
                    <span class="font-mono"
                      >{{ (Math.PI * Math.pow(editForm.location.radius, 2)).toFixed(2) }} km²</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showEditDialog = false">取消</Button>
          <Button @click="handleSave">
            <Save class="w-4 h-4 mr-2" />
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 地图查看对话框 -->
    <Dialog :open="showMapDialog" @update:open="showMapDialog = $event">
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <DialogTitle>地图查看 - {{ currentArea?.name }}</DialogTitle>
          <DialogDescription> 查看监测区域的地理围栏范围和相关信息 </DialogDescription>
        </DialogHeader>

        <div class="py-4">
          <div class="h-96 border rounded-lg flex items-center justify-center bg-muted">
            <div class="text-center">
              <MapPin class="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <p class="font-medium">交互式地图视图</p>
              <p class="text-sm text-muted-foreground mt-2">在此处显示完整的地理围栏地图，包括：</p>
              <div class="text-sm text-muted-foreground mt-2 space-y-1">
                <p>• 区域边界和中心点</p>
                <p>• 历史告警位置标记</p>
                <p>• 实时车辆位置信息</p>
                <p>• 周边相关区域显示</p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showMapDialog = false">关闭</Button>
          <Button @click="handleEditArea(currentArea?.id)">
            <Edit class="w-4 h-4 mr-2" />
            编辑区域
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Activity,
  AlertTriangle,
  Ban,
  Bell,
  Copy,
  Edit,
  MapPin,
  MoreHorizontal,
  Pause,
  Play,
  Plus,
  Save,
  Trash2,
  Upload,
} from 'lucide-vue-next'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import GeofenceMap from '@/components/ui/map/GeofenceMap.vue'
import { PaginationBar } from '@/components/ui/pagination'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

type AreaType = '敏感区域' | '禁行区域' | '重点区域' | '一般区域'
type AlertLevel = '低' | '中' | '高' | '紧急'

interface Location {
  region: string
  longitude: number
  latitude: number
  radius: number
}

interface MonitorArea {
  id: string
  name: string
  type: AreaType
  location: Location
  isActive: boolean
  alertEnabled: boolean
  alertLevel: AlertLevel
  alertCount: number
  createdAt: string
  description?: string
  monitorFrequency?: string
  autoAction?: boolean
}

interface EditForm {
  name: string
  type: AreaType
  location: Location
  isActive: boolean
  alertEnabled: boolean
  alertLevel: AlertLevel
  monitorFrequency: string
  autoAction: boolean
  description: string
}

// 状态管理
const showEditDialog = ref(false)
const showMapDialog = ref(false)
const editMode = ref<'create' | 'edit'>('create')
const currentEditId = ref<string | null>(null)
const currentArea = ref<MonitorArea | null>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 筛选条件
const filters = ref({
  name: '',
  type: 'ALL' as 'ALL' | AreaType,
  isActive: 'ALL' as 'ALL' | 'true' | 'false',
  alertLevel: 'ALL' as 'ALL' | AlertLevel,
})

// 区域统计
const areaStats = ref({
  total: 28,
  sensitive: 8,
  restricted: 12,
  active: 24,
})

// 编辑表单
const editForm = ref<EditForm>({
  name: '',
  type: '一般区域',
  location: {
    region: '',
    longitude: 116.4074,
    latitude: 39.9042,
    radius: 5,
  },
  isActive: true,
  alertEnabled: true,
  alertLevel: '中',
  monitorFrequency: '实时',
  autoAction: false,
  description: '',
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '区域名称',
    type: 'input',
    placeholder: '请输入区域名称',
  },
  {
    key: 'type',
    label: '区域类型',
    type: 'select',
    placeholder: '请选择类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '敏感区域', value: '敏感区域' },
      { label: '禁行区域', value: '禁行区域' },
      { label: '重点区域', value: '重点区域' },
      { label: '一般区域', value: '一般区域' },
    ],
  },
  {
    key: 'isActive',
    label: '监控状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '监控中', value: 'true' },
      { label: '已停用', value: 'false' },
    ],
  },
  {
    key: 'alertLevel',
    label: '告警等级',
    type: 'select',
    placeholder: '请选择等级',
    options: [
      { label: '全部等级', value: 'ALL' },
      { label: '低', value: '低' },
      { label: '中', value: '中' },
      { label: '高', value: '高' },
      { label: '紧急', value: '紧急' },
    ],
  },
]

// Mock 数据（参考地理围栏管理表设计，生成更多条记录）
function generateMockAreas(count = 36): MonitorArea[] {
  const cities = [
    { region: '北京市东城区', lng: 116.3974, lat: 39.9063 },
    { region: '北京市朝阳区', lng: 116.4864, lat: 39.9219 },
    { region: '北京市海淀区', lng: 116.3103, lat: 39.9566 },
    { region: '上海市浦东新区', lng: 121.544, lat: 31.221 },
    { region: '上海市黄浦区', lng: 121.484, lat: 31.231 },
    { region: '深圳市南山区', lng: 113.93, lat: 22.533 },
    { region: '深圳市福田区', lng: 114.055, lat: 22.541 },
    { region: '广州市天河区', lng: 113.361, lat: 23.124 },
    { region: '杭州市西湖区', lng: 120.147, lat: 30.272 },
    { region: '成都市武侯区', lng: 104.043, lat: 30.642 },
    { region: '西安市雁塔区', lng: 108.951, lat: 34.222 },
    { region: '武汉市江汉区', lng: 114.272, lat: 30.588 },
    { region: '南京市鼓楼区', lng: 118.769, lat: 32.066 },
    { region: '天津市滨海新区', lng: 117.708, lat: 39.029 },
    { region: '重庆市渝中区', lng: 106.57, lat: 29.558 },
  ]
  const types: AreaType[] = ['敏感区域', '禁行区域', '重点区域', '一般区域']
  const levels: AlertLevel[] = ['低', '中', '高', '紧急']
  const random = (min: number, max: number) => Math.round(min + Math.random() * (max - min))
  const pick = <T,>(arr: T[]) => arr[random(0, arr.length - 1)]
  const items: MonitorArea[] = []
  for (let i = 1; i <= count; i++) {
    const city = pick(cities)
    const type = pick(types)
    const isActive = Math.random() > 0.2
    const radius = [1, 1.5, 2, 3, 5, 8, 10][random(0, 6)] as number
    items.push({
      id: `area-${i.toString().padStart(3, '0')}`,
      name: `${city.region}${type}`,
      type,
      location: {
        region: city.region,
        longitude: +(city.lng + (Math.random() - 0.5) * 0.2).toFixed(4),
        latitude: +(city.lat + (Math.random() - 0.5) * 0.2).toFixed(4),
        radius,
      },
      isActive,
      alertEnabled: Math.random() > 0.1,
      alertLevel: pick(levels),
      alertCount: random(0, 50),
      createdAt: new Date(2025, random(0, 7), random(1, 28), random(0, 23), random(0, 59))
        .toISOString()
        .replace('T', ' ')
        .slice(0, 16),
      description:
        type === '禁行区域'
          ? '禁止通行与停留'
          : type === '敏感区域'
            ? '敏感目标周边加强监测'
            : undefined,
      monitorFrequency: pick(['实时', '5分钟', '15分钟'] as unknown as string[]),
      autoAction: Math.random() > 0.6,
    })
  }
  return items
}

// 生成更多 mock 数据
const monitorAreas = ref<MonitorArea[]>(generateMockAreas())

// 基于初始数据同步统计
areaStats.value = {
  total: monitorAreas.value.length,
  sensitive: monitorAreas.value.filter((a) => a.type === '敏感区域').length,
  restricted: monitorAreas.value.filter((a) => a.type === '禁行区域').length,
  active: monitorAreas.value.filter((a) => a.isActive).length,
}

// 计算属性
const filteredAreas = computed(() => {
  return monitorAreas.value.filter((area) => {
    if (filters.value.name && !area.name.includes(filters.value.name)) {
      return false
    }
    if (filters.value.type !== 'ALL' && area.type !== filters.value.type) {
      return false
    }
    if (filters.value.isActive !== 'ALL') {
      const isActive = filters.value.isActive === 'true'
      if (area.isActive !== isActive) return false
    }
    if (filters.value.alertLevel !== 'ALL' && area.alertLevel !== filters.value.alertLevel) {
      return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredAreas.value.length / pageSize.value)),
)

const pagedAreas = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredAreas.value.slice(start, start + pageSize.value)
})

// 辅助函数
const getAreaTypeIcon = (type: AreaType) => {
  const icons = {
    敏感区域: AlertTriangle,
    禁行区域: Ban,
    重点区域: MapPin,
    一般区域: Activity,
  }
  return icons[type] || MapPin
}

const getAreaTypeColor = (type: AreaType) => {
  const colors = {
    敏感区域: 'text-red-500',
    禁行区域: 'text-orange-500',
    重点区域: 'text-blue-500',
    一般区域: 'text-green-500',
  }
  return colors[type] || 'text-gray-500'
}

import type { BadgeVariants } from '@/components/ui/badge'
const getAreaTypeVariant = (type: AreaType): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<AreaType, NonNullable<BadgeVariants['variant']>> = {
    敏感区域: 'destructive',
    禁行区域: 'secondary',
    重点区域: 'default',
    一般区域: 'outline',
  }
  return variants[type] ?? 'outline'
}

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    name: '',
    type: 'ALL',
    isActive: 'ALL',
    alertLevel: 'ALL',
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = ['序号', '区域名称', '区域类型', '地理范围', '监控状态', '告警次数', '创建时间']
  const rows = filteredAreas.value.map((area, index) => [
    (index + 1).toString(),
    area.name,
    area.type,
    `${area.location.region} (${area.location.radius}km)`,
    area.isActive ? '监控中' : '已停用',
    area.alertCount.toString(),
    area.createdAt,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `监测区域_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleCreateArea = () => {
  editMode.value = 'create'
  currentEditId.value = null
  editForm.value = {
    name: '',
    type: '一般区域',
    location: {
      region: '',
      longitude: 116.4074,
      latitude: 39.9042,
      radius: 5,
    },
    isActive: true,
    alertEnabled: true,
    alertLevel: '中',
    monitorFrequency: '实时',
    autoAction: false,
    description: '',
  }
  showEditDialog.value = true
}

const handleEditArea = (id?: string) => {
  const area = monitorAreas.value.find((a) => a.id === id)
  if (area) {
    editMode.value = 'edit'
    currentEditId.value = id!
    editForm.value = {
      name: area.name,
      type: area.type,
      location: { ...area.location },
      isActive: area.isActive,
      alertEnabled: area.alertEnabled,
      alertLevel: area.alertLevel,
      monitorFrequency: area.monitorFrequency || '实时',
      autoAction: area.autoAction || false,
      description: area.description || '',
    }
    showEditDialog.value = true
  }
  showMapDialog.value = false
}

const handleSave = () => {
  if (editMode.value === 'create') {
    const newArea: MonitorArea = {
      id: `area-${Date.now()}`,
      name: editForm.value.name,
      type: editForm.value.type,
      location: { ...editForm.value.location },
      isActive: editForm.value.isActive,
      alertEnabled: editForm.value.alertEnabled,
      alertLevel: editForm.value.alertLevel,
      alertCount: 0,
      createdAt: new Date().toLocaleString('zh-CN'),
      description: editForm.value.description,
      monitorFrequency: editForm.value.monitorFrequency,
      autoAction: editForm.value.autoAction,
    }
    monitorAreas.value.unshift(newArea)
    areaStats.value.total++
  } else if (currentEditId.value) {
    const index = monitorAreas.value.findIndex((a) => a.id === currentEditId.value)
    if (index > -1) {
      Object.assign(monitorAreas.value[index], {
        name: editForm.value.name,
        type: editForm.value.type,
        location: { ...editForm.value.location },
        isActive: editForm.value.isActive,
        alertEnabled: editForm.value.alertEnabled,
        alertLevel: editForm.value.alertLevel,
        description: editForm.value.description,
        monitorFrequency: editForm.value.monitorFrequency,
        autoAction: editForm.value.autoAction,
      })
    }
  }
  showEditDialog.value = false
}

const handleViewArea = (id: string) => {
  const area = monitorAreas.value.find((a) => a.id === id)
  if (area) {
    currentArea.value = area
    showMapDialog.value = true
  }
}

const handleViewAlerts = (id: string) => {
  console.log('查看告警:', id)
}

const handleToggleStatus = (id: string) => {
  const area = monitorAreas.value.find((a) => a.id === id)
  if (area) {
    area.isActive = !area.isActive
    if (area.isActive) {
      areaStats.value.active++
    } else {
      areaStats.value.active--
    }
  }
}

const handleDuplicate = (id: string) => {
  const area = monitorAreas.value.find((a) => a.id === id)
  if (area) {
    const newArea: MonitorArea = {
      ...area,
      id: `area-${Date.now()}`,
      name: area.name + ' (副本)',
      alertCount: 0,
      createdAt: new Date().toLocaleString('zh-CN'),
    }
    monitorAreas.value.unshift(newArea)
    areaStats.value.total++
  }
}

const handleDelete = (id: string) => {
  const index = monitorAreas.value.findIndex((a) => a.id === id)
  if (index > -1) {
    const area = monitorAreas.value[index]
    monitorAreas.value.splice(index, 1)
    areaStats.value.total--
    if (area.isActive) {
      areaStats.value.active--
    }
  }
}

const handleImportAreas = () => {
  console.log('导入区域')
}

// 地图事件处理
const updateMapCenter = () => {
  console.log('地图中心更新:', editForm.value.location.longitude, editForm.value.location.latitude)
}

const updateMapRadius = () => {
  console.log('地图半径更新:', editForm.value.location.radius)
}

const handleCenterChanged = (lng: number, lat: number) => {
  editForm.value.location.longitude = lng
  editForm.value.location.latitude = lat
}

const handleRadiusChanged = (radius: number) => {
  editForm.value.location.radius = radius
}
</script>
