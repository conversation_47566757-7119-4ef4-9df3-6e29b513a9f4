﻿表名: comm_management,,,,,,,
,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,connection_id,连接ID,VARCHAR,64,○,,唯一
3,enterprise_id,企业ID,VARCHAR,32,,,关联 enterprise_info.id
4,connection_type,连接类型,VARCHAR,64,○,,"e.g., API Gateway, MQTT"
5,status,状态,SMALLINT,,○,,"1: Active, 0: Inactive"
6,last_heartbeat,最后心跳时间,TIMESTAMP,,,,
7,configuration,配置信息,JSONB,,,,连接的具体配置
8,create_time,创建时间,TIMESTAMP,,○,,
