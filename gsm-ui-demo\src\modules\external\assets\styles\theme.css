/* External module theme variables - isolated from main project */
.gsm-external-module {
  /* Primary color scheme */
  --external-primary-color: #0080cc;
  --external-primary-dark: #005599;
  --external-primary-hover: #0066aa;
  --external-secondary-color: #4a90e2;
  --external-accent-color: #00ffff;
  --external-success-color: #00ff88;
  --external-warning-color: #ffaa00;
  --external-danger-color: #ff4444;

  /* Dark theme (default) */
  --external-background-color: #0f172a;
  --external-surface-color: #1e293b;
  --external-card-bg: rgba(30, 41, 59, 0.95);
  --external-text-color: #f8fafc;
  --external-text-color-secondary: #cbd5e1;
  --external-text-color-regular: #e2e8f0;
  --external-border-color: rgba(51, 65, 85, 0.6);
  --external-border-color-lighter: rgba(71, 85, 105, 0.4);
  --external-hover-bg: rgba(51, 65, 85, 0.8);
  --external-nav-bg: rgba(15, 23, 42, 0.95);
  --external-glow-color: rgba(0, 212, 255, 0.4);
  --external-gradient-primary: linear-gradient(135deg, #00d4ff 0%, #4a90e2 100%);
  --external-gradient-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);

  /* Base styles for external components */
  background: var(--external-background-color);
  color: var(--external-text-color);
  font-family: inherit;
}

/* Light theme support for external module */
.gsm-external-module[data-theme="light"] {
  --external-primary-color: #0066cc;
  --external-primary-dark: #004499;
  --external-primary-hover: #0052a3;
  --external-secondary-color: #4a90e2;
  --external-accent-color: #00aaff;
  --external-success-color: #00aa66;
  --external-warning-color: #ff8800;
  --external-danger-color: #cc3333;
  --external-background-color: #f0f4f8;
  --external-surface-color: #ffffff;
  --external-card-bg: rgba(255, 255, 255, 0.9);
  --external-text-color: #1a1f2e;
  --external-text-color-secondary: #64748b;
  --external-text-color-regular: #475569;
  --external-border-color: rgba(0, 102, 204, 0.2);
  --external-border-color-lighter: rgba(0, 102, 204, 0.1);
  --external-hover-bg: rgba(0, 102, 204, 0.05);
  --external-nav-bg: rgba(255, 255, 255, 0.95);
  --external-glow-color: rgba(0, 102, 204, 0.2);
  --external-gradient-primary: linear-gradient(135deg, #0066cc 0%, #4a90e2 100%);
  --external-gradient-secondary: linear-gradient(135deg, #ffffff 0%, #f0f4f8 100%);
}

/* External module utility classes */
.gsm-external-module .external-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.gsm-external-module .external-card {
  background: var(--external-card-bg);
  border: 1px solid var(--external-border-color);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.gsm-external-module .external-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--external-border-color);
  background: var(--external-surface-color);
  color: var(--external-text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.gsm-external-module .external-btn:hover {
  background: var(--external-hover-bg);
  transform: translateY(-1px);
}

.gsm-external-module .external-btn-primary {
  background: var(--external-gradient-primary);
  color: #fff;
  border-color: var(--external-primary-color);
  box-shadow: 0 4px 15px rgba(0, 128, 204, 0.3);
}

.gsm-external-module .external-text-muted {
  color: var(--external-text-color-secondary);
}

.gsm-external-module .external-text-regular {
  color: var(--external-text-color-regular);
}

/* Reset any conflicting global styles within the external module */
.gsm-external-module * {
  box-sizing: border-box;
}

.gsm-external-module a {
  color: var(--external-primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.gsm-external-module a:hover {
  color: var(--external-primary-hover);
}

/* Responsive container utilities */
.gsm-external-module .external-grid {
  display: grid;
  gap: 1.5rem;
}

.gsm-external-module .external-flex {
  display: flex;
  gap: 1rem;
}

.gsm-external-module .external-flex-col {
  flex-direction: column;
}

.gsm-external-module .external-items-center {
  align-items: center;
}

.gsm-external-module .external-justify-center {
  justify-content: center;
}

.gsm-external-module .external-justify-between {
  justify-content: space-between;
}

/* Animation utilities */
.gsm-external-module .external-fade-in {
  animation: externalFadeIn 0.6s ease forwards;
}

@keyframes externalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.gsm-external-module .external-pulse {
  animation: externalPulse 2s infinite;
}

@keyframes externalPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}