<template>
  <Card
    :class="cn('p-6 hover:shadow-lg transition-all duration-200 border-border/50', props.class)"
  >
    <div class="flex items-center justify-between">
      <div class="space-y-3 flex-1">
        <!-- 标题 - 增强可读性 -->
        <p class="text-lg font-bold text-muted-foreground/90 tracking-wide">{{ title }}</p>

        <!-- 数值区域 -->
        <div class="flex items-baseline space-x-3">
          <span class="text-3xl font-bold tracking-tight" :class="valueColor">{{
            formattedValue
          }}</span>
          <span v-if="unit" class="text-base font-medium text-muted-foreground/70">{{ unit }}</span>
        </div>

        <!-- 趋势指示器 -->
        <div v-if="trend" class="flex items-center space-x-2">
          <component :is="trendIcon" :class="cn('h-5 w-5', trendColor)" />
          <span :class="cn('text-sm font-semibold', trendColor)"> {{ Math.abs(trend) }}% </span>
          <span class="text-sm text-muted-foreground/70">较上期</span>
        </div>
      </div>

      <!-- 图标区域 - 增大尺寸 -->
      <div v-if="icon" class="flex-shrink-0">
        <div :class="cn('p-4 rounded-xl shadow-sm', iconBgColor)">
          <component :is="icon" :class="cn('h-10 w-10', iconColor)" />
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { TrendingUp, TrendingDown, Minus } from 'lucide-vue-next'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface Props {
  title: string
  value: number | string
  unit?: string
  trend?: number
  icon?: any
  variant?: 'default' | 'success' | 'warning' | 'danger'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  }
  return props.value
})

const trendIcon = computed(() => {
  if (!props.trend) return Minus
  return props.trend > 0 ? TrendingUp : TrendingDown
})

const trendColor = computed(() => {
  if (!props.trend) return 'text-muted-foreground'
  return props.trend > 0
    ? 'text-emerald-600 dark:text-emerald-500'
    : 'text-rose-600 dark:text-rose-500'
})

const valueColor = computed(() => {
  const colors = {
    default: 'text-foreground',
    success: 'text-emerald-700 dark:text-emerald-500',
    warning: 'text-amber-700 dark:text-amber-500',
    danger: 'text-rose-700 dark:text-rose-500',
  }
  return colors[props.variant]
})

const iconColor = computed(() => {
  const colors = {
    default: 'text-slate-700 dark:text-slate-300',
    success: 'text-emerald-700 dark:text-emerald-400',
    warning: 'text-amber-700 dark:text-amber-400',
    danger: 'text-rose-700 dark:text-rose-400',
  }
  return colors[props.variant]
})

const iconBgColor = computed(() => {
  const colors = {
    default: 'bg-slate-100 dark:bg-slate-800/60',
    success: 'bg-emerald-50 dark:bg-emerald-900/30',
    warning: 'bg-amber-50 dark:bg-amber-900/30',
    danger: 'bg-rose-50 dark:bg-rose-900/30',
  }
  return colors[props.variant]
})
</script>
