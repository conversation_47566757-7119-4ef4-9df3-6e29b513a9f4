<template>
  <section id="features" class="gsm-external-module features-section">
    <div class="external-container">
      <div class="section-header">
        <h2 class="section-title">政策落地与监管服务</h2>
        <p class="section-subtitle">
          以政策宣传为引领，以专业服务为支撑，构建智能网联汽车数据安全监管生态
        </p>
      </div>
      <div class="features-grid">
        <div v-for="feature in features" :key="feature.title" class="feature-card">
          <div class="feature-icon">
            <ElementIcon :name="feature.icon" :size="24" :color="'var(--external-primary-color)'" />
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import ElementIcon from './ElementIcon.vue'

const features = [
  {
    icon: 'document',
    title: '政策法规管理',
    description: '建立完善的政策法规体系，确保智能网联汽车数据安全合规运营',
  },
  {
    icon: 'office-building',
    title: '政府监管平台',
    description: '为政府部门提供统一的监管平台，实现对企业数据安全的有效监督',
  },
  {
    icon: 'shop',
    title: '企业合规管理',
    description: '帮助企业建立数据安全管理体系，确保符合国家相关法规要求',
  },
  {
    icon: 'warning',
    title: '风险预警系统',
    description: '实时监测数据安全风险，及时发现并处置潜在的安全威胁',
  },
  {
    icon: 'connection',
    title: '数据流向追踪',
    description: '全程追踪数据流向，确保数据在传输和使用过程中的安全可控',
  },
  {
    icon: 'user',
    title: '多方协同治理',
    description: '构建政府、企业、第三方机构协同治理的数据安全生态体系',
  },
]
</script>

<style scoped>
.features-section {
  padding: 6rem 0;
  background: var(--external-background-color);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--external-text-color);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--external-primary-color) 0%,
    var(--external-primary-hover) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--external-text-color-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--external-card-bg);
  border: 1px solid var(--external-border-color);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 128, 204, 0.15);
  border-color: var(--external-primary-color);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: var(--external-surface-color);
  border: 1px solid var(--external-border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  background: var(--external-primary-color);
  border-color: var(--external-primary-color);
  color: white;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--external-text-color);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--external-text-color-regular);
  line-height: 1.6;
  font-size: 0.875rem;
}

/* Light theme adjustments */
.gsm-external-module[data-theme='light'] .feature-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.gsm-external-module[data-theme='light'] .feature-card:hover {
  box-shadow: 0 16px 40px rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
}

@media (max-width: 768px) {
  .features-section {
    padding: 4rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }
}
</style>
