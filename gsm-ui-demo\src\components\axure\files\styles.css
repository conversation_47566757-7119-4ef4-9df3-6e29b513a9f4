body {
  margin: 0px;
  background-color: rgba(2, 11, 26, 1);
  background-image: none;
  position: static;
  left: auto;
  width: 1920px;
  margin-left: 0;
  margin-right: 0;
  text-align: left;
}
.form_sketch {
  border-color: transparent;
  background-color: transparent;
}
#base {
  position: absolute;
  z-index: 0;
  margin: 0 auto 0 auto;
}
#u96_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 1080px;
  background: -webkit-linear-gradient(270deg, rgba(53, 69, 83, 1) 0%, rgba(12, 29, 49, 1) 100%);
  background: -moz-linear-gradient(180deg, rgba(53, 69, 83, 1) 0%, rgba(12, 29, 49, 1) 100%);
  background: linear-gradient(180deg, rgba(53, 69, 83, 1) 0%, rgba(12, 29, 49, 1) 100%);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u96 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 10px;
  width: 1920px;
  height: 1080px;
  display: flex;
  opacity: 0.62;
}
#u96 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u96_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u97_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 1080px;
}
#u97 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 1080px;
  display: flex;
}
#u97 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u97_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u98_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 2160px;
  height: 1080px;
}
#u98 {
  border-width: 0px;
  position: absolute;
  left: -120px;
  top: 0px;
  width: 2160px;
  height: 1080px;
  display: flex;
}
#u98 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u98_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u99_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 585px;
  height: 1080px;
}
#u99 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 585px;
  height: 1080px;
  display: flex;
}
#u99 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u99_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u100_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 360px;
}
#u100 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 723px;
  width: 1920px;
  height: 360px;
  display: flex;
}
#u100 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u100_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u101_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 592px;
  height: 1080px;
}
#u101 {
  border-width: 0px;
  position: absolute;
  left: 1331px;
  top: 0px;
  width: 592px;
  height: 1080px;
  display: flex;
}
#u101 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u101_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u102_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 216px;
}
#u102 {
  border-width: 0px;
  position: absolute;
  left: 3px;
  top: 5px;
  width: 1920px;
  height: 216px;
  display: flex;
}
#u102 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u102_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u103_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 1080px;
}
#u103 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 1080px;
  display: flex;
}
#u103 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u103_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u104 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u105 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u106_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 116px;
  height: 52px;
}
#u106 {
  border-width: 0px;
  position: absolute;
  left: 87px;
  top: 117px;
  width: 100px;
  height: 36px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 32px;
}
#u106 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u106_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u107_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u107 {
  border-width: 0px;
  position: absolute;
  left: 52px;
  top: 123px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u107 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u107_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u108_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 434px;
  height: 10px;
}
#u108 {
  border-width: 0px;
  position: absolute;
  left: 47px;
  top: 155px;
  width: 434px;
  height: 10px;
  display: flex;
}
#u108 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u108_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u109 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 201px;
  width: 422px;
  height: 216px;
}
#u109_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 422px;
  height: 216px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u109_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u110_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 422px;
  height: 245px;
}
#u110 {
  border-width: 0px;
  position: absolute;
  left: -2px;
  top: -14px;
  width: 422px;
  height: 245px;
  display: flex;
}
#u110 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u110_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u111 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u112_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u112 {
  border-width: 0px;
  position: absolute;
  left: 86px;
  top: -32px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u112 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u112_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u113_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u113 {
  border-width: 0px;
  position: absolute;
  left: 205px;
  top: -32px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u113 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u113_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u114_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u114 {
  border-width: 0px;
  position: absolute;
  left: 205px;
  top: -32px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u114 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u114_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u115_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u115 {
  border-width: 0px;
  position: absolute;
  left: 210px;
  top: -30px;
  width: 106px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u115 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u115_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u115.selected {
}
#u115_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u116_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u116 {
  border-width: 0px;
  position: absolute;
  left: 86px;
  top: -32px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u116 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u116_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u117_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u117 {
  border-width: 0px;
  position: absolute;
  left: 91px;
  top: -30px;
  width: 106px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u117 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u117_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u117.selected {
}
#u117_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u109_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 422px;
  height: 216px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u109_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u118_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 422px;
  height: 245px;
}
#u118 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: -10px;
  width: 422px;
  height: 245px;
  display: flex;
}
#u118 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u118_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u119 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 165px;
  width: 300px;
  height: 32px;
}
#u119_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 300px;
  height: 32px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u119_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u120 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u121_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u121 {
  border-width: 0px;
  position: absolute;
  left: 23px;
  top: 4px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u121 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u121_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u122_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u122 {
  border-width: 0px;
  position: absolute;
  left: 142px;
  top: 4px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u122 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u122_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u123_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u123 {
  border-width: 0px;
  position: absolute;
  left: 142px;
  top: 4px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u123 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u123_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u124_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u124 {
  border-width: 0px;
  position: absolute;
  left: 147px;
  top: 6px;
  width: 106px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u124 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u124_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u124.selected {
}
#u124_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u125_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u125 {
  border-width: 0px;
  position: absolute;
  left: 23px;
  top: 4px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u125 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u125_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u126_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u126 {
  border-width: 0px;
  position: absolute;
  left: 28px;
  top: 6px;
  width: 106px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u126 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u126_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u126.selected {
}
#u126_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u119_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 300px;
  height: 32px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u119_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u127 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u128_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u128 {
  border-width: 0px;
  position: absolute;
  left: 23px;
  top: 4px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u128 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u128_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u129_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 120px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u129 {
  border-width: 0px;
  position: absolute;
  left: 142px;
  top: 4px;
  width: 120px;
  height: 28px;
  display: flex;
}
#u129 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u129_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u130_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u130 {
  border-width: 0px;
  position: absolute;
  left: 147px;
  top: 6px;
  width: 106px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u130 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u130_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u130.selected {
}
#u130_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u131_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u131 {
  border-width: 0px;
  position: absolute;
  left: 28px;
  top: 6px;
  width: 106px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u131 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u131_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 106px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u131.selected {
}
#u131_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u132 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u133 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u134_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 116px;
  height: 52px;
}
#u134 {
  border-width: 0px;
  position: absolute;
  left: 1518px;
  top: 117px;
  width: 100px;
  height: 36px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 32px;
}
#u134 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u134_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u135_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u135 {
  border-width: 0px;
  position: absolute;
  left: 1480px;
  top: 125px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u135 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u135_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u136_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 434px;
  height: 10px;
}
#u136 {
  border-width: 0px;
  position: absolute;
  left: 1475px;
  top: 157px;
  width: 434px;
  height: 10px;
  display: flex;
}
#u136 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u136_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u137_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 429px;
  height: 248px;
}
#u137 {
  border-width: 0px;
  position: absolute;
  left: 1482px;
  top: 169px;
  width: 429px;
  height: 248px;
  display: flex;
}
#u137 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u137_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u138 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u139 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u140 {
  border-width: 0px;
  position: absolute;
  left: 474px;
  top: 824px;
  width: 978px;
  height: 136px;
}
#u140_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 978px;
  height: 136px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u140_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u141 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u142_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.937254901960784);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u142 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 31px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u142 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u142_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u143_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u143 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 7px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u143 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u143_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u144_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u144 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 7px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u144 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u144_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u145_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u145 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 7px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u145 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u145_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u146_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u146 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 7px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u146 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u146_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u147_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u147 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 40px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u147 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u147_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u148_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u148 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 40px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u148 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u148_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u149_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u149 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 40px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u149 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u149_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u150_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u150 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 40px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u150 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u150_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u151_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 63px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u151 {
  border-width: 0px;
  position: absolute;
  left: 382px;
  top: 7px;
  width: 63px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u151 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u151_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u152_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 80px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u152 {
  border-width: 0px;
  position: absolute;
  left: 384px;
  top: 40px;
  width: 80px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u152 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u152_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u153_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.980392156862745);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u153 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 95px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u153 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u153_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u154_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u154 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 73px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u154 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u154_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u155_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u155 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 73px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u155 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u155_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u156_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u156 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 73px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u156 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u156_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u157_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u157 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 73px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u157 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u157_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u158_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 63px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u158 {
  border-width: 0px;
  position: absolute;
  left: 384px;
  top: 73px;
  width: 63px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u158 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u158_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u159_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u159 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 104px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u159 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u159_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u160_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u160 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 104px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u160 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u160_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u161_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u161 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 104px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u161 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u161_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u162_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u162 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 104px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u162 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u162_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u163_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 80px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u163 {
  border-width: 0px;
  position: absolute;
  left: 384px;
  top: 104px;
  width: 80px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u163 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u163_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u140_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 978px;
  height: 136px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u140_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u164 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u165_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u165 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 31px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u165 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u165_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u166_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u166 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 7px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u166 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u166_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u167_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u167 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 7px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u167 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u167_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u168_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u168 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 7px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u168 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u168_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u169_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u169 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 7px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u169 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u169_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u170_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u170 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 40px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u170 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u170_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u171_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u171 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 40px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u171 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u171_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u172_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u172 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 40px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u172 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u172_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u173_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u173 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 40px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u173 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u173_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u174_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 63px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u174 {
  border-width: 0px;
  position: absolute;
  left: 382px;
  top: 7px;
  width: 63px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u174 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u174_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u175_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 80px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u175 {
  border-width: 0px;
  position: absolute;
  left: 382px;
  top: 40px;
  width: 80px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u175 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u175_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u176_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u176 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 95px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u176 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u176_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u177_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u177 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 73px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u177 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u177_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u178_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u178 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 73px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u178 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u178_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u179_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u179 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 73px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u179 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u179_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u180_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u180 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 73px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u180 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u180_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u181_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 63px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u181 {
  border-width: 0px;
  position: absolute;
  left: 382px;
  top: 73px;
  width: 63px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u181 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u181_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u182_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u182 {
  border-width: 0px;
  position: absolute;
  left: 27px;
  top: 104px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u182 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u182_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u183_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 189px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u183 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 104px;
  width: 189px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u183 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u183_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u184_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 156px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u184 {
  border-width: 0px;
  position: absolute;
  left: 85px;
  top: 104px;
  width: 156px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u184 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u184_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u185_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u185 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 104px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u185 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u185_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u186_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 80px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u186 {
  border-width: 0px;
  position: absolute;
  left: 382px;
  top: 104px;
  width: 80px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u186 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u186_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u187_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 978px;
  height: 32px;
  background: inherit;
  background-color: rgba(229, 236, 248, 0.2);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u187 {
  border-width: 0px;
  position: absolute;
  left: 474px;
  top: 792px;
  width: 978px;
  height: 32px;
  display: flex;
}
#u187 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u187_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u188_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u188 {
  border-width: 0px;
  position: absolute;
  left: 497px;
  top: 799px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u188 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u188_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u189_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u189 {
  border-width: 0px;
  position: absolute;
  left: 1221px;
  top: 799px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u189 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u189_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u190_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u190 {
  border-width: 0px;
  position: absolute;
  left: 561px;
  top: 799px;
  width: 48px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u190 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u190_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u191_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u191 {
  border-width: 0px;
  position: absolute;
  left: 1099px;
  top: 800px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u191 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u191_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u192_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u192 {
  border-width: 0px;
  position: absolute;
  left: 856px;
  top: 800px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u192 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u192_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u193 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u194 {
  border-width: 0px;
  position: absolute;
  left: 474px;
  top: 824px;
  width: 978px;
  height: 133px;
}
#u194_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 978px;
  height: 133px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u194_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u195 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u196_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u196 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 31px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u196 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u196_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u197_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u197 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 7px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u197 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u197_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u198_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u198 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 7px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u198 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u198_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u199_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 222px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u199 {
  border-width: 0px;
  position: absolute;
  left: 109px;
  top: 7px;
  width: 222px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u199 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u199_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u200_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u200 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 7px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u200 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u200_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u201_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u201 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 40px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u201 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u201_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u202_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u202 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 40px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u202 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u202_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u203_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 242px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u203 {
  border-width: 0px;
  position: absolute;
  left: 98px;
  top: 40px;
  width: 242px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u203 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u203_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u204_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u204 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 40px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u204 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u204_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u205_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u205 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 7px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u205 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u205_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u206_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u206 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 40px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u206 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u206_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u207_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u207 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 95px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u207 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u207_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u208_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u208 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 73px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u208 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u208_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u209_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u209 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 73px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u209 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u209_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u210_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 222px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u210 {
  border-width: 0px;
  position: absolute;
  left: 109px;
  top: 73px;
  width: 222px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u210 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u210_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u211_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u211 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 73px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u211 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u211_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u212_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u212 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 73px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u212 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u212_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u213_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u213 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 104px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u213 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u213_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u214_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u214 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 104px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u214 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u214_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u215_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 242px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u215 {
  border-width: 0px;
  position: absolute;
  left: 98px;
  top: 104px;
  width: 242px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u215 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u215_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u216_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u216 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 104px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u216 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u216_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u217_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u217 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 104px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u217 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u217_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u194_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 978px;
  height: 133px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u194_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u218 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u219_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u219 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 31px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u219 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u219_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u220_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u220 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 7px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u220 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u220_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u221_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u221 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 7px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u221 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u221_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u222_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 222px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u222 {
  border-width: 0px;
  position: absolute;
  left: 109px;
  top: 7px;
  width: 222px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u222 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u222_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u223_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u223 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 7px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u223 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u223_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u224_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u224 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 40px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u224 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u224_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u225_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u225 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 40px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u225 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u225_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u226_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 242px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u226 {
  border-width: 0px;
  position: absolute;
  left: 98px;
  top: 40px;
  width: 242px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u226 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u226_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u227_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u227 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 40px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u227 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u227_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u228_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u228 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 7px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u228 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u228_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u229_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u229 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 40px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u229 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u229_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u230_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 973px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u230 {
  border-width: 0px;
  position: absolute;
  left: 5px;
  top: 95px;
  width: 973px;
  height: 32px;
  display: flex;
}
#u230 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u230_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u231_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u231 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 73px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u231 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u231_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u232_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u232 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 73px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u232 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u232_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u233_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 222px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u233 {
  border-width: 0px;
  position: absolute;
  left: 109px;
  top: 73px;
  width: 222px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u233 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u233_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u234_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u234 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 73px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u234 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u234_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u235_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u235 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 73px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u235 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u235_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u236_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u236 {
  border-width: 0px;
  position: absolute;
  left: 26px;
  top: 104px;
  width: 12px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u236 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u236_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u237_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 188px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u237 {
  border-width: 0px;
  position: absolute;
  left: 750px;
  top: 104px;
  width: 188px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u237 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u237_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u238_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 242px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u238 {
  border-width: 0px;
  position: absolute;
  left: 98px;
  top: 104px;
  width: 242px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u238 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u238_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u239_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u239 {
  border-width: 0px;
  position: absolute;
  left: 629px;
  top: 104px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u239 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u239_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u240_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 81px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u240 {
  border-width: 0px;
  position: absolute;
  left: 440px;
  top: 104px;
  width: 81px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u240 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u240_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u241_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 978px;
  height: 32px;
  background: inherit;
  background-color: rgba(229, 236, 248, 0.2);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u241 {
  border-width: 0px;
  position: absolute;
  left: 474px;
  top: 795px;
  width: 978px;
  height: 32px;
  display: flex;
}
#u241 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u241_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u242_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u242 {
  border-width: 0px;
  position: absolute;
  left: 497px;
  top: 799px;
  width: 40px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u242 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u242_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u243_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u243 {
  border-width: 0px;
  position: absolute;
  left: 1221px;
  top: 800px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u243 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u243_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u244_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u244 {
  border-width: 0px;
  position: absolute;
  left: 909px;
  top: 800px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u244 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u244_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u245_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u245 {
  border-width: 0px;
  position: absolute;
  left: 585px;
  top: 799px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: #ffffff;
}
#u245 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u245_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u246_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 82px;
  height: 17px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u246 {
  border-width: 0px;
  position: absolute;
  left: 1099px;
  top: 800px;
  width: 82px;
  height: 17px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u246 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u246_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u247 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u248 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u249_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 164px;
  height: 44px;
}
#u249 {
  border-width: 0px;
  position: absolute;
  left: 523px;
  top: 728px;
  width: 148px;
  height: 28px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 24px;
}
#u249 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u249_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u250_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u250 {
  border-width: 0px;
  position: absolute;
  left: 491px;
  top: 729px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u250 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u250_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u251_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 965px;
  height: 10px;
}
#u251 {
  border-width: 0px;
  position: absolute;
  left: 486px;
  top: 761px;
  width: 965px;
  height: 10px;
  display: flex;
}
#u251 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u251_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u252 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u253_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 152px;
  height: 32px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u253 {
  border-width: 0px;
  position: absolute;
  left: 1299px;
  top: 726px;
  width: 152px;
  height: 32px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u253 .text {
  position: absolute;
  align-self: center;
  padding: 6px 8px 6px 8px;
  box-sizing: border-box;
  width: 100%;
}
#u253_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 152px;
  height: 32px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u253.selected {
}
#u253_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u254_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 152px;
  height: 32px;
  background: inherit;
  background-color: rgba(12, 42, 85, 0.996078431372549);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u254 {
  border-width: 0px;
  position: absolute;
  left: 1139px;
  top: 726px;
  width: 152px;
  height: 32px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u254 .text {
  position: absolute;
  align-self: center;
  padding: 6px 8px 6px 8px;
  box-sizing: border-box;
  width: 100%;
}
#u254_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 152px;
  height: 32px;
  background: inherit;
  background-color: rgba(12, 42, 85, 0.996078431372549);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u254.selected {
}
#u254_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u255 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u256 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u257_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 164px;
  height: 52px;
}
#u257 {
  border-width: 0px;
  position: absolute;
  left: 87px;
  top: 724px;
  width: 148px;
  height: 36px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 32px;
}
#u257 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u257_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u258_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u258 {
  border-width: 0px;
  position: absolute;
  left: 52px;
  top: 729px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u258 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u258_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u259_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 434px;
  height: 10px;
}
#u259 {
  border-width: 0px;
  position: absolute;
  left: 47px;
  top: 761px;
  width: 434px;
  height: 10px;
  display: flex;
}
#u259 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u259_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u260 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u261 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u262_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 31px;
  background: inherit;
  background-color: rgba(229, 236, 248, 0.2);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u262 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 792px;
  width: 400px;
  height: 31px;
  display: flex;
}
#u262 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u262_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u263_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 18px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u263 {
  border-width: 0px;
  position: absolute;
  left: 47px;
  top: 798px;
  width: 24px;
  height: 18px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u263 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u263_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u264_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 52px;
  height: 18px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u264 {
  border-width: 0px;
  position: absolute;
  left: 325px;
  top: 798px;
  width: 52px;
  height: 18px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u264 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u264_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u265_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 52px;
  height: 18px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u265 {
  border-width: 0px;
  position: absolute;
  left: 157px;
  top: 798px;
  width: 52px;
  height: 18px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u265 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u265_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u266_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 35px;
  height: 18px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u266 {
  border-width: 0px;
  position: absolute;
  left: 79px;
  top: 798px;
  width: 35px;
  height: 18px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u266 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u266_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u267_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 52px;
  height: 18px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u267 {
  border-width: 0px;
  position: absolute;
  left: 213px;
  top: 798px;
  width: 52px;
  height: 18px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u267 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u267_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u268_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 52px;
  height: 18px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u268 {
  border-width: 0px;
  position: absolute;
  left: 269px;
  top: 798px;
  width: 52px;
  height: 18px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u268 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u268_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u269 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 823px;
  width: 400px;
  height: 134px;
}
#u269_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 134px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u269_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u270 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u271_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.92156862745098);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u271 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 32px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u271 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u271_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u272_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u272 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 96px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u272 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u272_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u273_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u273 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 8px;
  width: 6px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u273 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u273_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u274_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u274 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 8px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u274 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u274_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u275_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u275 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 8px;
  width: 79px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u275 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u275_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u276_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u276 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 8px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u276 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u276_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u277_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u277 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 36px;
  width: 6px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u277 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u277_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u278_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u278 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 40px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u278 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u278_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u279_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u279 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 36px;
  width: 79px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u279 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u279_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u280_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u280 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 36px;
  width: 24px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u280 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u280_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u281_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u281 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 68px;
  width: 6px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u281 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u281_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u282_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u282 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 72px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u282 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u282_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u283_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u283 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 68px;
  width: 79px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u283 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u283_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u284_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u284 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 68px;
  width: 24px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u284 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u284_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u285_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u285 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 8px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u285 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u285_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u286_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u286 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 36px;
  width: 36px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u286 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u286_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u287_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u287 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 68px;
  width: 36px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u287 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u287_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u288_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u288 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 100px;
  width: 6px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u288 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u288_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u289_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u289 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 104px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u289 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u289_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u290_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u290 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 100px;
  width: 79px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u290 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u290_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u291_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u291 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 100px;
  width: 24px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u291 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u291_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u292_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u292 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 100px;
  width: 36px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u292 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u292_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u293_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(204, 11, 11, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(204, 11, 11, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u293 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 8px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u293 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u293_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u294_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u294 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 40px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u294 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u294_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u295_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
}
#u295 {
  border-width: 0px;
  position: absolute;
  left: 115px;
  top: 72px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(131, 230, 34, 0.956862745098039);
  text-align: center;
  line-height: 20px;
}
#u295 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u295_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u296_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u296 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 104px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u296 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u296_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u269_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 134px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u269_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u297 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u298_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.717647058823529);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u298 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 32px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u298 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u298_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u299_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u299 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 96px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u299 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u299_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u300_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u300 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 8px;
  width: 6px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u300 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u300_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u301_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u301 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 8px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u301 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u301_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u302_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u302 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 8px;
  width: 79px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u302 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u302_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u303_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u303 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 8px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u303 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u303_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u304_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u304 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 36px;
  width: 6px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u304 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u304_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u305_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u305 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 40px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u305 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u305_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u306_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u306 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 36px;
  width: 79px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u306 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u306_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u307_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u307 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 36px;
  width: 24px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u307 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u307_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u308_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u308 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 68px;
  width: 6px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u308 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u308_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u309_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u309 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 72px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u309 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u309_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u310_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u310 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 68px;
  width: 79px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u310 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u310_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u311_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u311 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 68px;
  width: 24px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u311 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u311_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u312_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u312 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 8px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u312 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u312_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u313_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u313 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 36px;
  width: 36px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u313 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u313_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u314_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u314 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 68px;
  width: 36px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u314 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u314_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u315_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 6px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u315 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 100px;
  width: 6px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u315 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u315_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u316_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u316 {
  border-width: 0px;
  position: absolute;
  left: 278px;
  top: 104px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 16px;
}
#u316 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u316_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u317_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 79px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u317 {
  border-width: 0px;
  position: absolute;
  left: 29px;
  top: 100px;
  width: 79px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u317 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u317_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u318_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u318 {
  border-width: 0px;
  position: absolute;
  left: 170px;
  top: 100px;
  width: 24px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u318 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u318_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u319_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u319 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 100px;
  width: 36px;
  height: 24px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #f2f5fa;
  line-height: 24px;
}
#u319 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u319_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u320_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(204, 11, 11, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(204, 11, 11, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u320 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 8px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u320 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u320_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u321_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u321 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 40px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u321 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u321_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u322_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
}
#u322 {
  border-width: 0px;
  position: absolute;
  left: 115px;
  top: 72px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(131, 230, 34, 0.956862745098039);
  text-align: center;
  line-height: 20px;
}
#u322 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u322_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u323_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u323 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 104px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u323 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u323_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u324 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u325 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u326_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 164px;
  height: 52px;
}
#u326 {
  border-width: 0px;
  position: absolute;
  left: 1519px;
  top: 724px;
  width: 148px;
  height: 36px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 32px;
}
#u326 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u326_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u327_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u327 {
  border-width: 0px;
  position: absolute;
  left: 1483px;
  top: 729px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u327 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u327_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u328_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 434px;
  height: 10px;
}
#u328 {
  border-width: 0px;
  position: absolute;
  left: 1478px;
  top: 761px;
  width: 434px;
  height: 10px;
  display: flex;
}
#u328 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u328_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u329 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u330_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(48, 61, 81, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u330 {
  border-width: 0px;
  position: absolute;
  left: 1475px;
  top: 785px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u330 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u330_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u331_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u331 {
  border-width: 0px;
  position: absolute;
  left: 1482px;
  top: 793px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u331 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u331_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u332_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u332 {
  border-width: 0px;
  position: absolute;
  left: 1766px;
  top: 793px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u332 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u332_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u333_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u333 {
  border-width: 0px;
  position: absolute;
  left: 1581px;
  top: 793px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u333 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u333_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u334_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u334 {
  border-width: 0px;
  position: absolute;
  left: 1518px;
  top: 793px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u334 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u334_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u335_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u335 {
  border-width: 0px;
  position: absolute;
  left: 1638px;
  top: 793px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u335 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u335_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u336_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u336 {
  border-width: 0px;
  position: absolute;
  left: 1697px;
  top: 793px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 16px;
}
#u336 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u336_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u337 {
  border-width: 0px;
  position: absolute;
  left: 1475px;
  top: 817px;
  width: 400px;
  height: 132px;
}
#u337_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 132px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u337_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u338 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u339_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u339 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 32px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u339 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u339_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u340_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u340 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 96px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u340 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u340_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u341_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u341 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 8px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u341 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u341_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u342_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u342 {
  border-width: 0px;
  position: absolute;
  left: 291px;
  top: 8px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u342 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u342_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u343_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u343 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 8px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u343 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u343_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u344_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u344 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 8px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u344 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u344_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u345_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u345 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 40px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u345 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u345_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u346_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u346 {
  border-width: 0px;
  position: absolute;
  left: 291px;
  top: 40px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u346 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u346_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u347_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u347 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 40px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u347 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u347_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u348_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u348 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 40px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u348 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u348_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u349_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u349 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 71px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u349 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u349_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u350_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u350 {
  border-width: 0px;
  position: absolute;
  left: 291px;
  top: 71px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u350 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u350_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u351_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u351 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 71px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u351 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u351_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u352_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u352 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 71px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u352 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u352_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u353_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u353 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 8px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u353 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u353_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u354_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u354 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 40px;
  width: 36px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u354 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u354_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u355_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u355 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 71px;
  width: 36px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u355 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u355_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u356_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u356 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 104px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u356 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u356_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u357_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u357 {
  border-width: 0px;
  position: absolute;
  left: 291px;
  top: 104px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u357 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u357_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u358_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u358 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 104px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u358 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u358_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u359_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u359 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 104px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u359 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u359_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u360_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u360 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 104px;
  width: 36px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u360 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u360_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u361_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(204, 11, 11, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(204, 11, 11, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u361 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 8px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u361 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u361_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u362_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u362 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 40px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u362 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u362_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u363_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
}
#u363 {
  border-width: 0px;
  position: absolute;
  left: 107px;
  top: 72px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(131, 230, 34, 0.956862745098039);
  text-align: center;
  line-height: 20px;
}
#u363 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u363_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u364_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u364 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 104px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u364 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u364_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u337_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 132px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u337_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u365 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u366_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u366 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 32px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u366 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u366_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u367_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 400px;
  height: 32px;
  background: inherit;
  background-color: rgba(28, 38, 59, 0.996078431372549);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u367 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 96px;
  width: 400px;
  height: 32px;
  display: flex;
}
#u367 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u367_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u368_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u368 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 8px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u368 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u368_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u369_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u369 {
  border-width: 0px;
  position: absolute;
  left: 277px;
  top: 8px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u369 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u369_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u370_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u370 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 8px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u370 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u370_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u371_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u371 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 8px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u371 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u371_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u372_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u372 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 40px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u372 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u372_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u373_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u373 {
  border-width: 0px;
  position: absolute;
  left: 277px;
  top: 40px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u373 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u373_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u374_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u374 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 40px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u374 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u374_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u375_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u375 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 40px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u375 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u375_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u376_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u376 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 71px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u376 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u376_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u377_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u377 {
  border-width: 0px;
  position: absolute;
  left: 277px;
  top: 71px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u377 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u377_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u378_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u378 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 71px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u378 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u378_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u379_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u379 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 71px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u379 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u379_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u380_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 48px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u380 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 8px;
  width: 48px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u380 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u380_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u381_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u381 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 40px;
  width: 36px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u381 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u381_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u382_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u382 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 71px;
  width: 36px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u382 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u382_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u383_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 7px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u383 {
  border-width: 0px;
  position: absolute;
  left: 7px;
  top: 104px;
  width: 7px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u383 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u383_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u384_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 109px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u384 {
  border-width: 0px;
  position: absolute;
  left: 277px;
  top: 104px;
  width: 109px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u384 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u384_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u385_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u385 {
  border-width: 0px;
  position: absolute;
  left: 44px;
  top: 104px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u385 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u385_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u386_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 24px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u386 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 104px;
  width: 24px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u386 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u386_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u387_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u387 {
  border-width: 0px;
  position: absolute;
  left: 217px;
  top: 104px;
  width: 36px;
  height: 16px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
  line-height: 16px;
}
#u387 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u387_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u388_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(204, 11, 11, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(204, 11, 11, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u388 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 8px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #cc0b0b;
  text-align: center;
  line-height: 14px;
}
#u388 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u388_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u389_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u389 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 40px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u389 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u389_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u390_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
  background: inherit;
  background-color: rgba(230, 128, 34, 0.149019607843137);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(230, 128, 34, 1);
  border-radius: 2px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u390 {
  border-width: 0px;
  position: absolute;
  left: 106px;
  top: 104px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #e68022;
  line-height: 20px;
}
#u390 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u390_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u391_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 36px;
  height: 20px;
}
#u391 {
  border-width: 0px;
  position: absolute;
  left: 107px;
  top: 72px;
  width: 36px;
  height: 20px;
  display: flex;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: rgba(131, 230, 34, 0.956862745098039);
  text-align: center;
  line-height: 20px;
}
#u391 .text {
  position: absolute;
  align-self: center;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u391_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u392 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u393 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u394_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 116px;
  height: 52px;
}
#u394 {
  border-width: 0px;
  position: absolute;
  left: 75px;
  top: 426px;
  width: 100px;
  height: 36px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 32px;
}
#u394 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u394_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u395_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u395 {
  border-width: 0px;
  position: absolute;
  left: 36px;
  top: 427px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u395 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u395_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u396_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 434px;
  height: 10px;
}
#u396 {
  border-width: 0px;
  position: absolute;
  left: 31px;
  top: 459px;
  width: 434px;
  height: 10px;
  display: flex;
}
#u396 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u396_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}

#u399 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u400_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u400 {
  border-width: 0px;
  position: absolute;
  left: 437px;
  top: 561px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u400 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u400_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u401_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u401 {
  border-width: 0px;
  position: absolute;
  left: 437px;
  top: 584px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u401 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u401_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u402_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u402 {
  border-width: 0px;
  position: absolute;
  left: 437px;
  top: 611px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u402 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u402_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u403_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u403 {
  border-width: 0px;
  position: absolute;
  left: 421px;
  top: 562px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u403 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u403_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u404_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u404 {
  border-width: 0px;
  position: absolute;
  left: 437px;
  top: 633px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u404 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u404_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u405_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u405 {
  border-width: 0px;
  position: absolute;
  left: 421px;
  top: 585px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u405 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u405_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u406_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u406 {
  border-width: 0px;
  position: absolute;
  left: 421px;
  top: 611px;
  width: 12px;
  height: 12px;
  display: flex;
  color: #22d989;
}
#u406 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u406_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u407_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u407 {
  border-width: 0px;
  position: absolute;
  left: 421px;
  top: 634px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u407 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u407_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u408_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 145px;
  height: 161px;
}
#u408 {
  border-width: 0px;
  position: absolute;
  left: 270px;
  top: 515px;
  width: 145px;
  height: 161px;
  display: flex;
}
#u408 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u408_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u409 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u410 {
  border-width: 0px;
  position: absolute;
  left: 110px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u411 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u412_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u412 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 649px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u412 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u412_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u413_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u413 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 614px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u413 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u413_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u414_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u414 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 579px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u414 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u414_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u415_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u415 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 544px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u415 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u415_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u416_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u416 {
  border-width: 0px;
  position: absolute;
  left: 43px;
  top: 509px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u416 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u416_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u417 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u418_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #d9dce1;
  text-align: center;
}
#u418 {
  border-width: 0px;
  position: absolute;
  left: 47px;
  top: 656px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #d9dce1;
  text-align: center;
}
#u418 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u418_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u419_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #d9dce1;
  text-align: center;
}
#u419 {
  border-width: 0px;
  position: absolute;
  left: 104px;
  top: 656px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #d9dce1;
  text-align: center;
}
#u419 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u419_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u420_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #d9dce1;
  text-align: center;
}
#u420 {
  border-width: 0px;
  position: absolute;
  left: 163px;
  top: 656px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #d9dce1;
  text-align: center;
}
#u420 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u420_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u421 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u422 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u423_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 39px;
}
#u423 {
  border-width: 0px;
  position: absolute;
  left: 56px;
  top: 611px;
  width: 23px;
  height: 39px;
  display: flex;
}
#u423 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u423_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u424_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 28px;
}
#u424 {
  border-width: 0px;
  position: absolute;
  left: 56px;
  top: 583px;
  width: 23px;
  height: 28px;
  display: flex;
}
#u424 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u424_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u425_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 27px;
}
#u425 {
  border-width: 0px;
  position: absolute;
  left: 56px;
  top: 556px;
  width: 23px;
  height: 27px;
  display: flex;
}
#u425 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u425_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u426_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u426 {
  border-width: 0px;
  position: absolute;
  left: 64px;
  top: 554px;
  width: 8px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u426 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u426_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u427_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 48px;
}
#u427 {
  border-width: 0px;
  position: absolute;
  left: 111px;
  top: 602px;
  width: 23px;
  height: 48px;
  display: flex;
}
#u427 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u427_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u428_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 34px;
}
#u428 {
  border-width: 0px;
  position: absolute;
  left: 111px;
  top: 568px;
  width: 23px;
  height: 34px;
  display: flex;
}
#u428 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u428_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u429_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 33px;
}
#u429 {
  border-width: 0px;
  position: absolute;
  left: 111px;
  top: 535px;
  width: 23px;
  height: 33px;
  display: flex;
}
#u429 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u429_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u430_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 16px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u430 {
  border-width: 0px;
  position: absolute;
  left: 114px;
  top: 533px;
  width: 16px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u430 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u430_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u431_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 35px;
}
#u431 {
  border-width: 0px;
  position: absolute;
  left: 167px;
  top: 615px;
  width: 23px;
  height: 35px;
  display: flex;
}
#u431 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u431_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u432_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 26px;
}
#u432 {
  border-width: 0px;
  position: absolute;
  left: 167px;
  top: 589px;
  width: 23px;
  height: 26px;
  display: flex;
}
#u432 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u432_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u433_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 25px;
}
#u433 {
  border-width: 0px;
  position: absolute;
  left: 167px;
  top: 564px;
  width: 23px;
  height: 25px;
  display: flex;
}
#u433 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u433_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u434_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u434 {
  border-width: 0px;
  position: absolute;
  left: 176px;
  top: 564px;
  width: 8px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u434 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u434_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u435_img {
  border-width: 0px;
  position: absolute;
  left: -1px;
  top: -1px;
  width: 111px;
  height: 27px;
}
#u435 {
  border-width: 0px;
  position: absolute;
  left: 68px;
  top: 549px;
  width: 108px;
  height: 24px;
  display: flex;
}
#u435 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u435_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u436_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u436 {
  border-width: 0px;
  position: absolute;
  left: 227px;
  top: 554px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u436 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u436_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u437_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u437 {
  border-width: 0px;
  position: absolute;
  left: 227px;
  top: 591px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u437 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u437_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u438_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u438 {
  border-width: 0px;
  position: absolute;
  left: 227px;
  top: 626px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u438 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u438_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u439_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u439 {
  border-width: 0px;
  position: absolute;
  left: 211px;
  top: 555px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u439 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u439_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u440_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u440 {
  border-width: 0px;
  position: absolute;
  left: 211px;
  top: 591px;
  width: 12px;
  height: 12px;
  display: flex;
  color: #22d989;
}
#u440 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u440_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u441_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u441 {
  border-width: 0px;
  position: absolute;
  left: 211px;
  top: 627px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u441 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u441_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u442 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u443_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u443 {
  border-width: 0px;
  position: absolute;
  left: 339px;
  top: 429px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u443 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u443_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u444_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u444 {
  border-width: 0px;
  position: absolute;
  left: 381px;
  top: 429px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u444 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u444_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u445_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u445 {
  border-width: 0px;
  position: absolute;
  left: 381px;
  top: 429px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u445 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u445_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u446_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u446 {
  border-width: 0px;
  position: absolute;
  left: 392px;
  top: 431px;
  width: 21px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u446 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u446_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u446.selected {
}
#u446_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u447_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u447 {
  border-width: 0px;
  position: absolute;
  left: 339px;
  top: 429px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u447 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u447_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u448_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u448 {
  border-width: 0px;
  position: absolute;
  left: 350px;
  top: 431px;
  width: 21px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u448 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u448_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u448.selected {
}
#u448_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u449_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u449 {
  border-width: 0px;
  position: absolute;
  left: 423px;
  top: 429px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u449 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u449_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u450_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u450 {
  border-width: 0px;
  position: absolute;
  left: 423px;
  top: 429px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u450 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u450_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u451_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u451 {
  border-width: 0px;
  position: absolute;
  left: 434px;
  top: 431px;
  width: 21px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u451 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u451_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u451.selected {
}
#u451_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u452 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u453 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u454_img {
  border-width: 0px;
  position: absolute;
  left: -8px;
  top: -4px;
  width: 116px;
  height: 52px;
}
#u454 {
  border-width: 0px;
  position: absolute;
  left: 1527px;
  top: 437px;
  width: 100px;
  height: 36px;
  display: flex;
  font-family: '庞门正道标题体免费版 常规', '庞门正道标题体免费版', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 24px;
  color: rgba(238, 240, 244, 0.980392156862745);
  text-align: left;
  line-height: 32px;
}
#u454 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u454_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u455_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 27px;
}
#u455 {
  border-width: 0px;
  position: absolute;
  left: 1487px;
  top: 443px;
  width: 26px;
  height: 27px;
  display: flex;
}
#u455 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u455_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u456_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 434px;
  height: 10px;
}
#u456 {
  border-width: 0px;
  position: absolute;
  left: 1482px;
  top: 475px;
  width: 434px;
  height: 10px;
  display: flex;
}
#u456 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u456_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}

#u459 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u460_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u460 {
  border-width: 0px;
  position: absolute;
  left: 1887px;
  top: 553px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u460 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u460_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u461_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u461 {
  border-width: 0px;
  position: absolute;
  left: 1887px;
  top: 576px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u461 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u461_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u462_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u462 {
  border-width: 0px;
  position: absolute;
  left: 1887px;
  top: 603px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u462 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u462_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u463_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u463 {
  border-width: 0px;
  position: absolute;
  left: 1871px;
  top: 554px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u463 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u463_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u464_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u464 {
  border-width: 0px;
  position: absolute;
  left: 1887px;
  top: 625px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u464 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u464_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u465_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u465 {
  border-width: 0px;
  position: absolute;
  left: 1871px;
  top: 577px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u465 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u465_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u466_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u466 {
  border-width: 0px;
  position: absolute;
  left: 1871px;
  top: 603px;
  width: 12px;
  height: 12px;
  display: flex;
  color: #22d989;
}
#u466 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u466_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u467_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u467 {
  border-width: 0px;
  position: absolute;
  left: 1871px;
  top: 626px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u467 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u467_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u468_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 145px;
  height: 161px;
}
#u468 {
  border-width: 0px;
  position: absolute;
  left: 1720px;
  top: 507px;
  width: 145px;
  height: 161px;
  display: flex;
}
#u468 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u468_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u469 {
  border-width: 0px;
  position: absolute;
  left: 110px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u470 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u471 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u472_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u472 {
  border-width: 0px;
  position: absolute;
  left: 1493px;
  top: 642px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u472 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u472_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u473_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u473 {
  border-width: 0px;
  position: absolute;
  left: 1493px;
  top: 607px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u473 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u473_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u474_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u474 {
  border-width: 0px;
  position: absolute;
  left: 1493px;
  top: 572px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u474 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u474_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u475_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u475 {
  border-width: 0px;
  position: absolute;
  left: 1493px;
  top: 537px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u475 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u475_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u476_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 158px;
  height: 2px;
}
#u476 {
  border-width: 0px;
  position: absolute;
  left: 1493px;
  top: 502px;
  width: 157px;
  height: 1px;
  display: flex;
  opacity: 0.27;
}
#u476 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u476_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u477_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #d9dce1;
  text-align: center;
}
#u477 {
  border-width: 0px;
  position: absolute;
  left: 1497px;
  top: 649px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #d9dce1;
  text-align: center;
}
#u477 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u477_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u478_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #d9dce1;
  text-align: center;
}
#u478 {
  border-width: 0px;
  position: absolute;
  left: 1554px;
  top: 649px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #d9dce1;
  text-align: center;
}
#u478 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u478_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u479_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #d9dce1;
  text-align: center;
}
#u479 {
  border-width: 0px;
  position: absolute;
  left: 1613px;
  top: 649px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #d9dce1;
  text-align: center;
}
#u479 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u479_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u480 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u481 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u482_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 39px;
}
#u482 {
  border-width: 0px;
  position: absolute;
  left: 1506px;
  top: 604px;
  width: 23px;
  height: 39px;
  display: flex;
}
#u482 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u482_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u483_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 28px;
}
#u483 {
  border-width: 0px;
  position: absolute;
  left: 1506px;
  top: 576px;
  width: 23px;
  height: 28px;
  display: flex;
}
#u483 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u483_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u484_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 27px;
}
#u484 {
  border-width: 0px;
  position: absolute;
  left: 1506px;
  top: 549px;
  width: 23px;
  height: 27px;
  display: flex;
}
#u484 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u484_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u485_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u485 {
  border-width: 0px;
  position: absolute;
  left: 1514px;
  top: 547px;
  width: 8px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u485 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u485_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u486_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 48px;
}
#u486 {
  border-width: 0px;
  position: absolute;
  left: 1561px;
  top: 595px;
  width: 23px;
  height: 48px;
  display: flex;
}
#u486 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u486_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u487_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 34px;
}
#u487 {
  border-width: 0px;
  position: absolute;
  left: 1561px;
  top: 561px;
  width: 23px;
  height: 34px;
  display: flex;
}
#u487 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u487_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u488_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 33px;
}
#u488 {
  border-width: 0px;
  position: absolute;
  left: 1561px;
  top: 528px;
  width: 23px;
  height: 33px;
  display: flex;
}
#u488 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u488_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u489_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 16px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u489 {
  border-width: 0px;
  position: absolute;
  left: 1564px;
  top: 526px;
  width: 16px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u489 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u489_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u490_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 35px;
}
#u490 {
  border-width: 0px;
  position: absolute;
  left: 1617px;
  top: 608px;
  width: 23px;
  height: 35px;
  display: flex;
}
#u490 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u490_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u491_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 26px;
}
#u491 {
  border-width: 0px;
  position: absolute;
  left: 1617px;
  top: 582px;
  width: 23px;
  height: 26px;
  display: flex;
}
#u491 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u491_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u492_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 23px;
  height: 25px;
}
#u492 {
  border-width: 0px;
  position: absolute;
  left: 1617px;
  top: 557px;
  width: 23px;
  height: 25px;
  display: flex;
}
#u492 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u492_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u493_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u493 {
  border-width: 0px;
  position: absolute;
  left: 1626px;
  top: 557px;
  width: 8px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u493 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u493_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u494_img {
  border-width: 0px;
  position: absolute;
  left: -1px;
  top: -1px;
  width: 111px;
  height: 27px;
}
#u494 {
  border-width: 0px;
  position: absolute;
  left: 1518px;
  top: 549px;
  width: 108px;
  height: 24px;
  display: flex;
}
#u494 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u494_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u495_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u495 {
  border-width: 0px;
  position: absolute;
  left: 1671px;
  top: 545px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u495 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u495_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u496_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u496 {
  border-width: 0px;
  position: absolute;
  left: 1671px;
  top: 582px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u496 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u496_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u497_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u497 {
  border-width: 0px;
  position: absolute;
  left: 1671px;
  top: 617px;
  width: 28px;
  height: 16px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u497 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u497_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u498_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u498 {
  border-width: 0px;
  position: absolute;
  left: 1655px;
  top: 546px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u498 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u498_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u499_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u499 {
  border-width: 0px;
  position: absolute;
  left: 1655px;
  top: 582px;
  width: 12px;
  height: 12px;
  display: flex;
  color: #22d989;
}
#u499 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u499_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u500_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 12px;
}
#u500 {
  border-width: 0px;
  position: absolute;
  left: 1655px;
  top: 618px;
  width: 12px;
  height: 12px;
  display: flex;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u500 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u500_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u501 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u502_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u502 {
  border-width: 0px;
  position: absolute;
  left: 1790px;
  top: 443px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u502 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u502_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u503_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u503 {
  border-width: 0px;
  position: absolute;
  left: 1832px;
  top: 443px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u503 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u503_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u504_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u504 {
  border-width: 0px;
  position: absolute;
  left: 1832px;
  top: 443px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u504 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u504_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u505_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u505 {
  border-width: 0px;
  position: absolute;
  left: 1843px;
  top: 445px;
  width: 21px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u505 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u505_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u505.selected {
}
#u505_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u506_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u506 {
  border-width: 0px;
  position: absolute;
  left: 1790px;
  top: 443px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u506 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u506_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u507_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u507 {
  border-width: 0px;
  position: absolute;
  left: 1801px;
  top: 445px;
  width: 21px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u507 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u507_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u507.selected {
}
#u507_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u508_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(53, 255, 245, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u508 {
  border-width: 0px;
  position: absolute;
  left: 1874px;
  top: 443px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u508 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u508_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u509_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 28px;
  background: inherit;
  background-color: rgba(12, 42, 85, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(29, 91, 175, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u509 {
  border-width: 0px;
  position: absolute;
  left: 1874px;
  top: 443px;
  width: 42px;
  height: 28px;
  display: flex;
}
#u509 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u509_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u510_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u510 {
  border-width: 0px;
  position: absolute;
  left: 1885px;
  top: 445px;
  width: 21px;
  height: 24px;
  display: flex;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u510 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u510_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 21px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.8);
}
#u510.selected {
}
#u510_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u511 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u512 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u513_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 174px;
  height: 60px;
}
#u513 {
  border-width: 0px;
  position: absolute;
  left: 595px;
  top: 63px;
  width: 174px;
  height: 60px;
  display: flex;
  opacity: 0.6;
}
#u513 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u513_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u514_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 40px;
}
#u514 {
  border-width: 0px;
  position: absolute;
  left: 603px;
  top: 73px;
  width: 40px;
  height: 40px;
  display: flex;
}
#u514 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u514_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u515_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 56px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u515 {
  border-width: 0px;
  position: absolute;
  left: 649px;
  top: 99px;
  width: 56px;
  height: 16px;
  display: flex;
  font-size: 14px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u515 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u515_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u516_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 92px;
  height: 27px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #00edff;
}
#u516 {
  border-width: 0px;
  position: absolute;
  left: 649px;
  top: 71px;
  width: 92px;
  height: 27px;
  display: flex;
  color: #00edff;
}
#u516 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u516_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u517 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u518_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 178px;
  height: 60px;
}
#u518 {
  border-width: 0px;
  position: absolute;
  left: 785px;
  top: 63px;
  width: 178px;
  height: 60px;
  display: flex;
  opacity: 0.72;
}
#u518 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u518_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u519_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 14px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u519 {
  border-width: 0px;
  position: absolute;
  left: 842px;
  top: 99px;
  width: 96px;
  height: 14px;
  display: flex;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u519 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u519_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u520_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 76px;
  height: 27px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family:
    'Montserrat Alternates Bold', 'Montserrat Alternates Regular', 'Montserrat Alternates',
    sans-serif;
  font-weight: 700;
  font-style: normal;
  color: #00edff;
}
#u520 {
  border-width: 0px;
  position: absolute;
  left: 842px;
  top: 71px;
  width: 76px;
  height: 27px;
  display: flex;
  font-family:
    'Montserrat Alternates Bold', 'Montserrat Alternates Regular', 'Montserrat Alternates',
    sans-serif;
  font-weight: 700;
  font-style: normal;
  color: #00edff;
}
#u520 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u520_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u521_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 44px;
  height: 44px;
}
#u521 {
  border-width: 0px;
  position: absolute;
  left: 794px;
  top: 71px;
  width: 44px;
  height: 44px;
  display: flex;
}
#u521 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u521_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u522 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u523_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 170px;
  height: 60px;
}
#u523 {
  border-width: 0px;
  position: absolute;
  left: 979px;
  top: 63px;
  width: 170px;
  height: 60px;
  display: flex;
}
#u523 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u523_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u524_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 72px;
  height: 14px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 12px;
  color: rgba(205, 211, 223, 0.996078431372549);
}
#u524 {
  border-width: 0px;
  position: absolute;
  left: 1033px;
  top: 100px;
  width: 72px;
  height: 14px;
  display: flex;
  font-size: 12px;
  color: rgba(205, 211, 223, 0.996078431372549);
}
#u524 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u524_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u525_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 94px;
  height: 27px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #00edff;
}
#u525 {
  border-width: 0px;
  position: absolute;
  left: 1034px;
  top: 72px;
  width: 94px;
  height: 27px;
  display: flex;
  color: #00edff;
}
#u525 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u525_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u526_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 37px;
  height: 39px;
}
#u526 {
  border-width: 0px;
  position: absolute;
  left: 988px;
  top: 74px;
  width: 37px;
  height: 39px;
  display: flex;
}
#u526 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u526_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u527 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u528_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 167px;
  height: 60px;
}
#u528 {
  border-width: 0px;
  position: absolute;
  left: 1165px;
  top: 63px;
  width: 167px;
  height: 60px;
  display: flex;
}
#u528 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u528_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u529_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 72px;
  height: 14px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u529 {
  border-width: 0px;
  position: absolute;
  left: 1221px;
  top: 97px;
  width: 72px;
  height: 14px;
  display: flex;
  font-size: 12px;
  color: rgba(229, 236, 248, 0.996078431372549);
}
#u529 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u529_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u530_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 94px;
  height: 27px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #00edff;
}
#u530 {
  border-width: 0px;
  position: absolute;
  left: 1221px;
  top: 71px;
  width: 94px;
  height: 27px;
  display: flex;
  color: #00edff;
}
#u530 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u530_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u531_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 40px;
}
#u531 {
  border-width: 0px;
  position: absolute;
  left: 1173px;
  top: 73px;
  width: 40px;
  height: 40px;
  display: flex;
}
#u531 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u531_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u532 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u533_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 107px;
  height: 69px;
}
#u533 {
  border-width: 0px;
  position: absolute;
  left: 1630px;
  top: 24px;
  width: 107px;
  height: 69px;
  display: flex;
}
#u533 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u533_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u534_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 28px;
}
#u534 {
  border-width: 0px;
  position: absolute;
  left: 1630px;
  top: 44px;
  width: 8px;
  height: 28px;
  display: flex;
}
#u534 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u534_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u535_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 27px;
}
#u535 {
  border-width: 0px;
  position: absolute;
  left: 1729px;
  top: 45px;
  width: 8px;
  height: 27px;
  display: flex;
}
#u535 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u535_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u536_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 85px;
  height: 34px;
}
#u536 {
  border-width: 0px;
  position: absolute;
  left: 1641px;
  top: 41px;
  width: 85px;
  height: 34px;
  display: flex;
}
#u536 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u536_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u537 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u538_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 107px;
  height: 69px;
}
#u538 {
  border-width: 0px;
  position: absolute;
  left: 190px;
  top: 24px;
  width: 107px;
  height: 69px;
  display: flex;
}
#u538 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u538_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u539_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 28px;
}
#u539 {
  border-width: 0px;
  position: absolute;
  left: 190px;
  top: 44px;
  width: 8px;
  height: 28px;
  display: flex;
}
#u539 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u539_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u540_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 8px;
  height: 27px;
}
#u540 {
  border-width: 0px;
  position: absolute;
  left: 289px;
  top: 45px;
  width: 8px;
  height: 27px;
  display: flex;
}
#u540 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u540_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u541_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 85px;
  height: 60px;
}
#u541 {
  border-width: 0px;
  position: absolute;
  left: 200px;
  top: 29px;
  width: 85px;
  height: 60px;
  display: flex;
  font-family: '优设标题黑', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 46px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u541 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u541_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u542 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u543_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 68px;
}
#u543 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1920px;
  height: 68px;
  display: flex;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 10px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
}
#u543 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u543_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u544_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 328px;
  height: 40px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '优设标题黑', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 34px;
  letter-spacing: 1.5px;
  color: #f4f8ff;
  text-align: center;
  line-height: 40px;
}
#u544 {
  border-width: 0px;
  position: absolute;
  left: 796px;
  top: 13px;
  width: 328px;
  height: 40px;
  display: flex;
  font-family: '优设标题黑', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 34px;
  letter-spacing: 1.5px;
  color: #f4f8ff;
  text-align: center;
  line-height: 40px;
}
#u544 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u544_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u545_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 490px;
  height: 15px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '优设标题黑', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  letter-spacing: 1.5px;
  color: #f4f8ff;
  text-align: center;
  line-height: 15px;
}
#u545 {
  border-width: 0px;
  position: absolute;
  left: 724px;
  top: 53px;
  width: 490px;
  height: 15px;
  display: flex;
  opacity: 0.54;
  font-family: '优设标题黑', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  letter-spacing: 1.5px;
  color: #f4f8ff;
  text-align: center;
  line-height: 15px;
}
#u545 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u545_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u546_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 116px;
  height: 13px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'D-DIN Exp DINExp-Bold', 'D-DIN Exp Regular', 'D-DIN Exp', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  letter-spacing: 0.2px;
  color: rgba(254, 254, 254, 0.996078431372549);
  line-height: 16px;
}
#u546 {
  border-width: 0px;
  position: absolute;
  left: 250px;
  top: 15px;
  width: 116px;
  height: 13px;
  display: flex;
  font-family: 'D-DIN Exp DINExp-Bold', 'D-DIN Exp Regular', 'D-DIN Exp', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 12px;
  letter-spacing: 0.2px;
  color: rgba(254, 254, 254, 0.996078431372549);
  line-height: 16px;
}
#u546 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u546_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u547_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 33px;
}
#u547 {
  border-width: 0px;
  position: absolute;
  left: 24px;
  top: 8px;
  width: 40px;
  height: 33px;
  display: flex;
}
#u547 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u547_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u548_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 98px;
  height: 32px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 24px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 32px;
}
#u548 {
  border-width: 0px;
  position: absolute;
  left: 112px;
  top: 8px;
  width: 98px;
  height: 32px;
  display: flex;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 24px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 32px;
}
#u548 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u548_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u549_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 22px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 18px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 24px;
}
#u549 {
  border-width: 0px;
  position: absolute;
  left: 70px;
  top: 6px;
  width: 22px;
  height: 24px;
  display: flex;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 18px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 24px;
}
#u549 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u549_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u550_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 9px;
  height: 10px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 8px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 10px;
}
#u550 {
  border-width: 0px;
  position: absolute;
  left: 101px;
  top: 11px;
  width: 9px;
  height: 10px;
  display: flex;
  font-family: 'Orbitron Bold', 'Orbitron', sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 8px;
  letter-spacing: 0.2px;
  color: rgba(255, 255, 255, 0.996078431372549);
  line-height: 10px;
}
#u550 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u550_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u551_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 26px;
  height: 13px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: 'D-DIN Exp', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 8px;
  letter-spacing: 0.2px;
  color: rgba(254, 254, 254, 0.8);
  line-height: 8px;
}
#u551 {
  border-width: 0px;
  position: absolute;
  left: 72px;
  top: 30px;
  width: 26px;
  height: 13px;
  display: flex;
  font-family: 'D-DIN Exp', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 8px;
  letter-spacing: 0.2px;
  color: rgba(254, 254, 254, 0.8);
  line-height: 8px;
}
#u551 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u551_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u552 {
  border-width: 0px;
  position: absolute;
  left: 481px;
  top: 146px;
  width: 970px;
  height: 820px;
}
#u552_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u552_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u553 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  touch-action: none;
}
#u553_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u553_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u554_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 80px;
  width: 500px;
  height: 720px;
}
#u554 {
  border-width: 0px;
  position: absolute;
  left: 200px;
  top: 0px;
  width: 500px;
  height: 820px;
  display: flex;
}
#u554 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u554_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u552_state1 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u552_state1_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u555_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 251px;
  height: 44px;
}
#u555 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 251px;
  height: 44px;
  display: flex;
}
#u555 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u555_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u556_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 31px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fedc00;
}
#u556 {
  border-width: 0px;
  position: absolute;
  left: 6px;
  top: 14px;
  width: 31px;
  height: 16px;
  display: flex;
  color: #fedc00;
}
#u556 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u556_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u557_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 19px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '华康方圆体W7(P) ', '华康方圆体W7(P)', sans-serif;
  font-weight: 400;
  font-style: normal;
  color: #000000;
}
#u557 {
  border-width: 0px;
  position: absolute;
  left: 37px;
  top: 14px;
  width: 42px;
  height: 19px;
  display: flex;
  font-family: '华康方圆体W7(P) ', '华康方圆体W7(P)', sans-serif;
  font-weight: 400;
  font-style: normal;
  color: #000000;
}
#u557 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u557_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u558_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 17px;
  height: 32px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 28px;
}
#u558 {
  border-width: 0px;
  position: absolute;
  left: 226px;
  top: 6px;
  width: 17px;
  height: 32px;
  display: flex;
  font-size: 28px;
}
#u558 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u558_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u559_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 251px;
  height: 387px;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u559 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 44px;
  width: 251px;
  height: 387px;
  display: flex;
}
#u559 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u559_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u560_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 98px;
  height: 32px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 22px;
}
#u560 {
  border-width: 0px;
  position: absolute;
  left: 8px;
  top: 66px;
  width: 98px;
  height: 32px;
  display: flex;
  font-size: 22px;
}
#u560 .text {
  position: absolute;
  align-self: center;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u560_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u561_input {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  padding: 2px 2px 2px 2px;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  letter-spacing: normal;
  color: #000000;
  vertical-align: none;
  text-align: left;
  text-transform: none;
  background-color: transparent;
  border-color: transparent;
}
#u561_input.focused {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  padding: 2px 2px 2px 2px;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  letter-spacing: normal;
  color: #000000;
  vertical-align: none;
  text-align: left;
  text-transform: none;
  background-color: transparent;
  border-color: transparent;
}
#u561_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u561 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 128px;
  width: 231px;
  height: 25px;
  display: flex;
}
#u561 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u561_div.focused {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u561.focused {
}
#u562_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 232px;
  height: 2px;
}
#u562 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 154px;
  width: 231px;
  height: 1px;
  display: flex;
}
#u562 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u562_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u563_input {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  padding: 2px 2px 2px 2px;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  letter-spacing: normal;
  color: #000000;
  vertical-align: none;
  text-align: left;
  text-transform: none;
  background-color: transparent;
  border-color: transparent;
}
#u563_input.focused {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  padding: 2px 2px 2px 2px;
  font-family: '';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  letter-spacing: normal;
  color: #000000;
  vertical-align: none;
  text-align: left;
  text-transform: none;
  background-color: transparent;
  border-color: transparent;
}
#u563_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u563 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 195px;
  width: 231px;
  height: 25px;
  display: flex;
}
#u563 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u563_div.focused {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 25px;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u563.focused {
}
#u564_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 232px;
  height: 2px;
}
#u564 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 221px;
  width: 231px;
  height: 1px;
  display: flex;
}
#u564 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u564_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u565_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 34px;
  background: inherit;
  background-color: rgba(206, 206, 206, 1);
  box-sizing: border-box;
  border-width: 2px;
  border-style: solid;
  border-color: rgba(64, 158, 255, 1);
  border-radius: 53px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #646464;
}
#u565 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 259px;
  width: 231px;
  height: 34px;
  display: flex;
  color: #646464;
}
#u565 .text {
  position: absolute;
  align-self: center;
  padding: 2px 12px 2px 12px;
  box-sizing: border-box;
  width: 100%;
}
#u565_div.selected {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 34px;
  background: inherit;
  background-color: rgba(241, 219, 81, 1);
  box-sizing: border-box;
  border-width: 2px;
  border-style: solid;
  border-color: rgba(64, 158, 255, 1);
  border-radius: 53px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #646464;
}
#u565.selected {
}
#u565_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u566_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 20px;
  height: 24px;
  background: inherit;
  background-color: rgba(255, 255, 255, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(121, 121, 121, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u566 {
  border-width: 0px;
  position: absolute;
  left: 13px;
  top: 300px;
  width: 20px;
  height: 24px;
  display: flex;
}
#u566 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u566_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u567_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 153px;
  height: 108px;
  background: inherit;
  background-color: rgba(215, 212, 212, 0.996078431372549);
  border: none;
  border-radius: 23px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 18px;
}
#u567 {
  border-width: 0px;
  position: absolute;
  left: 49px;
  top: 142px;
  width: 153px;
  height: 108px;
  display: flex;
  font-size: 18px;
}
#u567 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u567_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
}
#u552_state2 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u552_state2_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u568_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 31px;
  height: 31px;
}
#u568 {
  border-width: 0px;
  position: absolute;
  left: 20px;
  top: 31px;
  width: 31px;
  height: 31px;
  display: flex;
}
#u568 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u568_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u569_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 32px;
  height: 32px;
}
#u569 {
  border-width: 0px;
  position: absolute;
  left: 79px;
  top: 30px;
  width: 32px;
  height: 32px;
  display: flex;
}
#u569 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u569_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u570_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 32px;
  height: 32px;
}
#u570 {
  border-width: 0px;
  position: absolute;
  left: 139px;
  top: 30px;
  width: 32px;
  height: 32px;
  display: flex;
}
#u570 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u570_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u571_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 32px;
  height: 32px;
}
#u571 {
  border-width: 0px;
  position: absolute;
  left: 199px;
  top: 30px;
  width: 32px;
  height: 32px;
  display: flex;
}
#u571 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u571_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u572_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 20px;
  height: 11px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 10px;
}
#u572 {
  border-width: 0px;
  position: absolute;
  left: 22px;
  top: 67px;
  width: 20px;
  height: 11px;
  display: flex;
  font-size: 10px;
}
#u572 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u572_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u573_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 11px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 10px;
}
#u573 {
  border-width: 0px;
  position: absolute;
  left: 75px;
  top: 67px;
  width: 40px;
  height: 11px;
  display: flex;
  font-size: 10px;
}
#u573 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u573_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u574_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 20px;
  height: 11px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 10px;
}
#u574 {
  border-width: 0px;
  position: absolute;
  left: 145px;
  top: 67px;
  width: 20px;
  height: 11px;
  display: flex;
  font-size: 10px;
}
#u574 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u574_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u575_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 11px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 10px;
}
#u575 {
  border-width: 0px;
  position: absolute;
  left: 195px;
  top: 67px;
  width: 40px;
  height: 11px;
  display: flex;
  font-size: 10px;
}
#u575 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u575_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u576 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 84px;
  width: 251px;
  height: 310px;
  touch-action: none;
}
#u576_state0 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 251px;
  height: 310px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u576_state0_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u577 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u578_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 117px;
}
#u578 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 4px;
  width: 231px;
  height: 117px;
  display: flex;
}
#u578 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u578_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u579_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 117px;
}
#u579 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 132px;
  width: 231px;
  height: 117px;
  display: flex;
}
#u579 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u579_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u580_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 117px;
}
#u580 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 262px;
  width: 231px;
  height: 117px;
  display: flex;
}
#u580 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u580_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u581_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 231px;
  height: 117px;
}
#u581 {
  border-width: 0px;
  position: absolute;
  left: 10px;
  top: 393px;
  width: 231px;
  height: 117px;
  display: flex;
}
#u581 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u581_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u552_state3 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u552_state3_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u552_state4 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 970px;
  height: 820px;
  -ms-overflow-x: hidden;
  overflow-x: hidden;
  -ms-overflow-y: hidden;
  overflow-y: hidden;
  background-image: none;
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  visibility: hidden;
}
#u552_state4_content {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1px;
  height: 1px;
}
#u582 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 250px;
  width: 0px;
  height: 0px;
}
#u583 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u584_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 80px;
  height: 98px;
  background: inherit;
  background-color: rgba(2, 42, 85, 0.729411764705882);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(25, 118, 215, 1);
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u584 {
  border-width: 0px;
  position: absolute;
  left: 492px;
  top: 574px;
  width: 80px;
  height: 98px;
  display: flex;
}
#u584 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u584_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u585_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u585 {
  border-width: 0px;
  position: absolute;
  left: 522px;
  top: 586px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u585 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u585_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u586_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u586 {
  border-width: 0px;
  position: absolute;
  left: 522px;
  top: 615px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u586 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u586_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u587_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 42px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #ffffff;
}
#u587 {
  border-width: 0px;
  position: absolute;
  left: 522px;
  top: 644px;
  width: 42px;
  height: 16px;
  display: flex;
  color: #ffffff;
}
#u587 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u587_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u588_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 18px;
}
#u588 {
  border-width: 0px;
  position: absolute;
  left: 500px;
  top: 586px;
  width: 12px;
  height: 18px;
  display: flex;
}
#u588 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u588_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u589_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 18px;
}
#u589 {
  border-width: 0px;
  position: absolute;
  left: 500px;
  top: 614px;
  width: 12px;
  height: 18px;
  display: flex;
}
#u589 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u589_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u590_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 12px;
  height: 18px;
}
#u590 {
  border-width: 0px;
  position: absolute;
  left: 500px;
  top: 643px;
  width: 12px;
  height: 18px;
  display: flex;
}
#u590 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u590_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u591_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 54px;
  height: 54px;
}
#u591 {
  border-width: 0px;
  position: absolute;
  left: 1393px;
  top: 404px;
  width: 54px;
  height: 54px;
  display: flex;
}
#u591 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u591_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u592_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 109px;
}
#u592 {
  border-width: 0px;
  position: absolute;
  left: 1400px;
  top: 563px;
  width: 40px;
  height: 109px;
  display: flex;
}
#u592 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u592_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u593_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 40px;
}
#u593 {
  border-width: 0px;
  position: absolute;
  left: 1400px;
  top: 515px;
  width: 40px;
  height: 40px;
  display: flex;
}
#u593 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u593_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u594_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 40px;
  height: 40px;
}
#u594 {
  border-width: 0px;
  position: absolute;
  left: 1400px;
  top: 467px;
  width: 40px;
  height: 40px;
  display: flex;
}
#u594 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u594_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u595 {
  border-width: 0px;
  position: absolute;
  left: 1400px;
  top: 563px;
  width: 40px;
  height: 35px;
  overflow: hidden;
  background-image: url('../../resources/images/transparent.gif');
}
#u596 {
  border-width: 0px;
  position: absolute;
  left: 1400px;
  top: 637px;
  width: 40px;
  height: 35px;
  overflow: hidden;
  background-image: url('../../resources/images/transparent.gif');
}
#u597 {
  border-width: 0px;
  position: absolute;
  left: 1400px;
  top: 515px;
  width: 40px;
  height: 40px;
  overflow: hidden;
  background-image: url('../../resources/images/transparent.gif');
}
#u598 {
  border-width: 0px;
  position: absolute;
  left: 883px;
  top: 448px;
  width: 23px;
  height: 23px;
  overflow: hidden;
  background-image: url('../../resources/images/transparent.gif');
}
#u599 {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}
#u600_img {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 325px;
  height: 135px;
}
#u600 {
  border-width: 0px;
  position: absolute;
  left: 724px;
  top: 321px;
  width: 325px;
  height: 135px;
  display: flex;
}
#u600 .text {
  position: absolute;
  align-self: center;
  padding: 2px 2px 2px 2px;
  box-sizing: border-box;
  width: 100%;
}
#u600_text {
  border-width: 0px;
  word-wrap: break-word;
  text-transform: none;
  visibility: hidden;
}
#u601_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 180px;
  height: 21px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  color: #ffffff;
}
#u601 {
  border-width: 0px;
  position: absolute;
  left: 792px;
  top: 326px;
  width: 180px;
  height: 21px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  color: #ffffff;
}
#u601 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u601_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u602_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 112px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  color: #ffffff;
}
#u602 {
  border-width: 0px;
  position: absolute;
  left: 732px;
  top: 352px;
  width: 112px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  color: #ffffff;
}
#u602 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u602_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u603_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 20px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u603 {
  border-width: 0px;
  position: absolute;
  left: 735px;
  top: 378px;
  width: 96px;
  height: 20px;
  display: flex;
}
#u603 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u603_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u604_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 20px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u604 {
  border-width: 0px;
  position: absolute;
  left: 842px;
  top: 378px;
  width: 96px;
  height: 20px;
  display: flex;
}
#u604 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u604_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u605_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 20px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u605 {
  border-width: 0px;
  position: absolute;
  left: 951px;
  top: 378px;
  width: 96px;
  height: 20px;
  display: flex;
}
#u605 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u605_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u606_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 112px;
  height: 16px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  color: #ffffff;
}
#u606 {
  border-width: 0px;
  position: absolute;
  left: 732px;
  top: 408px;
  width: 112px;
  height: 16px;
  display: flex;
  font-family: '';
  font-weight: 700;
  font-style: normal;
  color: #ffffff;
}
#u606 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u606_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u607_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 20px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u607 {
  border-width: 0px;
  position: absolute;
  left: 732px;
  top: 430px;
  width: 96px;
  height: 20px;
  display: flex;
}
#u607 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u607_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u608_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 20px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u608 {
  border-width: 0px;
  position: absolute;
  left: 845px;
  top: 430px;
  width: 96px;
  height: 20px;
  display: flex;
}
#u608 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u608_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
#u609_div {
  border-width: 0px;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 96px;
  height: 20px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  border: none;
  border-radius: 0px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#u609 {
  border-width: 0px;
  position: absolute;
  left: 954px;
  top: 430px;
  width: 96px;
  height: 20px;
  display: flex;
}
#u609 .text {
  position: absolute;
  align-self: flex-start;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  width: 100%;
}
#u609_text {
  border-width: 0px;
  white-space: nowrap;
  text-transform: none;
}
