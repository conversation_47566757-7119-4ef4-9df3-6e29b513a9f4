/**
 * Mock 数据生成器
 * 用于生成更真实的统计数据
 */

/**
 * 生成指定范围内的随机整数
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成指定范围内的随机浮点数
 */
export function randomFloat(min: number, max: number, decimals: number = 2): number {
  const result = Math.random() * (max - min) + min
  return parseFloat(result.toFixed(decimals))
}

/**
 * 生成趋势数据（带波动）
 */
export function generateTrendData(
  baseValue: number,
  points: number,
  volatility: number = 0.2,
  trend: 'up' | 'down' | 'stable' = 'stable',
): number[] {
  const data: number[] = []
  let currentValue = baseValue

  for (let i = 0; i < points; i++) {
    // 计算趋势影响
    let trendFactor = 0
    if (trend === 'up') {
      trendFactor = (i / points) * baseValue * 0.3
    } else if (trend === 'down') {
      trendFactor = -(i / points) * baseValue * 0.3
    }

    // 添加随机波动
    const fluctuation = (Math.random() - 0.5) * baseValue * volatility
    currentValue = Math.max(0, baseValue + trendFactor + fluctuation)
    data.push(Math.round(currentValue))
  }

  return data
}

/**
 * 生成时序数据
 */
export function generateTimeSeriesData(
  startDate: Date,
  days: number,
  baseValue: number,
  volatility: number = 0.2,
): Array<{ date: string; value: number }> {
  const data = []
  const values = generateTrendData(baseValue, days, volatility, 'up')

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    data.push({
      date: date.toISOString().split('T')[0],
      value: values[i],
    })
  }

  return data
}

/**
 * 生成每小时数据
 */
export function generateHourlyData(
  baseValue: number = 100,
): Array<{ name: string; value: number }> {
  const hours = []
  const peakHours = [8, 9, 10, 14, 15, 16, 17] // 高峰时段

  for (let i = 0; i < 24; i++) {
    const hourStr = `${i.toString().padStart(2, '0')}:00`
    let value = baseValue

    // 高峰时段数据增加
    if (peakHours.includes(i)) {
      value = randomInt(baseValue * 1.5, baseValue * 2.5)
    } else if (i >= 0 && i <= 6) {
      // 凌晨数据较少
      value = randomInt(baseValue * 0.2, baseValue * 0.5)
    } else {
      value = randomInt(baseValue * 0.7, baseValue * 1.3)
    }

    hours.push({ name: hourStr, value })
  }

  return hours
}

/**
 * 生成每日数据（30天）
 */
export function generateDailyData(baseValue: number = 100): Array<{ name: string; value: number }> {
  const data = []
  const today = new Date()

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = `${date.getMonth() + 1}/${date.getDate()}`

    // 周末数据稍微降低
    const isWeekend = date.getDay() === 0 || date.getDay() === 6
    const value = isWeekend
      ? randomInt(baseValue * 0.6, baseValue * 0.9)
      : randomInt(baseValue * 0.8, baseValue * 1.2)

    data.push({ name: dateStr, value })
  }

  return data
}

/**
 * 生成每月数据（12个月）
 */
export function generateMonthlyData(
  baseValue: number = 100,
): Array<{ name: string; value: number }> {
  const months = [
    '1月',
    '2月',
    '3月',
    '4月',
    '5月',
    '6月',
    '7月',
    '8月',
    '9月',
    '10月',
    '11月',
    '12月',
  ]
  const data = []

  for (let i = 0; i < 12; i++) {
    // 模拟季节性波动
    let seasonalFactor = 1
    if (i >= 3 && i <= 5) {
      // 春季
      seasonalFactor = 1.1
    } else if (i >= 6 && i <= 8) {
      // 夏季
      seasonalFactor = 1.3
    } else if (i >= 9 && i <= 11) {
      // 秋季
      seasonalFactor = 1.2
    }

    const value = randomInt(baseValue * 0.8 * seasonalFactor, baseValue * 1.2 * seasonalFactor)

    data.push({ name: months[i], value })
  }

  return data
}

/**
 * 生成饼图数据（确保总和为100%）
 */
export function generatePieData(
  categories: string[],
  weights?: number[],
): Array<{ name: string; value: number }> {
  const data = []
  let remaining = 100

  // 如果没有提供权重，随机生成
  if (!weights) {
    weights = categories.map(() => Math.random())
  }

  // 归一化权重
  const totalWeight = weights.reduce((sum, w) => sum + w, 0)
  const normalizedWeights = weights.map((w) => w / totalWeight)

  for (let i = 0; i < categories.length; i++) {
    if (i === categories.length - 1) {
      // 最后一个类别取剩余值，确保总和为100
      data.push({ name: categories[i], value: remaining })
    } else {
      const value = Math.round(normalizedWeights[i] * 100)
      data.push({ name: categories[i], value })
      remaining -= value
    }
  }

  return data
}

/**
 * 生成风险等级分布数据
 */
export function generateRiskDistribution(): Array<{ name: string; value: number }> {
  const total = randomInt(50, 200)
  const highRisk = randomInt(5, Math.floor(total * 0.2))
  const mediumRisk = randomInt(Math.floor(total * 0.2), Math.floor(total * 0.4))
  const lowRisk = total - highRisk - mediumRisk

  return [
    { name: '高风险', value: highRisk },
    { name: '中风险', value: mediumRisk },
    { name: '低风险', value: lowRisk },
  ]
}

/**
 * 生成处理状态分布
 */
export function generateStatusDistribution(): Array<{ name: string; value: number }> {
  const total = randomInt(100, 500)
  const pending = randomInt(10, Math.floor(total * 0.3))
  const processing = randomInt(20, Math.floor(total * 0.4))
  const completed = total - pending - processing

  return [
    { name: '待处理', value: pending },
    { name: '处理中', value: processing },
    { name: '已完成', value: completed },
  ]
}

/**
 * 生成七阶段数据
 */
export function generateSevenStageData(): Array<{ name: string; value: number }> {
  return [
    { name: '收集', value: randomInt(80, 150) },
    { name: '存储', value: randomInt(70, 140) },
    { name: '传输', value: randomInt(60, 130) },
    { name: '加工', value: randomInt(50, 120) },
    { name: '提供', value: randomInt(40, 100) },
    { name: '公开', value: randomInt(20, 60) },
    { name: '销毁', value: randomInt(10, 40) },
  ]
}

/**
 * 生成企业类型分布
 */
export function generateEnterpriseDistribution(): Array<{ name: string; value: number }> {
  return generatePieData(
    ['整车生产企业', '平台运营商', '智驾方案提供商', '地图服务商', '其他'],
    [35, 28, 20, 12, 5],
  )
}

/**
 * 生成车辆类型分布
 */
export function generateVehicleTypeDistribution(): Array<{ name: string; value: number }> {
  return generatePieData(['乘用车', '商用车', '专用车'], [65, 25, 10])
}

/**
 * 生成地区分布数据
 */
export function generateRegionData(): Array<{ name: string; value: number }> {
  const regions = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '重庆', '南京']

  return regions
    .map((region) => ({
      name: region,
      value: randomInt(50, 500),
    }))
    .sort((a, b) => b.value - a.value)
}

/**
 * 生成增长率
 */
export function generateGrowthRate(): number {
  return randomFloat(-20, 30, 1)
}

/**
 * 生成完成率
 */
export function generateCompletionRate(): number {
  return randomInt(60, 98)
}
