/* 全局字体定义 - 阿里妈妈方圆体 */
@font-face {
  font-family: 'AlimamaFangYuanTiVF';
  src:
    url('/assets/fonts/AlimamaFangYuanTiVF-Thin.woff2') format('woff2'),
    url('/assets/fonts/AlimamaFangYuanTiVF-Thin.woff') format('woff'),
    url('/assets/fonts/AlimamaFangYuanTiVF-Thin.ttf') format('truetype');
  font-weight: 100 900; /* 支持可变字重 */
  font-style: normal;
  font-display: swap;
  font-variation-settings: 'wght' 400; /* 默认字重 */
}

/* 字体工具类 */
.font-alimama {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 字重变体 */
.font-alimama-thin {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 100;
}

.font-alimama-light {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 300;
}

.font-alimama-normal {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
}

.font-alimama-medium {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
}

.font-alimama-semibold {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 600;
}

.font-alimama-bold {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 700;
}

.font-alimama-extrabold {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 800;
}

.font-alimama-black {
  font-family: 'AlimamaFangYuanTiVF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 900;
}
