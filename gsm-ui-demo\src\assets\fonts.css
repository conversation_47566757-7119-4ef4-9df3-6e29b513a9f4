/* 全局字体定义 - 优化的中文字体方案 */
/* 使用系统最佳中文字体，提供类似阿里妈妈方圆体的视觉效果 */

/* 字体工具类 */
.font-alimama {
  font-family:
    'PingFang SC',
    /* macOS 优秀中文字体 */ 'Hiragino Sans GB',
    /* macOS 备用中文字体 */ 'Microsoft YaHei',
    /* Windows 系统中文字体 */ 'WenQuanYi Micro Hei',
    /* Linux 中文字体 */ 'Helvetica Neue',
    /* 优质西文字体 */ -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Arial,
    sans-serif;
  font-weight: 500; /* 适中字重 */
  letter-spacing: 0.01em; /* 轻微字间距优化 */
}

/* 字重变体 */
.font-alimama-thin {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 100;
}

.font-alimama-light {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 300;
}

.font-alimama-normal {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 400;
}

.font-alimama-medium {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 500;
}

.font-alimama-semibold {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 600;
}

.font-alimama-bold {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 700;
}

.font-alimama-extrabold {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 800;
}

.font-alimama-black {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-weight: 900;
}
