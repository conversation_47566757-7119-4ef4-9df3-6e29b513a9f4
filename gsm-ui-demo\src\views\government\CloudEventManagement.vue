<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          监测与管理云端安全事件，包含统计、筛选与详情查看，支持事件处置与导出
        </p>
      </div>
    </div>

    <!-- 上端统计：事件总数、处置状态、分类占比、处置率 -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <!-- 事件总数 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">事件总数</CardTitle>
          <Cloud class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold mb-2">{{ totalEvents }}</div>
          <p class="text-xs text-muted-foreground mb-4">累计上报事件数量</p>
          <div class="h-32">
            <BarChart
              :data="weeklyTrendData"
              :height="128"
              color-scheme="eventTypes"
              :show-values="false"
              :bar-width="'70%'"
              :grid-top="8"
              :grid-bottom="15"
            />
          </div>
          <p class="text-xs text-center text-muted-foreground mt-2">近7天趋势</p>
        </CardContent>
      </Card>

      <!-- 处置状态 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">处置状态分布</CardTitle>
          <CheckCircle2 class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent class="p-6">
          <div class="h-[220px]">
            <PieChart
              :data="statusStatsData"
              color-scheme="status"
              chart-type="doughnut"
              center-text="总计"
              :center-sub-text="totalStatus.toString() + '条'"
              :height="220"
              :show-legend="true"
              :show-percentages="false"
              :show-values="true"
              class="w-full"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 事件分类占比 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">事件分类占比</CardTitle>
          <AlertTriangle class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent class="p-6">
          <div class="h-[220px]">
            <PieChart
              :data="typeStatsData"
              color-scheme="eventTypes"
              chart-type="doughnut"
              center-text="分类"
              :center-sub-text="totalTypes.toString() + '类'"
              :height="220"
              :show-legend="true"
              :show-percentages="true"
              :show-values="false"
              class="w-full"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 处置率统计 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">处置率</CardTitle>
          <Percent class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent class="p-6">
          <div class="space-y-4">
            <!-- 处置率数值显示 -->
            <div class="text-center">
              <div class="text-3xl font-bold text-foreground mb-1">{{ handleRate }}%</div>
              <p class="text-sm text-muted-foreground">已处置 / 总事件</p>
              <div class="text-xs text-muted-foreground mt-2">
                今日新增已处置：<span class="text-foreground font-medium">{{ todayHandled }}</span>
              </div>
            </div>

            <!-- 环形图 -->
            <div class="h-[180px] p-2">
              <PieChart
                :data="handleRateData"
                color-scheme="status"
                chart-type="doughnut"
                center-text="处置率"
                :center-sub-text="handleRate.toString() + '%'"
                :height="180"
                :show-legend="true"
                :show-percentages="true"
                :show-values="true"
                class="w-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 列表 -->
    <Card>
      <CardHeader>
        <CardTitle>云端事件列表</CardTitle>
        <CardDescription>
          共 {{ filteredTotal }} 条记录，当前显示第
          {{ pageSize * (currentPage - 1) + 1 }}
          -
          {{ Math.min(pageSize * currentPage, filteredTotal) }}
          条
        </CardDescription>
      </CardHeader>
      <CardContent>
        <!-- 查询表单 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportCsv"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead class="min-w-[180px]">企业名称</TableHead>
                <TableHead class="min-w-[120px]">企业类型</TableHead>
                <TableHead>事件类型</TableHead>
                <TableHead>当前状态</TableHead>
                <TableHead class="min-w-[140px]">上报时间</TableHead>
                <TableHead class="min-w-[140px]">处置完成时间</TableHead>
                <TableHead class="w-[160px] text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedItems.length === 0">
                <TableCell :colspan="8" class="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedItems"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.enterprise }}</div>
                    <div class="text-xs text-muted-foreground">{{ item.enterpriseCode }}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">{{ item.enterpriseType }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{{ item.eventType }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="statusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap">{{ item.reportAt }}</TableCell>
                <TableCell class="whitespace-nowrap">{{ item.resolvedAt || '-' }}</TableCell>
                <TableCell class="text-right">
                  <div class="flex justify-end gap-2">
                    <Button size="sm" variant="outline" @click="openDetail(item)">详细信息</Button>
                    <Button size="sm" @click="goTrace(item)">风险溯源</Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页（统一使用 shadcn-vue Pagination） -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredTotal) }} 条， 共 {{ filteredTotal }} 条记录
          </div>
          <div class="flex items-center gap-4">
            <div class="pagination-size-control">
              <span>每页显示</span>
              <Select :model-value="pageSize.toString()" @update:model-value="onPageSizeChange">
                <SelectTrigger class="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span>条</span>
            </div>
            <Pagination
              v-model:page="currentPage"
              :total="filteredTotal"
              :items-per-page="pageSize"
              :sibling-count="1"
              :show-edges="true"
            >
              <PaginationContent v-slot="{ items }">
                <PaginationFirst />
                <PaginationPrevious />
                <template v-for="(item, idx) in items" :key="idx">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === currentPage"
                  />
                  <PaginationEllipsis v-else />
                </template>
                <PaginationNext />
                <PaginationLast />
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>云端事件详细信息</DialogTitle>
          <DialogDescription>展示事件详情、影响范围与处置记录等信息</DialogDescription>
        </DialogHeader>

        <div v-if="selectedEvent" class="space-y-4">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <div class="text-xs text-muted-foreground">企业名称</div>
              <div class="text-base font-semibold">{{ selectedEvent.enterprise }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">企业类型</div>
              <div class="text-base font-semibold">{{ selectedEvent.enterpriseType }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">事件类型</div>
              <div class="text-base font-semibold">{{ selectedEvent.eventType }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">当前状态</div>
              <div class="text-base font-semibold">{{ selectedEvent.status }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">影响范围</div>
              <div class="text-base font-semibold">{{ selectedEvent.impact }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">涉及数据量</div>
              <div class="text-base font-semibold">{{ selectedEvent.dataVolume }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">上报时间</div>
              <div class="text-base font-semibold">{{ selectedEvent.reportAt }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">处置完成时间</div>
              <div class="text-base font-semibold">{{ selectedEvent.resolvedAt || '-' }}</div>
            </div>
          </div>

          <!-- 事件描述 -->
          <div class="space-y-2">
            <div class="text-xs text-muted-foreground">事件描述</div>
            <div class="text-sm">{{ selectedEvent.description }}</div>
          </div>

          <!-- 处置记录 -->
          <div class="space-y-2" v-if="selectedEvent.handleRecord">
            <div class="text-xs text-muted-foreground">处置记录</div>
            <div class="text-sm whitespace-pre-line">{{ selectedEvent.handleRecord }}</div>
          </div>

          <div class="flex items-center justify-between">
            <div class="text-xs text-muted-foreground">
              事件 ID：<span class="text-foreground">{{ selectedEvent.id }}</span>
            </div>
            <div class="flex gap-2">
              <Button variant="outline" @click="detailOpen = false">关闭</Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { AlertTriangle, CheckCircle2, Cloud, Percent } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import { PieChart, BarChart } from '@/components/charts'

const router = useRouter()
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type CloudEventType = '数据泄露' | '违规处理' | '访问异常' | '系统故障'
type HandleStatus = '未处理' | '处理中' | '已处理'
type EnterpriseType = '整车生产企业' | '平台运营商' | '智驾方案提供商' | '地图服务商' | '其他'

type EventTypeFilter = 'ALL' | CloudEventType
type StatusFilter = 'ALL' | HandleStatus
type EnterpriseTypeFilter = 'ALL' | EnterpriseType

interface CloudEventItem {
  id: string
  enterprise: string
  enterpriseCode: string
  enterpriseType: EnterpriseType
  eventType: CloudEventType
  status: HandleStatus
  description: string
  impact: string
  dataVolume: string
  reportAt: string // YYYY-MM-DD HH:mm
  resolvedAt?: string // YYYY-MM-DD HH:mm | undefined
  handleRecord?: string
}

interface Filters {
  enterprise: string
  enterpriseType: EnterpriseTypeFilter
  eventType: EventTypeFilter
  status: StatusFilter
  timeRange: [Date, Date] | null
}

// 筛选器
const filters = ref<Filters>({
  enterprise: '',
  enterpriseType: 'ALL',
  eventType: 'ALL',
  status: 'ALL',
  timeRange: null,
})

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'enterprise',
    label: '企业名称',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'enterpriseType',
    label: '企业类型',
    type: 'select',
    placeholder: '请选择企业类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '整车生产企业', value: '整车生产企业' },
      { label: '平台运营商', value: '平台运营商' },
      { label: '智驾方案提供商', value: '智驾方案提供商' },
      { label: '地图服务商', value: '地图服务商' },
      { label: '其他', value: '其他' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'eventType',
    label: '事件类型',
    type: 'select',
    placeholder: '请选择事件类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '数据泄露', value: '数据泄露' },
      { label: '违规处理', value: '违规处理' },
      { label: '访问异常', value: '访问异常' },
      { label: '系统故障', value: '系统故障' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'status',
    label: '处理状态',
    type: 'select',
    placeholder: '请选择处理状态',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '未处理', value: '未处理' },
      { label: '处理中', value: '处理中' },
      { label: '已处理', value: '已处理' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'timeRange',
    label: '上报时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    enterprise: '',
    enterpriseType: 'ALL',
    eventType: 'ALL',
    status: 'ALL',
    timeRange: null,
  }
}

// Mock 数据
const eventItems = ref<CloudEventItem[]>([
  {
    id: 'CE-202501-0001',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    enterpriseType: '平台运营商',
    eventType: '数据泄露',
    status: '处理中',
    description: '云端数据库配置错误导致部分地理位置数据暴露在公网，已发现异常访问行为。',
    impact: '中等',
    dataVolume: '约5万条记录',
    reportAt: '2025-08-20 09:15',
    handleRecord: '已立即关闭公网访问，正在进行数据泄露影响评估和用户通知。',
  },
  {
    id: 'CE-202501-0002',
    enterprise: '上海车联网服务有限公司',
    enterpriseCode: '91310000987654321Y',
    enterpriseType: '地图服务商',
    eventType: '违规处理',
    status: '已处理',
    description: '发现企业未按规定进行数据出境申报，私自将敏感地理数据传输至境外服务器。',
    impact: '高',
    dataVolume: '约12万条记录',
    reportAt: '2025-08-18 14:30',
    resolvedAt: '2025-08-20 16:45',
    handleRecord: '已停止违规数据传输，召回境外数据，完成合规整改，提交自查报告。',
  },
  {
    id: 'CE-202501-0003',
    enterprise: '深圳自动驾驶技术有限公司',
    enterpriseCode: '91440300567890123Z',
    enterpriseType: '智驾方案提供商',
    eventType: '访问异常',
    status: '未处理',
    description: '监测到异常IP地址大量访问云端API接口，疑似恶意爬取数据行为。',
    impact: '低',
    dataVolume: '未确定',
    reportAt: '2025-08-21 11:20',
  },
  {
    id: 'CE-202501-0004',
    enterprise: '广州汽车制造有限公司',
    enterpriseCode: '91440000456789012A',
    enterpriseType: '整车生产企业',
    eventType: '系统故障',
    status: '已处理',
    description: '云端存储系统出现硬件故障，导致部分数据暂时无法访问，已启动备份恢复。',
    impact: '中等',
    dataVolume: '约8万条记录',
    reportAt: '2025-08-19 22:10',
    resolvedAt: '2025-08-20 08:30',
    handleRecord: '已完成数据恢复，所有服务正常运行，强化了备份策略。',
  },
  {
    id: 'CE-202501-0005',
    enterprise: '济南智能驾驶科技公司',
    enterpriseCode: '91370100MA3CHD5N3P',
    enterpriseType: '智驾方案提供商',
    eventType: '访问异常',
    status: '已处理',
    description: '检测到境外IP地址批量下载云端地图数据，存在数据窃取风险。',
    impact: '高',
    dataVolume: '约20万条记录',
    reportAt: '2025-08-19 03:45',
    resolvedAt: '2025-08-19 12:20',
    handleRecord: '已阻断异常访问，追踪IP来源，强化地理围栏策略，通知相关监管部门。',
  },
  {
    id: 'CE-202501-0006',
    enterprise: '福州智慧交通技术公司',
    enterpriseCode: '91350100MA31N7YK8N',
    enterpriseType: '平台运营商',
    eventType: '系统故障',
    status: '处理中',
    description: '云端密钥管理服务异常，导致加密数据无法正常解密，影响数据可用性。',
    impact: '高',
    dataVolume: '约30万条记录',
    reportAt: '2025-08-21 16:30',
    handleRecord: '已启动应急密钥恢复流程，正在逐步恢复服务，预计24小时内完成。',
  },
  {
    id: 'CE-202501-0007',
    enterprise: '厦门海峡智联科技公司',
    enterpriseCode: '91350200MA32F8TN7X',
    enterpriseType: '地图服务商',
    eventType: '数据泄露',
    status: '未处理',
    description: '第三方云存储服务商发生数据泄露事件，涉及我司部分敏感地理数据。',
    impact: '高',
    dataVolume: '约15万条记录',
    reportAt: '2025-08-21 18:45',
  },
  {
    id: 'CE-202501-0008',
    enterprise: '长沙智行科技有限公司',
    enterpriseCode: '91430100MA4L8N9K2P',
    enterpriseType: '整车生产企业',
    eventType: '违规处理',
    status: '处理中',
    description: '发现云端数据处理流程违反GDPR规定，未获得用户明确授权即处理位置数据。',
    impact: '中等',
    dataVolume: '约3万条记录',
    reportAt: '2025-08-20 20:15',
    handleRecord: '已暂停相关数据处理，正在补充用户授权流程，修订隐私政策。',
  },
  {
    id: 'CE-202501-0009',
    enterprise: '郑州中原智驾公司',
    enterpriseCode: '91410100MA458KLM9T',
    enterpriseType: '智驾方案提供商',
    eventType: '访问异常',
    status: '已处理',
    description: 'CDN节点遭受DDoS攻击，导致云端数据同步服务中断超过2小时。',
    impact: '中等',
    dataVolume: '影响实时传输',
    reportAt: '2025-08-18 19:30',
    resolvedAt: '2025-08-18 22:45',
    handleRecord: '已切换到备用CDN，部署DDoS防护策略，恢复正常服务。',
  },
  {
    id: 'CE-202501-0010',
    enterprise: '哈尔滨北方智行科技',
    enterpriseCode: '91230100MA1B8YTK3W',
    enterpriseType: '平台运营商',
    eventType: '数据泄露',
    status: '处理中',
    description: '开发环境配置错误导致测试数据库可公网访问，包含真实地理数据。',
    impact: '中等',
    dataVolume: '约10万条记录',
    reportAt: '2025-08-21 21:10',
    handleRecord: '已关闭公网访问，审查所有环境配置，加强开发安全规范。',
  },
  {
    id: 'CE-202501-0011',
    enterprise: '昆明高原智驾技术公司',
    enterpriseCode: '91530100MA6NF8TM4Y',
    enterpriseType: '地图服务商',
    eventType: '访问异常',
    status: '未处理',
    description: '监测到使用爬虫程序高频访问地图API，每秒请求超过1000次。',
    impact: '低',
    dataVolume: '累计请求500万次',
    reportAt: '2025-08-21 22:30',
  },
  {
    id: 'CE-202501-0012',
    enterprise: '石家庄智慧城市科技',
    enterpriseCode: '91130100MA0CHN8K7D',
    enterpriseType: '整车生产企业',
    eventType: '系统故障',
    status: '已处理',
    description: '云端负载均衡器故障导致部分地区服务不可用，影响数据上传。',
    impact: '低',
    dataVolume: '约2万条记录延迟',
    reportAt: '2025-08-17 13:20',
    resolvedAt: '2025-08-17 15:45',
    handleRecord: '已修复负载均衡器，优化了故障转移机制，服务已恢复正常。',
  },
  {
    id: 'CE-202501-0013',
    enterprise: '兰州西部智行有限公司',
    enterpriseCode: '91620100MA73W8NK5P',
    enterpriseType: '智驾方案提供商',
    eventType: '违规处理',
    status: '处理中',
    description: '发现将包含敏感区域的地图数据存储在境外云服务器，违反数据本地化要求。',
    impact: '高',
    dataVolume: '约50GB数据',
    reportAt: '2025-08-21 08:00',
    handleRecord: '正在紧急迁移数据至境内服务器，配合监管调查，加强合规审查。',
  },
  {
    id: 'CE-202501-0014',
    enterprise: '海口椰城智联科技',
    enterpriseCode: '91460100MA5T8YNK2W',
    enterpriseType: '平台运营商',
    eventType: '数据泄露',
    status: '已处理',
    description: '员工账号被钓鱼攻击，导致云端管理权限被盗用，部分数据被非法下载。',
    impact: '中等',
    dataVolume: '约8万条记录',
    reportAt: '2025-08-16 11:15',
    resolvedAt: '2025-08-17 08:30',
    handleRecord: '已重置所有管理员密码，启用多因素认证，完成安全审计和员工培训。',
  },
  {
    id: 'CE-202501-0015',
    enterprise: '银川智慧出行科技公司',
    enterpriseCode: '91640100MA76K8TM3N',
    enterpriseType: '地图服务商',
    eventType: '访问异常',
    status: '处理中',
    description: '发现内部系统存在未授权API调用，可能存在内部威胁。',
    impact: '中等',
    dataVolume: '访问记录异常',
    reportAt: '2025-08-21 19:55',
    handleRecord: '正在审查访问日志，加强内部访问控制，部署行为分析系统。',
  },
])

// 统计数据
const totalEvents = computed(() => eventItems.value.length)

const statusStatsData = computed(() => {
  const count: Record<HandleStatus, number> = { 未处理: 0, 处理中: 0, 已处理: 0 }
  for (const e of eventItems.value) count[e.status]++
  return (Object.keys(count) as HandleStatus[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}状态的事件数量`,
  }))
})
const totalStatus = computed(() => statusStatsData.value.reduce((s, i) => s + i.value, 0))

const typeStatsData = computed(() => {
  const count: Record<CloudEventType, number> = {
    数据泄露: 0,
    违规处理: 0,
    访问异常: 0,
    系统故障: 0,
  }
  for (const e of eventItems.value) count[e.eventType]++
  return (Object.keys(count) as CloudEventType[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}类型事件数量`,
  }))
})
const totalTypes = computed(() => typeStatsData.value.filter((t) => t.value > 0).length)

const handleRate = computed(() => {
  const total = eventItems.value.length
  const handled = eventItems.value.filter((e) => e.status === '已处理').length
  return total === 0 ? 0 : Math.round((handled / total) * 100)
})
const todayHandled = 1 // mock
const handleRateData = computed(() => [
  { name: '已处置', value: handleRate.value, description: '已完成处置的事件占比' },
  { name: '未处置', value: 100 - handleRate.value, description: '尚未处置的事件占比' },
])

// 近7天事件趋势数据
const weeklyTrendData = computed(() => {
  const today = new Date()
  const weekData = []

  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    const dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]

    // 基于现有数据模拟7天趋势
    const baseCount = Math.floor(eventItems.value.length / 7)
    const variance = Math.floor(Math.random() * 3) - 1 // -1 到 +1 的随机变化
    const eventCount = Math.max(0, baseCount + variance)

    weekData.push({
      name: dayName,
      value: eventCount,
    })
  }

  return weekData
})

// 过滤与分页
const filtered = computed(() => {
  const { enterprise, enterpriseType, eventType, status, timeRange } = filters.value
  return eventItems.value.filter((e) => {
    if (enterprise && !e.enterprise.includes(enterprise)) return false
    if (enterpriseType !== 'ALL' && e.enterpriseType !== enterpriseType) return false
    if (eventType !== 'ALL' && e.eventType !== eventType) return false
    if (status !== 'ALL' && e.status !== status) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
        0,
        0,
        0,
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const report = new Date(e.reportAt.replace(/-/g, '/')).getTime()
      if (report < start || report > end) return false
    }
    return true
  })
})
const filteredTotal = computed(() => filtered.value.length)

const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = computed(() => Math.max(1, Math.ceil(filteredTotal.value / pageSize.value)))
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.value.slice(start, start + pageSize.value)
})

const goPage = (p: number) => {
  if (p < 1 || p > totalPages.value) return
  currentPage.value = p
}
const onPageSizeChange = (val: string) => {
  const n = parseInt(val)
  if (!Number.isNaN(n) && n > 0) {
    pageSize.value = n
    currentPage.value = 1
  }
}

// 详情弹窗
const detailOpen = ref(false)
const selectedEvent = ref<CloudEventItem | null>(null)
const openDetail = (e: CloudEventItem) => {
  selectedEvent.value = e
  detailOpen.value = true
}

// 溯源
const goTrace = (e: CloudEventItem) => {
  router.push({
    path: '/gov/trace/cloud',
    query: {
      external: '1',
      eventId: e.id,
      enterprise: e.enterprise,
      eventType: e.eventType,
      desc: e.description,
    },
  })
}

// 导出 CSV
const exportCsv = () => {
  const headers = [
    '企业名称',
    '企业代码',
    '企业类型',
    '事件类型',
    '当前状态',
    '事件描述',
    '影响范围',
    '涉及数据量',
    '上报时间',
    '处置完成时间',
  ]
  const rows = filtered.value.map((e) => [
    e.enterprise,
    e.enterpriseCode,
    e.enterpriseType,
    e.eventType,
    e.status,
    e.description.replace(/\n/g, ' '),
    e.impact,
    e.dataVolume,
    e.reportAt,
    e.resolvedAt || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `云端事件管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}
const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}

// Badge 样式
const statusVariant = (st: HandleStatus) => {
  switch (st) {
    case '未处理':
      return 'outline'
    case '处理中':
      return 'secondary'
    case '已处理':
      return 'default'
    default:
      return 'outline'
  }
}
</script>
