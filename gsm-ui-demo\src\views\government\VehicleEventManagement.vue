<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          监测与管理车端安全事件，包含统计、筛选与详情查看，支持事件处置与导出
        </p>
      </div>
    </div>

    <!-- 上端统计：事件总数、处置状态、分类占比、处置率 -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <!-- 事件总数 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">事件总数</CardTitle>
          <FileText class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold mb-2">{{ totalEvents }}</div>
          <p class="text-xs text-muted-foreground mb-4">累计上报事件数量</p>
          <div class="h-32">
            <BarChart
              :data="weeklyTrendData"
              :height="128"
              color-scheme="status"
              :show-values="false"
              :bar-width="'70%'"
              :grid-top="8"
              :grid-bottom="15"
            />
          </div>
          <p class="text-xs text-center text-muted-foreground mt-2">近7天趋势</p>
        </CardContent>
      </Card>

      <!-- 处置状态 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">处置状态分布</CardTitle>
          <CheckCircle2 class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent class="p-6">
          <div class="h-[220px] p-2">
            <PieChart
              :data="statusStatsData"
              color-scheme="status"
              chart-type="doughnut"
              center-text="总计"
              :center-sub-text="totalStatus.toString() + '条'"
              :height="220"
              :show-legend="true"
              :show-percentages="false"
              :show-values="true"
              class="w-full"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 事件分类占比 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">事件分类占比</CardTitle>
          <AlertTriangle class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent class="p-6">
          <div class="h-[220px] p-2">
            <PieChart
              :data="typeStatsData"
              color-scheme="status"
              chart-type="doughnut"
              center-text="分类"
              :center-sub-text="totalTypes.toString() + '类'"
              :height="220"
              :show-legend="true"
              :show-percentages="true"
              :show-values="false"
              class="w-full"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 处置率统计 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">处置率</CardTitle>
          <Percent class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent class="p-6">
          <div class="space-y-4">
            <!-- 处置率数值显示 -->
            <div class="text-center">
              <div class="text-3xl font-bold text-foreground mb-1">{{ handleRate }}%</div>
              <p class="text-sm text-muted-foreground">已处置 / 总事件</p>
              <div class="text-xs text-muted-foreground mt-2">
                今日新增已处置：<span class="text-foreground font-medium">{{ todayHandled }}</span>
              </div>
            </div>

            <!-- 环形图 -->
            <div class="h-[180px] p-2">
              <PieChart
                :data="handleRateData"
                color-scheme="status"
                chart-type="doughnut"
                center-text="处置率"
                :center-sub-text="handleRate.toString() + '%'"
                :height="180"
                :show-legend="true"
                :show-percentages="true"
                :show-values="true"
                class="w-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 列表 -->
    <Card>
      <CardHeader>
        <CardTitle>车端事件列表</CardTitle>
        <CardDescription>
          共 {{ filteredTotal }} 条记录，当前显示第
          {{ pageSize * (currentPage - 1) + 1 }}
          -
          {{ Math.min(pageSize * currentPage, filteredTotal) }}
          条
        </CardDescription>
      </CardHeader>
      <CardContent>
        <!-- 查询表单 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportCsv"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead class="min-w-[180px]">注册企业</TableHead>
                <TableHead class="min-w-[160px]">车辆 VIN</TableHead>
                <TableHead>品牌</TableHead>
                <TableHead>车型</TableHead>
                <TableHead>事件类型</TableHead>
                <TableHead>当前状态</TableHead>
                <TableHead class="min-w-[140px]">上报时间</TableHead>
                <TableHead class="min-w-[140px]">处置完成时间</TableHead>
                <TableHead class="w-[160px] text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedItems.length === 0">
                <TableCell :colspan="10" class="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedItems"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.enterprise }}</div>
                    <div class="text-xs text-muted-foreground">{{ item.enterpriseCode }}</div>
                  </div>
                </TableCell>
                <TableCell>{{ item.vin }}</TableCell>
                <TableCell>{{ item.brand }}</TableCell>
                <TableCell>{{ item.model }}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{{ item.eventType }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="statusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap">{{ item.reportAt }}</TableCell>
                <TableCell class="whitespace-nowrap">{{ item.resolvedAt || '-' }}</TableCell>
                <TableCell class="text-right">
                  <div class="flex justify-end gap-2">
                    <Button size="sm" variant="outline" @click="openDetail(item)">详细信息</Button>
                    <Button size="sm" @click="goTrace(item)">风险溯源</Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页（统一使用 shadcn-vue Pagination） -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredTotal) }} 条， 共 {{ filteredTotal }} 条记录
          </div>
          <div class="flex items-center gap-4">
            <div class="pagination-size-control">
              <span>每页显示</span>
              <Select :model-value="pageSize.toString()" @update:model-value="onPageSizeChange">
                <SelectTrigger class="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span>条</span>
            </div>
            <Pagination
              v-model:page="currentPage"
              :total="filteredTotal"
              :items-per-page="pageSize"
              :sibling-count="1"
              :show-edges="true"
            >
              <PaginationContent v-slot="{ items }">
                <PaginationFirst />
                <PaginationPrevious />
                <template v-for="(item, idx) in items" :key="idx">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === currentPage"
                  />
                  <PaginationEllipsis v-else />
                </template>
                <PaginationNext />
                <PaginationLast />
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>事件详细信息</DialogTitle>
          <DialogDescription>展示事件详情、影响范围与处置记录等信息</DialogDescription>
        </DialogHeader>

        <div v-if="selectedEvent" class="space-y-4">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <div class="text-xs text-muted-foreground">注册企业</div>
              <div class="text-base font-semibold">{{ selectedEvent.enterprise }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">车辆 VIN</div>
              <div class="text-base font-semibold">{{ selectedEvent.vin }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">品牌/车型</div>
              <div class="text-base font-semibold">
                {{ selectedEvent.brand }} / {{ selectedEvent.model }}
              </div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">事件类型</div>
              <div class="text-base font-semibold">{{ selectedEvent.eventType }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">当前状态</div>
              <div class="text-base font-semibold">{{ selectedEvent.status }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">影响范围</div>
              <div class="text-base font-semibold">{{ selectedEvent.impact }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">上报时间</div>
              <div class="text-base font-semibold">{{ selectedEvent.reportAt }}</div>
            </div>
            <div>
              <div class="text-xs text-muted-foreground">处置完成时间</div>
              <div class="text-base font-semibold">{{ selectedEvent.resolvedAt || '-' }}</div>
            </div>
          </div>

          <!-- 事件描述 -->
          <div class="space-y-2">
            <div class="text-xs text-muted-foreground">事件描述</div>
            <div class="text-sm">{{ selectedEvent.description }}</div>
          </div>

          <!-- 处置记录 -->
          <div class="space-y-2" v-if="selectedEvent.handleRecord">
            <div class="text-xs text-muted-foreground">处置记录</div>
            <div class="text-sm whitespace-pre-line">{{ selectedEvent.handleRecord }}</div>
          </div>

          <div class="flex items-center justify-between">
            <div class="text-xs text-muted-foreground">
              事件 ID：<span class="text-foreground">{{ selectedEvent.id }}</span>
            </div>
            <div class="flex gap-2">
              <Button variant="outline" @click="detailOpen = false">关闭</Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { AlertTriangle, CheckCircle2, FileText, Percent } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PieChart, BarChart } from '@/components/charts'

const router = useRouter()
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'

type EventType = '数据泄露' | '访问异常' | '传输异常' | '系统故障'
type HandleStatus = '未处理' | '处理中' | '已处理'

type EventTypeFilter = 'ALL' | EventType
type StatusFilter = 'ALL' | HandleStatus

interface EventItem {
  id: string
  enterprise: string
  enterpriseCode: string
  vin: string
  brand: string
  model: string
  eventType: EventType
  status: HandleStatus
  description: string
  impact: string
  reportAt: string // YYYY-MM-DD HH:mm
  resolvedAt?: string // YYYY-MM-DD HH:mm | undefined
  handleRecord?: string
}

interface Filters {
  enterprise: string
  vin: string
  brand: string
  model: string
  eventType: EventTypeFilter
  status: StatusFilter
  timeRange: [Date, Date] | null
}

// 筛选器
const filters = ref<Filters>({
  enterprise: '',
  vin: '',
  brand: '',
  model: '',
  eventType: 'ALL',
  status: 'ALL',
  timeRange: null,
})

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'enterprise',
    label: '注册企业',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'vin',
    label: '车辆VIN',
    type: 'input',
    placeholder: 'VIN编号',
  },
  {
    key: 'brand',
    label: '品牌',
    type: 'input',
    placeholder: '车辆品牌',
  },
  {
    key: 'model',
    label: '车型',
    type: 'input',
    placeholder: '车型',
  },
  {
    key: 'eventType',
    label: '事件类型',
    type: 'select',
    placeholder: '请选择事件类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '数据泄露', value: '数据泄露' },
      { label: '访问异常', value: '访问异常' },
      { label: '传输异常', value: '传输异常' },
      { label: '系统故障', value: '系统故障' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'status',
    label: '处理状态',
    type: 'select',
    placeholder: '请选择处理状态',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '未处理', value: '未处理' },
      { label: '处理中', value: '处理中' },
      { label: '已处理', value: '已处理' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'timeRange',
    label: '上报时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    enterprise: '',
    vin: '',
    brand: '',
    model: '',
    eventType: 'ALL',
    status: 'ALL',
    timeRange: null,
  }
}

// Mock 数据
const eventItems = ref<EventItem[]>([
  {
    id: 'E-202501-0001',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    vin: 'LSGJ8A12X34567890',
    brand: '极氪汽车',
    model: 'G1 Pro',
    eventType: '数据泄露',
    status: '处理中',
    description: '发现车辆传感器数据在传输过程中出现泄露，可能涉及地理位置敏感信息。',
    impact: '中等',
    reportAt: '2025-08-20 14:32',
    handleRecord: '已启动应急响应流程，正在调查泄露原因和影响范围。',
  },
  {
    id: 'E-202501-0002',
    enterprise: '上海车联网服务有限公司',
    enterpriseCode: '91310000987654321Y',
    vin: 'WBAVC31060A123456',
    brand: '联行',
    model: 'L5',
    eventType: '访问异常',
    status: '已处理',
    description: '检测到异常IP地址尝试访问车辆数据接口，疑似恶意攻击行为。',
    impact: '低',
    reportAt: '2025-08-19 16:21',
    resolvedAt: '2025-08-20 09:15',
    handleRecord: '已阻断异常访问，加强了访问控制策略，未发生数据泄露。',
  },
  {
    id: 'E-202501-0003',
    enterprise: '深圳自动驾驶技术有限公司',
    enterpriseCode: '91440300567890123Z',
    vin: 'LFPH3ACC9J1234567',
    brand: '南山智能',
    model: 'N7 Max',
    eventType: '传输异常',
    status: '未处理',
    description: '车辆与云端的数据传输出现中断，影响实时监测数据的上传。',
    impact: '中等',
    reportAt: '2025-08-21 10:05',
  },
  {
    id: 'E-202501-0004',
    enterprise: '广州未来出行科技有限公司',
    enterpriseCode: '91440100MA5CYN4L8X',
    vin: 'LGWEF6A51LH234567',
    brand: '未来汽车',
    model: 'Future X3',
    eventType: '系统故障',
    status: '处理中',
    description: '车载安全监测模块出现故障，无法正常记录和上报安全事件。',
    impact: '高',
    reportAt: '2025-08-21 08:45',
    handleRecord: '已派遣技术团队现场维修，预计24小时内恢复正常。',
  },
  {
    id: 'E-202501-0005',
    enterprise: '杭州智慧交通有限公司',
    enterpriseCode: '91330100MA2H3N9T7W',
    vin: 'LVSHCAME5JE345678',
    brand: '智慧车联',
    model: 'Smart V2',
    eventType: '数据泄露',
    status: '已处理',
    description: '发现历史轨迹数据在第三方存储服务商处被意外公开访问。',
    impact: '高',
    reportAt: '2025-08-18 13:22',
    resolvedAt: '2025-08-19 18:30',
    handleRecord: '已紧急关闭公开访问权限，通知受影响用户，加强供应商安全审查。',
  },
  {
    id: 'E-202501-0006',
    enterprise: '成都车路协同技术公司',
    enterpriseCode: '91510100MA7G8P2K3A',
    vin: 'LSJA24U66KG456789',
    brand: '协同智驾',
    model: 'C-Drive Pro',
    eventType: '访问异常',
    status: '处理中',
    description: '凌晨时段检测到大量异常数据访问请求，来源IP地址位于境外。',
    impact: '中等',
    reportAt: '2025-08-21 03:15',
    handleRecord: '已启动IP封禁措施，正在分析访问日志确定是否有数据外泄。',
  },
  {
    id: 'E-202501-0007',
    enterprise: '天津智能网联汽车公司',
    enterpriseCode: '91120116MA5JJHXN4R',
    vin: 'LFV3A21K8L3567890',
    brand: '津联智行',
    model: 'TJ-Auto X1',
    eventType: '传输异常',
    status: '已处理',
    description: '网络攻击导致数据传输通道被中断，影响实时监测功能。',
    impact: '低',
    reportAt: '2025-08-17 22:30',
    resolvedAt: '2025-08-18 06:20',
    handleRecord: '已切换到备用传输通道，部署了DDoS防护措施，系统运行正常。',
  },
  {
    id: 'E-202501-0008',
    enterprise: '南京智慧出行科技公司',
    enterpriseCode: '91320100MA1W8J9K7T',
    vin: 'LJDCAH229L1678901',
    brand: '宁行智能',
    model: 'NX Smart 5',
    eventType: '系统故障',
    status: '未处理',
    description: '车载加密模块固件异常，导致数据无法正常加密传输。',
    impact: '高',
    reportAt: '2025-08-21 11:40',
  },
  {
    id: 'E-202501-0009',
    enterprise: '武汉未来智行有限公司',
    enterpriseCode: '91420100MA4KYJQJ5D',
    vin: 'LWVCA3D54LA789012',
    brand: '楚天智驾',
    model: 'CT-Auto Z3',
    eventType: '数据泄露',
    status: '处理中',
    description: '员工误操作导致部分测试数据被上传至公开代码仓库。',
    impact: '中等',
    reportAt: '2025-08-20 16:55',
    handleRecord: '已删除公开数据，进行全面安全审计，加强员工安全意识培训。',
  },
  {
    id: 'E-202501-0010',
    enterprise: '重庆智能交通系统公司',
    enterpriseCode: '91500000MA5U8H7K2P',
    vin: 'LMGJE1S90L1890123',
    brand: '渝驾智联',
    model: 'CQ-Smart X',
    eventType: '访问异常',
    status: '已处理',
    description: '检测到使用已撤销凭证的访问尝试，可能存在内部安全隐患。',
    impact: '低',
    reportAt: '2025-08-16 14:20',
    resolvedAt: '2025-08-16 18:45',
    handleRecord: '已更新访问控制列表，强制所有凭证重新认证，加强内部审计。',
  },
  {
    id: 'E-202501-0011',
    enterprise: '西安高新智驾科技公司',
    enterpriseCode: '91610131MA6TGMRF9X',
    vin: 'LVHRU685XLN901234',
    brand: '秦智行',
    model: 'QZX-Pro 7',
    eventType: '传输异常',
    status: '处理中',
    description: '5G网络信号不稳定导致数据传输频繁中断，影响监测连续性。',
    impact: '中等',
    reportAt: '2025-08-21 09:30',
    handleRecord: '正在与运营商协调优化网络覆盖，临时启用4G备份链路。',
  },
  {
    id: 'E-202501-0012',
    enterprise: '苏州智慧车联网公司',
    enterpriseCode: '91320500MA1XRDNM91',
    vin: 'LSKG4GC10MA012345',
    brand: '姑苏智驾',
    model: 'SZ-Intel 3',
    eventType: '系统故障',
    status: '已处理',
    description: '车载操作系统更新失败导致安全监测功能异常。',
    impact: '低',
    reportAt: '2025-08-15 20:15',
    resolvedAt: '2025-08-16 10:30',
    handleRecord: '已回滚至稳定版本，优化更新流程，增加更新前系统备份机制。',
  },
  {
    id: 'E-202501-0013',
    enterprise: '青岛海洋智行科技公司',
    enterpriseCode: '91370200MA3QHTN42W',
    vin: 'LBVCU3108MAB23456',
    brand: '海洋智驾',
    model: 'Ocean-AI 5',
    eventType: '数据泄露',
    status: '未处理',
    description: '第三方SDK存在安全漏洞，可能导致位置数据被恶意收集。',
    impact: '高',
    reportAt: '2025-08-21 15:20',
  },
  {
    id: 'E-202501-0014',
    enterprise: '合肥智能驾驶研究院',
    enterpriseCode: '91340100MA2TYKRM8E',
    vin: 'LAHDC16476MB34567',
    brand: '科创智行',
    model: 'KC-Drive X',
    eventType: '访问异常',
    status: '处理中',
    description: '监测到暴力破解车载系统管理密码的行为。',
    impact: '中等',
    reportAt: '2025-08-20 23:45',
    handleRecord: '已锁定相关账户，启用双因素认证，加强密码复杂度要求。',
  },
  {
    id: 'E-202501-0015',
    enterprise: '济南智慧城市科技公司',
    enterpriseCode: '91370100MA3CHD5N3P',
    vin: 'LJNTB2X36LBC45678',
    brand: '泉城智驾',
    model: 'QC-Smart Pro',
    eventType: '传输异常',
    status: '已处理',
    description: '加密证书过期导致数据传输中断，安全通道无法建立。',
    impact: '低',
    reportAt: '2025-08-14 08:30',
    resolvedAt: '2025-08-14 12:20',
    handleRecord: '已更新证书，建立证书自动更新机制，避免类似问题再次发生。',
  },
])

// 统计数据
const totalEvents = computed(() => eventItems.value.length)

const statusStatsData = computed(() => {
  const count: Record<HandleStatus, number> = { 未处理: 0, 处理中: 0, 已处理: 0 }
  for (const e of eventItems.value) count[e.status]++
  return (Object.keys(count) as HandleStatus[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}状态的事件数量`,
  }))
})
const totalStatus = computed(() => statusStatsData.value.reduce((s, i) => s + i.value, 0))

const typeStatsData = computed(() => {
  const count: Record<EventType, number> = { 数据泄露: 0, 访问异常: 0, 传输异常: 0, 系统故障: 0 }
  for (const e of eventItems.value) count[e.eventType]++
  return (Object.keys(count) as EventType[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}类型事件数量`,
  }))
})
const totalTypes = computed(() => typeStatsData.value.filter((t) => t.value > 0).length)

const handleRate = computed(() => {
  const total = eventItems.value.length
  const handled = eventItems.value.filter((e) => e.status === '已处理').length
  return total === 0 ? 0 : Math.round((handled / total) * 100)
})
const todayHandled = 2 // mock
const handleRateData = computed(() => [
  { name: '已处置', value: handleRate.value, description: '已完成处置的事件占比' },
  { name: '未处置', value: 100 - handleRate.value, description: '尚未处置的事件占比' },
])

// 近7天事件趋势数据
const weeklyTrendData = computed(() => {
  const today = new Date()
  const weekData = []

  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    const dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]

    // 基于现有数据模拟7天趋势
    const baseCount = Math.floor(eventItems.value.length / 7)
    const variance = Math.floor(Math.random() * 4) - 2 // -2 到 +2 的随机变化
    const eventCount = Math.max(0, baseCount + variance)

    weekData.push({
      name: dayName,
      value: eventCount,
    })
  }

  return weekData
})

// 过滤与分页
const filtered = computed(() => {
  const { enterprise, vin, brand, model, eventType, status, timeRange } = filters.value
  return eventItems.value.filter((e) => {
    if (enterprise && !e.enterprise.includes(enterprise)) return false
    if (vin && !e.vin.includes(vin)) return false
    if (brand && !e.brand.includes(brand)) return false
    if (model && !e.model.includes(model)) return false
    if (eventType !== 'ALL' && e.eventType !== eventType) return false
    if (status !== 'ALL' && e.status !== status) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
        0,
        0,
        0,
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const report = new Date(e.reportAt.replace(/-/g, '/')).getTime()
      if (report < start || report > end) return false
    }
    return true
  })
})
const filteredTotal = computed(() => filtered.value.length)

const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = computed(() => Math.max(1, Math.ceil(filteredTotal.value / pageSize.value)))
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.value.slice(start, start + pageSize.value)
})

const goPage = (p: number) => {
  if (p < 1 || p > totalPages.value) return
  currentPage.value = p
}
const onPageSizeChange = (val: string) => {
  const n = parseInt(val)
  if (!Number.isNaN(n) && n > 0) {
    pageSize.value = n
    currentPage.value = 1
  }
}

// 详情弹窗
const detailOpen = ref(false)
const selectedEvent = ref<EventItem | null>(null)
const openDetail = (e: EventItem) => {
  selectedEvent.value = e
  detailOpen.value = true
}

// 溯源
const goTrace = (e: EventItem) => {
  router.push({
    path: '/gov/trace/vehicle',
    query: {
      external: '1',
      eventId: e.id,
      enterprise: e.enterprise,
      vin: e.vin,
      eventType: e.eventType,
      desc: e.description,
    },
  })
}

// 导出 CSV
const exportCsv = () => {
  const headers = [
    '注册企业',
    '企业代码',
    '车辆VIN',
    '品牌',
    '车型',
    '事件类型',
    '当前状态',
    '事件描述',
    '影响范围',
    '上报时间',
    '处置完成时间',
  ]
  const rows = filtered.value.map((e) => [
    e.enterprise,
    e.enterpriseCode,
    e.vin,
    e.brand,
    e.model,
    e.eventType,
    e.status,
    e.description.replace(/\n/g, ' '),
    e.impact,
    e.reportAt,
    e.resolvedAt || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `车端事件管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}
const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}

// Badge 样式
const statusVariant = (st: HandleStatus) => {
  switch (st) {
    case '未处理':
      return 'outline'
    case '处理中':
      return 'secondary'
    case '已处理':
      return 'default'
    default:
      return 'outline'
  }
}
</script>
