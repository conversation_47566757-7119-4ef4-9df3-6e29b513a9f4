<template>
  <ChartContainer
    :option="chartOption"
    :height="height"
    :color-scheme="colorScheme"
    :class="class"
    @click="handleClick"
    @mouseover="handleMouseover"
    @mouseout="handleMouseout"
  />
</template>

<script setup lang="ts" generic="<PERSON><PERSON> extends Record<string, unknown>">
import { computed } from 'vue'
import ChartContainer from '@/components/charts/ChartContainer.vue'
import { CHART_COLOR_SCHEMES } from '@/lib/chart-themes'

interface Props {
  data: Datum[]
  xAccessor?: (d: Datum) => string | number
  yAccessor?: (d: Datum) => number
  colorAccessor?: (d: Datum, i: number) => string
  xTickFormat?: (value: any) => string
  yTickFormat?: (value: any) => string
  barWidth?: number
  roundCorners?: boolean
  groupPadding?: number
  barPadding?: number
  height?: number
  class?: string
  colorScheme?:
    | 'primary'
    | 'risk'
    | 'enterprise'
    | 'stages'
    | 'oceanDepths'
    | 'status'
    | 'vehicleTypes'
  // Event handlers
  onClick?: (params: any) => void
  onMouseover?: (params: any) => void
  onMouseout?: (params: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  roundCorners: true,
  height: 280,
  colorScheme: 'primary',
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
}>()

// Convert data to ECharts format
const chartData = computed(() => {
  if (!props.data || !Array.isArray(props.data)) {
    return { categories: [], values: [] }
  }

  const categories = props.data.map((item, index) => {
    if (props.xAccessor) {
      return String(props.xAccessor(item))
    }
    return typeof item === 'object' && 'name' in item ? String(item.name) : `Item ${index + 1}`
  })

  const values = props.data.map((item, index) => {
    const value = props.yAccessor
      ? props.yAccessor(item)
      : typeof item === 'object' && 'value' in item
        ? Number(item.value)
        : 0
    return {
      value,
      itemStyle: {
        color: props.colorAccessor ? props.colorAccessor(item, index) : undefined,
      },
    }
  })

  return { categories, values }
})

// ECharts bar chart configuration
const chartOption = computed(() => {
  const { categories, values } = chartData.value
  const colors = getColorScheme(props.colorScheme)

  const option: any = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const param = Array.isArray(params) ? params[0] : params
        return `${param.name}<br/>${param.seriesName}: ${param.value}`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        rotate: categories.some((cat) => String(cat).length > 8) ? 45 : 0,
        formatter: props.xTickFormat || ((value: any) => String(value)),
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: props.yTickFormat || ((value: any) => String(value)),
      },
    },
    series: [
      {
        name: '数据',
        type: 'bar',
        data: values,
        barWidth: props.barWidth ? `${props.barWidth}%` : '60%',
        itemStyle: {
          borderRadius: props.roundCorners ? [4, 4, 0, 0] : 0,
          shadowBlur: 6,
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowOffsetX: 0,
          shadowOffsetY: 2,
          opacity: props.colorScheme === 'risk' || props.colorScheme === 'oceanDepths' ? 0.7 : 1, // 风险配色和oceanDepths配色使用半透明
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },
        // 显示值在柱子上方
        label: {
          show: false,
          position: 'top',
        },
      },
    ],
    color: colors,
  }

  return option
})

// Get color scheme
function getColorScheme(scheme: Props['colorScheme']) {
  switch (scheme) {
    case 'risk':
      return CHART_COLOR_SCHEMES.risk
    case 'enterprise':
      return CHART_COLOR_SCHEMES.enterprise
    case 'stages':
      return CHART_COLOR_SCHEMES.stages
    case 'oceanDepths':
      return CHART_COLOR_SCHEMES.oceanDepths
    default:
      return CHART_COLOR_SCHEMES.primary
  }
}

// Event handlers
const handleClick = (params: any) => {
  emit('click', params)
  props.onClick?.(params)
}

const handleMouseover = (params: any) => {
  emit('mouseover', params)
  props.onMouseover?.(params)
}

const handleMouseout = (params: any) => {
  emit('mouseout', params)
  props.onMouseout?.(params)
}
</script>
