<script setup lang="ts">
import type { SidebarProps } from '@/components/ui/sidebar'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import NavMain from '@/components/NavMain.vue'
import NavSecondary from '@/components/NavSecondary.vue'
import { Sidebar, SidebarContent } from '@/components/ui/sidebar'
import {
  getSidebarSection,
  resolveRoleFromPath,
  resolveSectionFromPath,
} from '@/config/sidebar-menu'
import {
  SquareTerminal,
  ClipboardCheck,
  ClipboardList,
  Activity,
  Settings2,
  AlertTriangle,
  Inbox,
  Users,
  LifeBuoy,
  Send,
} from 'lucide-vue-next'

const props = withDefaults(defineProps<SidebarProps>(), {
  variant: 'inset',
})

const route = useRoute()

const iconMap: Record<string, any> = {
  SquareTerminal,
  ClipboardCheck,
  ClipboardList,
  Activity,
  Settings2,
  AlertTriangle,
  Inbox,
  Users,
}

const role = computed(() => resolveRoleFromPath(route.path))
const sectionKey = computed(() => resolveSectionFromPath(route.path))
const section = computed(() => getSidebarSection(role.value, sectionKey.value))

const navMain = computed(() => {
  const IconComp = iconMap[section.value.icon || 'SquareTerminal'] || SquareTerminal
  return [
    {
      title: section.value.label,
      url: section.value.to || '#',
      icon: IconComp,
      isActive: true,
      items: section.value.items,
    },
  ]
})

const navSecondary = [
  { title: '技术支持', url: 'mailto:<EMAIL>', icon: LifeBuoy },
  { title: '政策咨询', url: 'mailto:<EMAIL>', icon: Send },
]
</script>

<template>
  <Sidebar 
    v-bind="props" 
    data-sidebar="sidebar"
    side="left" 
    class="border-sidebar-border bg-sidebar-background"
  >
    <SidebarContent 
      data-sidebar="content"
      class="bg-sidebar-background overflow-y-auto"
    >
      <NavMain :items="navMain" />
      <NavSecondary 
        :items="navSecondary" 
        class="mt-auto border-t border-sidebar-border/50 pt-4" 
      />
    </SidebarContent>
  </Sidebar>
</template>
