<template>
  <div class="space-y-6 relative">
    <div class="relative z-10">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
          <p class="text-muted-foreground">
            监测与管理车端安全风险，包含统计、筛选与详情查看，支持风险溯源与导出
          </p>
        </div>
      </div>

      <!-- 上端统计：阶段/等级/处置状态/处置率 -->
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <!-- 按处理阶段 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">按处理阶段统计</CardTitle>
            <GitBranch class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="text-xs text-muted-foreground mb-2">收集 / 存储 / 传输</div>
            <div class="h-[220px] p-2">
              <BarChart
                :data="stageStatsData"
                color-scheme="oceanDepths"
                :height="220"
                :show-values="true"
                :bar-width="'45%'"
                :bar-gap="'25%'"
                y-axis-name="风险数量"
                :grid-lines="true"
                :smooth-animation="true"
                class="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 风险等级 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">风险等级分布</CardTitle>
            <AlertTriangle class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="h-[220px] p-2">
              <PieChart
                :data="levelStatsData"
                color-scheme="oceanDepths"
                chart-type="doughnut"
                center-text="总计"
                :center-sub-text="totalLevel.toString() + '个'"
                :height="220"
                :show-legend="true"
                :show-percentages="false"
                :show-values="true"
                class="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 处置状态 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">处置状态</CardTitle>
            <CheckCircle2 class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div class="h-[220px] p-2">
              <PieChart
                :data="statusStatsData"
                color-scheme="oceanDepths"
                chart-type="doughnut"
                center-text="总计"
                :center-sub-text="totalStatus.toString() + '条'"
                :height="220"
                :show-legend="true"
                :show-percentages="false"
                :show-values="true"
                class="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 处置率统计 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">处置率</CardTitle>
            <Percent class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent class="p-6">
            <div class="space-y-4">
              <!-- 处置率数值显示 -->
              <div class="text-center">
                <div class="text-3xl font-bold text-foreground mb-1">{{ handleRate }}%</div>
                <p class="text-sm text-muted-foreground">已处置 / 总风险</p>
                <div class="text-xs text-muted-foreground mt-2">
                  今日新增已处置：<span class="text-foreground font-medium">{{
                    todayHandled
                  }}</span>
                </div>
              </div>

              <!-- 环形图 -->
              <div class="h-[180px] p-2">
                <PieChart
                  :data="handleRateData"
                  color-scheme="oceanDepths"
                  chart-type="doughnut"
                  center-text="处置率"
                  :center-sub-text="handleRate.toString() + '%'"
                  :height="180"
                  :show-legend="true"
                  :show-percentages="true"
                  :show-values="true"
                  class="w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 列表 -->
      <Card>
        <CardHeader>
          <CardTitle>车端风险列表</CardTitle>
          <CardDescription>
            共 {{ filteredTotal }} 条记录，当前显示第
            {{ pageSize * (currentPage - 1) + 1 }}
            -
            {{ Math.min(pageSize * currentPage, filteredTotal) }}
            条
          </CardDescription>
        </CardHeader>
        <CardContent>
          <!-- 筛选表单 -->
          <CompactFilterForm
            :filter-fields="filterFields"
            :show-export="true"
            :initial-values="filters"
            @search="handleSearch"
            @reset="resetFilters"
            @export="exportCsv"
          />
          <div class="rounded-md border mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-[80px]">序号</TableHead>
                  <TableHead class="min-w-[180px]">注册企业</TableHead>
                  <TableHead class="min-w-[160px]">车辆 VIN</TableHead>
                  <TableHead>品牌</TableHead>
                  <TableHead>车型</TableHead>
                  <TableHead>处理阶段</TableHead>
                  <TableHead>风险等级</TableHead>
                  <TableHead class="min-w-[200px]">风险描述</TableHead>
                  <TableHead>当前状态</TableHead>
                  <TableHead class="min-w-[140px]">发生时间</TableHead>
                  <TableHead class="min-w-[140px]">处置完成时间</TableHead>
                  <TableHead class="w-[200px] text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="pagedItems.length === 0">
                  <TableCell :colspan="12" class="h-24 text-center text-muted-foreground">
                    暂无数据
                  </TableCell>
                </TableRow>
                <TableRow
                  v-for="(item, index) in pagedItems"
                  :key="item.id"
                  class="hover:bg-muted/40"
                >
                  <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                  <TableCell>
                    <div class="space-y-1">
                      <div class="font-medium">{{ item.enterprise }}</div>
                      <div class="text-xs text-muted-foreground">{{ item.enterpriseCode }}</div>
                    </div>
                  </TableCell>
                  <TableCell>{{ item.vin }}</TableCell>
                  <TableCell>{{ item.brand }}</TableCell>
                  <TableCell>{{ item.model }}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{{ item.stage }}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="levelVariantDisplay(item.level)">{{
                      formatLevel(item.level)
                    }}</Badge>
                  </TableCell>
                  <TableCell class="max-w-[260px]">
                    <div class="text-sm line-clamp-2">{{ item.description }}</div>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="statusVariant(item.status)">{{ item.status }}</Badge>
                  </TableCell>
                  <TableCell class="whitespace-nowrap">{{ item.occurAt }}</TableCell>
                  <TableCell class="whitespace-nowrap">{{ item.resolvedAt || '-' }}</TableCell>
                  <TableCell class="text-right">
                    <div class="flex justify-end gap-2">
                      <Button size="sm" variant="outline" @click="openDetail(item)"
                        >详细信息</Button
                      >
                      <Button size="sm" @click="goTrace(item)">风险溯源</Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- 分页（统一使用 shadcn-vue Pagination） -->
          <div class="flex items-center justify-between mt-4 px-4 pb-4">
            <div class="pagination-size-control">
              <span>每页显示</span>
              <Select :model-value="pageSize.toString()" @update:model-value="onPageSizeChange">
                <SelectTrigger class="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span>条</span>
            </div>

            <Pagination
              v-model:page="currentPage"
              :total="filteredTotal"
              :items-per-page="pageSize"
              :sibling-count="1"
              :show-edges="true"
            >
              <PaginationContent v-slot="{ items }">
                <PaginationFirst />
                <PaginationPrevious />
                <template v-for="(item, idx) in items" :key="idx">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === currentPage"
                  />
                  <PaginationEllipsis v-else />
                </template>
                <PaginationNext />
                <PaginationLast />
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>

      <!-- 详情弹窗 -->
      <Dialog v-model:open="detailOpen">
        <DialogContent class="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>风险详细信息</DialogTitle>
            <DialogDescription>展示风险类型、危害、可能性与处置建议等信息</DialogDescription>
          </DialogHeader>

          <div v-if="selectedRisk" class="space-y-4">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <div class="text-xs text-muted-foreground">注册企业</div>
                <div class="text-base font-semibold">{{ selectedRisk.enterprise }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">车辆 VIN</div>
                <div class="text-base font-semibold">{{ selectedRisk.vin }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">品牌/车型</div>
                <div class="text-base font-semibold">
                  {{ selectedRisk.brand }} / {{ selectedRisk.model }}
                </div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">处理阶段</div>
                <div class="text-base font-semibold">{{ selectedRisk.stage }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">风险等级</div>
                <div class="text-base font-semibold">{{ formatLevel(selectedRisk.level) }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">当前状态</div>
                <div class="text-base font-semibold">{{ selectedRisk.status }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">发生时间</div>
                <div class="text-base font-semibold">{{ selectedRisk.occurAt }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">处置完成时间</div>
                <div class="text-base font-semibold">{{ selectedRisk.resolvedAt || '-' }}</div>
              </div>
            </div>

            <!-- 风险信息 -->
            <div class="space-y-2">
              <div class="text-xs text-muted-foreground">风险描述</div>
              <div class="text-sm">{{ selectedRisk.description }}</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <div class="text-xs text-muted-foreground">风险类型</div>
                <div class="text-base font-semibold">{{ selectedRisk.riskType }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">危害程度</div>
                <div class="text-base font-semibold">{{ selectedRisk.harm }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">发生可能性</div>
                <div class="text-base font-semibold">{{ selectedRisk.probability }}</div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="text-xs text-muted-foreground">处置建议</div>
              <div class="text-sm whitespace-pre-line">
                {{ selectedRisk.suggestion }}
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="text-xs text-muted-foreground">
                风险 ID：<span class="text-foreground">{{ selectedRisk.id }}</span>
              </div>
              <div class="flex gap-2">
                <Button variant="outline" @click="detailOpen = false">关闭</Button>
                <Button @click="goTrace(selectedRisk)">前往溯源</Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { AlertTriangle, CheckCircle2, GitBranch, Percent } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PieChart, BarChart } from '@/components/charts'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'

type RiskStage = '收集' | '存储' | '传输'
type RiskLevel = '高' | '中' | '低'
type HandleStatus = '未处理' | '处理中' | '已处理'

type StageFilter = 'ALL' | RiskStage
type StatusFilter = 'ALL' | HandleStatus
type LevelFilter = 'ALL' | RiskLevel

interface RiskItem {
  id: string
  enterprise: string
  enterpriseCode: string
  vin: string
  brand: string
  model: string
  stage: RiskStage
  level: RiskLevel
  description: string
  status: HandleStatus
  occurAt: string // YYYY-MM-DD HH:mm
  resolvedAt?: string // YYYY-MM-DD HH:mm | undefined
  riskType: string
  harm: '高' | '中' | '低'
  probability: '高' | '中' | '低'
  suggestion: string
}

interface Filters {
  enterprise: string
  vin: string
  brand: string
  model: string
  stage: StageFilter
  status: StatusFilter
  level: LevelFilter
  timeRange: [Date, Date] | null
}

const router = useRouter()

// 当前时间
const currentTime = ref('')
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 筛选器
const filters = ref<Filters>({
  enterprise: '',
  vin: '',
  brand: '',
  model: '',
  stage: 'ALL',
  status: 'ALL',
  level: 'ALL',
  timeRange: null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'enterprise',
    label: '注册企业',
    type: 'input',
    placeholder: '企业名称',
  },
  {
    key: 'vin',
    label: '车辆VIN',
    type: 'input',
    placeholder: 'VIN码',
  },
  {
    key: 'brand',
    label: '品牌',
    type: 'input',
    placeholder: '车辆品牌',
  },
  {
    key: 'model',
    label: '车型',
    type: 'input',
    placeholder: '车辆型号',
  },
  {
    key: 'stage',
    label: '处理阶段',
    type: 'select',
    placeholder: '选择阶段',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '收集', value: '收集' },
      { label: '存储', value: '存储' },
      { label: '传输', value: '传输' },
    ],
  },
  {
    key: 'status',
    label: '当前状态',
    type: 'select',
    placeholder: '选择状态',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '未处理', value: '未处理' },
      { label: '处理中', value: '处理中' },
      { label: '已处理', value: '已处理' },
    ],
  },
  {
    key: 'level',
    label: '风险等级',
    type: 'select',
    placeholder: '选择等级',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' },
    ],
  },
  {
    key: 'timeRange',
    label: '时间范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

const handleSearch = (searchFilters: Record<string, string | number | Date | null>) => {
  // 更新筛选条件
  Object.assign(filters.value, searchFilters)
  // 原型：无需请求，过滤在前端完成
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    enterprise: '',
    vin: '',
    brand: '',
    model: '',
    stage: 'ALL',
    status: 'ALL',
    level: 'ALL',
    timeRange: null,
  }
}

// Mock 数据
const riskItems = ref<RiskItem[]>([
  {
    id: 'R-202501-0001',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    vin: 'LSGJ8A12X34567890',
    brand: '极氪汽车',
    model: 'G1 Pro',
    stage: '收集',
    level: '高',
    description: '车辆在受限区域进行高频图像数据收集，疑似违规采集敏感地理信息。',
    status: '未处理',
    occurAt: '2025-08-20 10:32',
    riskType: '违规收集',
    harm: '高',
    probability: '中',
    suggestion: '立刻停止相关采集任务；核查采集清单；对采集数据做紧急脱敏与隔离；提交整改报告。',
  },
  {
    id: 'R-202501-0002',
    enterprise: '上海车联网服务有限公司',
    enterpriseCode: '91310000987654321Y',
    vin: 'WBAVC31060A123456',
    brand: '联行',
    model: 'L5',
    stage: '存储',
    level: '中',
    description: '存储策略未按要求进行地理信息脱敏，存在少量原始坐标残留。',
    status: '处理中',
    occurAt: '2025-08-20 12:21',
    riskType: '脱敏不充分',
    harm: '中',
    probability: '中',
    suggestion: '完善存储前脱敏策略；补充密钥管理；对存量数据执行批量脱敏任务。',
  },
  {
    id: 'R-202501-0003',
    enterprise: '深圳自动驾驶技术有限公司',
    enterpriseCode: '91440300567890123Z',
    vin: 'LFPH3ACC9J1234567',
    brand: '南山智能',
    model: 'N7 Max',
    stage: '传输',
    level: '低',
    description: '夜间传输日志发现少量未加密流量，可能由设备降级导致。',
    status: '已处理',
    occurAt: '2025-08-19 22:05',
    resolvedAt: '2025-08-20 01:18',
    riskType: '明文传输',
    harm: '低',
    probability: '低',
    suggestion: '已恢复安全传输通道；建议对固件升级并开启降级禁止策略。',
  },
  {
    id: 'R-202501-0004',
    enterprise: '广州未来出行科技有限公司',
    enterpriseCode: '91440100MA5CYN4L8X',
    vin: 'LGWEF6A51LH234567',
    brand: '未来汽车',
    model: 'Future X3',
    stage: '收集',
    level: '高',
    description: '车辆在未报备测试/采集区域进行高精度地图数据采集，严重违反测绘法规。',
    status: '未处理',
    occurAt: '2025-08-21 08:45',
    riskType: '违法测绘',
    harm: '高',
    probability: '高',
    suggestion: '立即停止所有采集活动；删除已采集的敏感数据；配合监管部门调查；进行合规培训。',
  },
  {
    id: 'R-202501-0005',
    enterprise: '杭州智慧交通有限公司',
    enterpriseCode: '91330100MA2H3N9T7W',
    vin: 'LVSHCAME5JE345678',
    brand: '智慧车联',
    model: 'Smart V2',
    stage: '收集',
    level: '中',
    description: '车辆在商业区域进行超出导航需求的高频位置采集，疑似过度收集用户轨迹。',
    status: '处理中',
    occurAt: '2025-08-21 11:22',
    riskType: '过度采集',
    harm: '中',
    probability: '高',
    suggestion: '调整采集策略；限制采集频率；建立采集审计机制；定期评估采集必要性。',
  },
  {
    id: 'R-202501-0006',
    enterprise: '成都车路协同技术公司',
    enterpriseCode: '91510100MA7G8P2K3A',
    vin: 'LSJA24U66KG456789',
    brand: '协同智驾',
    model: 'C-Drive Pro',
    stage: '存储',
    level: '高',
    description: '车载存储设备未加密，包含大量未脱敏的轨迹数据，存在重大泄露风险。',
    status: '未处理',
    occurAt: '2025-08-21 14:15',
    riskType: '存储安全',
    harm: '高',
    probability: '中',
    suggestion: '立即启用全盘加密；对历史数据进行加密处理；部署密钥管理系统；制定数据安全策略。',
  },
  {
    id: 'R-202501-0007',
    enterprise: '天津智能网联汽车公司',
    enterpriseCode: '91120116MA5JJHXN4R',
    vin: 'LFV3A21K8L3567890',
    brand: '津联智行',
    model: 'TJ-Auto X1',
    stage: '收集',
    level: '中',
    description: '车辆采集频率异常升高，超出正常导航需求300%，疑似过度采集。',
    status: '已处理',
    occurAt: '2025-08-18 16:30',
    resolvedAt: '2025-08-19 09:20',
    riskType: '过度采集',
    harm: '中',
    probability: '中',
    suggestion: '已调整采集策略；限制采集频率；建立采集审计机制；定期评估采集必要性。',
  },
  {
    id: 'R-202501-0008',
    enterprise: '南京智慧出行科技公司',
    enterpriseCode: '91320100MA1W8J9K7T',
    vin: 'LJDCAH229L1678901',
    brand: '宁行智能',
    model: 'NX Smart 5',
    stage: '收集',
    level: '低',
    description: '车辆传感器采集精度设置过高，收集了超出业务需求的详细环境数据。',
    status: '已处理',
    occurAt: '2025-08-17 20:18',
    resolvedAt: '2025-08-18 10:45',
    riskType: '精度过高',
    harm: '低',
    probability: '中',
    suggestion: '已调整传感器精度；优化采集参数；建立精度评估机制；定期校准设备。',
  },
  {
    id: 'R-202501-0009',
    enterprise: '武汉未来智行有限公司',
    enterpriseCode: '91420100MA4KYJQJ5D',
    vin: 'LWVCA3D54LA789012',
    brand: '楚天智驾',
    model: 'CT-Auto Z3',
    stage: '收集',
    level: '中',
    description: '车辆摄像头在红绿灯路口采集了过多行人面部特征数据，超出交通辅助需求。',
    status: '处理中',
    occurAt: '2025-08-20 15:40',
    riskType: '隐私过采',
    harm: '中',
    probability: '低',
    suggestion: '调整摄像头采集范围；启用人脸模糊处理；优化图像识别算法；加强隐私保护措施。',
  },
  {
    id: 'R-202501-0010',
    enterprise: '重庆智能交通系统公司',
    enterpriseCode: '91500000MA5U8H7K2P',
    vin: 'LMGJE1S90L1890123',
    brand: '渝驾智联',
    model: 'CQ-Smart X',
    stage: '收集',
    level: '高',
    description: '在禁止测绘区域启动了高精度定位采集，包含敏感基础设施信息。',
    status: '未处理',
    occurAt: '2025-08-21 09:55',
    riskType: '违规测绘',
    harm: '高',
    probability: '高',
    suggestion: '立即停止采集；删除相关数据；报告监管部门；加强合规意识培训。',
  },
  {
    id: 'R-202501-0011',
    enterprise: '西安高新智驾科技公司',
    enterpriseCode: '91610131MA6TGMRF9X',
    vin: 'LVHRU685XLN901234',
    brand: '秦智行',
    model: 'QZX-Pro 7',
    stage: '收集',
    level: '中',
    description: '车辆在夜间停车状态下仍持续采集周边环境数据，存在不必要的隐私收集风险。',
    status: '处理中',
    occurAt: '2025-08-21 13:20',
    riskType: '无效采集',
    harm: '中',
    probability: '高',
    suggestion: '优化采集触发条件；设置停车状态检测；建立智能采集策略；减少无效数据收集。',
  },
  {
    id: 'R-202501-0012',
    enterprise: '苏州智慧车联网公司',
    enterpriseCode: '91320500MA1XRDNM91',
    vin: 'LSKG4GC10MA012345',
    brand: '姑苏智驾',
    model: 'SZ-Intel 3',
    stage: '存储',
    level: '低',
    description: '日志文件权限设置过于宽松，可能被未授权访问。',
    status: '已处理',
    occurAt: '2025-08-16 18:25',
    resolvedAt: '2025-08-17 09:10',
    riskType: '权限配置',
    harm: '低',
    probability: '低',
    suggestion: '已调整文件权限；实施最小权限原则；加强权限审计；定期检查配置。',
  },
])

// 顶部统计数据 - 使用港铁色系主题
const stageStatsData = computed(() => {
  const count: Record<RiskStage, number> = { 收集: 0, 存储: 0, 传输: 0 }
  for (const r of riskItems.value) count[r.stage]++
  return (Object.keys(count) as RiskStage[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}阶段风险数量`,
  }))
})

// 风险等级分布数据
const levelStatsData = computed(() => {
  const count: Record<RiskLevel, number> = { 高: 0, 中: 0, 低: 0 }
  for (const r of riskItems.value) count[r.level]++
  return (Object.keys(count) as RiskLevel[]).map((k) => ({
    name: `${k}风险`,
    value: count[k],
    description: `${k}风险等级事件数量`,
  }))
})
const totalLevel = computed(() => levelStatsData.value.reduce((s, i) => s + i.value, 0))

// 处置状态数据
const statusStatsData = computed(() => {
  const count: Record<HandleStatus, number> = { 未处理: 0, 处理中: 0, 已处理: 0 }
  for (const r of riskItems.value) count[r.status]++
  return (Object.keys(count) as HandleStatus[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}状态的风险数量`,
  }))
})
const totalStatus = computed(() => statusStatsData.value.reduce((s, i) => s + i.value, 0))

// 处置率数据
const handleRate = computed(() => {
  const total = riskItems.value.length
  const handled = riskItems.value.filter((r) => r.status === '已处理').length
  return total === 0 ? 0 : Math.round((handled / total) * 100)
})
const todayHandled = 3 // mock
const handleRateData = computed(() => [
  { name: '已处置', value: handleRate.value, description: '已完成处置的风险占比' },
  { name: '未处置', value: 100 - handleRate.value, description: '尚未处置的风险占比' },
])

// 过滤与分页
const filtered = computed(() => {
  const { enterprise, vin, brand, model, stage, status, level, timeRange } = filters.value
  return riskItems.value.filter((r) => {
    if (enterprise && !r.enterprise.includes(enterprise)) return false
    if (vin && !r.vin.includes(vin)) return false
    if (brand && !r.brand.includes(brand)) return false
    if (model && !r.model.includes(model)) return false
    if (stage !== 'ALL' && r.stage !== stage) return false
    if (status !== 'ALL' && r.status !== status) return false
    if (level !== 'ALL' && r.level !== level) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
        0,
        0,
        0,
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const occur = new Date(r.occurAt.replace(/-/g, '/')).getTime()
      if (occur < start || occur > end) return false
    }
    return true
  })
})
const filteredTotal = computed(() => filtered.value.length)

const currentPage = ref(1)
const pageSize = ref(10)
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.value.slice(start, start + pageSize.value)
})

const onPageSizeChange = (val: string) => {
  const n = parseInt(val)
  if (!Number.isNaN(n) && n > 0) {
    pageSize.value = n
    currentPage.value = 1
  }
}

// 详情弹窗
const detailOpen = ref(false)
const selectedRisk = ref<RiskItem | null>(null)
const openDetail = (r: RiskItem) => {
  selectedRisk.value = r
  detailOpen.value = true
}

// 文案统一：显示高/中/低
const formatLevel = (lv: string) => {
  if (lv === '特别重大') return '高'
  if (lv === '重大') return '中'
  if (lv === '一般') return '低'
  return lv
}
// 溯源参数映射：列表“高/中/低”映射为“特别重大/重大/一般”
const levelToEventLevel = (lv: RiskLevel) => {
  if (lv === '高') return '特别重大'
  if (lv === '中') return '重大'
  return '一般'
}
const goTrace = (r: RiskItem) => {
  router.push({
    path: '/gov/trace/vehicle',
    query: {
      external: '1',
      eventId: r.id,
      enterprise: r.enterprise,
      vin: r.vin,
      eventLevel: levelToEventLevel(r.level),
      desc: r.description,
    },
  })
}

// 导出 CSV（占位）
const exportCsv = () => {
  const headers = [
    '注册企业',
    '企业代码',
    '车辆VIN',
    '品牌',
    '车型',
    '处理阶段',
    '风险等级',
    '风险描述',
    '当前状态',
    '发生时间',
    '处置完成时间',
  ]
  const rows = filtered.value.map((r) => [
    r.enterprise,
    r.enterpriseCode,
    r.vin,
    r.brand,
    r.model,
    r.stage,
    r.level,
    r.description.replace(/\n/g, ' '),
    r.status,
    r.occurAt,
    r.resolvedAt || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `车端安全风险_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}
const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}

// Badge 样式
const levelVariant = (lv: RiskLevel) => {
  switch (lv) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}
const statusVariant = (st: HandleStatus) => {
  switch (st) {
    case '未处理':
      return 'outline'
    case '处理中':
      return 'secondary'
    case '已处理':
      return 'default'
    default:
      return 'outline'
  }
}
// 统一给文本等级做 variant 映射
const levelVariantDisplay = (lv: string) => {
  const d = formatLevel(lv)
  switch (d as RiskLevel) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}

// 生命周期
let t: ReturnType<typeof setInterval>
onMounted(() => {
  updateTime()
  t = setInterval(updateTime, 1000)
})
onUnmounted(() => {
  if (t) clearInterval(t)
})
</script>
