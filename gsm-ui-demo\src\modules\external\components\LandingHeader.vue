<template>
  <header class="gsm-external-module header">
    <nav class="nav-container">
      <div class="nav-brand">
        <RouterLink
          to="/"
          class="external-flex external-items-center space-x-3 brand-link"
          aria-label="返回首页"
          title="返回首页"
        >
          <AppLogo
            size="medium"
            alt="地理信息安全监测平台"
            className="brand-logo hover:scale-105 transition-transform duration-300"
            logoType="public"
          />
          <div>
            <h1 class="text-xl font-bold brand-title">地理信息安全监测平台</h1>
            <p class="text-xs brand-subtitle">Geospatial Security Monitoring Platform</p>
          </div>
        </RouterLink>
      </div>
      <div class="nav-actions">
        <button
          class="theme-switch"
          type="button"
          role="switch"
          :title="isDark ? 'Switch to light theme' : 'Switch to dark theme'"
          :aria-checked="isDark"
          @click="toggleTheme"
        >
          <span class="switch-track">
            <span class="switch-thumb">
              <svg
                class="sun-icon"
                :class="{ active: !isDark }"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="4"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 17.66l-1.41 1.41M19.07 4.93l-1.41 1.41"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                class="moon-icon"
                :class="{ active: isDark }"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </span>
          </span>
        </button>
        <button
          class="external-btn external-btn-primary btn-primary"
          @click="handleLogin('enterprise')"
        >
          企业端登录
        </button>
        <button class="external-btn btn-outline" @click="handleLogin('government')">
          政府端登录
        </button>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AppLogo from '@/components/layout/AppLogo.vue'

interface Props {
  onThemeToggle?: (isDark: boolean) => void
  onLogin?: (type: 'government' | 'enterprise') => void
}

const props = defineProps<Props>()

const isDark = ref(false)

const applyTheme = (theme: 'light' | 'dark') => {
  isDark.value = theme === 'dark'
  // Apply theme to external module wrapper
  const wrapper = document.querySelector('.gsm-external-module')
  if (wrapper) wrapper.setAttribute('data-theme', theme)
  // Also apply to document for global theme consistency if needed
  document.documentElement.setAttribute('data-theme', theme)
  try {
    localStorage.setItem('theme', theme)
  } catch {}
  // Notify parent component
  props.onThemeToggle?.(isDark.value)
}

const toggleTheme = () => {
  applyTheme(isDark.value ? 'light' : 'dark')
}

const handleLogin = (type: 'government' | 'enterprise') => {
  // Use callback instead of router to maintain flexibility
  if (props.onLogin) {
    props.onLogin(type)
  } else {
    // Fallback to window navigation
    const routes = {
      government: '/app/government/dashboard',
      enterprise: '/app/enterprise/dashboard',
    }
    window.location.href = routes[type]
  }
}

onMounted(() => {
  // 优先读取用户上次选择；否则按本地时间自动：白天(7-19)亮色，夜间暗色
  try {
    const saved = localStorage.getItem('theme') as 'light' | 'dark' | null
    if (saved === 'light' || saved === 'dark') {
      applyTheme(saved)
      return
    }
  } catch {}
  const hour = new Date().getHours()
  const isNight = hour >= 19 || hour < 7
  applyTheme(isNight ? 'dark' : 'light')
})
</script>

<style scoped>
/* 导航栏样式 */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--external-nav-bg);
  border-bottom: 1px solid var(--external-border-color);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  width: 100%;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  width: 100%;
  box-sizing: border-box;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Utility classes for the logo structure */
.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.relative {
  position: relative;
}

.w-10 {
  width: 2.5rem;
}

.h-10 {
  height: 2.5rem;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.text-blue-400 {
  color: #60a5fa;
}

.text-cyan-400 {
  color: #22d3ee;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.drop-shadow-lg {
  filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
}

.hover\:text-cyan-400:hover {
  color: #22d3ee;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-300 {
  transition-duration: 300ms;
}

.brand-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--external-text-color);
  margin: 0;
  line-height: 1.75rem;
}

.brand-subtitle {
  font-size: 0.75rem;
  line-height: 1rem;
  color: var(--external-text-color-secondary);
  margin: 0;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 按钮样式 */
.btn-outline {
  color: var(--external-text-color);
  border-color: var(--external-border-color);
}

.btn-outline:hover {
  background: var(--external-hover-bg);
  color: var(--external-text-color);
  border-color: var(--external-primary-color);
}

.btn-primary {
  background: var(--external-gradient-primary);
  color: white;
  border-color: var(--external-primary-color);
  box-shadow: 0 4px 15px rgba(0, 128, 204, 0.3);
}

.btn-primary:hover {
  background: var(--external-primary-hover);
  border-color: var(--external-primary-hover);
}

/* VitePress风格的主题切换按钮 */
.theme-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  width: 40px;
  height: 20px;
  border: 1px solid var(--external-border-color);
  border-radius: 10px;
  background: var(--external-surface-color);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  padding: 2px;
}

.theme-switch:hover {
  border-color: var(--external-primary-color);
  background: var(--external-hover-bg);
}

.theme-switch:focus-visible {
  border-color: var(--external-primary-color);
  box-shadow: 0 0 0 2px var(--external-glow-color);
  outline: none;
}

.switch-track {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}

.switch-thumb {
  position: absolute;
  top: -1px;
  left: 0px;
  width: 16px;
  height: 16px;
  background: var(--external-text-color);
  border-radius: 50%;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--external-border-color);
}

.gsm-external-module[data-theme='dark'] .switch-thumb {
  transform: translateX(18px);
  background: var(--external-text-color);
}

/* 主题切换图标 */
.sun-icon,
.moon-icon {
  position: absolute;
  width: 10px;
  height: 10px;
  color: var(--external-text-color-secondary);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: scale(0.6) rotate(180deg);
}

.sun-icon.active,
.moon-icon.active {
  opacity: 1;
  transform: scale(1) rotate(0deg);
  color: var(--external-background-color);
}

/* Light theme adjustments */
.gsm-external-module[data-theme='light'] .header {
  background: var(--external-nav-bg);
  border-bottom: 1px solid var(--external-border-color);
}

.gsm-external-module[data-theme='light'] .brand-title {
  color: var(--external-text-color);
}

.gsm-external-module[data-theme='light'] .brand-subtitle {
  color: var(--external-text-color-secondary);
}

.gsm-external-module[data-theme='light'] .btn-outline {
  color: var(--external-text-color);
  border-color: var(--external-border-color);
}

.gsm-external-module[data-theme='light'] .btn-outline:hover {
  background: var(--external-hover-bg);
  color: var(--external-text-color);
  border-color: var(--external-primary-color);
}

.gsm-external-module[data-theme='light'] .theme-switch {
  background: var(--external-surface-color);
  border-color: var(--external-border-color);
}

.gsm-external-module[data-theme='light'] .theme-switch:hover {
  background: var(--external-hover-bg);
  border-color: var(--external-primary-color);
}

.gsm-external-module[data-theme='light'] .switch-thumb {
  background: var(--external-text-color);
  border-color: var(--external-border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  /* 使品牌区（含 logo 与标题）可点击回到首页，并具备良好可访问性 */
  .brand-link {
    text-decoration: none;
    color: inherit;
    cursor: pointer;
  }
  .brand-link:focus-visible {
    outline: 2px solid rgba(59, 130, 246, 0.6);
    outline-offset: 2px;
    border-radius: 8px;
  }
  .nav-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>
