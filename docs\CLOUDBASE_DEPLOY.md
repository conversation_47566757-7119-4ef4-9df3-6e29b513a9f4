# 腾讯云 CloudBase 部署指南

## 📦 项目部署准备

✅ **项目构建完成**
- 构建产物位于 `dist/` 目录
- 已配置 `cloudbaserc.json` 部署配置
- 环境ID: `cloud1-0gc8cbzg3efd6a99`
- 区域: `ap-shanghai`

## 🚀 部署方式

### 方式一：CloudBase CLI 部署（推荐）

1. **安装 CloudBase CLI**
```bash
npm install -g @cloudbase/cli
```

2. **登录到腾讯云**
```bash
tcb login
```

3. **部署项目**
```bash
cd gsm-ui-demo
npm run deploy
# 或者
tcb framework deploy
```

### 方式二：腾讯云控制台手动上传

1. **登录腾讯云控制台**
   - 访问：https://console.cloud.tencent.com/tcb
   - 选择环境：`cloud1-0gc8cbzg3efd6a99`

2. **进入静态网站托管**
   - 点击左侧菜单 "静态网站托管"
   - 点击 "文件管理"

3. **上传构建产物**
   - 将 `dist/` 目录下的所有文件上传到根目录
   - 确保 `index.html` 在根目录

4. **配置域名和SSL**
   - 在 "设置" 页面配置自定义域名
   - 启用 HTTPS

### 方式三：GitHub Actions 自动部署

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to CloudBase
on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Deploy to CloudBase
      run: |
        npm install -g @cloudbase/cli
        tcb login --key ${{ secrets.CLOUDBASE_SECRET_ID }} --secret ${{ secrets.CLOUDBASE_SECRET_KEY }}
        tcb framework deploy
```

## 📋 部署配置说明

### cloudbaserc.json 配置
```json
{
  "envId": "cloud1-0gc8cbzg3efd6a99",
  "framework": {
    "name": "vite-spa",
    "plugins": {
      "frontend": {
        "use": "@cloudbase/framework-plugin-website",
        "inputs": {
          "outputPath": "dist",
          "cloudPath": "/",
          "redirects": [
            {
              "source": "/:path*", 
              "destination": "/index.html",
              "type": 200
            }
          ]
        }
      }
    }
  }
}
```

### 关键特性
- ✅ **SPA路由支持**: 所有路径重定向到 `index.html`
- ✅ **静态资源缓存**: JS/CSS/图片文件长期缓存
- ✅ **HTTPS支持**: 自动配置SSL证书
- ✅ **CDN加速**: 全球CDN节点加速访问

## 🌐 访问地址

部署成功后，应用将可通过以下地址访问：

**默认域名**: https://cloud1-0gc8cbzg3efd6a99.ap-shanghai.tcb.qcloud.la

**自定义域名**: 可在控制台配置

## 🔧 环境变量配置

如需配置环境变量，在 `cloudbaserc.json` 中添加：

```json
"envVariables": {
  "VITE_API_BASE_URL": "https://api.your-domain.com",
  "VITE_APP_ENV": "production"
}
```

## 📈 监控和日志

- **访问统计**: CloudBase 控制台 → 统计分析
- **错误监控**: CloudBase 控制台 → 监控告警
- **访问日志**: CloudBase 控制台 → 日志检索

## 🔍 故障排除

### 常见问题

1. **路由404问题**
   - 确保 `redirects` 配置正确
   - 检查 `index.html` 是否在根目录

2. **资源加载失败**
   - 检查静态资源路径配置
   - 确认 `base` 路径设置

3. **部署失败**
   - 检查网络连接
   - 确认腾讯云账号权限
   - 查看构建日志错误信息

### 调试命令

```bash
# 检查环境信息
tcb env list

# 查看部署日志
tcb functions log list

# 清理缓存重新部署
tcb framework deploy --force
```

---

**部署状态**: ✅ 准备就绪，可直接部署
**估计部署时间**: 2-5分钟
**技术支持**: 腾讯云 CloudBase 官方文档