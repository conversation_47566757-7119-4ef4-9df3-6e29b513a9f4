<template>
  <ChartContainer
:option="pieOption"
    :height="height"
    :color-scheme="inferredScheme"
    :loading="loading"
    :enable-toolbox="enableToolbox"
    @click="handleClick"
    @mouseover="handleMouseOver"
    @mouseout="handleMouseOut"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ChartContainer from './ChartContainer.vue'
import { createTooltipFormatter } from '@/lib/chart-themes'

interface DataItem {
  name: string
  value: number
  color?: string
  description?: string
}

interface Props {
  data: DataItem[]
  title?: string
  height?: string | number
  showLegend?: boolean
  centerText?: string
  centerSubText?: string
  colorScheme?:
    | 'primary'
    | 'risk'
    | 'enterprise'
    | 'stages'
    | 'status'
    | 'oceanDepths'
    | 'sunsetWarmth'
    | 'forestGrove'
    | 'modernCorporate'
    | 'pastelDreams'
    | 'neonTech'
    | 'vintageEarth'
    | 'arcticAurora'
    | 'floralPalette'
    | 'summerAfternoon'
    | 'retroFuturistic'
    | 'resilience'
  chartType?: 'pie' | 'doughnut'
  showValues?: boolean
  showPercentages?: boolean
  loading?: boolean
  enableToolbox?: boolean
  minAngle?: number
  roseType?: boolean | 'radius' | 'area'
  // Optional radius controls (percentage values, without the % sign)
  innerRadiusPct?: number
  outerRadiusPct?: number
  // 自动配色：当为 true 时，根据数据内容推断企业/风险/默认配色
  autoColor?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  showLegend: true,
  colorScheme: 'primary',
  chartType: 'doughnut',
  showValues: true,
  showPercentages: true,
  loading: false,
  enableToolbox: false,
  minAngle: 0,
  roseType: false,
  innerRadiusPct: 35,
  outerRadiusPct: 55,
  autoColor: true,
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
}>()

const enterpriseKeywords = ['平台', '智驾', '地图', '企业类型', '厂商', 'enterprise', 'company']
const riskKeywords = ['风险', '告警', '异常', '严重', '高', '中', '低', 'risk']

const inferredScheme = computed(() => {
  if (!props.autoColor) return props.colorScheme
  // 若父组件明确指定了非 primary，则尊重传入
  if (props.colorScheme && props.colorScheme !== 'primary') return props.colorScheme
  const names = (props.data || []).map(d => String(d?.name ?? '')).join(' ')
  if (enterpriseKeywords.some(k => names.includes(k))) return 'enterprise'
  if (riskKeywords.some(k => names.includes(k))) return 'risk'
  return 'primary'
})

const pieOption = computed(() => {
  const isDoughnut = props.chartType === 'doughnut' || props.centerText || props.centerSubText

  return {
    title: props.title
      ? {
          text: props.title,
          left: 'center',
          top: 8,
          textStyle: {
            fontSize: 16,
            fontWeight: '600',
          },
        }
      : undefined,

    legend: props.showLegend
      ? {
          type: 'scroll',
          orient: 'horizontal',
          bottom: 5,
          left: 'center',
          itemGap: 12,
          itemWidth: 12,
          itemHeight: 12,
          pageButtonItemGap: 5,
          pageIconColor: '#64748b',
          pageIconInactiveColor: '#cbd5e1',
          pageTextStyle: {
            color: '#64748b',
            fontSize: 11,
          },
          textStyle: {
            fontSize: 11,
          },
          animation: true,
          animationDurationUpdate: 800,
          selector: false,
          selectorLabel: {
            show: false,
          },
        }
      : undefined,

    tooltip: {
      trigger: 'item',
      formatter: createTooltipFormatter('pie'),
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        fontSize: 12,
      },
      padding: [12, 16],
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); backdrop-filter: blur(8px);',
    },

    series: [
      {
        name: props.title || '数据统计',
        type: 'pie',
        radius: isDoughnut ? [`${props.innerRadiusPct}%`, `${props.outerRadiusPct}%`] : `${props.outerRadiusPct}%`,
        center: ['50%', props.showLegend ? '45%' : '50%'],
        roseType: props.roseType,
        minAngle: props.minAngle,
        avoidLabelOverlap: true,
        stillShowZeroSum: false,

        itemStyle: {
          borderRadius: isDoughnut ? 4 : 3,
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          shadowBlur: 4,
          shadowColor: 'rgba(0, 0, 0, 0.1)',
        },

        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {d}%',
        },

        labelLine: {
          show: true,
          smooth: false,
        },

        emphasis: {
          focus: 'self',
          blurScope: 'coordinateSystem',
          scale: true,
          scaleSize: 6,
          itemStyle: {
            shadowBlur: 12,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },

        select: {
          itemStyle: {
            borderColor: '#8b1538',
            borderWidth: 2,
          },
        },

        data: props.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          description: item.description,
          // 只有当数据项指定了颜色时才设置itemStyle，让ChartContainer处理主题颜色
          itemStyle: item.color
            ? {
                color: item.color,
              }
            : undefined,
        })),

        // 动画配置
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => idx * 100,
        animationDuration: 1000,
      },
    ],

    // 中心文字（环形图）- 调整位置避免与图例重叠
    graphic:
      isDoughnut && (props.centerText || props.centerSubText)
        ? [
            // 主标题
            props.centerText
              ? {
                  type: 'text',
                  left: 'center',
                  top: props.showLegend ? '42%' : 'middle',
                  style: {
                    text: props.centerText,
                    textAlign: 'center',
                    fill: 'currentColor',
                    fontSize: 18,
                    fontWeight: '700',
                    y: props.centerSubText ? -8 : 0,
                  },
                }
              : null,
            // 副标题
            props.centerSubText
              ? {
                  type: 'text',
                  left: 'center',
                  top: props.showLegend ? '42%' : 'middle',
                  style: {
                    text: props.centerSubText,
                    textAlign: 'center',
                    fill: '#64748b',
                    fontSize: 14,
                    fontWeight: '500',
                    y: props.centerText ? 15 : 0,
                  },
                }
              : null,
          ].filter(Boolean)
        : undefined,

    // 全局动画配置
    animationDuration: 1000,
    animationEasing: 'cubicOut',

    // 媒体查询配置
    media: [
      {
        query: {
          maxWidth: 768,
        },
        option: {
          series: [
            {
              radius: isDoughnut ? [`${Math.max(10, props.innerRadiusPct - 5)}%`, `${Math.max(15, props.outerRadiusPct - 5)}%`] : `${Math.max(15, props.outerRadiusPct - 5)}%`,
              center: ['50%', props.showLegend ? '42%' : '50%'],
            },
          ],
          graphic:
            isDoughnut && (props.centerText || props.centerSubText)
              ? [
                  props.centerText
                    ? {
                        style: {
                          fontSize: 16,
                          y: props.centerSubText ? -6 : 0,
                        },
                      }
                    : null,
                  props.centerSubText
                    ? {
                        style: {
                          fontSize: 12,
                          y: props.centerText ? 12 : 0,
                        },
                      }
                    : null,
                ].filter(Boolean)
              : undefined,
        },
      },
    ],
  }
})

const handleClick = (params: any) => {
  emit('click', params)
}

const handleMouseOver = (params: any) => {
  emit('mouseover', params)
}

const handleMouseOut = (params: any) => {
  emit('mouseout', params)
}
</script>
