模拟风险与数据血缘溯源数据
模拟风险与数据血缘溯源数据
一、 车端风险数据及溯源
1. 车端风险事件：违规区域采集 (RT-001)
风险事件描述：测试车辆在未经备案的军事管理区内进行了时空数据采集，严重违反了地理信息安全规定。
风险事件记录 (risk_events 表):
| 字段 | 值 | 备注 |
| :--- | :--- | :--- |
| event_id | EVT-V-20250826-001 | 事件唯一标识 |
| enterprise_id | ENT-A-001 | 所属企业A |
| vin | LSVAP2A17PXXXXXXXX | 涉事车辆VIN码 |
| event_type | RT-001 | 风险项编号：违规区域采集 |
| event_level | 特别重大 | 风险级别 |
| event_timestamp | 1756219860000 | 2025-08-26 10:11:00 |
| event_longitude | 116334567 | 经度 (116.334567°) |
| event_latitude | 39912345 | 纬度 (39.912345°) |
| event_description | 车辆在军事管理区（未报备测试区）进行数据采集 | 事件描述 |
| disposal_status | 待处置 | 处置状态 |
关联数据血缘溯源过程：
数据血缘分析显示，该风险事件由以下一系列车端处理活动日志构成，清晰地展示了从数据采集到存储的全过程。
溯源日志 1: 采集阶段 (vehicle_processing_logs)
日志ID: LOG-V-COL-20250826-101100-A17P
核心内容: 记录了车辆在违规地理坐标点上执行了“采集”操作。
1 {
2   "log_id": "LOG-V-COL-20250826-101100-A17P",
3   "vin": "LSVAP2A17PXXXXXXXX",
4   "processing_stage": "收集",
5   "stage_timestamp": 1756219860000,
6   "data_type_bitmap": "00011", // 点云, 影像
7   "collection_longitude": 116334567, // 违规位置
8   "collection_latitude": 39912345,   // 违规位置
9   "collection_altitude": 5010,       // 高度 50.1m
10   "coordinate_precision": "高精度(<1m)",
11   "geographic_fence_status": "区域外" // 地理围栏判断为在禁止区域外
12 }
溯源日志 2: 存储阶段 (vehicle_processing_logs)
日志ID: LOG-V-STO-20250826-101105-A17P
核心内容: 记录了上述违规采集的数据被明文存储在车载设备中。
1 {
2   "log_id": "LOG-V-STO-20250826-101105-A17P",
3   "vin": "LSVAP2A17PXXXXXXXX",
4   "processing_stage": "存储",
5   "stage_timestamp": 1756219865000,
6   "source_log_id": "LOG-V-COL-20250826-101100-A17P", // 关联采集日志
7   "data_type_bitmap": "00011",
8   "encryption_storage_status": "未加密存储", // 风险点：明文存储
9   "access_control_status": "关闭访问控制"
10 }
2. 车端风险事件：未授权跨境传输 (RT-021)
风险事件描述：一辆运营车辆在未经任何报备和安全评估的情况下，将采集的时空数据直接传输至境外服务器。
风险事件记录 (risk_events 表):
| 字段 | 值 | 备注 |
| :--- | :--- | :--- |
| event_id | EVT-V-20250826-002 | 事件唯一标识 |
| enterprise_id | ENT-B-002 | 所属企业B |
| vin | LSVBP2B18PXXXXXXXX | 涉事车辆VIN码 |
| event_type | RT-021 | 风险项编号：未授权跨境传输 |
| event_level | 特别重大 | 风险级别 |
| event_timestamp | 1756223700000 | 2025-08-26 11:15:00 |
| event_description | 车辆将包含轨迹与影像的数据传输至境外IP地址 | 事件描述 |
| disposal_status | 处置中 | 处置状态 |
关联数据血缘溯源过程：
溯源日志 1: 传输阶段 (vehicle_processing_logs)
日志ID: LOG-V-TRN-20250826-111500-B18P
核心内容: 记录了车辆将数据传输至一个明确的境外IP地址。
1 {
2   "log_id": "LOG-V-TRN-20250826-111500-B18P",
3   "vin": "LSVBP2B18PXXXXXXXX",
4   "processing_stage": "传输",
5   "stage_timestamp": 1756223700000,
6   "data_type_bitmap": "00110", // 影像, 轨迹
7   "transmission_mileage": 152, // 传输数据覆盖里程 15.2km
8   "destination_ip": "************", // 风险点：境外IP
9   "destination_region": "境外", // 风险点：明确标识为境外
10   "security_protocol": "HTTP", // 风险点：使用非加密传输
11   "coordinate_processed_flag": "传输真实坐标"
12 }
二、 云端风险数据及溯源
1. 云端风险事件：未脱敏对外提供 (RE-021)
风险事件描述：企业云平台在数据共享过程中，将一批包含高精度坐标和关联影像的原始数据，在未进行任何脱敏处理的情况下，提供给了第三方合作单位。
风险事件记录 (risk_events 表):
| 字段 | 值 | 备注 |
| :--- | :--- | :--- |
| event_id | EVT-C-20250826-001 | 事件唯一标识 |
| enterprise_id | ENT-A-001 | 所属企业A |
| vin | N/A | 云端操作，不直接关联单车 |
| event_type | RE-021 | 风险项编号：未脱敏对外提供 |
| event_level | 重大 | 风险级别 |
| event_timestamp | 1756232400000 | 2025-08-26 13:00:00 |
| event_description | 云平台将原始点云和影像数据提供给第三方，未执行脱敏 | 事件描述 |
| disposal_status | 待处置 | 处置状态 |
关联数据血缘溯源过程：
溯源日志 1: 收集阶段 (enterprise_processing_logs)
日志ID: LOG-C-COL-20250826-123000-ENT-A
核心内容: 云平台从多辆车汇聚了一批重要数据。
1 {
2   "log_id": "LOG-C-COL-20250826-123000-ENT-A",
3   "enterprise_id": "ENT-A-001",
4   "processing_stage": "收集",
5   "stage_timestamp": 1756230600000,
6   "data_importance": "重要",
7   "data_source": "车端上传",
8   "data_type_bitmap": "00011" // 点云, 影像
9 }
　　溯源日志 2: 提 供阶段 (enterprise_processing_logs)
日志ID: LOG-C-PRO-20250826-130000-ENT-A
核心内容: 记录了将上述数据提供给第三方，且明确标识为“未经安全处理”。
1 {
2   "log_id": "LOG-C-PRO-20250826-130000-ENT-A",
3   "enterprise_id": "ENT-A-001",
4   "processing_stage": "提供",
5   "stage_timestamp": 1756232400000,
6   "source_log_id": "LOG-C-COL-20250826-123000-ENT-A",
7   "receiver_id": "ENT-C-003", // 第三方合作单位
8   "security_processing_flag": "未经安全处理", // 风险点
9   "contract_status": "有合同协议",
10   "operator_id": "user_ops_05"
11 }
2. 云端风险事件：境外存储核心数据 (RE-011)
风险事件描述：某企业将其在国内采集的、被定义为“核心数据”的构图类数据，存储在了位于境外的云服务器上。
风险事件记录 (risk_events 表):
| 字段 | 值 | 备注 |
| :--- | :--- | :--- |
| event_id | EVT-C-20250826-002 | 事件唯一标识 |
| enterprise_id | ENT-D-004 | 所属企业D |
| vin | N/A | 云端操作 |
| event_type | RE-011 | 风险项编号：境外存储核心数据 |
| event_level | 特别重大 | 风险级别 |
| event_timestamp | 1756237800000 | 2025-08-26 14:30:00 |
| event_description | 企业将核心构图类数据存储于境外服务器 | 事件描述 |
| disposal_status | 已上报 | 处置状态 |
关联数据血缘溯源过程：
溯源日志 1: 存储阶段 (enterprise_processing_logs)
日志ID: LOG-C-STO-20250826-143000-ENT-D
核心内容: 记录了将“核心”数据存储在“境外”区域的操作。
1 {
2   "log_id": "LOG-C-STO-20250826-143000-ENT-D",
3   "enterprise_id": "ENT-D-004",
4   "processing_stage": "存储",
5   "stage_timestamp": 1756237800000,
6   "data_importance": "核心", // 风险点
7   "data_type_bitmap": "10000", // 构图类数据
8   "storage_area": "境外存储", // 风险点
9   "device_type": "云服务器",
10   "storage_security_bitmap": "1101", // 完整、真实、可用性保障，但无保密性
11   "operator_id": "user_admin_01"
12 }