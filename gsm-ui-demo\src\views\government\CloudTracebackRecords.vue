<template>
  <div class="min-h-screen bg-background">
    <div class="flex items-center justify-between p-6 border-b">
      <h1 class="text-2xl font-bold">{{ $route.meta.title }}</h1>
      <Button
        @click="saveTraceInfo"
        :disabled="!canSaveTrace"
        class="bg-emerald-600 hover:bg-emerald-700 text-white"
        >保存溯源信息</Button
      >
    </div>

    <div class="flex h-screen">
      <!-- 左侧：溯源记录历史 -->
      <div class="w-1/3 border-r bg-muted/10 p-6 overflow-y-auto">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <div class="w-2 h-2 bg-slate-600 rounded-full"></div>
              溯源记录历史
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TraceFilterForm
              :filter-fields="filterFields"
              :show-export="true"
              :initial-values="searchForm"
              @search="handleSearch"
              @reset="resetFilters"
              @export="exportCsv"
            />
            <div class="mt-4 space-y-2">
              <div
                v-for="(record, idx) in filteredRecords"
                :key="idx"
                class="p-3 rounded-lg border cursor-pointer hover:bg-accent/50 transition-colors"
                :class="{
                  'bg-accent': selectedRecord?.id === record.id,
                  'border-primary': selectedRecord?.id === record.id,
                }"
                @click="selectRecord(record)"
              >
                <div class="flex justify-between items-start mb-2">
                  <span class="font-medium text-sm">{{ record.eventId }}</span>
                  <Badge
                    :variant="
                      record.eventLevel === '特别重大'
                        ? 'destructive'
                        : record.eventLevel === '重大'
                          ? 'default'
                          : 'secondary'
                    "
                  >
                    {{ formatLevel(record.eventLevel) }}
                  </Badge>
                </div>
                <div class="text-xs text-muted-foreground space-y-1">
                  <div>
                    数据包ID: {{ record.dataPacketIds?.slice(0, 2).join(', ')
                    }}{{ record.dataPacketIds?.length > 2 ? '...' : '' }}
                  </div>
                  <div>企业: {{ record.enterprise }}</div>
                  <div>时间: {{ formatDate(record.eventTimestamp) }}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：溯源看板 -->
      <div class="w-2/3 p-6 overflow-y-auto">
        <div
          v-if="!selectedRecord && !externalEventData"
          class="h-full flex items-center justify-center text-muted-foreground"
        >
          <div class="text-center">
            <div class="text-4xl mb-4">
              <Cloud class="w-16 h-16 text-muted-foreground" />
            </div>
            <p>请从左侧选择一条溯源记录以开始分析</p>
          </div>
        </div>

        <div v-else class="space-y-6">
          <!-- 1. 云端风险基本信息卡片 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <div class="flex items-center justify-between">
                <CardTitle class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-slate-600 rounded-full"></div>
                  云端风险基本信息
                </CardTitle>
                <div class="flex items-center gap-2">
                  <Badge
                    v-if="tracingStarted && !tracing"
                    variant="secondary"
                    class="bg-emerald-600 text-white"
                    >溯源成功</Badge
                  >
                  <Button
                    v-else
                    @click="startTracing"
                    :disabled="tracing"
                    class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                  >
                    {{ tracing ? '溯源中...' : '开始溯源' }}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <h4 class="font-semibold mb-3">风险事件详情</h4>
                  <div class="space-y-2 text-sm">
                    <div>
                      <span class="text-muted-foreground">事件ID:</span>
                      {{ (selectedRecord || externalEventData)?.eventId }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">风险类型:</span>
                      {{ getRiskTypeLabel((selectedRecord || externalEventData)?.eventType) }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">风险级别:</span>
                      <Badge
                        :variant="
                          (selectedRecord || externalEventData)?.eventLevel === '特别重大'
                            ? 'destructive'
                            : 'default'
                        "
                      >
                        {{ formatLevel((selectedRecord || externalEventData)?.eventLevel) }}
                      </Badge>
                    </div>
                    <div>
                      <span class="text-muted-foreground">发生时间:</span>
                      {{ formatDate((selectedRecord || externalEventData)?.eventTimestamp) }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">处置状态:</span>
                      <Badge variant="outline">{{
                        (selectedRecord || externalEventData)?.disposalStatus
                      }}</Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-3">云端数据信息</h4>
                  <div class="space-y-2 text-sm">
                    <div>
                      <span class="text-muted-foreground">数据包ID列表:</span>
                      <div class="mt-1 max-h-20 overflow-y-auto">
                        <div
                          v-for="id in (selectedRecord || externalEventData)?.dataPacketIds || []"
                          :key="id"
                          class="text-xs font-mono bg-muted p-1 rounded mb-1"
                        >
                          {{ id }}
                        </div>
                      </div>
                    </div>
                    <div>
                      <span class="text-muted-foreground">所属企业:</span>
                      {{ (selectedRecord || externalEventData)?.enterprise }}
                    </div>
                    <div>
                      <span class="text-muted-foreground">数据重要性:</span>
                      <Badge variant="secondary">{{
                        (selectedRecord || externalEventData)?.dataImportance
                      }}</Badge>
                    </div>
                    <div>
                      <span class="text-muted-foreground">风险描述:</span>
                      <p class="mt-1 p-2 bg-muted rounded text-xs">
                        {{ (selectedRecord || externalEventData)?.eventDescription }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div v-if="!(tracing || tracingStarted)" class="pl-8 text-sm text-muted-foreground">
            提示：点击“开始溯源”执行溯源流程与分析
          </div>

          <!-- 2. 溯源图谱卡片 -->
          <Card v-show="tracing || tracingStarted" class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <div class="flex items-center justify-between">
                <CardTitle class="flex items-center gap-2">
                  <div class="w-3 h-3 bg-blue-700 rounded-full"></div>
                  溯源图谱
                </CardTitle>
                <Badge variant="outline" class="text-blue-700 border-blue-200 bg-blue-50"
                  >数据传播溯源流程</Badge
                >
              </div>
            </CardHeader>
            <CardContent>
              <LineageVisualization
                :data="lineageData"
                :height="540"
                theme="light"
                layout="linear"
              />
            </CardContent>
          </Card>

          <!-- 3. 风险溯源分析 -->
          <Card v-show="tracingStarted" class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <div class="w-3 h-3 bg-teal-700 rounded-full"></div>
                风险溯源分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-3 gap-6">
                <div>
                  <h4 class="font-semibold mb-3">数据包信息</h4>
                  <div class="space-y-2 text-sm">
                    <div
                      v-for="log in (selectedRecord || externalEventData)?.traceData || []"
                      :key="log.logId"
                      class="p-2 border rounded"
                    >
                      <div class="font-medium">{{ log.logId }}</div>
                      <div class="text-muted-foreground">阶段: {{ log.processingStage }}</div>
                      <div class="text-muted-foreground">
                        时间: {{ formatDate(log.stageTimestamp) }}
                      </div>
                      <div v-if="log.sourceLogId" class="text-muted-foreground text-xs">
                        关联: {{ log.sourceLogId }}
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-3">风险点描述</h4>
                  <div class="space-y-2">
                    <div
                      v-for="risk in (selectedRecord || externalEventData)?.riskPoints || []"
                      :key="risk.stage"
                      class="p-3 bg-destructive/10 border border-destructive/20 rounded"
                    >
                      <div class="font-medium text-destructive">{{ risk.stage }}</div>
                      <div class="text-sm mt-1">{{ risk.description }}</div>
                      <div class="text-xs text-muted-foreground mt-1">
                        风险字段: {{ risk.field }}
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 class="font-semibold mb-3">关联责任主体</h4>
                  <div class="space-y-2">
                    <Button variant="outline" size="sm" class="w-full justify-start">
                      {{ (selectedRecord || externalEventData)?.enterprise }}
                    </Button>
                    <div v-if="(selectedRecord || externalEventData)?.receiverId" class="space-y-1">
                      <Button variant="outline" size="sm" class="w-full justify-start text-xs">
                        第三方: {{ (selectedRecord || externalEventData)?.receiverId }}
                      </Button>
                    </div>
                    <div class="text-xs text-muted-foreground p-2">
                      <div>企业ID: {{ (selectedRecord || externalEventData)?.enterpriseId }}</div>
                      <div>责任类型: 云端数据处理与提供</div>
                      <div v-if="(selectedRecord || externalEventData)?.operatorId">
                        操作员: {{ (selectedRecord || externalEventData)?.operatorId }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 4. 溯源报告 -->
          <Card v-show="tracingStarted" class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <div class="w-3 h-3 bg-emerald-700 rounded-full"></div>
                溯源报告
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="bg-muted/50 rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                  <h4 class="font-semibold">云端风险溯源报告</h4>
                  <div class="space-x-2">
                    <Button variant="outline" size="sm">预览</Button>
                    <Button size="sm" class="bg-emerald-700 hover:bg-emerald-800">导出PDF</Button>
                  </div>
                </div>
                <div class="text-sm text-muted-foreground space-y-2">
                  <div>
                    • 风险事件: {{ (selectedRecord || externalEventData)?.eventDescription }}
                  </div>
                  <div>
                    • 涉及数据类型:
                    {{ ((selectedRecord || externalEventData)?.dataTypes || []).join(', ') }}
                  </div>
                  <div>
                    • 数据重要性: {{ (selectedRecord || externalEventData)?.dataImportance }}
                  </div>
                  <div>
                    • 溯源深度:
                    {{ ((selectedRecord || externalEventData)?.traceData || []).length }} 个处理阶段
                  </div>
                  <div>
                    • 风险等级: {{ formatLevel((selectedRecord || externalEventData)?.eventLevel) }}
                  </div>
                  <div>• 建议处置: 立即暂停数据提供服务，评估数据安全处理流程</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <!-- 全局模态 Loading -->
    <div
      v-if="tracing"
      class="fixed inset-0 z-[60] bg-black/40 backdrop-blur-sm flex items-center justify-center"
    >
      <div
        class="bg-white dark:bg-slate-900 border rounded-lg px-8 py-6 shadow-xl flex items-center gap-3"
      >
        <span
          class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></span>
        <span class="text-sm text-slate-700 dark:text-slate-200"
          >正在溯源中，第 {{ Math.max(1, traceStep) }} 步...</span
        >
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Cloud } from 'lucide-vue-next'
import TraceFilterForm, {
  type FilterField as TraceFilterField,
} from '@/components/ui/filter/TraceFilterForm.vue'
import LineageVisualization from '@/components/charts/LineageVisualization.vue'
import { getRiskTypeLabel } from '@/lib/riskTypeMapping'
import { buildCloudDemoTrace } from '@/lib/demoTraceTemplates'

const searchForm = ref({ eventId: '', enterprise: '', dataPacketId: '', dateRange: null })
const selectedRecord = ref<any>(null)

// 外部来源数据（当记录库中未找到时）
const route = useRoute()
const isExternalEntry = ref(false)
const externalEventData = ref<any>(null)

const initFromRoute = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const hasExternal = urlParams.has('external') || route.query.external !== undefined
  const eventId = (route.query.eventId as string) || urlParams.get('eventId') || ''
  if (!hasExternal && !eventId) return
  isExternalEntry.value = true
  // 每次外部进入都初始化“新会话”
  tracing.value = false
  tracingStarted.value = false
  traceStep.value = 0
  selectedRecord.value = null
  // 如果记录库能找到同 eventId，则以其字段为模板，但溯源数据留空
  const found = records.value.find((r) => r.eventId === eventId)
  const base: any = found || {}
  // 演示阶段：统一采用云端溯源模板，确保外部跳转也有完整溯源步骤
  const demo = buildCloudDemoTrace({
    dataPacketIds: base.dataPacketIds,
    dataTypes: base.dataTypes,
    dataImportance: base.dataImportance,
    operatorId: base.operatorId,
    receiverId: base.receiverId,
    vin: (route.query.vin as string) || undefined,
  })
  externalEventData.value = {
    eventId: eventId || base.eventId || `EVT-C-${Date.now()}`,
    enterprise: (route.query.enterprise as string) || base.enterprise || '示例企业',
    enterpriseId: base.enterpriseId || 'UNKNOWN',
    eventType: (route.query.eventType as string) || base.eventType || '0x10',
    eventLevel: (route.query.eventLevel as string) || base.eventLevel || '重大',
    eventTimestamp: Date.now(),
    eventDescription: (route.query.desc as string) || base.eventDescription || '外部跳转的风险事件',
    disposalStatus: base.disposalStatus || '待处置',
    dataImportance: demo.dataImportance,
    dataTypes: demo.dataTypes,
    dataPacketIds: demo.dataPacketIds,
    operatorId: demo.operatorId,
    receiverId: demo.receiverId,
    traceData: demo.traceData,
    riskPoints: demo.riskPoints,
  }
}

const formatLevel = (level: string | undefined) => {
  if (!level) return ''
  if (level === '特别重大') return '高'
  if (level === '重大') return '中'
  return level
}
const tracingStarted = ref(false)
const tracing = ref(false)
const traceStep = ref(0)
let traceTimer: number | null = null

// 可保存条件：外部进入 + 已完成溯源 + 有来源数据
const canSaveTrace = computed(() => {
  return (
    isExternalEntry.value &&
    tracingStarted.value &&
    !!(externalEventData.value || selectedRecord.value)
  )
})

// 开始溯源（逐步显示 + 全局加载态 + 成功提示）
const startTracing = async () => {
  if (!selectedRecord.value && !externalEventData.value) return
  tracing.value = true
  tracingStarted.value = false
  traceStep.value = 0
  const steps = (selectedRecord.value || externalEventData.value).traceData?.length || 0
  const interval = 800
  traceTimer && clearInterval(traceTimer)
  traceTimer = window.setInterval(() => {
    traceStep.value++
    if (traceStep.value >= steps) {
      traceTimer && clearInterval(traceTimer)
      tracing.value = false
      tracingStarted.value = true
    }
  }, interval)
}

// 筛选表单配置
const filterFields: TraceFilterField[] = [
  {
    key: 'eventId',
    label: '事件ID',
    type: 'input',
    placeholder: '事件ID',
  },
  {
    key: 'enterprise',
    label: '企业名称',
    type: 'input',
    placeholder: '企业名称',
  },
  {
    key: 'dataPacketId',
    label: '数据包ID',
    type: 'input',
    placeholder: '数据包ID',
  },
  {
    key: 'dateRange',
    label: '时间范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

// 数据血缘图谱数据处理
const lineageData = computed(() => {
  const currentRecord = selectedRecord.value || externalEventData.value
  if (!currentRecord?.traceData) return []
  const full = currentRecord.traceData.map((trace: any, index: number) => ({
    id: `node-${index}`,
    name: trace.processingStage,
    stage: trace.processingStage,
    timestamp: trace.stageTimestamp,
    isRisk: trace.isRisk || false,
    riskDescription: trace.riskDescription,
    logId: trace.logId,
    details: {
      数据重要性: trace.dataImportance || '未知',
      操作员ID: trace.operatorId || '系统自动',
      存储区域: trace.storageArea || '本地',
      备份状态: trace.backupStatus || '正常',
      安全处理标识: trace.securityProcessingFlag || '已处理',
      关联日志: trace.sourceLogId || '无',
    },
  }))
  if (tracing.value) {
    return full.slice(0, Math.max(1, traceStep.value))
  }
  if (tracingStarted.value) return full
  return []
})

// 模拟云端风险数据（基于设计文档要求，专注于两种风险类型）
const records = ref([
  // 云端风险1：数据未经安全处理直接提供数据
  {
    id: 1,
    eventId: 'EVT-C-20250826-001',
    enterpriseId: '91110000000000001A', // 18位统一社会信用代码
    eventType: '0x10', // 数据泄露/丢失
    eventLevel: '特别重大',
    eventTimestamp: 1756232400000,
    eventDescription: '云端风险1：数据未经安全处理直接提供数据',
    disposalStatus: '待处置',
    enterprise: '北京某地图服务科技有限公司',
    dataImportance: '重要',
    dataTypes: ['点云数据', '影像数据', '轨迹数据'],
    dataPacketIds: [
      'a1b2c3d4-e5f6-7890-abcd-ef1234567890', // UUID格式数据包ID
      'b2c3d4e5-f6g7-8901-bcde-f23456789012',
      'c3d4e5f6-g7h8-9012-cdef-************',
    ],
    receiverId: '91310000000000002B', // 第三方接收方企业代码
    operatorId: 'ops_user_001',
    traceData: [
      {
        logId: 'LOG-C-COL-20250826-123000-ENT-A',
        processingStage: '数据收集',
        stageTimestamp: 1756230600000,
        isRisk: false,
        dataPacketId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        dataImportance: 0x02, // 重要数据
        dataSourceType: 0x02, // 本企业量产车
        dataSourceId: 'LSVAP2A17PXXXXXXXX', // 车端VIN
        dataUsage: 0x02, // 导航电子地图制作
        operatorIdentity: 0x01, // 重要数据操作人员
        riskDescription: null,
      },
      {
        logId: 'LOG-C-STO-20250826-124000-ENT-A',
        processingStage: '数据存储',
        stageTimestamp: 1756231200000,
        isRisk: false,
        dataPacketId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        dataImportance: 0x02,
        storageDeviceType: 0x01, // 服务器
        storageAreaFlag: 0x01, // 境内存储
        storageDeviceIP: [
          0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 10, 0, 1, 100,
        ],
        storageZoneType: 0x01, // 原始数据处理区
        dataPartitionFlag: 0x01, // 已分区存储
        storageSecurityBitmap: 0x07, // 完整性+真实性+可用性
        desensitizationFlag: 0x02, // 未脱敏
        operatorIdentity: 0x01,
        riskDescription: null,
      },
      {
        logId: 'LOG-C-PRO-20250826-130000-ENT-A',
        processingStage: '数据提供',
        stageTimestamp: 1756232400000,
        sourceLogId: 'LOG-C-STO-20250826-124000-ENT-A',
        isRisk: true,
        dataPacketIds: [
          'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
          'b2c3d4e5-f6g7-8901-bcde-f23456789012',
        ],
        providedPacketCount: 2,
        receiverType: 0x01, // 境内非外商投资企业
        receiverSecurityCapacity: 0x02, // 安全能力不足
        exemptionCondition: 0x02, // 不满足豁免条件
        securityProcessingTech: 0x0000, // 未采用任何安全处理技术
        contractAgreementStatus: 0x02, // 有合同协议
        operatorIdentity: 0x01,
        riskDescription: '向第三方提供重要数据，但未采用任何安全处理技术，且接收方安全能力不足',
      },
    ],
    riskPoints: [
      {
        stage: '数据提供阶段',
        description: '未采用任何安全处理技术直接提供重要数据',
        field: 'securityProcessingTech',
      },
      {
        stage: '数据提供阶段',
        description: '接收方安全能力不足但仍提供数据',
        field: 'receiverSecurityCapacity',
      },
    ],
  },
  // 云端风险1：数据未经安全处理直接提供数据（另一实例）
  {
    id: 2,
    eventId: 'EVT-C-20250826-002',
    enterpriseId: '91310000000000003C',
    eventType: '0x10',
    eventLevel: '重大',
    eventTimestamp: 1756237800000,
    eventDescription: '云端风险1：数据未经安全处理直接提供数据',
    disposalStatus: '处置中',
    enterprise: '上海某智慧交通数据服务公司',
    dataImportance: '重要',
    dataTypes: ['轨迹数据', '构图类数据'],
    dataPacketIds: ['d4e5f6g7-h8i9-0123-defg-456789012345', 'e5f6g7h8-i9j0-1234-efgh-567890123456'],
    receiverId: '91440000000000004D',
    operatorId: 'ops_user_002',
    traceData: [
      {
        logId: 'LOG-C-PRO-20250826-143000-ENT-C',
        processingStage: '数据提供',
        stageTimestamp: 1756237800000,
        isRisk: true,
        dataPacketIds: ['d4e5f6g7-h8i9-0123-defg-456789012345'],
        providedPacketCount: 1,
        receiverType: 0x02, // 境内外商投资企业
        receiverSecurityCapacity: 0x01, // 安全能力满足
        exemptionCondition: 0x02, // 不满足豁免条件
        securityProcessingTech: 0x0000, // 未采用安全处理技术
        contractAgreementStatus: 0x02, // 有合同协议
        operatorIdentity: 0x02, // 一般数据操作人员（权限不匹配）
        riskDescription: '一般权限操作人员提供重要数据，且未采用安全处理技术',
      },
    ],
    riskPoints: [
      {
        stage: '数据提供阶段',
        description: '一般权限操作人员处理重要数据',
        field: 'operatorIdentity',
      },
      {
        stage: '数据提供阶段',
        description: '未采用安全处理技术直接提供数据',
        field: 'securityProcessingTech',
      },
    ],
  },
  // 云端事件1：数据泄露/丢失
  {
    id: 3,
    eventId: 'EVT-C-********-003',
    enterpriseId: '91440000000000005E',
    eventType: '0x10', // 数据泄露/丢失
    eventLevel: '特别重大',
    eventTimestamp: 1756320000000,
    eventDescription: '云端事件1：数据泄露/丢失',
    disposalStatus: '待处置',
    enterprise: '深圳某车联网平台运营公司',
    dataImportance: '核心',
    dataTypes: ['轨迹数据', '影像数据', '点云数据'],
    dataPacketIds: ['f6g7h8i9-j0k1-2345-fghi-678901234567', 'g7h8i9j0-k1l2-3456-ghij-************'],
    operatorId: 'sys_admin_001',
    traceData: [
      {
        logId: 'LOG-C-LEK-********-100000-ENT-E',
        processingStage: '数据存储',
        stageTimestamp: 1756320000000,
        isRisk: true,
        eventOccurTime: [25, 8, 27, 10, 0, 0], // BCD码：YY-MM-DD-hh-mm-ss
        dataPacketIds: [
          'f6g7h8i9-j0k1-2345-fghi-678901234567',
          'g7h8i9j0-k1l2-3456-ghij-************',
        ],
        occurStage: 0x02, // 存储阶段
        leakLossReason: 0x02, // 网络攻击
        affectedDataSize: '2.5GB',
        riskDescription: '存储系统遭受网络攻击，导致核心数据泄露',
      },
      {
        logId: 'LOG-C-ACC-********-100001-ENT-E',
        processingStage: '访问控制',
        stageTimestamp: *************,
        isRisk: true,
        eventOccurTime: [25, 8, 27, 10, 0, 1],
        unauthorizedAccount: 'hacker_001',
        sourceIP: [
          0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 203, 0, 113, 50,
        ],
        violationDescription: '恶意用户通过系统漏洞获取未授权访问权限，访问核心数据',
        riskDescription: '检测到未授权访问核心数据的行为',
      },
    ],
    riskPoints: [
      {
        stage: '数据存储阶段',
        description: '存储系统安全防护不足，遭受网络攻击',
        field: 'leakLossReason',
      },
      {
        stage: '访问控制阶段',
        description: '访问控制机制存在漏洞，允许未授权访问',
        field: 'unauthorizedAccount',
      },
    ],
  },
  // 云端事件1：数据泄露/丢失（另一实例）
  {
    id: 4,
    eventId: 'EVT-C-********-004',
    enterpriseId: '91510000000000006F',
    eventType: '0x10',
    eventLevel: '重大',
    eventTimestamp: *************,
    eventDescription: '云端事件1：数据泄露/丢失',
    disposalStatus: '已处置',
    enterprise: '成都某智能出行数据公司',
    dataImportance: '重要',
    dataTypes: ['轨迹数据'],
    dataPacketIds: ['h8i9j0k1-l2m3-4567-hijk-************'],
    operatorId: 'ops_internal_003',
    traceData: [
      {
        logId: 'LOG-C-LEK-********-120000-ENT-F',
        processingStage: '数据传输',
        stageTimestamp: *************,
        isRisk: true,
        eventOccurTime: [25, 8, 28, 12, 0, 0],
        dataPacketIds: ['h8i9j0k1-l2m3-4567-hijk-************'],
        occurStage: 0x04, // 传输阶段
        leakLossReason: 0x03, // 内部人员违规
        internalViolationAccount: 'ops_internal_003',
        violationDescription: '内部操作员违规将数据传输至个人邮箱',
        riskDescription: '内部操作员违规操作导致数据泄露',
      },
    ],
    riskPoints: [
      {
        stage: '数据传输阶段',
        description: '内部人员违规操作，将数据传输至非授权地址',
        field: 'leakLossReason',
      },
    ],
  },
])

const filteredRecords = computed(() => {
  return records.value.filter((record) => {
    return (
      (!searchForm.value.eventId || record.eventId.includes(searchForm.value.eventId)) &&
      (!searchForm.value.enterprise || record.enterprise.includes(searchForm.value.enterprise)) &&
      (!searchForm.value.dataPacketId ||
        record.dataPacketIds.some((id) => id.includes(searchForm.value.dataPacketId)))
    )
  })
})

const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const fetchRecords = () => {
  console.log('刷新云端溯源记录')
}

const handleSearch = (searchFilters: Record<string, any>) => {
  Object.assign(searchForm.value, searchFilters)
  console.log('搜索条件:', searchForm.value)
}

const resetFilters = () => {
  searchForm.value = { eventId: '', enterprise: '', dataPacketId: '', dateRange: null }
}

const saveTraceInfo = () => {
  if (!canSaveTrace.value) return
  const source: any = externalEventData.value || selectedRecord.value
  if (!source) return
  const newRecord: any = Object.assign(
    {},
    {
      id: Date.now(),
      eventId: source.eventId,
      enterpriseId: source.enterpriseId || 'UNKNOWN',
      eventType: source.eventType || '0x10',
      eventLevel: source.eventLevel || '重大',
      eventTimestamp: source.eventTimestamp || Date.now(),
      eventDescription: source.eventDescription || '外部来源风险事件',
      disposalStatus: source.disposalStatus || '待处置',
      enterprise: source.enterprise || '未知企业',
      dataImportance: source.dataImportance || '一般',
      dataTypes: source.dataTypes || [],
      dataPacketIds: source.dataPacketIds || ['external-packet-1'],
      traceData: source.traceData || [],
      riskPoints: source.riskPoints || [],
    },
  )
  ;(records.value as any).unshift(newRecord as any)
  selectedRecord.value = newRecord
  isExternalEntry.value = false
  console.log('保存溯源信息完成:', selectedRecord.value?.eventId)
}

const exportCsv = () => {
  const headers = [
    '事件ID',
    '企业名称',
    '数据包数量',
    '发生时间',
    '风险级别',
    '处置状态',
    '数据重要性',
  ]
  const rows = filteredRecords.value.map((r) => [
    r.eventId,
    r.enterprise,
    r.dataPacketIds.length,
    formatDate(r.eventTimestamp),
    r.eventLevel,
    r.disposalStatus,
    r.dataImportance,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `云端溯源记录_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const selectRecord = (record: any) => {
  selectedRecord.value = record
  console.log('选择记录:', record)
  // 选择历史记录时，直接展示所有信息
}

onMounted(() => {
  fetchRecords()
  initFromRoute()
})

// 监听路由变化，支持同页多次外部跳转生成新会话
watch(
  () => route.fullPath,
  () => {
    initFromRoute()
  },
)

onUnmounted(() => {
  if (traceTimer) clearInterval(traceTimer)
})
</script>
