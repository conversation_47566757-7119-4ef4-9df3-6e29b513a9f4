﻿表名: log_veh_collect_detail,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id"""
3,positioning_status,定位状态,SMALLINT,,○,,协议中的定位状态 (BYTE)。
4,longitude,经度,BIGINT,,○,,"""WGS84  乘以10^6 (定点数)。使用BIGINT存储协议DWORD。"""
5,latitude,纬度,BIGINT,,○,,"""WGS84  乘以10^6 (定点数)。使用BIGINT存储协议DWORD。"""
6,altitude,高度,BIGINT,,○,,"""以米为单位的值乘以100 (定点数)。使用BIGINT存储协议DWORD。"""
7,data_type_bitmap,采集数据类型位图,BIGINT,,○,,协议中的DWORD BitMap。
8,business_type,业务形式,SMALLINT,,○,,"""(BYTE) 0x01:导航电子地图更新服务; 0x02:研发测试; 0x03:众源更新; 0x04:其他"""
9,security_measures_bitmap,安全处理技术,INTEGER,,○,,"""(WORD) BitMap。bit0: 地理围栏技术; bit1: 位置数据保密处理技术; bit2: 属性脱敏技术; bit3: 里程限制技术。"""
10,security_measure_types,安全处理技术类型,SMALLINT,,○,,"""(BYTE) BitMap。bit0: 自研... (预留）"""
11,map_approval_number,地图审图号,VARCHAR,32,,,如果涉及车载地图更新或使用则填写。
