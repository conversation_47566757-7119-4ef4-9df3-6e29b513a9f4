﻿表名: log_ent_transfer_detail,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,log_id,日志ID,VARCHAR,64,○,,"""关联 log_main.log_id"""
3,data_importance_level,数据重要程度,SMALLINT,,○,,"""(BYTE) 0x01:一般; 0x02:重要"""
4,target_enterprise_code,传输目的地(信用代码),VARCHAR,18,○,,接收方企业的18位统一社会信用代码
5,communication_channel_type,通信通道类型,SMALLINT,,○,,"""(BYTE) 0x01:国密通道; 0x02:非国密通道"""
6,public_network_usage_flag,公共网络使用标识,SMALLINT,,○,,"""(BYTE) 0x01:使用公共信息网络; 0x02:不使用"""
7,transmission_method,传输方式,SMALLINT,,○,,"""(BYTE) 0x01:网络传输; 0x02:硬盘拷贝; 0x03:其他"""
8,network_type,通信网络类型,SMALLINT,,○,,"""(BYTE) 0x01:公共网络; 0x02:专用网络; 0x03:国家认定网络; 0x04:其他"""
9,security_protocol,安全传输协议,SMALLINT,,○,,"""(BYTE) 0x01:HTTP; 0x02:HTTPS; 0x03:国密通道; 0x04:其他"""
10,hdd_transfer_record_status,硬盘传输记录,SMALLINT,,,,"""(BYTE) 0x01:记录完整; 0x02:记录缺失 (仅当传输方式为硬盘拷贝时有效)"""
11,hdd_security_measures_status,硬盘安全措施,SMALLINT,,,,"""(BYTE) 0x01:安全措施合规; 0x02:不合规 (仅当传输方式为硬盘拷贝时有效)"""
12,transmission_safeguard_bitmap,传输保障标识,SMALLINT,,○,,"""(BYTE) BitMap。Bit0:完整性  Bit1:真实性  Bit2:可用性"""
13,operator_identity,操作员身份,SMALLINT,,○,,"""(BYTE) 0x01:重要数据操作人员; 0x02:一般数据操作人员"""
