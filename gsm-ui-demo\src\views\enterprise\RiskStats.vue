<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          企业车端与云端安全风险统计分析，支持风险等级分布、处置状态跟踪与详情查看
        </p>
      </div>
    </div>

    <!-- Tab 切换 -->
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="vehicle" class="flex items-center gap-2">
          <Car class="w-4 h-4" />
          车端风险
        </TabsTrigger>
        <TabsTrigger value="cloud" class="flex items-center gap-2">
          <Cloud class="w-4 h-4" />
          云端风险
        </TabsTrigger>
      </TabsList>

      <!-- 车端风险 Tab -->
      <TabsContent value="vehicle" class="space-y-6">
        <!-- 统计卡片区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 总风险数 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">总风险数</CardTitle>
              <AlertTriangle class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">{{ vehicleRiskItems.length }}</div>
              <p class="text-xs text-muted-foreground">累计风险记录</p>
            </CardContent>
          </Card>

          <!-- 待处理风险 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">待处理风险</CardTitle>
              <AlertCircle class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-orange-600">
                {{ vehicleRiskItems.filter((r) => r.status === 1).length }}
              </div>
              <p class="text-xs text-muted-foreground">需要立即处理</p>
            </CardContent>
          </Card>

          <!-- 高风险事件 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">高风险事件</CardTitle>
              <AlertTriangle class="h-8 w-8 text-destructive" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-red-600">
                {{ vehicleRiskItems.filter((r) => r.riskLevel === 1).length }}
              </div>
              <p class="text-xs text-muted-foreground">高危险等级</p>
            </CardContent>
          </Card>

          <!-- 处置率 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">风险处置率</CardTitle>
              <CheckCircle2 class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-green-600">{{ vehicleHandleRate }}%</div>
              <p class="text-xs text-muted-foreground">已处置/总风险</p>
            </CardContent>
          </Card>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 风险类别分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">风险类别分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <PieChart
                  :data="vehicleRiskCategoryData"
                  :height="200"
                  chart-type="doughnut"
                  :show-legend="true"
                  color-scheme="oceanDepths"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 风险等级分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">风险等级分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <BarChart
                  :data="vehicleRiskLevelData"
                  :height="200"
                  :show-values="true"
                  color-scheme="oceanDepths"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 处置状态分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">处置状态分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <PieChart
                  :data="vehicleStatusData"
                  :height="200"
                  :show-percentages="true"
                  color-scheme="primary"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 风险列表 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>车端风险列表</span>
              <Badge variant="outline">共 {{ filteredVehicleRisks.length }} 条记录</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <!-- 筛选条件 -->
            <CompactFilterForm
              :filter-fields="vehicleFilterFields"
              :show-export="true"
              :initial-values="vehicleFilters"
              @search="handleVehicleSearch"
              @reset="resetVehicleFilters"
              @export="exportVehicleData"
            />

            <div class="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[60px]">序号</TableHead>
                    <TableHead>风险ID</TableHead>
                    <TableHead>关联日志</TableHead>
                    <TableHead>风险类别</TableHead>
                    <TableHead>风险等级</TableHead>
                    <TableHead>风险描述</TableHead>
                    <TableHead>处置状态</TableHead>
                    <TableHead>发生时间</TableHead>
                    <TableHead>处置人</TableHead>
                    <TableHead class="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="pagedVehicleRisks.length === 0">
                    <TableCell :colspan="10" class="h-24 text-center text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                  <TableRow
                    v-for="(item, index) in pagedVehicleRisks"
                    :key="item.id"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{
                      (vehicleCurrentPage - 1) * vehiclePageSize + index + 1
                    }}</TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.riskId }}</span>
                    </TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.logId }}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{{ getRiskCategoryLabel(item.riskCategory) }}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="getRiskLevelVariant(item.riskLevel)">
                        {{ getRiskLevelLabel(item.riskLevel) }}
                      </Badge>
                    </TableCell>
                    <TableCell class="max-w-[200px]">
                      <div class="text-sm line-clamp-2" :title="item.riskDescription">
                        {{ item.riskDescription }}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="getStatusVariant(item.status)">
                        {{ getStatusLabel(item.status) }}
                      </Badge>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">
                      {{ formatTimestamp(item.eventTimestamp) }}
                    </TableCell>
                    <TableCell>{{ item.handlerId || '-' }}</TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" @click="viewRiskDetail(item)">
                        详情
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <!-- 分页（统一使用 shadcn-vue Pagination） -->
            <div class="flex items-center justify-between mt-4">
              <div class="text-sm text-muted-foreground">
                显示第 {{ (vehicleCurrentPage - 1) * vehiclePageSize + 1 }} -
                {{ Math.min(vehicleCurrentPage * vehiclePageSize, filteredVehicleRisks.length) }}
                条， 共 {{ filteredVehicleRisks.length }} 条记录
              </div>
              <div class="flex items-center gap-4">
                <div class="pagination-size-control">
                  <span>每页显示</span>
                  <Select
                    :model-value="vehiclePageSize.toString()"
                    @update:model-value="
                      (v: string) => {
                        const n = +v
                        if (!Number.isNaN(n) && n > 0) {
                          vehiclePageSize = n
                          vehicleCurrentPage = 1
                        }
                      }
                    "
                  >
                    <SelectTrigger class="w-20"><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span>条</span>
                </div>
                <Pagination
                  v-model:page="vehicleCurrentPage"
                  :total="filteredVehicleRisks.length"
                  :items-per-page="vehiclePageSize"
                  :sibling-count="1"
                  :show-edges="true"
                >
                  <PaginationContent v-slot="{ items }">
                    <PaginationFirst />
                    <PaginationPrevious />
                    <template v-for="(item, idx) in items" :key="idx">
                      <PaginationItem
                        v-if="item.type === 'page'"
                        :value="item.value"
                        :is-active="item.value === vehicleCurrentPage"
                      />
                      <PaginationEllipsis v-else />
                    </template>
                    <PaginationNext />
                    <PaginationLast />
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 云端风险 Tab -->
      <TabsContent value="cloud" class="space-y-6">
        <!-- 统计卡片区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 总风险数 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">总风险数</CardTitle>
              <AlertTriangle class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold">{{ cloudRiskItems.length }}</div>
              <p class="text-xs text-muted-foreground">累计风险记录</p>
            </CardContent>
          </Card>

          <!-- 待处理风险 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">待处理风险</CardTitle>
              <AlertCircle class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-orange-600">
                {{ cloudRiskItems.filter((r) => r.status === 1).length }}
              </div>
              <p class="text-xs text-muted-foreground">需要立即处理</p>
            </CardContent>
          </Card>

          <!-- 高风险事件 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">高风险事件</CardTitle>
              <AlertTriangle class="h-8 w-8 text-destructive" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-red-600">
                {{ cloudRiskItems.filter((r) => r.riskLevel === 1).length }}
              </div>
              <p class="text-xs text-muted-foreground">高危险等级</p>
            </CardContent>
          </Card>

          <!-- 处置率 -->
          <Card>
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle class="text-base font-semibold">风险处置率</CardTitle>
              <CheckCircle2 class="h-8 w-8 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div class="text-2xl font-bold text-green-600">{{ cloudHandleRate }}%</div>
              <p class="text-xs text-muted-foreground">已处置/总风险</p>
            </CardContent>
          </Card>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 风险类别分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">风险类别分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <PieChart
                  :data="cloudRiskCategoryData"
                  :height="200"
                  chart-type="doughnut"
                  :show-legend="true"
                  color-scheme="oceanDepths"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 风险等级分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">风险等级分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <BarChart
                  :data="cloudRiskLevelData"
                  :height="200"
                  :show-values="true"
                  color-scheme="oceanDepths"
                />
              </div>
            </CardContent>
          </Card>

          <!-- 处置状态分布 -->
          <Card>
            <CardHeader>
              <CardTitle class="text-lg font-semibold">处置状态分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="h-[200px]">
                <PieChart
                  :data="cloudStatusData"
                  :height="200"
                  :show-percentages="true"
                  color-scheme="primary"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 风险列表 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <span>云端风险列表</span>
              <Badge variant="outline">共 {{ filteredCloudRisks.length }} 条记录</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <!-- 筛选条件 -->
            <CompactFilterForm
              :filter-fields="cloudFilterFields"
              :show-export="true"
              :initial-values="cloudFilters"
              @search="handleCloudSearch"
              @reset="resetCloudFilters"
              @export="exportCloudData"
            />

            <div class="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[60px]">序号</TableHead>
                    <TableHead>风险ID</TableHead>
                    <TableHead>关联日志</TableHead>
                    <TableHead>风险类别</TableHead>
                    <TableHead>风险等级</TableHead>
                    <TableHead>风险描述</TableHead>
                    <TableHead>处置状态</TableHead>
                    <TableHead>发生时间</TableHead>
                    <TableHead>处置人</TableHead>
                    <TableHead class="w-[100px]">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-if="pagedCloudRisks.length === 0">
                    <TableCell :colspan="10" class="h-24 text-center text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                  <TableRow
                    v-for="(item, index) in pagedCloudRisks"
                    :key="item.id"
                    class="hover:bg-muted/40"
                  >
                    <TableCell>{{ (cloudCurrentPage - 1) * cloudPageSize + index + 1 }}</TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.riskId }}</span>
                    </TableCell>
                    <TableCell>
                      <span class="font-mono text-sm">{{ item.logId }}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{{ getRiskCategoryLabel(item.riskCategory) }}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="getRiskLevelVariant(item.riskLevel)">
                        {{ getRiskLevelLabel(item.riskLevel) }}
                      </Badge>
                    </TableCell>
                    <TableCell class="max-w-[200px]">
                      <div class="text-sm line-clamp-2" :title="item.riskDescription">
                        {{ item.riskDescription }}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge :variant="getStatusVariant(item.status)">
                        {{ getStatusLabel(item.status) }}
                      </Badge>
                    </TableCell>
                    <TableCell class="whitespace-nowrap">
                      {{ formatTimestamp(item.eventTimestamp) }}
                    </TableCell>
                    <TableCell>{{ item.handlerId || '-' }}</TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline" @click="viewRiskDetail(item)">
                        详情
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <!-- 分页（统一使用 shadcn-vue Pagination） -->
            <div class="flex items-center justify-between mt-4">
              <div class="text-sm text-muted-foreground">
                显示第 {{ (cloudCurrentPage - 1) * cloudPageSize + 1 }} -
                {{ Math.min(cloudCurrentPage * cloudPageSize, filteredCloudRisks.length) }} 条， 共
                {{ filteredCloudRisks.length }} 条记录
              </div>
              <div class="flex items-center gap-4">
                <div class="pagination-size-control">
                  <span>每页显示</span>
                  <Select
                    :model-value="cloudPageSize.toString()"
                    @update:model-value="
                      (v: string) => {
                        const n = +v
                        if (!Number.isNaN(n) && n > 0) {
                          cloudPageSize = n
                          cloudCurrentPage = 1
                        }
                      }
                    "
                  >
                    <SelectTrigger class="w-20"><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span>条</span>
                </div>
                <Pagination
                  v-model:page="cloudCurrentPage"
                  :total="filteredCloudRisks.length"
                  :items-per-page="cloudPageSize"
                  :sibling-count="1"
                  :show-edges="true"
                >
                  <PaginationContent v-slot="{ items }">
                    <PaginationFirst />
                    <PaginationPrevious />
                    <template v-for="(item, idx) in items" :key="idx">
                      <PaginationItem
                        v-if="item.type === 'page'"
                        :value="item.value"
                        :is-active="item.value === cloudCurrentPage"
                      />
                      <PaginationEllipsis v-else />
                    </template>
                    <PaginationNext />
                    <PaginationLast />
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>

    <!-- 风险详情弹窗 -->
    <Dialog v-model:open="detailOpen">
      <DialogContent class="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>风险详细信息</DialogTitle>
          <DialogDescription> 风险ID: {{ selectedRisk?.riskId }} </DialogDescription>
        </DialogHeader>

        <div v-if="selectedRisk" class="space-y-4">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-muted-foreground">风险ID</div>
              <div class="font-medium">{{ selectedRisk.riskId }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">关联日志ID</div>
              <div class="font-medium">{{ selectedRisk.logId }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">风险类别</div>
              <div class="font-medium">{{ getRiskCategoryLabel(selectedRisk.riskCategory) }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">风险等级</div>
              <div class="font-medium">{{ getRiskLevelLabel(selectedRisk.riskLevel) }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">处置状态</div>
              <div class="font-medium">{{ getStatusLabel(selectedRisk.status) }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">发生时间</div>
              <div class="font-medium">{{ formatTimestamp(selectedRisk.eventTimestamp) }}</div>
            </div>
          </div>

          <!-- 位置信息 -->
          <div v-if="selectedRisk.eventLongitude && selectedRisk.eventLatitude">
            <div class="text-sm text-muted-foreground">风险位置</div>
            <div class="font-medium">
              经度: {{ selectedRisk.eventLongitude / 1000000 }}°, 纬度:
              {{ selectedRisk.eventLatitude / 1000000 }}°
            </div>
          </div>

          <!-- 风险描述 -->
          <div>
            <div class="text-sm text-muted-foreground">风险描述</div>
            <div class="mt-1 p-3 bg-muted rounded-md">
              {{ selectedRisk.riskDescription }}
            </div>
          </div>

          <!-- 处置信息 -->
          <div v-if="selectedRisk.handlerId" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-muted-foreground">处置人</div>
              <div class="font-medium">{{ selectedRisk.handlerId }}</div>
            </div>
            <div>
              <div class="text-sm text-muted-foreground">处置时间</div>
              <div class="font-medium">{{ selectedRisk.handleTime || '-' }}</div>
            </div>
          </div>

          <div class="flex items-center justify-end gap-2">
            <Button variant="outline" @click="detailOpen = false">关闭</Button>
            <Button v-if="selectedRisk.status === 1" @click="handleRisk"> 处置风险 </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { AlertTriangle, Car, CheckCircle2, Cloud, AlertCircle } from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PieChart, BarChart } from '@/components/charts'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

// 企业风险统计数据类型定义
interface RiskRecord {
  id: number
  riskId: string
  logId: string
  riskCategory: number // 1-违规采集, 2-非法传输, 3-数据泄露, 4-访问异常
  riskLevel: number // 1-高, 2-中, 3-低
  eventTimestamp: number
  eventLongitude?: number
  eventLatitude?: number
  riskDescription: string
  status: number // 1-待处置, 2-处置中, 3-已关闭
  handlerId?: string
  handleTime?: string
}

// 活动Tab
const activeTab = ref('vehicle')

// 弹窗状态
const detailOpen = ref(false)
const selectedRisk = ref<RiskRecord | null>(null)

// 筛选条件
const vehicleFilters = ref({
  riskId: '',
  logId: '',
  category: 0,
  level: 0,
  status: 0,
  timeRange: null as [Date, Date] | null,
})

const cloudFilters = ref({
  riskId: '',
  logId: '',
  category: 0,
  level: 0,
  status: 0,
  timeRange: null as [Date, Date] | null,
})

// 筛选表单配置
const vehicleFilterFields: FilterField[] = [
  {
    key: 'riskId',
    label: '风险ID',
    type: 'input',
    placeholder: '输入风险ID',
  },
  {
    key: 'logId',
    label: '日志ID',
    type: 'input',
    placeholder: '输入日志ID',
  },
  {
    key: 'category',
    label: '风险类别',
    type: 'select',
    placeholder: '选择类别',
    options: [
      { label: '全部', value: '0' },
      { label: '违规采集', value: '1' },
      { label: '非法传输', value: '2' },
      { label: '数据泄露', value: '3' },
      { label: '访问异常', value: '4' },
    ],
  },
  {
    key: 'level',
    label: '风险等级',
    type: 'select',
    placeholder: '选择等级',
    options: [
      { label: '全部', value: '0' },
      { label: '高', value: '1' },
      { label: '中', value: '2' },
      { label: '低', value: '3' },
    ],
  },
  {
    key: 'status',
    label: '处置状态',
    type: 'select',
    placeholder: '选择状态',
    options: [
      { label: '全部', value: '0' },
      { label: '待处置', value: '1' },
      { label: '处置中', value: '2' },
      { label: '已关闭', value: '3' },
    ],
  },
  {
    key: 'timeRange',
    label: '发生时间',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

const cloudFilterFields = vehicleFilterFields

// 生成Mock数据
const generateMockRisks = (count: number, type: 'vehicle' | 'cloud'): RiskRecord[] => {
  const risks: RiskRecord[] = []
  const now = Date.now()

  const riskCategories = [1, 2, 3, 4]
  const riskLevels = [1, 2, 3]
  const statuses = [1, 2, 3]

  const riskDescriptions = {
    1: [
      '车辆在敏感区域进行高频数据采集，疑似违规收集地理信息',
      '未经授权在未报备测试/采集区进行地图数据采集',
      '采集频率异常，超出正常导航需求',
      '在禁止测绘区域启动了高精度定位采集',
    ],
    2: [
      '检测到未加密的地理数据传输',
      '数据传输目标IP地址异常，疑似境外服务器',
      '大量敏感地理坐标数据通过不安全通道传输',
      '传输数据包含未脱敏的原始坐标信息',
    ],
    3: [
      '存储系统访问控制配置不当，敏感数据可能被泄露',
      '检测到异常数据导出行为，大量地理信息被下载',
      '数据库备份文件权限设置错误，存在泄露风险',
      '日志文件中包含未脱敏的位置信息',
    ],
    4: [
      '检测到异常访问模式，可能存在数据窃取行为',
      '多次失败的身份验证尝试，疑似暴力破解',
      '非工作时间的大量数据访问请求',
      '来自未知IP的异常访问行为',
    ],
  }

  for (let i = 1; i <= count; i++) {
    const category = riskCategories[Math.floor(Math.random() * riskCategories.length)]
    const level = Math.random() < 0.2 ? 1 : Math.random() < 0.5 ? 2 : 3
    const status = Math.random() < 0.3 ? 1 : Math.random() < 0.6 ? 2 : 3
    const descriptions = riskDescriptions[category as keyof typeof riskDescriptions]

    const risk: RiskRecord = {
      id: i,
      riskId: `${type === 'vehicle' ? 'VR' : 'CR'}-2025-${String(i).padStart(4, '0')}`,
      logId: `LOG-2025-${String(Math.floor(Math.random() * 9999)).padStart(4, '0')}`,
      riskCategory: category,
      riskLevel: level,
      eventTimestamp: now - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000),
      eventLongitude: 116000000 + Math.floor(Math.random() * 1000000),
      eventLatitude: 39000000 + Math.floor(Math.random() * 1000000),
      riskDescription: descriptions[Math.floor(Math.random() * descriptions.length)],
      status: status,
      handlerId: status !== 1 ? `USER-${Math.floor(Math.random() * 100)}` : undefined,
      handleTime:
        status === 3
          ? new Date(now - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString()
          : undefined,
    }

    risks.push(risk)
  }

  return risks
}

// Mock数据
const vehicleRiskItems = ref<RiskRecord[]>(generateMockRisks(35, 'vehicle'))
const cloudRiskItems = ref<RiskRecord[]>(generateMockRisks(40, 'cloud'))

// 车端统计数据
const vehicleRiskCategoryData = computed(() => {
  const categoryMap: Record<number, string> = {
    1: '违规采集',
    2: '非法传输',
    3: '数据泄露',
    4: '访问异常',
  }
  const counts: Record<string, number> = {}

  vehicleRiskItems.value.forEach((item) => {
    const label = categoryMap[item.riskCategory]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const vehicleRiskLevelData = computed(() => {
  const levelMap: Record<number, string> = {
    1: '高风险',
    2: '中风险',
    3: '低风险',
  }
  const counts: Record<string, number> = {}

  vehicleRiskItems.value.forEach((item) => {
    const label = levelMap[item.riskLevel]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const vehicleStatusData = computed(() => {
  const statusMap: Record<number, string> = {
    1: '待处置',
    2: '处置中',
    3: '已关闭',
  }
  const counts: Record<string, number> = {}

  vehicleRiskItems.value.forEach((item) => {
    const label = statusMap[item.status]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const vehicleHandleRate = computed(() => {
  const total = vehicleRiskItems.value.length
  const handled = vehicleRiskItems.value.filter((r) => r.status === 3).length
  return total === 0 ? 0 : Math.round((handled / total) * 100)
})

// 云端统计数据
const cloudRiskCategoryData = computed(() => {
  const categoryMap: Record<number, string> = {
    1: '违规采集',
    2: '非法传输',
    3: '数据泄露',
    4: '访问异常',
  }
  const counts: Record<string, number> = {}

  cloudRiskItems.value.forEach((item) => {
    const label = categoryMap[item.riskCategory]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const cloudRiskLevelData = computed(() => {
  const levelMap: Record<number, string> = {
    1: '高风险',
    2: '中风险',
    3: '低风险',
  }
  const counts: Record<string, number> = {}

  cloudRiskItems.value.forEach((item) => {
    const label = levelMap[item.riskLevel]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const cloudStatusData = computed(() => {
  const statusMap: Record<number, string> = {
    1: '待处置',
    2: '处置中',
    3: '已关闭',
  }
  const counts: Record<string, number> = {}

  cloudRiskItems.value.forEach((item) => {
    const label = statusMap[item.status]
    counts[label] = (counts[label] || 0) + 1
  })

  return Object.entries(counts).map(([name, value]) => ({ name, value }))
})

const cloudHandleRate = computed(() => {
  const total = cloudRiskItems.value.length
  const handled = cloudRiskItems.value.filter((r) => r.status === 3).length
  return total === 0 ? 0 : Math.round((handled / total) * 100)
})

// 车端过滤与分页
const filteredVehicleRisks = computed(() => {
  const { riskId, logId, category, level, status, timeRange } = vehicleFilters.value
  return vehicleRiskItems.value.filter((r) => {
    if (riskId && !r.riskId.includes(riskId)) return false
    if (logId && !r.logId.includes(logId)) return false
    if (category && r.riskCategory !== category) return false
    if (level && r.riskLevel !== level) return false
    if (status && r.status !== status) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = timeRange[0].getTime()
      const end = timeRange[1].getTime() + 24 * 60 * 60 * 1000 - 1
      if (r.eventTimestamp < start || r.eventTimestamp > end) return false
    }
    return true
  })
})

const vehicleCurrentPage = ref(1)
const vehiclePageSize = ref(10)
const vehicleTotalPages = computed(() =>
  Math.max(1, Math.ceil(filteredVehicleRisks.value.length / vehiclePageSize.value)),
)
const pagedVehicleRisks = computed(() => {
  const start = (vehicleCurrentPage.value - 1) * vehiclePageSize.value
  return filteredVehicleRisks.value.slice(start, start + vehiclePageSize.value)
})

// 云端过滤与分页
const filteredCloudRisks = computed(() => {
  const { riskId, logId, category, level, status, timeRange } = cloudFilters.value
  return cloudRiskItems.value.filter((r) => {
    if (riskId && !r.riskId.includes(riskId)) return false
    if (logId && !r.logId.includes(logId)) return false
    if (category && r.riskCategory !== category) return false
    if (level && r.riskLevel !== level) return false
    if (status && r.status !== status) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = timeRange[0].getTime()
      const end = timeRange[1].getTime() + 24 * 60 * 60 * 1000 - 1
      if (r.eventTimestamp < start || r.eventTimestamp > end) return false
    }
    return true
  })
})

const cloudCurrentPage = ref(1)
const cloudPageSize = ref(10)
const cloudTotalPages = computed(() =>
  Math.max(1, Math.ceil(filteredCloudRisks.value.length / cloudPageSize.value)),
)
const pagedCloudRisks = computed(() => {
  const start = (cloudCurrentPage.value - 1) * cloudPageSize.value
  return filteredCloudRisks.value.slice(start, start + cloudPageSize.value)
})

// 事件处理函数
const handleVehicleSearch = () => {
  vehicleCurrentPage.value = 1
  console.log('车端搜索条件:', vehicleFilters.value)
}

const resetVehicleFilters = () => {
  vehicleFilters.value = {
    riskId: '',
    logId: '',
    category: 0,
    level: 0,
    status: 0,
    timeRange: null,
  }
  vehicleCurrentPage.value = 1
}

const handleCloudSearch = () => {
  cloudCurrentPage.value = 1
  console.log('云端搜索条件:', cloudFilters.value)
}

const resetCloudFilters = () => {
  cloudFilters.value = {
    riskId: '',
    logId: '',
    category: 0,
    level: 0,
    status: 0,
    timeRange: null,
  }
  cloudCurrentPage.value = 1
}

const viewRiskDetail = (risk: RiskRecord) => {
  selectedRisk.value = risk
  detailOpen.value = true
}

const handleRisk = () => {
  if (selectedRisk.value) {
    selectedRisk.value.status = 2
    detailOpen.value = false
  }
}

const exportVehicleData = () => {
  const headers = [
    '序号',
    '风险ID',
    '日志ID',
    '风险类别',
    '风险等级',
    '风险描述',
    '处置状态',
    '发生时间',
    '处置人',
  ]
  const rows = filteredVehicleRisks.value.map((r, index) => [
    (index + 1).toString(),
    r.riskId,
    r.logId,
    getRiskCategoryLabel(r.riskCategory),
    getRiskLevelLabel(r.riskLevel),
    r.riskDescription.replace(/\n/g, ' '),
    getStatusLabel(r.status),
    formatTimestamp(r.eventTimestamp),
    r.handlerId || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  downloadCsv(content, `企业端车端风险统计_${new Date().toISOString().slice(0, 10)}.csv`)
}

const exportCloudData = () => {
  const headers = [
    '序号',
    '风险ID',
    '日志ID',
    '风险类别',
    '风险等级',
    '风险描述',
    '处置状态',
    '发生时间',
    '处置人',
  ]
  const rows = filteredCloudRisks.value.map((r, index) => [
    (index + 1).toString(),
    r.riskId,
    r.logId,
    getRiskCategoryLabel(r.riskCategory),
    getRiskLevelLabel(r.riskLevel),
    r.riskDescription.replace(/\n/g, ' '),
    getStatusLabel(r.status),
    formatTimestamp(r.eventTimestamp),
    r.handlerId || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  downloadCsv(content, `企业端云端风险统计_${new Date().toISOString().slice(0, 10)}.csv`)
}

// 工具函数
const formatTimestamp = (ts: number) => {
  const date = new Date(ts)
  return date.toLocaleString('zh-CN')
}

const getRiskCategoryLabel = (category: number) => {
  const map: Record<number, string> = {
    1: '违规采集',
    2: '非法传输',
    3: '数据泄露',
    4: '访问异常',
  }
  return map[category] || '未知'
}

const getRiskLevelLabel = (level: number) => {
  const map: Record<number, string> = {
    1: '高',
    2: '中',
    3: '低',
  }
  return map[level] || '未知'
}

const getStatusLabel = (status: number) => {
  const map: Record<number, string> = {
    1: '待处置',
    2: '处置中',
    3: '已关闭',
  }
  return map[status] || '未知'
}

const getRiskLevelVariant = (level: number) => {
  switch (level) {
    case 1:
      return 'destructive'
    case 2:
      return 'default'
    case 3:
      return 'secondary'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: number) => {
  switch (status) {
    case 1:
      return 'outline'
    case 2:
      return 'secondary'
    case 3:
      return 'default'
    default:
      return 'outline'
  }
}

const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}

const downloadCsv = (content: string, filename: string) => {
  const blob = new Blob([`\ufeff${content}`], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.click()
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
