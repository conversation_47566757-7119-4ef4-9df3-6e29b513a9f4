<template>
  <div class="password-reset-layout">
    <div class="reset-container">
      <Card class="mx-auto max-w-md">
        <CardHeader>
          <CardTitle>找回密码</CardTitle>
          <CardDescription>通过验证身份信息重置您的密码</CardDescription>
        </CardHeader>
        <CardContent>
          <!-- 步骤指示器 -->
          <div class="steps-indicator">
            <div class="step" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
              <div class="step-number">1</div>
              <span class="step-label">验证身份</span>
            </div>
            <div class="step-divider"></div>
            <div class="step" :class="{ active: currentStep === 2, completed: currentStep > 2 }">
              <div class="step-number">2</div>
              <span class="step-label">重置密码</span>
            </div>
            <div class="step-divider"></div>
            <div class="step" :class="{ active: currentStep === 3, completed: currentStep > 3 }">
              <div class="step-number">3</div>
              <span class="step-label">完成</span>
            </div>
          </div>

          <!-- 步骤1：验证身份 -->
          <div v-if="currentStep === 1" class="step-content">
            <form @submit.prevent="verifyIdentity" class="space-y-4">
              <div class="verification-tabs">
                <div class="tab-buttons">
                  <button
                    type="button"
                    v-for="tab in verificationTabs"
                    :key="tab.key"
                    @click="selectedTab = tab.key"
                    :class="{ active: selectedTab === tab.key }"
                    class="tab-button"
                  >
                    {{ tab.label }}
                  </button>
                </div>

                <!-- 企业邮箱验证 -->
                <div v-if="selectedTab === 'email'" class="tab-content">
                  <div class="space-y-2">
                    <Label for="email">企业邮箱</Label>
                    <Input
                      id="email"
                      v-model="verificationData.email"
                      type="email"
                      placeholder="请输入企业邮箱"
                      :class="{ 'border-destructive': errors.email }"
                    />
                    <p v-if="errors.email" class="text-sm text-destructive">
                      {{ errors.email }}
                    </p>
                  </div>
                </div>

                <!-- 手机号验证 -->
                <div v-if="selectedTab === 'phone'" class="tab-content">
                  <div class="space-y-2">
                    <Label for="phone">手机号码</Label>
                    <Input
                      id="phone"
                      v-model="verificationData.phone"
                      placeholder="请输入注册手机号"
                      maxlength="11"
                      :class="{ 'border-destructive': errors.phone }"
                    />
                    <p v-if="errors.phone" class="text-sm text-destructive">
                      {{ errors.phone }}
                    </p>
                  </div>
                  <div class="space-y-2">
                    <Label for="phoneCode">验证码</Label>
                    <div class="flex gap-2">
                      <Input
                        id="phoneCode"
                        v-model="verificationData.phoneCode"
                        placeholder="请输入验证码"
                        maxlength="6"
                        class="flex-1"
                        :class="{ 'border-destructive': errors.phoneCode }"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        @click="sendPhoneCode"
                        :disabled="phoneSms.sending || phoneSms.countdown > 0"
                      >
                        {{ getPhoneSmsText() }}
                      </Button>
                    </div>
                    <p v-if="errors.phoneCode" class="text-sm text-destructive">
                      {{ errors.phoneCode }}
                    </p>
                  </div>
                </div>

                <!-- 身份证验证 -->
                <div v-if="selectedTab === 'idcard'" class="tab-content">
                  <div class="space-y-2">
                    <Label for="realName">真实姓名</Label>
                    <Input
                      id="realName"
                      v-model="verificationData.realName"
                      placeholder="请输入真实姓名"
                      :class="{ 'border-destructive': errors.realName }"
                    />
                    <p v-if="errors.realName" class="text-sm text-destructive">
                      {{ errors.realName }}
                    </p>
                  </div>
                  <div class="space-y-2">
                    <Label for="idCard">身份证号码</Label>
                    <Input
                      id="idCard"
                      v-model="verificationData.idCard"
                      placeholder="请输入18位身份证号码"
                      maxlength="18"
                      :class="{ 'border-destructive': errors.idCard }"
                    />
                    <p v-if="errors.idCard" class="text-sm text-destructive">
                      {{ errors.idCard }}
                    </p>
                  </div>
                </div>
              </div>

              <Button type="submit" class="w-full" :disabled="isLoading">
                {{ isLoading ? '验证中...' : '验证身份' }}
              </Button>
            </form>
          </div>

          <!-- 步骤2：重置密码 -->
          <div v-if="currentStep === 2" class="step-content">
            <form @submit.prevent="resetPassword" class="space-y-4">
              <div class="space-y-2">
                <Label for="newPassword">新密码</Label>
                <Input
                  id="newPassword"
                  v-model="passwordData.newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  :class="{ 'border-destructive': errors.newPassword }"
                />
                <p v-if="errors.newPassword" class="text-sm text-destructive">
                  {{ errors.newPassword }}
                </p>
              </div>

              <div class="space-y-2">
                <Label for="confirmPassword">确认密码</Label>
                <Input
                  id="confirmPassword"
                  v-model="passwordData.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  :class="{ 'border-destructive': errors.confirmPassword }"
                />
                <p v-if="errors.confirmPassword" class="text-sm text-destructive">
                  {{ errors.confirmPassword }}
                </p>
              </div>

              <div class="password-tips">
                <p class="text-sm text-muted-foreground">密码要求：</p>
                <ul class="text-sm text-muted-foreground">
                  <li>• 长度8-20位</li>
                  <li>• 包含字母和数字</li>
                  <li>• 建议包含特殊字符</li>
                </ul>
              </div>

              <div class="flex gap-2">
                <Button type="button" variant="outline" @click="currentStep = 1" class="flex-1">
                  上一步
                </Button>
                <Button type="submit" class="flex-1" :disabled="isLoading">
                  {{ isLoading ? '重置中...' : '重置密码' }}
                </Button>
              </div>
            </form>
          </div>

          <!-- 步骤3：完成 -->
          <div v-if="currentStep === 3" class="step-content">
            <div class="success-content">
              <div class="success-icon">
                <svg
                  class="w-16 h-16 text-green-500 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-lg font-medium text-center text-foreground">密码重置成功</h3>
              <p class="text-sm text-muted-foreground text-center">
                您的密码已成功重置，请使用新密码登录系统
              </p>

              <div class="flex gap-2 mt-6">
                <Button @click="goToLogin" class="flex-1"> 立即登录 </Button>
                <Button variant="outline" @click="resetForm" class="flex-1"> 重新操作 </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

const router = useRouter()

// 当前步骤
const currentStep = ref(1)

// 验证方式选项
const verificationTabs = [
  { key: 'email', label: '企业邮箱' },
  { key: 'phone', label: '手机验证' },
  { key: 'idcard', label: '身份证' },
]

const selectedTab = ref('email')

// 表单数据 - 预填充演示数据
const verificationData = reactive({
  email: '<EMAIL>',
  phone: '13800138000',
  phoneCode: '123456',
  realName: '张三',
  idCard: '110101199001011234',
})

const passwordData = reactive({
  newPassword: 'NewPass123!',
  confirmPassword: 'NewPass123!',
})

// 表单错误
const errors = reactive({
  email: '',
  phone: '',
  phoneCode: '',
  realName: '',
  idCard: '',
  newPassword: '',
  confirmPassword: '',
})

// 手机验证码状态
const phoneSms = reactive({
  sending: false,
  countdown: 0,
  sent: false,
})

const isLoading = ref(false)

// 发送手机验证码
const sendPhoneCode = async () => {
  if (!/^1[3-9]\d{9}$/.test(verificationData.phone)) {
    errors.phone = '请输入正确的手机号码'
    return
  }

  phoneSms.sending = true
  try {
    await new Promise((resolve) => setTimeout(resolve, 800))
    phoneSms.sent = true
    phoneSms.countdown = 60

    const timer = setInterval(() => {
      phoneSms.countdown--
      if (phoneSms.countdown <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } finally {
    phoneSms.sending = false
  }
}

const getPhoneSmsText = (): string => {
  if (phoneSms.sending) return '发送中...'
  if (phoneSms.countdown > 0) return `${phoneSms.countdown}s`
  return phoneSms.sent ? '重新发送' : '发送验证码'
}

// 验证身份
const verifyIdentity = async () => {
  // 清空错误
  Object.keys(errors).forEach((key) => {
    errors[key as keyof typeof errors] = ''
  })

  // 简单验证
  let hasError = false

  if (selectedTab.value === 'email') {
    if (!verificationData.email || !/\S+@\S+\.\S+/.test(verificationData.email)) {
      errors.email = '请输入正确的邮箱地址'
      hasError = true
    }
  } else if (selectedTab.value === 'phone') {
    if (!verificationData.phone || !/^1[3-9]\d{9}$/.test(verificationData.phone)) {
      errors.phone = '请输入正确的手机号码'
      hasError = true
    }
    if (!verificationData.phoneCode || !/^\d{6}$/.test(verificationData.phoneCode)) {
      errors.phoneCode = '请输入6位验证码'
      hasError = true
    }
  } else if (selectedTab.value === 'idcard') {
    if (!verificationData.realName || !/^[\u4e00-\u9fa5]{2,10}$/.test(verificationData.realName)) {
      errors.realName = '请输入正确的姓名'
      hasError = true
    }
    if (
      !verificationData.idCard ||
      !/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(
        verificationData.idCard,
      )
    ) {
      errors.idCard = '请输入正确的身份证号码'
      hasError = true
    }
  }

  if (hasError) return

  isLoading.value = true
  try {
    // 模拟验证
    await new Promise((resolve) => setTimeout(resolve, 1500))
    currentStep.value = 2
  } catch (error) {
    console.error('验证失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 重置密码
const resetPassword = async () => {
  // 清空错误
  Object.keys(errors).forEach((key) => {
    errors[key as keyof typeof errors] = ''
  })

  let hasError = false

  if (!passwordData.newPassword || passwordData.newPassword.length < 8) {
    errors.newPassword = '密码长度至少8位'
    hasError = true
  }

  if (passwordData.newPassword !== passwordData.confirmPassword) {
    errors.confirmPassword = '两次密码输入不一致'
    hasError = true
  }

  if (hasError) return

  isLoading.value = true
  try {
    // 模拟重置
    await new Promise((resolve) => setTimeout(resolve, 2000))
    currentStep.value = 3
  } catch (error) {
    console.error('重置失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 跳转登录
const goToLogin = () => {
  router.push({ name: 'Login' })
}

// 重置表单
const resetForm = () => {
  currentStep.value = 1
  selectedTab.value = 'email'
  Object.keys(errors).forEach((key) => {
    errors[key as keyof typeof errors] = ''
  })
}
</script>

<style scoped>
.password-reset-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  padding: 1rem;
}

.reset-container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.steps-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  padding: 1rem 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 500;
  background: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  transition: all 0.2s;
}

.step.active .step-number {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.step.completed .step-number {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.step-label {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

.step.active .step-label {
  color: hsl(var(--foreground));
  font-weight: 500;
}

.step-divider {
  width: 3rem;
  height: 2px;
  background: hsl(var(--border));
  margin: 0 1rem;
}

.step-content {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.verification-tabs {
  margin-bottom: 1.5rem;
}

.tab-buttons {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
  background: hsl(var(--muted));
  border-radius: 6px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  color: hsl(var(--muted-foreground));
}

.tab-button.active {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-weight: 500;
}

.tab-content {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 0.5rem;
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 1rem;
}

.password-tips {
  padding: 1rem;
  background: hsl(var(--muted));
  border-radius: 6px;
  margin: 1rem 0;
}

.password-tips ul {
  margin-top: 0.5rem;
  margin-left: 0.5rem;
}

.success-content {
  text-align: center;
  padding: 1rem 0;
}

.success-icon {
  margin-bottom: 1rem;
}

.w-16 {
  width: 4rem;
}
.h-16 {
  height: 4rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.text-green-500 {
  color: #10b981;
}

@media (max-width: 640px) {
  .steps-indicator {
    padding: 0.5rem 0;
  }

  .step-divider {
    width: 2rem;
    margin: 0 0.5rem;
  }

  .step-label {
    font-size: 0.625rem;
  }
}
</style>
