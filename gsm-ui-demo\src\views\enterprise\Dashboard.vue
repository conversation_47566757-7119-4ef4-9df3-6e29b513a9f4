<template>
  <div class="p-6 space-y-6 overflow-x-hidden max-w-full">
    <!-- 页面标题和操作区 -->
    <div class="mb-6 flex items-start justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">数据安全合规管理平台 - 综合概览</p>
      </div>
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <RefreshCw class="h-4 w-4" />
          <span>数据最后更新: {{ lastUpdateTime }}</span>
        </div>
        <Button variant="outline" size="sm" @click="refreshData">
          <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': isRefreshing }" />
          刷新数据
        </Button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid gap-4 lg:grid-cols-3 min-w-0">
      <!-- 左侧栏：待办任务和通知信息 -->
      <div class="lg:col-span-1 space-y-4">
        <!-- 待办任务 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Clock class="h-6 w-6" />
                待办任务
              </div>
              <div class="flex items-center gap-2">
                <Badge variant="destructive" v-if="pendingTasks.length > 0">
                  {{ pendingTasks.length }}
                </Badge>
                <Button variant="ghost" size="sm" @click="router.push('/corp/notice/tasks')">
                  查看全部
                  <ChevronRight class="h-4 w-4 ml-1" />
                </Button>
              </div>
            </CardTitle>
            <CardDescription>需要你处理的任务和事项</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="pendingTasks.length === 0" class="text-center py-8 text-muted-foreground">
              暂无待办任务
            </div>
            <div v-else class="space-y-3">
              <div
                v-for="task in pendingTasks.slice(0, 5)"
                :key="task.id"
                class="flex items-start justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors cursor-pointer"
                @click="viewTaskDetail(task)"
              >
                <div class="flex-1 space-y-1">
                  <div class="flex items-center gap-2">
                    <Badge :variant="getTaskCategoryVariant(task.category)" class="text-xs">
                      {{ task.category }}
                    </Badge>
                    <Badge :variant="getTaskPriorityVariant(task.priority)" class="text-xs">
                      {{ task.priority }}
                    </Badge>
                  </div>
                  <p class="font-medium text-sm">{{ task.title }}</p>
                  <p class="text-xs text-muted-foreground line-clamp-2">{{ task.description }}</p>
                  <div class="flex items-center gap-3 text-xs text-muted-foreground">
                    <span>接收: {{ formatTime(task.receivedAt) }}</span>
                    <span class="text-orange-600">截止: {{ formatTime(task.deadline) }}</span>
                  </div>
                </div>
                <ChevronRight class="h-4 w-4 text-muted-foreground mt-1" />
              </div>
              <div v-if="pendingTasks.length > 5" class="text-center pt-2">
                <Button variant="link" size="sm" @click="router.push('/corp/notice/tasks')">
                  还有 {{ pendingTasks.length - 5 }} 个任务待处理
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 通知信息 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Bell class="h-6 w-6" />
                通知信息
              </div>
              <div class="flex items-center gap-2">
                <Badge v-if="unreadNotifications > 0" variant="destructive">
                  {{ unreadNotifications }}未读
                </Badge>
                <Button variant="ghost" size="sm" @click="router.push('/corp/notice/notices')">
                  查看全部
                  <ChevronRight class="h-4 w-4 ml-1" />
                </Button>
              </div>
            </CardTitle>
            <CardDescription>政策法规、公告通知等重要信息</CardDescription>
          </CardHeader>
          <CardContent>
            <!-- 置顶通知 -->
            <div v-if="pinnedNotices.length > 0" class="mb-3">
              <div class="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-1">
                <Pin class="h-3 w-3" />
                置顶通知
              </div>
              <div
                v-for="notice in pinnedNotices"
                :key="notice.id"
                class="p-3 rounded-lg border border-amber-200 bg-amber-50 dark:bg-amber-950/20 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-950/40 transition-colors cursor-pointer mb-2"
                @click="viewNoticeDetail(notice)"
              >
                <div class="flex items-start gap-2">
                  <component
                    :is="getNoticeIcon(notice.category)"
                    class="h-4 w-4 mt-0.5 text-amber-600"
                  />
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-1">
                      <Badge :variant="getNoticeCategoryVariant(notice.category)" class="text-xs">
                        {{ notice.category }}
                      </Badge>
                      <Badge v-if="notice.priority === '高'" variant="destructive" class="text-xs">
                        {{ notice.priority }}
                      </Badge>
                      <div
                        v-if="notice.status === '未读'"
                        class="w-2 h-2 bg-red-500 rounded-full"
                      ></div>
                    </div>
                    <p class="text-base font-semibold">{{ notice.title }}</p>
                    <p class="text-xs text-muted-foreground mt-1">
                      {{ notice.publisher }} · {{ formatTime(notice.publishAt) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 普通通知 -->
            <div class="space-y-2">
              <div
                v-if="normalNotices.length === 0 && pinnedNotices.length === 0"
                class="text-center py-8 text-muted-foreground"
              >
                暂无通知信息
              </div>
              <div
                v-for="notice in normalNotices.slice(0, 3)"
                :key="notice.id"
                class="p-3 rounded-lg border hover:bg-accent/50 transition-colors cursor-pointer"
                :class="{
                  'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800':
                    notice.status === '未读',
                }"
                @click="viewNoticeDetail(notice)"
              >
                <div class="flex items-start gap-2">
                  <component
                    :is="getNoticeIcon(notice.category)"
                    class="h-4 w-4 mt-0.5 text-muted-foreground"
                  />
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-1">
                      <Badge :variant="getNoticeCategoryVariant(notice.category)" class="text-xs">
                        {{ notice.category }}
                      </Badge>
                      <Badge v-if="notice.priority === '高'" variant="destructive" class="text-xs">
                        {{ notice.priority }}
                      </Badge>
                      <div
                        v-if="notice.status === '未读'"
                        class="w-2 h-2 bg-red-500 rounded-full"
                      ></div>
                    </div>
                    <p
                      class="text-base font-semibold"
                      :class="{ 'text-blue-900 dark:text-blue-100': notice.status === '未读' }"
                    >
                      {{ notice.title }}
                    </p>
                    <p class="text-xs text-muted-foreground line-clamp-2 mt-1">
                      {{ notice.summary }}
                    </p>
                    <p class="text-xs text-muted-foreground mt-1">
                      {{ notice.publisher }} · {{ formatTime(notice.publishAt) }}
                    </p>
                  </div>
                </div>
              </div>
              <div v-if="normalNotices.length > 3" class="text-center pt-2">
                <Button variant="link" size="sm" @click="router.push('/corp/notice/notices')">
                  查看更多通知
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧栏：统计和图表 -->
      <div class="lg:col-span-2 space-y-4 min-w-0">
        <!-- 统计卡片区域 -->
        <div class="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          <Card v-for="stat in statistics" :key="stat.title">
            <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1">
              <CardTitle class="text-sm font-semibold">{{ stat.title }}</CardTitle>
              <component :is="stat.icon" class="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent class="pt-0">
              <div class="text-xl font-semibold">{{ stat.value }}</div>
              <p class="text-xs text-muted-foreground">
                <span :class="getTrendClass(stat.trend)">
                  {{ stat.trend > 0 ? '↑' : stat.trend < 0 ? '↓' : '→' }}
                  {{ Math.abs(stat.trend) }}%
                </span>
                {{ stat.description }}
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 图表展示区域 - 使用Tabs组织 -->
        <Tabs default-value="overview" class="space-y-4">
          <TabsList class="grid w-full grid-cols-4">
            <TabsTrigger value="overview">数据概览</TabsTrigger>
            <TabsTrigger value="trend">趋势分析</TabsTrigger>
            <TabsTrigger value="risk">风险分布</TabsTrigger>
            <TabsTrigger value="activity">业务活动</TabsTrigger>
          </TabsList>

          <!-- 概览标签页 -->
          <TabsContent value="overview" class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2 min-w-0">
              <!-- 业务分布饼图 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">业务类型分布</CardTitle>
                  <CardDescription>各类数据处理业务占比统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart
                    :data="businessDistribution"
                    :height="300"
                    :show-legend="true"
                    :show-percentages="true"
                    color-scheme="enterprise"
                  />
                </CardContent>
              </Card>

              <!-- 风险等级分布 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">风险等级分布</CardTitle>
                  <CardDescription>当前各级别风险事件统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    :data="riskLevelData"
                    :height="300"
                    :show-values="true"
                    color-scheme="oceanDepths"
                    direction="vertical"
                  />
                </CardContent>
              </Card>
            </div>

            <!-- 月度合规率趋势 -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg font-semibold">月度合规率趋势</CardTitle>
                <CardDescription>过去12个月合规率变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <LineChart
                  :series="complianceTrendData"
                  :height="350"
                  :smooth="true"
                  :show-area="true"
                  color-scheme="primary"
                  y-axis-name="合规率(%)"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 趋势分析标签页 -->
          <TabsContent value="trend" class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2 min-w-0">
              <!-- 数据上报趋势 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">数据上报趋势</CardTitle>
                  <CardDescription>30天数据上报量变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart
                    :series="dataSubmissionTrend"
                    :height="300"
                    :smooth="true"
                    color-scheme="primary"
                    y-axis-name="上报量"
                  />
                </CardContent>
              </Card>

              <!-- 事件处理效率 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">事件处理效率</CardTitle>
                  <CardDescription>平均处理时长趋势（小时）</CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart
                    :series="eventProcessingTrend"
                    :height="300"
                    :smooth="true"
                    color-scheme="enterprise"
                    y-axis-name="处理时长(h)"
                  />
                </CardContent>
              </Card>
            </div>

            <!-- 多维度对比分析 -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg font-semibold">多维度业务对比</CardTitle>
                <CardDescription>各业务线关键指标对比分析</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart
                  :series="multiDimensionComparison"
                  :height="350"
                  :show-legend="true"
                  color-scheme="primary"
                  y-axis-name="数值"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 风险分布标签页 -->
          <TabsContent value="risk" class="space-y-4">
            <div class="grid gap-4 lg:grid-cols-3 min-w-0">
              <!-- 风险类型分布 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">风险类型分布</CardTitle>
                  <CardDescription>各类风险占比</CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart
                    :data="riskTypeDistribution"
                    :height="250"
                    chart-type="doughnut"
                    :show-legend="true"
                    color-scheme="oceanDepths"
                  />
                </CardContent>
              </Card>

              <!-- 风险趋势 -->
              <Card class="lg:col-span-2">
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">风险事件趋势</CardTitle>
                  <CardDescription>近30天风险事件数量变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart
                    :series="riskTrendData"
                    :height="250"
                    :smooth="false"
                    color-scheme="oceanDepths"
                  />
                </CardContent>
              </Card>
            </div>

            <!-- 车辆型号风险分布 -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg font-semibold">车辆型号风险分布</CardTitle>
                <CardDescription>不同车辆型号的风险等级事件统计</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart
                  :series="vehicleModelRiskData"
                  :height="300"
                  :show-legend="true"
                  stack-type="normal"
                  color-scheme="oceanDepths"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 业务活动标签页 -->
          <TabsContent value="activity" class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2 min-w-0">
              <!-- 数据处理活动 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">数据处理活动统计</CardTitle>
                  <CardDescription>各类数据处理活动数量</CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    :data="dataProcessingActivities"
                    :height="300"
                    direction="horizontal"
                    :show-values="true"
                    color-scheme="enterprise"
                  />
                </CardContent>
              </Card>

              <!-- 审核进度 -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-lg font-semibold">审核进度分布</CardTitle>
                  <CardDescription>各阶段任务数量统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart
                    :data="auditProgressData"
                    :height="300"
                    chart-type="pie"
                    :show-legend="true"
                    color-scheme="stages"
                  />
                </CardContent>
              </Card>
            </div>

            <!-- 活动时间线 -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg font-semibold">近期业务活动时间线</CardTitle>
                <CardDescription>最近7天业务活动频率分布</CardDescription>
              </CardHeader>
              <CardContent>
                <LineChart
                  :series="activityTimelineData"
                  :height="300"
                  :show-area="true"
                  color-scheme="enterprise"
                  x-axis-name="时间"
                  y-axis-name="活动数量"
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
 
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import LineChart from '@/components/charts/LineChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import {
  AlertTriangle,
  Bell,
  CheckCircle2,
  ChevronRight,
  Info,
  RefreshCw,
  FileCheck,
  Upload,
  Clock,
  Pin,
  FileText,
} from 'lucide-vue-next'

const router = useRouter()
const isRefreshing = ref(false)
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))

// 任务类别
type TaskCategory = '注册反馈' | '风险提醒' | '风险处置' | '事件处置'
type Priority = '紧急' | '一般' | '不紧急'
type NotificationCategory = '法规' | '政策' | '公告' | '通用信息'
type ReadStatus = '未读' | '已读'

interface PendingTask {
  id: string
  category: TaskCategory
  title: string
  description: string
  priority: Priority
  receivedAt: string
  deadline: string
}

interface NotificationItem {
  id: string
  title: string
  category: NotificationCategory
  priority: '高' | '中' | '低'
  status: ReadStatus
  summary: string
  publisher: string
  publishAt: string
  isPinned: boolean
}

// 统计数据
const statistics = ref([
  {
    title: '数据上报任务',
    value: '125',
    trend: 12,
    description: '较上月',
    icon: Upload,
  },
  {
    title: '合规率',
    value: '98.5%',
    trend: 2.1,
    description: '较上月',
    icon: CheckCircle2,
  },
  {
    title: '待处理风险',
    value: '3',
    trend: -25,
    description: '较上月',
    icon: AlertTriangle,
  },
  {
    title: '本月审查',
    value: '15',
    trend: 0,
    description: '较上月',
    icon: FileCheck,
  },
])

// 业务分布数据
const businessDistribution = ref([
  { name: '数据汇聚及脱敏处理', value: 35 },
  { name: '导航电子地图制作', value: 28 },
  { name: '场景库制作及服务', value: 20 },
  { name: '互联网地图服务', value: 10 },
  { name: '其他', value: 7 },
])

// 风险等级数据
const riskLevelData = ref([
  { name: '高', value: 10 },
  { name: '中', value: 23 },
  { name: '低', value: 45 },
])

// 合规率趋势数据
const complianceTrendData = ref([
  {
    name: '合规率',
    data: [
      { name: '1月', value: 92 },
      { name: '2月', value: 93 },
      { name: '3月', value: 94 },
      { name: '4月', value: 93 },
      { name: '5月', value: 95 },
      { name: '6月', value: 96 },
      { name: '7月', value: 95 },
      { name: '8月', value: 97 },
      { name: '9月', value: 97 },
      { name: '10月', value: 98 },
      { name: '11月', value: 98 },
      { name: '12月', value: 98.5 },
    ],
  },
])

// 数据上报趋势
const dataSubmissionTrend = ref([
  {
    name: '日上报量',
    data: Array.from({ length: 30 }, (_, i) => ({
      name: `${i + 1}日`,
      value: Math.floor(Math.random() * 50) + 80,
    })),
  },
])

// 事件处理效率趋势
const eventProcessingTrend = ref([
  {
    name: '平均处理时长',
    data: Array.from({ length: 30 }, (_, i) => ({
      name: `${i + 1}日`,
      value: Math.floor(Math.random() * 8) + 2,
    })),
  },
])

// 多维度对比数据
const multiDimensionComparison = ref([
  {
    name: '数据采集',
    data: [
      { name: 'Q1', value: 120 },
      { name: 'Q2', value: 132 },
      { name: 'Q3', value: 145 },
      { name: 'Q4', value: 150 },
    ],
  },
  {
    name: '数据处理',
    data: [
      { name: 'Q1', value: 100 },
      { name: 'Q2', value: 108 },
      { name: 'Q3', value: 115 },
      { name: 'Q4', value: 125 },
    ],
  },
  {
    name: '数据存储',
    data: [
      { name: 'Q1', value: 80 },
      { name: 'Q2', value: 85 },
      { name: 'Q3', value: 88 },
      { name: 'Q4', value: 92 },
    ],
  },
])

// 风险类型分布
const riskTypeDistribution = ref([
  { name: '数据泄露', value: 15 },
  { name: '未授权访问', value: 23 },
  { name: '数据篡改', value: 8 },
  { name: '系统漏洞', value: 12 },
  { name: '合规风险', value: 18 },
])

// 风险趋势数据
const riskTrendData = ref([
  {
    name: '风险事件',
    data: Array.from({ length: 30 }, (_, i) => ({
      name: `${i + 1}日`,
      value: Math.floor(Math.random() * 10) + 2,
    })),
  },
])

// 车辆型号风险数据
const vehicleModelRiskData = ref([
  {
    name: '低风险',
    data: [
      { name: 'Model S', value: 15 },
      { name: 'Model 3', value: 22 },
      { name: 'Model X', value: 18 },
      { name: 'Model Y', value: 25 },
      { name: '其他型号', value: 12 },
    ],
  },
  {
    name: '中风险',
    data: [
      { name: 'Model S', value: 8 },
      { name: 'Model 3', value: 12 },
      { name: 'Model X', value: 6 },
      { name: 'Model Y', value: 10 },
      { name: '其他型号', value: 5 },
    ],
  },
  {
    name: '高风险',
    data: [
      { name: 'Model S', value: 2 },
      { name: 'Model 3', value: 3 },
      { name: 'Model X', value: 1 },
      { name: 'Model Y', value: 2 },
      { name: '其他型号', value: 1 },
    ],
  },
])

// 数据处理活动
const dataProcessingActivities = ref([
  { name: '数据采集', value: 450 },
  { name: '数据清洗', value: 380 },
  { name: '数据分析', value: 320 },
  { name: '数据存储', value: 280 },
  { name: '数据共享', value: 150 },
])

// 审核进度数据
const auditProgressData = ref([
  { name: '待审核', value: 25 },
  { name: '审核中', value: 15 },
  { name: '已通过', value: 85 },
  { name: '已驳回', value: 5 },
])

// 活动时间线数据
const activityTimelineData = ref([
  {
    name: '业务活动',
    data: Array.from({ length: 7 }, (_, i) => ({
      name: `${['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i]}`,
      value: Math.floor(Math.random() * 30) + 20,
    })),
  },
])

// 待办任务数据
const pendingTasks = ref<PendingTask[]>([
  {
    id: 'PT-2025-001',
    category: '注册反馈',
    title: '企业注册信息填报失败反馈',
    description:
      '您提交的企业注册信息存在问题，请重新填报相关信息。主要问题：测绘资质证书扫描件不清晰。',
    priority: '紧急',
    receivedAt: '2025-08-26 09:30',
    deadline: '2025-08-28 18:00',
  },
  {
    id: 'PT-2025-002',
    category: '风险提醒',
    title: '车端数据采集违规风险提醒',
    description: '检测到您企业车辆在敏感区域进行高频数据采集，存在违规风险，请及时处理。',
    priority: '紧急',
    receivedAt: '2025-08-26 14:15',
    deadline: '2025-08-27 18:00',
  },
  {
    id: 'PT-2025-003',
    category: '事件处置',
    title: '数据安全事件处置要求',
    description: '根据安全监测发现的数据异常传输事件，要求企业立即采取相应处置措施。',
    priority: '一般',
    receivedAt: '2025-08-25 16:30',
    deadline: '2025-08-30 18:00',
  },
])

// 通知信息数据
const notificationItems = ref<NotificationItem[]>([
  {
    id: 'N-2025-001',
    title: '关于加强智能网联汽车地理信息安全管理的通知',
    category: '法规',
    priority: '高',
    status: '未读',
    summary:
      '进一步规范智能网联汽车地理信息数据处理活动，切实维护国家地理信息安全，现就有关事项通知如下...',
    publisher: '国家测绘地理信息局',
    publishAt: '2025-08-26 10:00',
    isPinned: true,
  },
  {
    id: 'N-2025-002',
    title: '智能网联汽车数据安全管理若干规定（试行）',
    category: '政策',
    priority: '高',
    status: '未读',
    summary: '为了规范汽车数据处理活动，保护个人、组织的合法权益，维护国家安全和社会公共利益...',
    publisher: '国家互联网信息办公室',
    publishAt: '2025-08-25 15:30',
    isPinned: true,
  },
  {
    id: 'N-2025-003',
    title: '关于开展智能网联汽车地理信息安全专项检查的公告',
    category: '公告',
    priority: '中',
    status: '已读',
    summary: '为确保智能网联汽车地理信息安全，维护国家地理信息安全，决定开展专项检查工作...',
    publisher: '国家测绘地理信息局',
    publishAt: '2025-08-24 09:15',
    isPinned: false,
  },
  {
    id: 'N-2025-004',
    title: '智能网联汽车地图数据采集技术规范更新说明',
    category: '通用信息',
    priority: '低',
    status: '已读',
    summary: '为适应技术发展需要，对智能网联汽车地图数据采集技术规范进行更新...',
    publisher: '中国测绘学会',
    publishAt: '2025-08-23 14:20',
    isPinned: false,
  },
  {
    id: 'N-2025-005',
    title: '系统维护公告',
    category: '公告',
    priority: '低',
    status: '未读',
    summary: '系统将于今晚22:00-24:00进行维护升级，期间部分功能可能无法正常使用。',
    publisher: '系统管理员',
    publishAt: '2025-08-26 17:00',
    isPinned: false,
  },
])

// 置顶通知
const pinnedNotices = computed(() => notificationItems.value.filter((n) => n.isPinned))

// 普通通知
const normalNotices = computed(() => notificationItems.value.filter((n) => !n.isPinned))

// 未读通知数量
const unreadNotifications = computed(
  () => notificationItems.value.filter((n) => n.status === '未读').length,
)

// 获取趋势样式类
const getTrendClass = (trend: number) => {
  if (trend > 0) return 'text-green-600'
  if (trend < 0) return 'text-red-600'
  return 'text-gray-500'
}

// 获取任务类别颜色
const getTaskCategoryVariant = (category: TaskCategory) => {
  switch (category) {
    case '注册反馈':
      return 'default'
    case '风险提醒':
      return 'destructive'
    case '风险处置':
      return 'secondary'
    case '事件处置':
      return 'outline'
    default:
      return 'outline'
  }
}

// 获取任务紧急程度颜色
const getTaskPriorityVariant = (priority: Priority) => {
  switch (priority) {
    case '紧急':
      return 'destructive'
    case '一般':
      return 'default'
    case '不紧急':
      return 'secondary'
    default:
      return 'outline'
  }
}

// 获取通知类别颜色
const getNoticeCategoryVariant = (category: NotificationCategory) => {
  switch (category) {
    case '法规':
      return 'destructive'
    case '政策':
      return 'default'
    case '公告':
      return 'secondary'
    case '通用信息':
      return 'outline'
    default:
      return 'outline'
  }
}

// 获取通知图标
const getNoticeIcon = (category: NotificationCategory) => {
  switch (category) {
    case '法规':
      return FileText
    case '政策':
      return FileText
    case '公告':
      return Info
    case '通用信息':
      return Info
    default:
      return Info
  }
}

// 格式化时间
const formatTime = (time: string) => {
  const date = new Date(time.replace(/-/g, '/'))
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor(diff / (1000 * 60))

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

// 查看任务详情
const viewTaskDetail = (task: PendingTask) => {
  console.log('查看任务详情:', task)
  // TODO: 可以添加弹窗显示详情或跳转到任务管理页面
  router.push('/corp/notice/tasks')
}

// 查看通知详情
const viewNoticeDetail = (notice: NotificationItem) => {
  console.log('查看通知详情:', notice)
  // 标记为已读
  if (notice.status === '未读') {
    notice.status = '已读'
  }
  // TODO: 可以添加弹窗显示详情或跳转到通知列表页面
  router.push('/corp/notice/notices')
}

// 刷新数据
const refreshData = async () => {
  isRefreshing.value = true
  // 模拟数据刷新
  setTimeout(() => {
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    isRefreshing.value = false
    // 这里可以更新各种数据
  }, 2000)
}

onMounted(() => {
  console.log('企业端控制台已加载')
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
