name: PR Checks

on:
  pull_request:
    branches: [ main ]
    types: [opened, synchronize, reopened, ready_for_review]
  workflow_dispatch:

concurrency:
  group: pr-checks-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  quality:
    if: ${{ github.event_name != 'pull_request' || (github.event.pull_request.draft == false) }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    defaults:
      run:
        working-directory: gsm-ui-demo
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'gsm-ui-demo/package-lock.json'

      - name: Install dependencies (ignore scripts, with fallback registry and timeouts)
        run: |
          npm config set fetch-timeout 120000
          npm config set fetch-retry-maxtimeout 120000
          echo "Attempt 1: using default npm registry (ignore scripts)"
          npm config delete registry || true
          npm ci --no-audit --no-fund --ignore-scripts || (
            echo "Attempt 1 failed; switching to npmmirror and retrying (ignore scripts)" && \
            npm config set registry https://registry.npmmirror.com && \
            npm ci --no-audit --no-fund --prefer-offline --ignore-scripts
          )

      - name: Rebuild native addons needed for build
        run: |
          # Rebuild esbuild to fetch the correct platform binary since scripts were ignored
          npm rebuild esbuild --verbose || true

      - name: Lint (no fix)
        run: npm run lint:check

      - name: Format check (Prettier)
        run: npm run format:check

      - name: Type Check
        run: npm run type-check

      - name: Build (fail on errors)
        run: npm run build-only

  build-artifact:
    if: ${{ github.event_name != 'pull_request' || (github.event.pull_request.draft == false) }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    defaults:
      run:
        working-directory: gsm-ui-demo
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'gsm-ui-demo/package-lock.json'

      - name: Install dependencies (ignore scripts, with fallback registry and timeouts)
        run: |
          npm config set fetch-timeout 120000
          npm config set fetch-retry-maxtimeout 120000
          echo "Attempt 1: using default npm registry (ignore scripts)"
          npm config delete registry || true
          npm ci --no-audit --no-fund --ignore-scripts || (
            echo "Attempt 1 failed; switching to npmmirror and retrying (ignore scripts)" && \
            npm config set registry https://registry.npmmirror.com && \
            npm ci --no-audit --no-fund --prefer-offline --ignore-scripts
          )

      - name: Rebuild native addons needed for build
        run: |
          # Rebuild esbuild to fetch the correct platform binary since scripts were ignored
          npm rebuild esbuild --verbose || true

      - name: Build (skip type errors)
        run: npm run build-only

      - name: Upload dist artifact
        uses: actions/upload-artifact@v4
        with:
          name: pr-dist
          path: gsm-ui-demo/dist
          if-no-files-found: error
          retention-days: 3