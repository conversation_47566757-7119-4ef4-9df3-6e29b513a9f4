<template>
  <ChartContainer
    :option="chartOption"
    :height="height"
    :color-scheme="colorScheme"
    :class="class"
    @click="handleClick"
    @mouseover="handleMouseover"
    @mouseout="handleMouseout"
  />
</template>

<script setup lang="ts" generic="<PERSON><PERSON> extends Record<string, unknown>">
import { computed } from 'vue'
import ChartContainer from '@/components/charts/ChartContainer.vue'
import { CHART_COLOR_SCHEMES } from '@/lib/chart-themes'

interface Props {
  data: Datum[]
  xAccessor?: (d: Datum) => string | number | Date
  yAccessor?: (d: Datum) => number
  colorAccessor?: (d: Datum, i: number) => string
  xTickFormat?: (value: any) => string
  yTickFormat?: (value: any) => string
  curveType?: 'linear' | 'basis' | 'cardinal' | 'monotone'
  lineWidth?: number
  showPoints?: boolean
  pointSize?: number | ((d: <PERSON><PERSON>) => number)
  height?: number
  class?: string
  colorScheme?: 'primary' | 'risk' | 'enterprise' | 'stages'
  // Event handlers
  onClick?: (params: any) => void
  onMouseover?: (params: any) => void
  onMouseout?: (params: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  curveType: 'cardinal',
  lineWidth: 2,
  showPoints: false,
  pointSize: 4,
  height: 280,
  colorScheme: 'primary',
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
}>()

// Convert data to ECharts format
const chartData = computed(() => {
  if (!props.data || !Array.isArray(props.data)) {
    return { categories: [], values: [] }
  }

  const categories = props.data.map((item, index) => {
    if (props.xAccessor) {
      const xValue = props.xAccessor(item)
      if (xValue instanceof Date) {
        return xValue.toISOString().split('T')[0] // Format date to YYYY-MM-DD
      }
      return String(xValue)
    }
    return typeof item === 'object' && 'name' in item ? String(item.name) : `Point ${index + 1}`
  })

  const values = props.data.map((item, index) => {
    const value = props.yAccessor
      ? props.yAccessor(item)
      : typeof item === 'object' && 'value' in item
        ? Number(item.value)
        : 0
    return {
      value,
      itemStyle: {
        color: props.colorAccessor ? props.colorAccessor(item, index) : undefined,
      },
    }
  })

  return { categories, values }
})

// ECharts line chart configuration
const chartOption = computed(() => {
  const { categories, values } = chartData.value
  const colors = getColorScheme(props.colorScheme)

  const option: any = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params: any) => {
        const param = Array.isArray(params) ? params[0] : params
        return `${param.name}<br/>${param.seriesName}: ${param.value}`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: categories,
      boundaryGap: false,
      axisLabel: {
        formatter: props.xTickFormat || ((value: any) => String(value)),
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: props.yTickFormat || ((value: any) => String(value)),
      },
    },
    series: [
      {
        name: '数据',
        type: 'line',
        data: values,
        smooth: props.curveType !== 'linear',
        lineStyle: {
          width: props.lineWidth,
          cap: 'round',
        },
        symbol: props.showPoints ? 'circle' : 'none',
        symbolSize: typeof props.pointSize === 'number' ? props.pointSize : 6,
        itemStyle: {
          borderWidth: 2,
          borderColor: '#fff',
        },
        emphasis: {
          scale: true,
          scaleSize: 8,
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },
      },
    ],
    color: colors,
  }

  return option
})

// Get color scheme
function getColorScheme(scheme: Props['colorScheme']) {
  switch (scheme) {
    case 'risk':
      return CHART_COLOR_SCHEMES.risk
    case 'enterprise':
      return CHART_COLOR_SCHEMES.enterprise
    case 'stages':
      return CHART_COLOR_SCHEMES.stages
    default:
      return CHART_COLOR_SCHEMES.primary
  }
}

// Event handlers
const handleClick = (params: any) => {
  emit('click', params)
  props.onClick?.(params)
}

const handleMouseover = (params: any) => {
  emit('mouseover', params)
  props.onMouseover?.(params)
}

const handleMouseout = (params: any) => {
  emit('mouseout', params)
  props.onMouseout?.(params)
}
</script>
