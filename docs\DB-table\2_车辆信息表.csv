﻿表名: vehicle_info,,,,,,,
,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,vin,车辆VIN,VARCHAR,17,○,●,主键
2,enterprise_id,所属企业ID,VARCHAR,32,○,,外键关联 enterprise_info.id
3,license_plate_number,车牌号,VARCHAR,16,,,
4,vehicle_model,车辆型号,VARCHAR,128,,,
5,vehicle_type,车辆类型,VARCHAR,64,,,
6,registration_date,注册日期,DATE,,,,
7,status,状态,SMALLINT,,○,,"1: 启用, 0: 停用"
8,create_time,创建时间,TIMESTAMP,,○,,
9,update_time,更新时间,TIMESTAMP,,○,,
