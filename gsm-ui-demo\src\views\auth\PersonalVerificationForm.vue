<template>
  <div class="auth-layout">
    <div class="auth-container">
      <Card class="mx-auto max-w-md">
        <CardHeader>
          <CardTitle>个人实名认证</CardTitle>
          <CardDescription>请填写真实信息完成身份验证</CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="handleSubmit" class="space-y-4">
            
            <!-- 姓名输入框 -->
            <div class="space-y-2">
              <Label for="realName">真实姓名</Label>
              <Input
                id="realName"
                v-model="formData.realName"
                placeholder="请输入真实姓名"
                :class="{ 'border-destructive': errors.realName }"
              />
              <p v-if="errors.realName" class="text-sm text-destructive">
                {{ errors.realName }}
              </p>
            </div>

            <!-- 身份证号输入框 -->
            <div class="space-y-2">
              <Label for="idCardNumber">身份证号码</Label>
              <Input
                id="idCardNumber"
                v-model="formData.idCardNumber"
                placeholder="请输入18位身份证号码"
                maxlength="18"
                :class="{ 'border-destructive': errors.idCardNumber }"
              />
              <p v-if="errors.idCardNumber" class="text-sm text-destructive">
                {{ errors.idCardNumber }}
              </p>
            </div>

            <!-- 手机号输入框 -->
            <div class="space-y-2">
              <Label for="phoneNumber">手机号码</Label>
              <Input
                id="phoneNumber"
                v-model="formData.phoneNumber"
                placeholder="请输入11位手机号码"
                maxlength="11"
                :class="{ 'border-destructive': errors.phoneNumber }"
              />
              <p v-if="errors.phoneNumber" class="text-sm text-destructive">
                {{ errors.phoneNumber }}
              </p>
            </div>

            <!-- 验证码输入框 -->
            <div class="space-y-2">
              <Label for="verifyCode">短信验证码</Label>
              <div class="flex gap-2">
                <Input
                  id="verifyCode"
                  v-model="formData.verifyCode"
                  placeholder="请输入验证码"
                  maxlength="6"
                  class="flex-1"
                  :class="{ 'border-destructive': errors.verifyCode }"
                />
                <Button
                  type="button"
                  variant="outline"
                  @click="sendVerifyCode"
                  :disabled="smsState.sending || smsState.countdown > 0 || !isPhoneValid"
                  class="px-4"
                >
                  {{ getSmsButtonText() }}
                </Button>
              </div>
              <p v-if="errors.verifyCode" class="text-sm text-destructive">
                {{ errors.verifyCode }}
              </p>
            </div>

            <!-- 提交按钮 -->
            <Button
              type="submit"
              class="w-full"
              :disabled="isSubmitting || !isFormValid"
            >
              {{ isSubmitting ? '认证中...' : '完成认证' }}
            </Button>
          </form>
        </CardContent>
      </Card>
      
      <!-- 企业认证引导 -->
      <div v-if="showEnterpriseGuide" class="enterprise-guide">
        <Card>
          <CardHeader>
            <CardTitle>检测到企业身份</CardTitle>
            <CardDescription>您的身份信息显示为企业用户，是否需要进行企业实名认证？</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="guide-actions">
              <Button @click="goToEnterpriseRegistration" class="mr-2">
                进行企业认证
              </Button>
              <Button variant="outline" @click="skipEnterpriseGuide">
                暂时跳过
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import type { PersonalVerificationForm, VerificationResult, SmsCodeState } from '@/types/auth'

// 定义组件事件
const emit = defineEmits<{
  'on-verification-success': [result: VerificationResult]
  'on-enterprise-guide': [guidance: { showGuide: boolean; message: string }]
}>()

const router = useRouter()
const showEnterpriseGuide = ref(false)

// 表单数据 - 预填充演示数据
const formData = reactive<PersonalVerificationForm>({
  realName: '张三',
  idCardNumber: '110101199001011234',
  phoneNumber: '13800138000',
  verifyCode: '123456'
})

// 表单错误信息
const errors = reactive({
  realName: '',
  idCardNumber: '',
  phoneNumber: '',
  verifyCode: ''
})

// 短信验证码状态
const smsState = reactive<SmsCodeState>({
  sending: false,
  countdown: 0,
  sent: false
})

// 表单提交状态
const isSubmitting = ref(false)

// 验证逻辑
const validateRealName = (name: string): boolean => {
  const pattern = /^[\u4e00-\u9fa5]{2,10}$/
  return pattern.test(name)
}

const validateIdCard = (idCard: string): boolean => {
  const pattern = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
  return pattern.test(idCard)
}

const validatePhone = (phone: string): boolean => {
  const pattern = /^1[3-9]\d{9}$/
  return pattern.test(phone)
}

const validateVerifyCode = (code: string): boolean => {
  const pattern = /^\d{6}$/
  return pattern.test(code)
}

// 计算属性
const isPhoneValid = computed(() => validatePhone(formData.phoneNumber))
const isFormValid = computed(() => {
  return validateRealName(formData.realName) &&
         validateIdCard(formData.idCardNumber) &&
         validatePhone(formData.phoneNumber) &&
         validateVerifyCode(formData.verifyCode)
})

// 发送验证码
const sendVerifyCode = async () => {
  if (!isPhoneValid.value) {
    errors.phoneNumber = '请输入正确的手机号码'
    return
  }

  smsState.sending = true
  try {
    // 模拟发送验证码
    await new Promise(resolve => setTimeout(resolve, 800))
    
    smsState.sent = true
    smsState.countdown = 60
    
    // 开始倒计时
    const timer = setInterval(() => {
      smsState.countdown--
      if (smsState.countdown <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
    console.log('验证码已发送到:', formData.phoneNumber)
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    smsState.sending = false
  }
}

const getSmsButtonText = (): string => {
  if (smsState.sending) return '发送中...'
  if (smsState.countdown > 0) return `${smsState.countdown}s后重发`
  return smsState.sent ? '重新发送' : '发送验证码'
}

// 表单提交
const handleSubmit = async () => {
  // 清空之前的错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })

  // 验证表单
  let hasError = false

  if (!validateRealName(formData.realName)) {
    errors.realName = '请输入2-10个汉字的真实姓名'
    hasError = true
  }

  if (!validateIdCard(formData.idCardNumber)) {
    errors.idCardNumber = '请输入正确的18位身份证号码'
    hasError = true
  }

  if (!validatePhone(formData.phoneNumber)) {
    errors.phoneNumber = '请输入正确的11位手机号码'
    hasError = true
  }

  if (!validateVerifyCode(formData.verifyCode)) {
    errors.verifyCode = '请输入6位数字验证码'
    hasError = true
  }

  if (hasError) return

  isSubmitting.value = true
  try {
    // 模拟认证请求
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    // 模拟认证结果
    const result: VerificationResult = {
      success: true,
      message: '认证成功',
      userType: Math.random() > 0.5 ? 'enterprise' : 'individual',
      userId: 'user_' + Date.now(),
      token: 'token_' + Date.now()
    }

    emit('on-verification-success', result)
    
    if (result.userType === 'enterprise') {
      showEnterpriseGuide.value = true
      emit('on-enterprise-guide', {
        showGuide: true,
        message: '检测到您为企业用户，建议进行企业实名认证'
      })
    }
  } catch (error) {
    console.error('认证失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

// 跳转到企业注册填报页面
const goToEnterpriseRegistration = () => {
  // TODO: 跳转到 P-028 企业基本信息填报页面
  router.push({ path: '/corp/filing/form/basic-info' })
}

// 跳过企业认证引导
const skipEnterpriseGuide = () => {
  showEnterpriseGuide.value = false
  router.push({ name: 'Login' })
}
</script>

<style scoped>
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  padding: 1rem;
}

.auth-container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.enterprise-guide {
  margin-top: 1.5rem;
  animation: slideUp 0.3s ease-out;
}

.guide-actions {
  display: flex;
  gap: 0.5rem;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 640px) {
  .guide-actions {
    flex-direction: column;
  }
  
  .guide-actions .mr-2 {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
</style>