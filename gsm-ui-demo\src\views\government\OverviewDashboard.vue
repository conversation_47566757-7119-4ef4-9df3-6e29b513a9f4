<template>
  <div class="space-y-6 p-6">
    <!-- 顶端统计框 -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
      <StatCard title="接入车辆总数" :value="1245" :trend="12.5" :icon="Car" variant="default" />
      <StatCard title="在线车辆总数" :value="892" :trend="5.2" :icon="Wifi" variant="success" />
      <StatCard
        title="累计车端风险"
        :value="23"
        :trend="-8.1"
        :icon="AlertTriangle"
        variant="warning"
      />
      <StatCard
        title="车端累计上报事件"
        :value="156"
        :trend="15.3"
        :icon="FileText"
        variant="default"
      />
      <StatCard title="累计云端风险" :value="8" :trend="-12.0" :icon="Cloud" variant="danger" />
      <StatCard
        title="云端累计上报事件"
        :value="67"
        :trend="3.7"
        :icon="CloudLightning"
        variant="default"
      />
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 车端统计（左侧） -->
      <div class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="text-lg">车端统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-6">
              <!-- 车辆类型/品牌占比 -->
              <div>
                <h4 class="font-medium mb-3 text-slate-700 dark:text-slate-300">车辆类型占比</h4>
                <PieChart
                  :data="vehicleTypeData"
                  color-scheme="primary"
                  chart-type="doughnut"
                  center-text="总计"
                  :center-sub-text="
                    vehicleTypeData.reduce((sum, d) => sum + d.value, 0).toString() + '辆'
                  "
                  :height="220"
                  :inner-radius-pct="30"
                  :outer-radius-pct="50"
                  :show-values="false"
                  :show-percentages="true"
                  class="h-52"
                  @click="handleChartClick"
                  @mouseover="handleChartHover"
                />
              </div>

              <!-- 处理活动 -->
              <div>
                <h4 class="font-medium mb-3 text-slate-700 dark:text-slate-300">处理活动趋势</h4>
                <LineChart
                  :series="vehicleActivitySeries"
                  color-scheme="stages"
                  :smooth="true"
                  :show-area="false"
                  :height="180"
                  :show-legend="true"
                  x-axis-name="时间"
                  y-axis-name="处理量"
                  class="h-44"
                  @click="handleChartClick"
                  @mouseover="handleChartHover"
                />
              </div>

              <!-- 风险事件 -->
              <div>
                <h4 class="font-medium mb-3 text-slate-700 dark:text-slate-300">风险事件统计</h4>
                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <BarChart
                      :data="vehicleRiskStageData"
                      color-scheme="oceanDepths"
                      :height="140"
                      :show-values="true"
                      :bar-width="'30%'"
                      y-axis-name="事件数"
                      class="h-32"
                      @click="handleChartClick"
                    />
                  </div>
                  <div>
                    <PieChart
                      :data="vehicleRiskTypeData"
                      color-scheme="oceanDepths"
                      chart-type="pie"
                      :height="140"
                      :show-legend="false"
                      :show-values="false"
                      :show-percentages="true"
                      class="h-32"
                      @click="handleChartClick"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 地图显示（主体） -->
      <div class="lg:col-span-2">
        <Card class="h-full">
          <CardContent class="p-0 h-full">
            <div class="h-full relative">
              <!-- 地图状态指示器 -->
              <div
                class="absolute top-4 right-4 z-10 bg-background/80 backdrop-blur-sm border rounded-lg px-3 py-2"
              >
                <div class="flex items-center gap-2 text-sm text-muted-foreground">
                  <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>实时监测</span>
                  <span class="w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                  <span>{{ onlineNodesCount }}/{{ totalNodesCount }} 在线</span>
                </div>
              </div>
              <LeafletMap
                :is-dark="isDarkMode"
                :nodes="mapNodes"
                :center-position="[39.084158, 117.200983]"
                :initial-zoom="10"
                @node-click="handleNodeClick"
                @map-ready="handleMapReady"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 云端统计（右侧） -->
      <div class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="text-lg">云端统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-6">
              <!-- 企业类型占比 -->
              <div>
                <h4 class="font-medium mb-3 text-slate-700 dark:text-slate-300">企业类型占比</h4>
                <PieChart
                  :data="enterpriseTypeData"
                  color-scheme="enterprise"
                  chart-type="doughnut"
                  center-text="总计"
                  :center-sub-text="
                    enterpriseTypeData.reduce((sum, d) => sum + d.value, 0).toString() + '家'
                  "
                  :height="220"
                  :inner-radius-pct="30"
                  :outer-radius-pct="50"
                  :show-values="false"
                  :show-percentages="true"
                  class="h-52"
                  @click="handleChartClick"
                  @mouseover="handleChartHover"
                />
              </div>

              <!-- 处理活动（七阶段） -->
              <div>
                <h4 class="font-medium mb-3 text-slate-700 dark:text-slate-300">七阶段处理活动</h4>
                <BarChart
                  :data="cloudSevenStageData"
                  color-scheme="stages"
                  :height="180"
                  :show-values="true"
                  :bar-width="'70%'"
                  y-axis-name="活动量"
                  class="h-44"
                  @click="handleChartClick"
                  @mouseover="handleChartHover"
                />
              </div>

              <!-- 风险事件 -->
              <div>
                <h4 class="font-medium mb-3 text-slate-700 dark:text-slate-300">风险事件统计</h4>
                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <BarChart
                      :data="cloudRiskStageData"
                      color-scheme="oceanDepths"
                      :height="140"
                      :show-values="true"
                      :bar-width="'30%'"
                      y-axis-name="事件数"
                      class="h-32"
                      @click="handleChartClick"
                    />
                  </div>
                  <div>
                    <PieChart
                      :data="cloudRiskTypeData"
                      color-scheme="oceanDepths"
                      chart-type="pie"
                      :height="140"
                      :show-legend="false"
                      :show-values="false"
                      :show-percentages="true"
                      class="h-32"
                      @click="handleChartClick"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 风险预警信息（中下） -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 车端预警 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between">
          <CardTitle class="text-lg">车端风险预警</CardTitle>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedVehiclePeriod === 'year' }"
              @click="selectedVehiclePeriod = 'year'"
              >年</Button
            >
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedVehiclePeriod === 'month' }"
              @click="selectedVehiclePeriod = 'month'"
              >月</Button
            >
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedVehiclePeriod === 'day' }"
              @click="selectedVehiclePeriod = 'day'"
              >日</Button
            >
          </div>
        </CardHeader>
        <CardContent>
          <div class="h-64 overflow-hidden relative">
            <div
              class="space-y-3 transition-transform duration-1000 ease-in-out"
              :style="{ transform: `translateY(-${vehicleCurrentIndex * 76}px)` }"
            >
              <div
                v-for="(risk, index) in vehicleRiskData"
                :key="index"
                :class="getRiskCardClass(risk.level)"
                class="p-4 rounded-lg border transition-all duration-300 hover:shadow-md"
              >
                <div class="flex items-start justify-between">
                  <div class="flex items-center space-x-3">
                    <div :class="getRiskIndicatorClass(risk.level)"></div>
                    <div>
                      <div class="flex items-center space-x-2">
                        <span :class="getRiskTextClass(risk.level)" class="font-medium"
                          >{{ risk.level }}风险</span
                        >
                        <span
                          class="text-xs px-2 py-1 rounded-full"
                          :class="getRiskBadgeClass(risk.level)"
                          >{{ risk.type }}</span
                        >
                      </div>
                      <div class="text-sm text-muted-foreground mt-1">{{ risk.description }}</div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-base font-semibold">{{ risk.vehicleId }}</div>
                    <div class="text-xs text-muted-foreground">{{ risk.location }}</div>
                    <div class="text-xs text-muted-foreground">{{ risk.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 云端预警 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between">
          <CardTitle class="text-lg">云端风险预警</CardTitle>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedCloudPeriod === 'year' }"
              @click="selectedCloudPeriod = 'year'"
              >年</Button
            >
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedCloudPeriod === 'month' }"
              @click="selectedCloudPeriod = 'month'"
              >月</Button
            >
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedCloudPeriod === 'day' }"
              @click="selectedCloudPeriod = 'day'"
              >日</Button
            >
          </div>
        </CardHeader>
        <CardContent>
          <div class="h-64 overflow-hidden relative">
            <div
              class="space-y-3 transition-transform duration-1000 ease-in-out"
              :style="{ transform: `translateY(-${cloudCurrentIndex * 76}px)` }"
            >
              <div
                v-for="(risk, index) in cloudRiskData"
                :key="index"
                :class="getRiskCardClass(risk.level)"
                class="p-4 rounded-lg border transition-all duration-300 hover:shadow-md"
              >
                <div class="flex items-start justify-between">
                  <div class="flex items-center space-x-3">
                    <div :class="getRiskIndicatorClass(risk.level)"></div>
                    <div>
                      <div class="flex items-center space-x-2">
                        <span :class="getRiskTextClass(risk.level)" class="font-medium"
                          >{{ risk.level }}风险</span
                        >
                        <span
                          class="text-xs px-2 py-1 rounded-full"
                          :class="getRiskBadgeClass(risk.level)"
                          >{{ risk.type }}</span
                        >
                      </div>
                      <div class="text-sm text-muted-foreground mt-1">{{ risk.description }}</div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="text-base font-semibold">{{ risk.company }}</div>
                    <div class="text-xs text-muted-foreground">{{ risk.service }}</div>
                    <div class="text-xs text-muted-foreground">{{ risk.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import StatCard from '@/components/dashboard/StatCard.vue'
import { PieChart, BarChart, LineChart } from '@/components/charts'
import LeafletMap from '@/components/ui/map/LeafletMap.vue'
import { useColorMode } from '@vueuse/core'
import { Car, Wifi, AlertTriangle, FileText, Cloud, CloudLightning } from 'lucide-vue-next'
import {
  generateVehicleTypeDistribution,
  generateEnterpriseDistribution,
  generateSevenStageData,
  generateRiskDistribution,
  randomInt,
} from '@/lib/mock-data-generator'

// 图表交互处理函数
const handleChartClick = (params: any) => {
  console.log('图表点击事件:', params)
  // 可以在这里添加跳转到详情页面等逻辑
}

const handleChartHover = (params: any) => {
  console.log('图表悬浮事件:', params)
  // 可以在这里添加悬浮提示增强逻辑
}

// 车辆类型数据 - 使用真实比例
const vehicleTypeData = generateVehicleTypeDistribution()

// 车端处理活动趋势数据 - 更真实的24小时数据
const vehicleActivitySeries = [
  {
    name: '数据收集',
    data: [
      { name: '00:00', value: randomInt(40, 60) },
      { name: '04:00', value: randomInt(20, 40) },
      { name: '08:00', value: randomInt(180, 220) },
      { name: '12:00', value: randomInt(240, 280) },
      { name: '16:00', value: randomInt(280, 320) },
      { name: '20:00', value: randomInt(160, 200) },
    ],
  },
  {
    name: '数据存储',
    data: [
      { name: '00:00', value: randomInt(35, 55) },
      { name: '04:00', value: randomInt(18, 35) },
      { name: '08:00', value: randomInt(170, 210) },
      { name: '12:00', value: randomInt(230, 270) },
      { name: '16:00', value: randomInt(270, 310) },
      { name: '20:00', value: randomInt(150, 190) },
    ],
  },
  {
    name: '数据传输',
    data: [
      { name: '00:00', value: randomInt(30, 50) },
      { name: '04:00', value: randomInt(15, 30) },
      { name: '08:00', value: randomInt(160, 200) },
      { name: '12:00', value: randomInt(220, 260) },
      { name: '16:00', value: randomInt(260, 300) },
      { name: '20:00', value: randomInt(140, 180) },
    ],
  },
]

// 车端风险阶段数据 - 使用更真实的分布
const vehicleRiskStageData = [
  { name: '采集', value: randomInt(15, 25), description: '数据采集阶段发现的风险' },
  { name: '存储', value: randomInt(20, 35), description: '数据存储阶段发现的风险' },
  { name: '传输', value: randomInt(8, 15), description: '数据传输阶段发现的风险' },
]

// 车端风险类型数据 - 使用生成的风险分布
const riskDistribution = generateRiskDistribution()
const vehicleRiskTypeData = [
  { name: '数据泄露', value: riskDistribution[0].value, description: '敏感数据泄露风险' },
  { name: '访问异常', value: riskDistribution[1].value, description: '异常访问行为' },
  { name: '传输异常', value: riskDistribution[2].value, description: '数据传输过程异常' },
]

// 企业类型数据 - 使用生成器
const enterpriseTypeData = generateEnterpriseDistribution()

// 云端七阶段数据 - 使用生成器
const cloudSevenStageData = generateSevenStageData()

// 云端风险阶段数据 - 更真实的分布
const cloudRiskStageData = [
  { name: '收集', value: randomInt(3, 8), description: '收集阶段风险事件' },
  { name: '存储', value: randomInt(5, 12), description: '存储阶段风险事件' },
  { name: '传输', value: randomInt(2, 6), description: '传输阶段风险事件' },
  { name: '加工', value: randomInt(4, 10), description: '加工阶段风险事件' },
]

// 云端风险类型数据 - 使用另一组风险分布
const cloudRiskDistribution = generateRiskDistribution()
const cloudRiskTypeData = [
  { name: '数据泄露', value: cloudRiskDistribution[0].value, description: '云端数据泄露事件' },
  { name: '违规处理', value: cloudRiskDistribution[1].value, description: '违规数据处理行为' },
  { name: '访问异常', value: cloudRiskDistribution[2].value, description: '异常访问云端资源' },
]

// 主题检测
const mode = useColorMode({
  selector: 'html',
  attribute: 'class',
  initialValue: 'auto',
  storageKey: 'vueuse-color-scheme',
  emitAuto: true,
})
const isDarkMode = computed(() => mode.value === 'dark')

// 地图节点数据类型定义
interface MapNode {
  id: string
  name: string
  type: 'vehicle' | 'cloud'
  lat: number
  lng: number
  status: '在线' | '离线'
  riskLevel: '低' | '中' | '高'
  vin?: string
  company?: string
}

// 地图节点数据
const mapNodes = ref<MapNode[]>([
  {
    id: 'vehicle_001',
    name: '津A·12345',
    type: 'vehicle',
    lat: 39.084158,
    lng: 117.200983,
    status: '在线',
    riskLevel: '低',
    vin: 'LSGJ2A1DXE1234567',
  },
  {
    id: 'vehicle_002',
    name: '津B·67890',
    type: 'vehicle',
    lat: 39.100158,
    lng: 117.180983,
    status: '在线',
    riskLevel: '高',
    vin: 'WBAVD31060A234567',
  },
  {
    id: 'vehicle_003',
    name: '津C·11111',
    type: 'vehicle',
    lat: 39.070158,
    lng: 117.250983,
    status: '离线',
    riskLevel: '中',
    vin: 'LFPH3DCC0L3456789',
  },
  {
    id: 'cloud_001',
    name: '高德地图服务',
    type: 'cloud',
    lat: 39.070158,
    lng: 117.220983,
    status: '在线',
    riskLevel: '中',
    company: '高德软件有限公司',
  },
  {
    id: 'cloud_002',
    name: '百度地图服务',
    type: 'cloud',
    lat: 39.110158,
    lng: 117.240983,
    status: '在线',
    riskLevel: '低',
    company: '北京百度智图科技有限公司',
  },
  {
    id: 'cloud_003',
    name: '腾讯地图服务',
    type: 'cloud',
    lat: 39.050158,
    lng: 117.190983,
    status: '离线',
    riskLevel: '低',
    company: '腾讯科技（北京）有限公司',
  },
  {
    id: 'vehicle_004',
    name: '津D·22222',
    type: 'vehicle',
    lat: 39.120158,
    lng: 117.160983,
    status: '在线',
    riskLevel: '中',
    vin: 'LFV8A24H4J4567890',
  },
  {
    id: 'vehicle_005',
    name: '津E·33333',
    type: 'vehicle',
    lat: 39.090158,
    lng: 117.270983,
    status: '在线',
    riskLevel: '低',
    vin: 'LSGJ3A1BXL5678901',
  },
])

// 地图统计数据
const totalNodesCount = computed(() => mapNodes.value.length)
const onlineNodesCount = computed(
  () => mapNodes.value.filter((node) => node.status === '在线').length,
)

// 地图事件处理
const handleNodeClick = (node: MapNode) => {
  console.log('点击节点:', node)
  // 这里可以添加点击节点后的逻辑，比如显示详情弹窗
}

const handleMapReady = (map: any) => {
  console.log('地图初始化完成:', map)
  // 地图初始化完成后的逻辑
}

// 风险预警数据接口
interface RiskItem {
  id: string
  level: '高' | '中' | '低'
  type: string
  description: string
  vehicleId?: string
  company?: string
  service?: string
  location: string
  time: string
  status: 'active' | 'resolved'
}

// 时间过滤选项
const selectedVehiclePeriod = ref('day')
const selectedCloudPeriod = ref('day')

// 车端风险预警数据 (10条)
const vehicleRiskData = ref<RiskItem[]>([
  {
    id: 'v001',
    level: '高',
    type: '数据泄露',
    description: '检测到敏感位置信息异常外传，疑似隐私泄露',
    vehicleId: '津A·12345',
    location: '天津市和平区',
    time: '2024-08-22 10:15',
    status: 'active',
  },
  {
    id: 'v002',
    level: '中',
    type: '访问异常',
    description: '发现未授权的数据访问请求，可能存在安全隐患',
    vehicleId: '津B·67890',
    location: '天津市河西区',
    time: '2024-08-22 09:42',
    status: 'active',
  },
  {
    id: 'v003',
    level: '低',
    type: '传输异常',
    description: '数据传输过程中发现轻微延迟，建议检查网络状态',
    vehicleId: '津C·11111',
    location: '天津市南开区',
    time: '2024-08-22 09:18',
    status: 'active',
  },
  {
    id: 'v004',
    level: '高',
    type: '违规操作',
    description: '检测到车辆数据被非法复制，立即需要安全检查',
    vehicleId: '津D·22222',
    location: '天津市河北区',
    time: '2024-08-22 08:55',
    status: 'active',
  },
  {
    id: 'v005',
    level: '中',
    type: '权限异常',
    description: '发现权限提升异常行为，可能存在恶意攻击',
    vehicleId: '津E·33333',
    location: '天津市红桥区',
    time: '2024-08-22 08:31',
    status: 'active',
  },
  {
    id: 'v006',
    level: '低',
    type: '配置错误',
    description: '系统配置存在轻微问题，建议进行优化调整',
    vehicleId: '津F·44444',
    location: '天津市东丽区',
    time: '2024-08-22 08:07',
    status: 'active',
  },
  {
    id: 'v007',
    level: '高',
    type: '恶意软件',
    description: '检测到可疑恶意软件活动，需要立即隔离处理',
    vehicleId: '津G·55555',
    location: '天津市西青区',
    time: '2024-08-22 07:43',
    status: 'active',
  },
  {
    id: 'v008',
    level: '中',
    type: '数据完整性',
    description: '发现数据完整性校验失败，可能存在篡改风险',
    vehicleId: '津H·66666',
    location: '天津市津南区',
    time: '2024-08-22 07:19',
    status: 'active',
  },
  {
    id: 'v009',
    level: '低',
    type: '性能异常',
    description: '系统性能监控发现轻微异常，建议关注',
    vehicleId: '津J·77777',
    location: '天津市北辰区',
    time: '2024-08-22 06:55',
    status: 'active',
  },
  {
    id: 'v010',
    level: '中',
    type: '网络异常',
    description: '网络连接出现异常波动，影响数据传输质量',
    vehicleId: '津K·88888',
    location: '天津市武清区',
    time: '2024-08-22 06:31',
    status: 'active',
  },
])

// 云端风险预警数据 (10条)
const cloudRiskData = ref<RiskItem[]>([
  {
    id: 'c001',
    level: '高',
    type: '数据泄露',
    description: '云端服务器检测到大量敏感数据异常访问',
    company: '高德软件有限公司',
    service: '地图数据服务',
    location: '北京数据中心',
    time: '2024-08-22 10:22',
    status: 'active',
  },
  {
    id: 'c002',
    level: '中',
    type: '违规处理',
    description: '发现数据处理流程不符合监管要求',
    company: '百度智图科技',
    service: '导航服务API',
    location: '上海数据中心',
    time: '2024-08-22 09:58',
    status: 'active',
  },
  {
    id: 'c003',
    level: '低',
    type: '访问异常',
    description: '检测到异常IP地址频繁访问云端资源',
    company: '腾讯科技北京',
    service: '位置服务平台',
    location: '深圳数据中心',
    time: '2024-08-22 09:34',
    status: 'active',
  },
  {
    id: 'c004',
    level: '高',
    type: '安全漏洞',
    description: '发现系统存在严重安全漏洞，需立即修复',
    company: '滴滴出行科技',
    service: '出行数据平台',
    location: '杭州数据中心',
    time: '2024-08-22 09:10',
    status: 'active',
  },
  {
    id: 'c005',
    level: '中',
    type: '权限滥用',
    description: '检测到管理员权限被异常使用',
    company: '美团点评',
    service: '配送路径优化',
    location: '成都数据中心',
    time: '2024-08-22 08:46',
    status: 'active',
  },
  {
    id: 'c006',
    level: '低',
    type: '性能预警',
    description: '云服务响应时间超出正常范围',
    company: '京东物流',
    service: '智能调度系统',
    location: '西安数据中心',
    time: '2024-08-22 08:22',
    status: 'active',
  },
  {
    id: 'c007',
    level: '高',
    type: '数据篡改',
    description: '发现关键业务数据被恶意篡改',
    company: '顺丰科技',
    service: '物流追踪平台',
    location: '广州数据中心',
    time: '2024-08-22 07:58',
    status: 'active',
  },
  {
    id: 'c008',
    level: '中',
    type: '合规违规',
    description: '数据跨境传输未按规定进行备案',
    company: '阿里云计算',
    service: '地图渲染服务',
    location: '青岛数据中心',
    time: '2024-08-22 07:34',
    status: 'active',
  },
  {
    id: 'c009',
    level: '低',
    type: '监控告警',
    description: '系统监控发现轻微异常指标',
    company: '华为云服务',
    service: '车联网平台',
    location: '南京数据中心',
    time: '2024-08-22 07:10',
    status: 'active',
  },
  {
    id: 'c010',
    level: '中',
    type: '流量异常',
    description: '检测到异常流量模式，可能存在攻击',
    company: '字节跳动',
    service: '地图SDK服务',
    location: '武汉数据中心',
    time: '2024-08-22 06:46',
    status: 'active',
  },
])

// 滚动索引
const vehicleCurrentIndex = ref(0)
const cloudCurrentIndex = ref(0)

// 滚动定时器
let vehicleScrollTimer: NodeJS.Timeout | null = null
let cloudScrollTimer: NodeJS.Timeout | null = null

// 启动滚动
const startScrolling = () => {
  // 车端滚动 - 每4秒滚动一次
  vehicleScrollTimer = setInterval(() => {
    vehicleCurrentIndex.value = (vehicleCurrentIndex.value + 1) % (vehicleRiskData.value.length - 2)
  }, 4000)

  // 云端滚动 - 每4.5秒滚动一次（错开时间）
  cloudScrollTimer = setInterval(() => {
    cloudCurrentIndex.value = (cloudCurrentIndex.value + 1) % (cloudRiskData.value.length - 2)
  }, 4500)
}

// 停止滚动
const stopScrolling = () => {
  if (vehicleScrollTimer) {
    clearInterval(vehicleScrollTimer)
    vehicleScrollTimer = null
  }
  if (cloudScrollTimer) {
    clearInterval(cloudScrollTimer)
    cloudScrollTimer = null
  }
}

// 风险卡片样式函数
const getRiskCardClass = (level: string) => {
  const baseClass = 'transition-all duration-300'
  switch (level) {
    case '高':
      return `${baseClass} bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800/50 hover:bg-red-100 dark:hover:bg-red-950/50`
    case '中':
      return `${baseClass} bg-orange-50 dark:bg-orange-950/30 border-orange-200 dark:border-orange-800/50 hover:bg-orange-100 dark:hover:bg-orange-950/50`
    case '低':
      return `${baseClass} bg-yellow-50 dark:bg-yellow-950/30 border-yellow-200 dark:border-yellow-800/50 hover:bg-yellow-100 dark:hover:bg-yellow-950/50`
    default:
      return baseClass
  }
}

const getRiskIndicatorClass = (level: string) => {
  const baseClass = 'w-3 h-3 rounded-full'
  switch (level) {
    case '高':
      return `${baseClass} bg-red-500 animate-pulse shadow-lg shadow-red-500/50`
    case '中':
      return `${baseClass} bg-orange-500 animate-pulse shadow-lg shadow-orange-500/50`
    case '低':
      return `${baseClass} bg-yellow-500`
    default:
      return baseClass
  }
}

const getRiskTextClass = (level: string) => {
  switch (level) {
    case '高':
      return 'text-red-800 dark:text-red-200'
    case '中':
      return 'text-orange-800 dark:text-orange-200'
    case '低':
      return 'text-yellow-800 dark:text-yellow-200'
    default:
      return ''
  }
}

const getRiskBadgeClass = (level: string) => {
  switch (level) {
    case '高':
      return 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800'
    case '中':
      return 'bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-800'
    case '低':
      return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800'
    default:
      return ''
  }
}

// 生命周期
onMounted(() => {
  startScrolling()
})

onUnmounted(() => {
  stopScrolling()
})
</script>
