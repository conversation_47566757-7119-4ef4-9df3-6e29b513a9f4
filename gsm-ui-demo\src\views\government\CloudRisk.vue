<template>
  <div class="space-y-6 relative">
    <div class="relative z-10">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
          <p class="text-muted-foreground">
            监测与管理云端安全风险，包含七阶段统计、筛选与详情查看，支持风险溯源与导出
          </p>
        </div>
      </div>

      <!-- 上端统计：七阶段/等级/处置状态/处置率 -->
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <!-- 按处理阶段（七阶段） -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">按处理阶段统计</CardTitle>
            <Cloud class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent class="p-6">
            <div class="text-xs text-muted-foreground mb-2">收集/存储/传输/加工/提供/公开/销毁</div>
            <div class="h-[220px] p-2">
              <BarChart
                :data="stageStatsData"
                color-scheme="status"
                :height="220"
                :show-values="true"
                :bar-width="'60%'"
                y-axis-name="风险数量"
                class="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 风险等级 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">风险等级分布</CardTitle>
            <AlertTriangle class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent class="p-6">
            <div class="h-[220px] p-2">
              <PieChart
                :data="levelStatsData"
                color-scheme="risk"
                chart-type="doughnut"
                center-text="总计"
                :center-sub-text="totalLevel.toString() + '个'"
                :height="220"
                :show-legend="true"
                :show-percentages="false"
                :show-values="true"
                class="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 处置状态 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">处置状态</CardTitle>
            <CheckCircle2 class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent class="p-6">
            <div class="h-[220px] p-2">
              <PieChart
                :data="statusStatsData"
                color-scheme="status"
                chart-type="doughnut"
                center-text="总计"
                :center-sub-text="totalStatus.toString() + '条'"
                :height="220"
                :show-legend="true"
                :show-percentages="false"
                :show-values="true"
                class="w-full"
              />
            </div>
          </CardContent>
        </Card>

        <!-- 处置率统计 -->
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-lg font-semibold">处置率</CardTitle>
            <Percent class="h-8 w-8 text-muted-foreground" />
          </CardHeader>
          <CardContent class="p-6">
            <div class="space-y-4">
              <!-- 处置率数值显示 -->
              <div class="text-center">
                <div class="text-3xl font-bold text-foreground mb-1">{{ handleRate }}%</div>
                <p class="text-sm text-muted-foreground">已处置 / 总风险</p>
                <div class="text-xs text-muted-foreground mt-2">
                  今日新增已处置：<span class="text-foreground font-medium">{{
                    todayHandled
                  }}</span>
                </div>
              </div>

              <!-- 环形图 -->
              <div class="h-[180px] p-2">
                <PieChart
                  :data="handleRateData"
                  color-scheme="status"
                  chart-type="doughnut"
                  center-text="处置率"
                  :center-sub-text="handleRate.toString() + '%'"
                  :height="180"
                  :show-legend="true"
                  :show-percentages="true"
                  :show-values="true"
                  class="w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 列表 -->
      <Card>
        <CardHeader>
          <CardTitle>云端风险列表</CardTitle>
          <CardDescription>
            共 {{ filteredTotal }} 条记录，当前显示第
            {{ pageSize * (currentPage - 1) + 1 }}
            -
            {{ Math.min(pageSize * currentPage, filteredTotal) }}
            条
          </CardDescription>
        </CardHeader>
        <CardContent>
          <!-- 筛选表单 -->
          <CompactFilterForm
            :filter-fields="filterFields"
            :show-export="true"
            :initial-values="filters"
            @search="handleSearch"
            @reset="resetFilters"
            @export="exportCsv"
          />
          <div class="rounded-md border mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-[80px]">序号</TableHead>
                  <TableHead class="min-w-[180px]">企业名称</TableHead>
                  <TableHead class="min-w-[120px]">企业类型</TableHead>
                  <TableHead>处理阶段</TableHead>
                  <TableHead>风险等级</TableHead>
                  <TableHead class="min-w-[200px]">风险描述</TableHead>
                  <TableHead>当前状态</TableHead>
                  <TableHead class="min-w-[140px]">发生时间</TableHead>
                  <TableHead class="min-w-[140px]">处置完成时间</TableHead>
                  <TableHead class="w-[200px] text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="pagedItems.length === 0">
                  <TableCell :colspan="10" class="h-24 text-center text-muted-foreground">
                    暂无数据
                  </TableCell>
                </TableRow>
                <TableRow
                  v-for="(item, index) in pagedItems"
                  :key="item.id"
                  class="hover:bg-muted/40"
                >
                  <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                  <TableCell>
                    <div class="space-y-1">
                      <div class="font-medium">{{ item.enterprise }}</div>
                      <div class="text-xs text-muted-foreground">{{ item.enterpriseCode }}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{{ item.enterpriseType }}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{{ item.stage }}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="levelVariantDisplay(item.level)">{{
                      formatLevel(item.level)
                    }}</Badge>
                  </TableCell>
                  <TableCell class="max-w-[260px]">
                    <div class="text-sm line-clamp-2">{{ item.description }}</div>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="statusVariant(item.status)">{{ item.status }}</Badge>
                  </TableCell>
                  <TableCell class="whitespace-nowrap">{{ item.occurAt }}</TableCell>
                  <TableCell class="whitespace-nowrap">{{ item.resolvedAt || '-' }}</TableCell>
                  <TableCell class="text-right">
                    <div class="flex justify-end gap-2">
                      <Button size="sm" variant="outline" @click="openDetail(item)"
                        >详细信息</Button
                      >
                      <Button size="sm" @click="goTrace(item)">风险溯源</Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- 分页：统一 PaginationBar -->
          <PaginationBar
            v-model:page="currentPage"
            v-model:pageSize="pageSize"
            :total="filteredTotal"
          />
        </CardContent>
      </Card>

      <!-- 详情弹窗 -->
      <Dialog v-model:open="detailOpen">
        <DialogContent class="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>云端风险详细信息</DialogTitle>
            <DialogDescription>展示风险类型、危害、可能性与处置建议等信息</DialogDescription>
          </DialogHeader>

          <div v-if="selectedRisk" class="space-y-4">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <div class="text-xs text-muted-foreground">企业名称</div>
                <div class="text-base font-semibold">{{ selectedRisk.enterprise }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">企业类型</div>
                <div class="text-base font-semibold">{{ selectedRisk.enterpriseType }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">处理阶段</div>
                <div class="text-base font-semibold">{{ selectedRisk.stage }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">风险等级</div>
                <div class="text-base font-semibold">{{ formatLevel(selectedRisk.level) }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">当前状态</div>
                <div class="text-base font-semibold">{{ selectedRisk.status }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">发生时间</div>
                <div class="text-base font-semibold">{{ selectedRisk.occurAt }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">处置完成时间</div>
                <div class="text-base font-semibold">{{ selectedRisk.resolvedAt || '-' }}</div>
              </div>
            </div>

            <!-- 风险信息 -->
            <div class="space-y-2">
              <div class="text-xs text-muted-foreground">风险描述</div>
              <div class="text-sm">{{ selectedRisk.description }}</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <div class="text-xs text-muted-foreground">风险类型</div>
                <div class="text-base font-semibold">{{ selectedRisk.riskType }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">危害程度</div>
                <div class="text-base font-semibold">{{ selectedRisk.harm }}</div>
              </div>
              <div>
                <div class="text-xs text-muted-foreground">发生可能性</div>
                <div class="text-base font-semibold">{{ selectedRisk.probability }}</div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="text-xs text-muted-foreground">处置建议</div>
              <div class="text-sm whitespace-pre-line">
                {{ selectedRisk.suggestion }}
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="text-xs text-muted-foreground">
                风险 ID：<span class="text-foreground">{{ selectedRisk.id }}</span>
              </div>
              <div class="flex gap-2">
                <Button variant="outline" @click="detailOpen = false">关闭</Button>
                <Button @click="goTrace(selectedRisk)">前往溯源</Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { AlertTriangle, CheckCircle2, Cloud, Percent } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PieChart, BarChart } from '@/components/charts'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import { PaginationBar } from '@/components/ui/pagination'

type CloudStage = '收集' | '存储' | '传输' | '加工' | '提供' | '公开' | '销毁'
type RiskLevel = '高' | '中' | '低'
type HandleStatus = '未处理' | '处理中' | '已处理'
type EnterpriseType = '整车生产企业' | '平台运营商' | '智驾方案提供商' | '地图服务商' | '其他'

type StageFilter = 'ALL' | CloudStage
type StatusFilter = 'ALL' | HandleStatus
type LevelFilter = 'ALL' | RiskLevel
type EnterpriseTypeFilter = 'ALL' | EnterpriseType

interface CloudRiskItem {
  id: string
  enterprise: string
  enterpriseCode: string
  enterpriseType: EnterpriseType
  stage: CloudStage
  level: RiskLevel
  description: string
  status: HandleStatus
  occurAt: string // YYYY-MM-DD HH:mm
  resolvedAt?: string // YYYY-MM-DD HH:mm | undefined
  riskType: string
  harm: '高' | '中' | '低'
  probability: '高' | '中' | '低'
  suggestion: string
}

interface Filters {
  enterprise: string
  enterpriseType: EnterpriseTypeFilter
  stage: StageFilter
  status: StatusFilter
  level: LevelFilter
  timeRange: [Date, Date] | null
}

const router = useRouter()

// 筛选器
const filters = ref<Filters>({
  enterprise: '',
  enterpriseType: 'ALL',
  stage: 'ALL',
  status: 'ALL',
  level: 'ALL',
  timeRange: null,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'enterprise',
    label: '企业名称',
    type: 'input',
    placeholder: '企业名称',
  },
  {
    key: 'enterpriseType',
    label: '企业类型',
    type: 'select',
    placeholder: '选择类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '整车生产企业', value: '整车生产企业' },
      { label: '平台运营商', value: '平台运营商' },
      { label: '智驾方案提供商', value: '智驾方案提供商' },
      { label: '地图服务商', value: '地图服务商' },
      { label: '其他', value: '其他' },
    ],
  },
  {
    key: 'stage',
    label: '处理阶段',
    type: 'select',
    placeholder: '选择阶段',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '收集', value: '收集' },
      { label: '存储', value: '存储' },
      { label: '传输', value: '传输' },
      { label: '加工', value: '加工' },
      { label: '提供', value: '提供' },
      { label: '公开', value: '公开' },
      { label: '销毁', value: '销毁' },
    ],
  },
  {
    key: 'status',
    label: '处理状态',
    type: 'select',
    placeholder: '选择状态',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '未处理', value: '未处理' },
      { label: '处理中', value: '处理中' },
      { label: '已处理', value: '已处理' },
    ],
  },
  {
    key: 'level',
    label: '风险等级',
    type: 'select',
    placeholder: '选择等级',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' },
    ],
  },
  {
    key: 'timeRange',
    label: '时间范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

const handleSearch = (searchFilters: Record<string, any>) => {
  Object.assign(filters.value, searchFilters)
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    enterprise: '',
    enterpriseType: 'ALL',
    stage: 'ALL',
    status: 'ALL',
    level: 'ALL',
    timeRange: null,
  }
}

// Mock 数据
const riskItems = ref<CloudRiskItem[]>([
  {
    id: 'CR-202501-0001',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    enterpriseType: '平台运营商',
    stage: '收集',
    level: '高',
    description: '数据收集过程中发现企业未按要求进行地理位置脱敏处理，存在敏感数据暴露风险。',
    status: '处理中',
    occurAt: '2025-08-20 10:32',
    riskType: '违规收集',
    harm: '高',
    probability: '中',
    suggestion: '立即停止相关收集活动；完善脱敏流程；对已收集数据进行脱敏处理；提交整改报告。',
  },
  {
    id: 'CR-202501-0002',
    enterprise: '上海车联网服务有限公司',
    enterpriseCode: '91310000987654321Y',
    enterpriseType: '地图服务商',
    stage: '存储',
    level: '中',
    description: '云端存储系统访问控制配置不当，部分敏感数据可能被未授权人员访问。',
    status: '已处理',
    occurAt: '2025-08-19 14:21',
    resolvedAt: '2025-08-20 16:30',
    riskType: '访问控制',
    harm: '中',
    probability: '低',
    suggestion: '已修复访问控制配置；加强权限管理；定期进行安全审计。',
  },
  {
    id: 'CR-202501-0003',
    enterprise: '深圳自动驾驶技术有限公司',
    enterpriseCode: '91440300567890123Z',
    enterpriseType: '智驾方案提供商',
    stage: '传输',
    level: '中',
    description: '数据传输过程中发现加密强度不足，可能存在被中间人攻击的风险。',
    status: '未处理',
    occurAt: '2025-08-20 16:45',
    riskType: '传输安全',
    harm: '中',
    probability: '中',
    suggestion: '升级加密算法；使用更强的密钥长度；实施传输层安全增强措施。',
  },
  {
    id: 'CR-202501-0004',
    enterprise: '广州汽车制造有限公司',
    enterpriseCode: '91440000456789012A',
    enterpriseType: '整车生产企业',
    stage: '加工',
    level: '低',
    description: '数据加工处理中发现少量日志记录不完整，可能影响后续审计工作。',
    status: '已处理',
    occurAt: '2025-08-18 11:20',
    resolvedAt: '2025-08-19 09:45',
    riskType: '日志完整性',
    harm: '低',
    probability: '低',
    suggestion: '已完善日志记录机制；增强日志完整性校验；定期检查日志质量。',
  },
  {
    id: 'CR-202501-0005',
    enterprise: '杭州智慧交通有限公司',
    enterpriseCode: '91330100MA2H3N9T7W',
    enterpriseType: '平台运营商',
    stage: '提供',
    level: '高',
    description: '发现企业向第三方提供包含敏感地理信息的原始数据，未进行必要的脱敏处理。',
    status: '处理中',
    occurAt: '2025-08-21 08:15',
    riskType: '数据泄露',
    harm: '高',
    probability: '高',
    suggestion:
      '立即停止数据提供；召回已提供的敏感数据；建立数据对外提供审核机制；加强数据脱敏管理。',
  },
  {
    id: 'CR-202501-0006',
    enterprise: '成都车路协同技术公司',
    enterpriseCode: '91510100MA7G8P2K3A',
    enterpriseType: '智驾方案提供商',
    stage: '公开',
    level: '中',
    description: '企业官网公开的技术文档中包含了部分敏感测绘数据，违反保密规定。',
    status: '已处理',
    occurAt: '2025-08-18 15:30',
    resolvedAt: '2025-08-19 10:20',
    riskType: '信息泄露',
    harm: '中',
    probability: '中',
    suggestion: '已删除公开的敏感信息；审核所有公开文档；建立信息发布审核流程。',
  },
  {
    id: 'CR-202501-0007',
    enterprise: '天津智能网联汽车公司',
    enterpriseCode: '91120116MA5JJHXN4R',
    enterpriseType: '地图服务商',
    stage: '销毁',
    level: '低',
    description: '数据销毁过程中未按规定进行彻底删除，存在数据恢复风险。',
    status: '处理中',
    occurAt: '2025-08-21 11:45',
    riskType: '销毁不彻底',
    harm: '低',
    probability: '低',
    suggestion: '采用安全删除工具；物理销毁存储介质；建立数据销毁验证机制。',
  },
  {
    id: 'CR-202501-0008',
    enterprise: '南京智慧出行科技公司',
    enterpriseCode: '91320100MA1W8J9K7T',
    enterpriseType: '整车生产企业',
    stage: '收集',
    level: '高',
    description: '企业私自扩大数据收集范围，采集了未报备测试/采集区域地理信息。',
    status: '未处理',
    occurAt: '2025-08-21 13:20',
    riskType: '违法测绘',
    harm: '高',
    probability: '高',
    suggestion: '立即停止违法采集；删除相关数据；配合监管部门调查；进行合规培训。',
  },
  {
    id: 'CR-202501-0009',
    enterprise: '武汉未来智行有限公司',
    enterpriseCode: '91420100MA4KYJQJ5D',
    enterpriseType: '平台运营商',
    stage: '存储',
    level: '中',
    description: '云端备份数据未进行加密存储，存在被非法访问的风险。',
    status: '处理中',
    occurAt: '2025-08-20 17:30',
    riskType: '存储安全',
    harm: '中',
    probability: '中',
    suggestion: '实施全盘加密；部署密钥管理系统；定期进行安全审计。',
  },
  {
    id: 'CR-202501-0010',
    enterprise: '重庆智能交通系统公司',
    enterpriseCode: '91500000MA5U8H7K2P',
    enterpriseType: '地图服务商',
    stage: '传输',
    level: '高',
    description: 'API接口未实施身份认证，任何人都可以访问敏感地理数据。',
    status: '未处理',
    occurAt: '2025-08-21 09:10',
    riskType: 'API安全',
    harm: '高',
    probability: '高',
    suggestion: '立即实施API认证机制；部署API网关；监控异常访问；定期安全评估。',
  },
  {
    id: 'CR-202501-0011',
    enterprise: '西安高新智驾科技公司',
    enterpriseCode: '91610131MA6TGMRF9X',
    enterpriseType: '智驾方案提供商',
    stage: '加工',
    level: '中',
    description: '数据处理过程中发现算法存在缺陷，可能导致隐私信息泄露。',
    status: '处理中',
    occurAt: '2025-08-21 14:55',
    riskType: '算法安全',
    harm: '中',
    probability: '中',
    suggestion: '修复算法缺陷；加强算法安全评估；实施差分隐私技术。',
  },
  {
    id: 'CR-202501-0012',
    enterprise: '苏州智慧车联网公司',
    enterpriseCode: '91320500MA1XRDNM91',
    enterpriseType: '整车生产企业',
    stage: '提供',
    level: '低',
    description: '数据提供协议条款不完善，可能导致数据使用范围失控。',
    status: '已处理',
    occurAt: '2025-08-17 16:40',
    resolvedAt: '2025-08-18 14:30',
    riskType: '合规风险',
    harm: '低',
    probability: '中',
    suggestion: '已完善数据提供协议；明确数据使用范围和期限；建立数据使用监督机制。',
  },
  {
    id: 'CR-202501-0013',
    enterprise: '青岛海洋智行科技公司',
    enterpriseCode: '91370200MA3QHTN42W',
    enterpriseType: '平台运营商',
    stage: '收集',
    level: '中',
    description: '车辆在海岸线敏感区域进行高频数据采集，可能涉及海防安全。',
    status: '未处理',
    occurAt: '2025-08-21 10:25',
    riskType: '敏感采集',
    harm: '中',
    probability: '高',
    suggestion: '限制敏感区域采集频率；申请特殊区域采集许可；加强数据安全管理。',
  },
  {
    id: 'CR-202501-0014',
    enterprise: '合肥智能驾驶研究院',
    enterpriseCode: '91340100MA2TYKRM8E',
    enterpriseType: '地图服务商',
    stage: '公开',
    level: '高',
    description: '研究论文中公开了精确的敏感设施坐标信息，严重违反保密规定。',
    status: '处理中',
    occurAt: '2025-08-20 19:45',
    riskType: '保密违规',
    harm: '高',
    probability: '低',
    suggestion: '紧急撤回论文；审查所有公开资料；加强保密意识培训；建立发布前审核机制。',
  },
])

// 顶部统计数据 - 七阶段统计
const stageStatsData = computed(() => {
  const stages: CloudStage[] = ['收集', '存储', '传输', '加工', '提供', '公开', '销毁']
  const count: Record<CloudStage, number> = {
    收集: 0,
    存储: 0,
    传输: 0,
    加工: 0,
    提供: 0,
    公开: 0,
    销毁: 0,
  }
  for (const r of riskItems.value) count[r.stage]++
  return stages.map((k) => ({
    name: k,
    value: count[k],
    description: `${k}阶段风险数量`,
  }))
})

// 风险等级分布数据
const levelStatsData = computed(() => {
  const count: Record<RiskLevel, number> = { 高: 0, 中: 0, 低: 0 }
  for (const r of riskItems.value) count[r.level]++
  return (Object.keys(count) as RiskLevel[]).map((k) => ({
    name: `${k}风险`,
    value: count[k],
    description: `${k}风险等级事件数量`,
  }))
})
const totalLevel = computed(() => levelStatsData.value.reduce((s, i) => s + i.value, 0))

// 处置状态数据
const statusStatsData = computed(() => {
  const count: Record<HandleStatus, number> = { 未处理: 0, 处理中: 0, 已处理: 0 }
  for (const r of riskItems.value) count[r.status]++
  return (Object.keys(count) as HandleStatus[]).map((k) => ({
    name: k,
    value: count[k],
    description: `${k}状态的风险数量`,
  }))
})
const totalStatus = computed(() => statusStatsData.value.reduce((s, i) => s + i.value, 0))

// 处置率数据
const handleRate = computed(() => {
  const total = riskItems.value.length
  const handled = riskItems.value.filter((r) => r.status === '已处理').length
  return total === 0 ? 0 : Math.round((handled / total) * 100)
})
const todayHandled = 2 // mock
const handleRateData = computed(() => [
  { name: '已处置', value: handleRate.value, description: '已完成处置的风险占比' },
  { name: '未处置', value: 100 - handleRate.value, description: '尚未处置的风险占比' },
])

// 过滤与分页
const filtered = computed(() => {
  const { enterprise, enterpriseType, stage, status, level, timeRange } = filters.value
  return riskItems.value.filter((r) => {
    if (enterprise && !r.enterprise.includes(enterprise)) return false
    if (enterpriseType !== 'ALL' && r.enterpriseType !== enterpriseType) return false
    if (stage !== 'ALL' && r.stage !== stage) return false
    if (status !== 'ALL' && r.status !== status) return false
    if (level !== 'ALL' && r.level !== level) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
        0,
        0,
        0,
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const occur = new Date(r.occurAt.replace(/-/g, '/')).getTime()
      if (occur < start || occur > end) return false
    }
    return true
  })
})
const filteredTotal = computed(() => filtered.value.length)

const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = computed(() => Math.max(1, Math.ceil(filteredTotal.value / pageSize.value)))
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.value.slice(start, start + pageSize.value)
})

const goPage = (p: number) => {
  if (p < 1 || p > totalPages.value) return
  currentPage.value = p
}
const onPageSizeChange = (val: string) => {
  const n = parseInt(val)
  if (!Number.isNaN(n) && n > 0) {
    pageSize.value = n
    currentPage.value = 1
  }
}

// 详情弹窗
const detailOpen = ref(false)
const selectedRisk = ref<CloudRiskItem | null>(null)
const openDetail = (r: CloudRiskItem) => {
  selectedRisk.value = r
  detailOpen.value = true
}

// 文案统一：显示高/中/低
const formatLevel = (lv: string) => {
  if (lv === '特别重大') return '高'
  if (lv === '重大') return '中'
  if (lv === '一般') return '低'
  return lv
}
// 溯源参数映射：列表“高/中/低”映射为“特别重大/重大/一般”
const levelToEventLevel = (lv: RiskLevel) => {
  if (lv === '高') return '特别重大'
  if (lv === '中') return '重大'
  return '一般'
}
const goTrace = (r: CloudRiskItem) => {
  router.push({
    path: '/gov/trace/cloud',
    query: {
      external: '1',
      eventId: r.id,
      enterprise: r.enterprise,
      eventLevel: levelToEventLevel(r.level),
      desc: r.description,
    },
  })
}

// 导出 CSV
const exportCsv = () => {
  const headers = [
    '企业名称',
    '企业代码',
    '企业类型',
    '处理阶段',
    '风险等级',
    '风险描述',
    '当前状态',
    '发生时间',
    '处置完成时间',
  ]
  const rows = filtered.value.map((r) => [
    r.enterprise,
    r.enterpriseCode,
    r.enterpriseType,
    r.stage,
    r.level,
    r.description.replace(/\n/g, ' '),
    r.status,
    r.occurAt,
    r.resolvedAt || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `云端安全风险_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}
const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}

// Badge 样式
const levelVariant = (lv: RiskLevel) => {
  switch (lv) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}
const statusVariant = (st: HandleStatus) => {
  switch (st) {
    case '未处理':
      return 'outline'
    case '处理中':
      return 'secondary'
    case '已处理':
      return 'default'
    default:
      return 'outline'
  }
}
// 统一给文本等级做 variant 映射
const levelVariantDisplay = (lv: string) => {
  const d = formatLevel(lv)
  switch (d as RiskLevel) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}
</script>
