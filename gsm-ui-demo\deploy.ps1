# 构建项目
Write-Host "Building project..." -ForegroundColor Green
npm run build-only

# 部署到 Cloudbase
Write-Host "Deploying to Cloudbase..." -ForegroundColor Green
tcb hosting deploy dist -e cloud1-0gc8cbzg3efd6a99 --mode dir

# 提示手动配置路由规则
Write-Host "`n" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Yellow
Write-Host "部署完成！但需要在腾讯云控制台配置路由规则：" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Yellow
Write-Host "1. 登录腾讯云控制台" -ForegroundColor Cyan
Write-Host "2. 进入 CloudBase -> 静态网站托管" -ForegroundColor Cyan
Write-Host "3. 找到'路由配置'或'重定向规则'" -ForegroundColor Cyan
Write-Host "4. 添加以下规则：" -ForegroundColor Cyan
Write-Host "   源路径: /*" -ForegroundColor Green
Write-Host "   目标路径: /index.html" -ForegroundColor Green
Write-Host "   状态码: 200" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Yellow
Write-Host "访问地址: https://cloud1-0gc8cbzg3efd6a99-1251221636.tcloudbaseapp.com" -ForegroundColor Green
