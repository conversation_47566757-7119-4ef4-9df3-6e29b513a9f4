#!/usr/bin/env node
import { readFile, writeFile, mkdir } from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

async function injectPreload(html) {
  const preloadLines = [
    '<link rel="preload" as="image" href="/axure/images/u103.png" />',
    '<link rel="preload" as="image" href="/axure/images/u513.png" />',
    '<link rel="preload" as="image" href="/axure/images/u554.png" />',
  ]
  const injectBlock = '  ' + preloadLines.join('\n  ') + '\n'
  return html.replace(/<head(\s*)>/i, (m) => `${m}\n${injectBlock}`)
}

// 收集 SPA 路由（从 src/router/index.ts 提取 path/redirect 字段），并生成所有父级目录
async function collectSpaRouteDirs(projectRoot) {
  const routerFile = path.join(projectRoot, 'src', 'router', 'index.ts')
  let text = ''
  try {
    text = await readFile(routerFile, 'utf8')
  } catch (err) {
    console.warn('[optimize-iframe] Router file not found, skip SPA route shadow generation:', err?.message || err)
    return new Set()
  }

  const paths = new Set()
  const addPathWithAncestors = (p) => {
    if (!p || p === '/' || /\.[a-zA-Z0-9]+$/.test(p)) return
    if (!p.startsWith('/')) p = '/' + p
    // 去掉尾部斜杠，处理动态段与通配符
    p = p.replace(/\/+$/g, '').replace(/\/:([^/]+)/g, '/x').replace(/\*+/g, 'x')
    const segs = p.split('/').filter(Boolean)
    for (let i = 1; i <= segs.length; i++) {
      const sub = '/' + segs.slice(0, i).join('/')
      paths.add(sub)
    }
  }

  const pathRegex = /\bpath\s*:\s*['"]([^'\"]+)['"]/g
  const redirectRegex = /\bredirect\s*:\s*['"]([^'\"]+)['"]/g
  let m
  while ((m = pathRegex.exec(text))) addPathWithAncestors(m[1])
  while ((m = redirectRegex.exec(text))) addPathWithAncestors(m[1])

  // 常见顶级前缀兜底
  ;['/gov', '/corp', '/government', '/enterprise'].forEach(addPathWithAncestors)
  return paths
}

async function minifyHtml(html) {
  try {
    const { minify } = await import('html-minifier-terser')
    return minify(html, {
      collapseWhitespace: true,
      removeComments: true,
      removeRedundantAttributes: true,
      useShortDoctype: true,
      minifyCSS: true,
      minifyJS: true,
      sortAttributes: true,
      sortClassName: true,
    })
  } catch {
    console.warn('[optimize-iframe] html-minifier-terser not available, skip html minify')
    return html
  }
}

async function optimizeImages(distRoot) {
  // 动态导入 imagemin 及插件；若不可用则跳过图片优化
  try {
    const { default: imagemin } = await import('imagemin')
    const { default: imageminPngquant } = await import('imagemin-pngquant')
    const pngDir = path.join(distRoot, 'axure', 'images')
    const patterns = [path.join(pngDir, '*.png')]
    await imagemin(patterns, {
      destination: pngDir,
      plugins: [
        imageminPngquant({
          quality: [0.6, 0.8],
          speed: 3,
        }),
      ],
    })
    console.log('[optimize-iframe] Optimized PNG images under:', path.relative(distRoot, pngDir))
  } catch {
    console.warn('[optimize-iframe] imagemin or pngquant not available, skip image optimization')
  }
}

// 为所有路由目录批量生成 index.html 与 404.html（内容同根 index.html），实现“全目录深链”兜底
async function createShadowRouteFiles(distRoot, projectRoot, indexHtml) {
  const routes = await collectSpaRouteDirs(projectRoot)
  if (!routes || routes.size === 0) return

  let count = 0
  for (const p of routes) {
    const dir = path.join(distRoot, p.replace(/^\//, ''))
    try {
      await mkdir(dir, { recursive: true })
      await writeFile(path.join(dir, 'index.html'), indexHtml, 'utf8')
      await writeFile(path.join(dir, '404.html'), indexHtml, 'utf8')
      count++
    } catch (err) {
      console.warn('[optimize-iframe] Skip writing shadow route for', p, err?.message || err)
    }
  }
  console.log(`[optimize-iframe] Created shadow routes for ${count} directories`)
}

async function main() {
  const __dirname = path.dirname(fileURLToPath(import.meta.url))
  const projectRoot = path.resolve(__dirname, '..')
  const distRoot = path.join(projectRoot, 'dist')

  const htmlPath = path.join(distRoot, 'iframe', 'government-screen.html')
  let html
  try {
    html = await readFile(htmlPath, 'utf8')
  } catch {
    console.warn(`[optimize-iframe] File not found: ${path.relative(projectRoot, htmlPath)} (skip)`) 
    return
  }

  // 先注入预加载，再压缩（若工具可用）
  const injected = await injectPreload(html)
  const minified = await minifyHtml(injected)
  await writeFile(htmlPath, minified, 'utf8')
  console.log('[optimize-iframe] Minified HTML and injected preloads:', path.relative(projectRoot, htmlPath))

  // 创建无后缀目录路由别名：/government-screen => /government-screen/index.html
  const aliasDir = path.join(distRoot, 'government-screen')
  await mkdir(aliasDir, { recursive: true })
  await writeFile(path.join(aliasDir, 'index.html'), minified, 'utf8')
  console.log('[optimize-iframe] Created alias route at:', path.relative(projectRoot, path.join(aliasDir, 'index.html')))

  // B 方案（推荐）：将 dist/index.html 同步为 dist/404.html，交由托管层以“返回错误页面”方式作为 404 页返回，实现 SPA 深链回退
  try {
    const indexHtmlPath = path.join(distRoot, 'index.html')
    const indexHtml = await readFile(indexHtmlPath, 'utf8')
    const notFoundPath = path.join(distRoot, '404.html')
    await writeFile(notFoundPath, indexHtml, 'utf8')
    console.log('[optimize-iframe] Synced 404.html from index.html')

    // C 方案（物理冗余）：为所有 SPA 路由目录批量生成 index.html + 404.html，覆盖“系统内所有目录”的深链
    await createShadowRouteFiles(distRoot, projectRoot, indexHtml)
  } catch (err) {
    console.warn('[optimize-iframe] Skip syncing 404.html (dist/index.html not found)', err?.message || err)
  }

  // 尝试优化图片（可选）
  await optimizeImages(distRoot)
}

main().catch((err) => {
  console.error('[optimize-iframe] Failed:', err)
  process.exit(1)
})

