#!/usr/bin/env node
import { readFile, writeFile } from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

async function injectPreload(html) {
  const preloadLines = [
    '<link rel="preload" as="image" href="/axure/images/u103.png" />',
    '<link rel="preload" as="image" href="/axure/images/u513.png" />',
    '<link rel="preload" as="image" href="/axure/images/u554.png" />',
  ]
  const injectBlock = '  ' + preloadLines.join('\n  ') + '\n'
  return html.replace(/<head(\s*)>/i, (m) => `${m}\n${injectBlock}`)
}

async function minifyHtml(html) {
  try {
    const { minify } = await import('html-minifier-terser')
    return minify(html, {
      collapseWhitespace: true,
      removeComments: true,
      removeRedundantAttributes: true,
      useShortDoctype: true,
      minifyCSS: true,
      minifyJS: true,
      sortAttributes: true,
      sortClassName: true,
    })
  } catch {
    console.warn('[optimize-iframe] html-minifier-terser not available, skip html minify')
    return html
  }
}

async function optimizeImages(distRoot) {
  // 动态导入 imagemin 及插件；若不可用则跳过图片优化
  try {
    const { default: imagemin } = await import('imagemin')
    const { default: imageminPngquant } = await import('imagemin-pngquant')
    const pngDir = path.join(distRoot, 'axure', 'images')
    const patterns = [path.join(pngDir, '*.png')]
    await imagemin(patterns, {
      destination: pngDir,
      plugins: [
        imageminPngquant({
          quality: [0.6, 0.8],
          speed: 3,
        }),
      ],
    })
    console.log('[optimize-iframe] Optimized PNG images under:', path.relative(distRoot, pngDir))
  } catch {
    console.warn('[optimize-iframe] imagemin or pngquant not available, skip image optimization')
  }
}

async function main() {
  const __dirname = path.dirname(fileURLToPath(import.meta.url))
  const projectRoot = path.resolve(__dirname, '..')
  const distRoot = path.join(projectRoot, 'dist')

  const htmlPath = path.join(distRoot, 'iframe', 'government-screen.html')
  let html
  try {
    html = await readFile(htmlPath, 'utf8')
  } catch {
    console.warn(`[optimize-iframe] File not found: ${path.relative(projectRoot, htmlPath)} (skip)`) 
    return
  }

  // 先注入预加载，再压缩（若工具可用）
  const injected = await injectPreload(html)
  const minified = await minifyHtml(injected)
  await writeFile(htmlPath, minified, 'utf8')
  console.log('[optimize-iframe] Minified HTML and injected preloads:', path.relative(projectRoot, htmlPath))

  // 尝试优化图片（可选）
  await optimizeImages(distRoot)
}

main().catch((err) => {
  console.error('[optimize-iframe] Failed:', err)
  process.exit(1)
})

