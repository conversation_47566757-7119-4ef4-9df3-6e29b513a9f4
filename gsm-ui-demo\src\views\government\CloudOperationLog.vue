<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          云端操作日志的记录、查询与分析，支持七阶段处理流程全量展示
        </p>
      </div>
    </div>

    <!-- 列表 -->
    <Card>
      <CardHeader>
        <CardTitle>云端操作日志列表</CardTitle>
        <CardDescription>
          共 {{ filteredTotal }} 条记录，当前显示第
          {{ pageSize * (currentPage - 1) + 1 }}
          -
          {{ Math.min(pageSize * currentPage, filteredTotal) }}
          条
        </CardDescription>
      </CardHeader>
      <CardContent>
        <!-- 查询表单 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportCsv"
        />
        <div class="rounded-md border mt-4 overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[60px]">序号</TableHead>
                <TableHead class="min-w-[150px]">企业名称</TableHead>
                <TableHead class="min-w-[120px]">企业类型</TableHead>
                <TableHead class="min-w-[80px]">处理阶段</TableHead>
                <TableHead class="min-w-[100px]">操作类型</TableHead>
                <TableHead class="min-w-[200px]">操作详情</TableHead>
                <TableHead class="min-w-[140px]">操作时间</TableHead>
                <TableHead class="min-w-[80px]">操作人</TableHead>
                <TableHead class="min-w-[100px]">来源IP</TableHead>
                <TableHead class="min-w-[100px]">数据类别</TableHead>
                <TableHead class="min-w-[80px]">数据量</TableHead>
                <TableHead class="min-w-[100px]">目标系统</TableHead>
                <TableHead class="min-w-[80px]">操作结果</TableHead>
                <TableHead class="min-w-[80px]">耗时</TableHead>
                <TableHead class="min-w-[150px]">错误信息</TableHead>
                <TableHead class="min-w-[120px]">追踪ID</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedItems.length === 0">
                <TableCell :colspan="16" class="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedItems"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.enterprise }}</div>
                    <div class="text-xs text-muted-foreground">{{ item.enterpriseCode }}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">{{ item.enterpriseType }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{{ item.stage }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{{ item.operationType }}</Badge>
                </TableCell>
                <TableCell class="max-w-[300px]">
                  <div class="text-sm line-clamp-2" :title="item.operationDetail">
                    {{ item.operationDetail }}
                  </div>
                </TableCell>
                <TableCell class="whitespace-nowrap text-xs">{{ item.operationTime }}</TableCell>
                <TableCell>{{ item.operator || '-' }}</TableCell>
                <TableCell class="font-mono text-xs">{{ maskIP(item.sourceIP) }}</TableCell>
                <TableCell>{{ item.dataCategory || '-' }}</TableCell>
                <TableCell>{{ item.dataSize || '-' }}</TableCell>
                <TableCell>{{ item.targetSystem || '-' }}</TableCell>
                <TableCell>
                  <Badge :variant="item.result === '成功' ? 'default' : 'destructive'">
                    {{ item.result }}
                  </Badge>
                </TableCell>
                <TableCell>{{ item.duration || '-' }}</TableCell>
                <TableCell class="max-w-[200px]">
                  <div
                    v-if="item.errorMsg"
                    class="text-xs text-destructive line-clamp-2"
                    :title="item.errorMsg"
                  >
                    {{ item.errorMsg }}
                  </div>
                  <span v-else class="text-muted-foreground">-</span>
                </TableCell>
                <TableCell class="font-mono text-xs">{{ item.traceId }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页：统一 PaginationBar -->
        <PaginationBar v-model:page="currentPage" v-model:pageSize="pageSize" :total="filteredTotal" />
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { PaginationBar } from '@/components/ui/pagination'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type CloudStage = '收集' | '存储' | '传输' | '加工' | '提供' | '公开' | '销毁'
type OperationResult = '成功' | '失败'
type EnterpriseType = '整车生产企业' | '平台运营商' | '智驾方案提供商' | '地图服务商' | '其他'

interface CloudOperationLog {
  id: string
  enterprise: string
  enterpriseCode: string
  enterpriseType: EnterpriseType
  stage: CloudStage
  operationType: string
  operationDetail: string
  operationTime: string
  operator?: string
  sourceIP?: string
  dataCategory?: string
  dataSize?: string
  targetSystem?: string
  result: OperationResult
  errorMsg?: string
  duration?: string
  traceId?: string
}

interface Filters {
  enterprise: string
  enterpriseType: 'ALL' | EnterpriseType
  stage: 'ALL' | CloudStage
  operationType: string
  dataCategory: string
  result: 'ALL' | OperationResult
  timeRange: [Date, Date] | null
}

// 筛选器
const filters = ref<Filters>({
  enterprise: '',
  enterpriseType: 'ALL',
  stage: 'ALL',
  operationType: 'ALL',
  dataCategory: 'ALL',
  result: 'ALL',
  timeRange: null,
})

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'enterprise',
    label: '企业名称',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'enterpriseType',
    label: '企业类型',
    type: 'select',
    placeholder: '请选择企业类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '整车生产企业', value: '整车生产企业' },
      { label: '平台运营商', value: '平台运营商' },
      { label: '智驾方案提供商', value: '智驾方案提供商' },
      { label: '地图服务商', value: '地图服务商' },
      { label: '其他', value: '其他' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'stage',
    label: '处理阶段',
    type: 'select',
    placeholder: '请选择处理阶段',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '收集', value: '收集' },
      { label: '存储', value: '存储' },
      { label: '传输', value: '传输' },
      { label: '加工', value: '加工' },
      { label: '提供', value: '提供' },
      { label: '公开', value: '公开' },
      { label: '销毁', value: '销毁' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'operationType',
    label: '操作类型',
    type: 'select',
    placeholder: '请选择操作类型',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '数据接收', value: '数据接收' },
      { label: '数据存储', value: '数据存储' },
      { label: '数据加工', value: '数据加工' },
      { label: '数据脱敏', value: '数据脱敏' },
      { label: '数据提供', value: '数据提供' },
      { label: '数据公开', value: '数据公开' },
      { label: '数据销毁', value: '数据销毁' },
      { label: '权限变更', value: '权限变更' },
      { label: '配置更新', value: '配置更新' },
      { label: '系统维护', value: '系统维护' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'dataCategory',
    label: '数据类别',
    type: 'select',
    placeholder: '请选择数据类别',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '位置数据', value: '位置数据' },
      { label: '轨迹数据', value: '轨迹数据' },
      { label: '地图数据', value: '地图数据' },
      { label: '环境感知数据', value: '环境感知数据' },
      { label: '车辆状态数据', value: '车辆状态数据' },
      { label: '用户行为数据', value: '用户行为数据' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'result',
    label: '操作结果',
    type: 'select',
    placeholder: '请选择操作结果',
    options: [
      { label: '全部', value: 'ALL' },
      { label: '成功', value: '成功' },
      { label: '失败', value: '失败' },
    ],
    defaultValue: 'ALL',
  },
  {
    key: 'timeRange',
    label: '操作时间范围',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  console.log('搜索条件:', filters.value)
}

const resetFilters = () => {
  filters.value = {
    enterprise: '',
    enterpriseType: 'ALL',
    stage: 'ALL',
    operationType: 'ALL',
    dataCategory: 'ALL',
    result: 'ALL',
    timeRange: null,
  }
}

// Mock 数据
const operationLogs = ref<CloudOperationLog[]>([
  {
    id: 'CLOG-202501-0001',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    enterpriseType: '平台运营商',
    stage: '收集',
    operationType: '数据接收',
    operationDetail: '接收来自车端的地理信息数据包，批次号：BATCH-20250821-001',
    operationTime: '2025-08-21 10:00:15',
    operator: 'data_receiver',
    sourceIP: '**************',
    dataCategory: '位置数据',
    dataSize: '128.5MB',
    targetSystem: '数据接收平台',
    result: '成功',
    duration: '3.2s',
    traceId: 'CTRC-20250821-100015',
  },
  {
    id: 'CLOG-202501-0002',
    enterprise: '上海车联网服务有限公司',
    enterpriseCode: '91310000987654321Y',
    enterpriseType: '地图服务商',
    stage: '存储',
    operationType: '数据存储',
    operationDetail: '将处理后的地图数据存储至分布式存储系统，应用加密策略',
    operationTime: '2025-08-21 10:15:30',
    operator: 'storage_service',
    sourceIP: '*************',
    dataCategory: '地图数据',
    dataSize: '256.8MB',
    targetSystem: 'HDFS集群',
    result: '成功',
    duration: '5.6s',
    traceId: 'CTRC-20250821-101530',
  },
  {
    id: 'CLOG-202501-0003',
    enterprise: '深圳自动驾驶技术有限公司',
    enterpriseCode: '91440300567890123Z',
    enterpriseType: '智驾方案提供商',
    stage: '加工',
    operationType: '数据脱敏',
    operationDetail: '执行地理位置数据脱敏处理，应用差分隐私算法',
    operationTime: '2025-08-21 10:30:45',
    operator: 'privacy_engine',
    sourceIP: '**************',
    dataCategory: '轨迹数据',
    dataSize: '89.3MB',
    targetSystem: '数据脱敏系统',
    result: '成功',
    duration: '12.8s',
    traceId: 'CTRC-20250821-103045',
  },
  {
    id: 'CLOG-202501-0004',
    enterprise: '北京智行科技有限公司',
    enterpriseCode: '91110000123456789X',
    enterpriseType: '平台运营商',
    stage: '传输',
    operationType: '数据传输',
    operationDetail: '向合作伙伴传输处理后的环境感知数据，使用安全传输通道',
    operationTime: '2025-08-21 11:00:00',
    operator: 'transfer_service',
    sourceIP: '**************',
    dataCategory: '环境感知数据',
    dataSize: '456.2MB',
    targetSystem: '合作伙伴接口',
    result: '失败',
    errorMsg: '目标系统连接超时，传输任务中断，错误代码：TRANS_TIMEOUT_002',
    duration: '30.0s',
    traceId: 'CTRC-20250821-110000',
  },
  {
    id: 'CLOG-202501-0005',
    enterprise: '广州汽车制造有限公司',
    enterpriseCode: '91440000456789012A',
    enterpriseType: '整车生产企业',
    stage: '提供',
    operationType: '数据提供',
    operationDetail: '向授权第三方提供脱敏后的车辆状态数据，协议号：PROV-20250821-001',
    operationTime: '2025-08-21 11:30:20',
    operator: 'api_gateway',
    sourceIP: '**************',
    dataCategory: '车辆状态数据',
    dataSize: '67.4MB',
    targetSystem: '第三方API网关',
    result: '成功',
    duration: '2.1s',
    traceId: 'CTRC-20250821-113020',
  },
  {
    id: 'CLOG-202501-0006',
    enterprise: '杭州智能交通有限公司',
    enterpriseCode: '91330000789012345B',
    enterpriseType: '平台运营商',
    stage: '公开',
    operationType: '数据公开',
    operationDetail: '发布统计后的交通流量热力图数据至公开平台',
    operationTime: '2025-08-21 12:00:00',
    operator: 'publish_service',
    sourceIP: '***************',
    dataCategory: '轨迹数据',
    dataSize: '12.8MB',
    targetSystem: '公开数据平台',
    result: '成功',
    duration: '1.5s',
    traceId: 'CTRC-20250821-120000',
  },
  {
    id: 'CLOG-202501-0007',
    enterprise: '成都新能源汽车有限公司',
    enterpriseCode: '91510000234567890C',
    enterpriseType: '整车生产企业',
    stage: '销毁',
    operationType: '数据销毁',
    operationDetail: '执行过期数据销毁任务，清理90天前的原始位置数据',
    operationTime: '2025-08-21 13:00:00',
    operator: 'cleanup_scheduler',
    sourceIP: '**************',
    dataCategory: '位置数据',
    dataSize: '2.3GB',
    targetSystem: '数据生命周期管理系统',
    result: '成功',
    duration: '45.6s',
    traceId: 'CTRC-20250821-130000',
  },
  {
    id: 'CLOG-202501-0008',
    enterprise: '南京科技创新有限公司',
    enterpriseCode: '91320000345678901D',
    enterpriseType: '地图服务商',
    stage: '加工',
    operationType: '数据加工',
    operationDetail: '执行高精地图数据融合处理，生成路网更新包',
    operationTime: '2025-08-21 14:15:30',
    operator: 'map_processor',
    sourceIP: '**************',
    dataCategory: '地图数据',
    dataSize: '512.6MB',
    targetSystem: '地图处理引擎',
    result: '成功',
    duration: '28.9s',
    traceId: 'CTRC-20250821-141530',
  },
  {
    id: 'CLOG-202501-0009',
    enterprise: '天津智驾科技有限公司',
    enterpriseCode: '91120000456789012E',
    enterpriseType: '智驾方案提供商',
    stage: '存储',
    operationType: '权限变更',
    operationDetail: '更新数据访问权限策略，限制敏感数据访问范围',
    operationTime: '2025-08-21 15:00:45',
    operator: 'security_admin',
    sourceIP: '*************',
    targetSystem: '权限管理系统',
    result: '成功',
    duration: '0.8s',
    traceId: 'CTRC-20250821-150045',
  },
  {
    id: 'CLOG-202501-0010',
    enterprise: '武汉光谷汽车有限公司',
    enterpriseCode: '91420000567890123F',
    enterpriseType: '整车生产企业',
    stage: '传输',
    operationType: '配置更新',
    operationDetail: '更新数据传输加密配置，升级TLS版本至1.3',
    operationTime: '2025-08-21 16:30:00',
    operator: 'config_manager',
    sourceIP: '*************',
    targetSystem: '传输网关',
    result: '成功',
    duration: '1.2s',
    traceId: 'CTRC-20250821-163000',
  },
  {
    id: 'CLOG-202501-0011',
    enterprise: '重庆智慧城市有限公司',
    enterpriseCode: '91500000678901234G',
    enterpriseType: '平台运营商',
    stage: '收集',
    operationType: '系统维护',
    operationDetail: '数据收集服务定期维护，清理缓存并优化性能',
    operationTime: '2025-08-21 17:00:00',
    operator: 'maintenance_bot',
    sourceIP: '**************',
    targetSystem: '数据收集服务',
    result: '成功',
    duration: '15.3s',
    traceId: 'CTRC-20250821-170000',
  },
  {
    id: 'CLOG-202501-0012',
    enterprise: '西安高新技术有限公司',
    enterpriseCode: '91610000789012345H',
    enterpriseType: '其他',
    stage: '加工',
    operationType: '数据加工',
    operationDetail: '执行用户行为数据分析，生成驾驶习惯报告',
    operationTime: '2025-08-21 17:30:15',
    operator: 'analytics_engine',
    sourceIP: '**************',
    dataCategory: '用户行为数据',
    dataSize: '34.7MB',
    targetSystem: '数据分析平台',
    result: '失败',
    errorMsg: '数据格式异常，分析任务失败，缺少必要字段：user_id',
    duration: '8.5s',
    traceId: 'CTRC-20250821-173015',
  },
])

// IP地址脱敏
const maskIP = (ip?: string) => {
  if (!ip) return '-'
  const parts = ip.split('.')
  if (parts.length !== 4) return ip
  return `${parts[0]}.${parts[1]}.***.***`
}

// 过滤与分页
const filtered = computed(() => {
  const { enterprise, enterpriseType, stage, operationType, dataCategory, result, timeRange } =
    filters.value
  return operationLogs.value.filter((log) => {
    if (enterprise && !log.enterprise.includes(enterprise)) return false
    if (enterpriseType !== 'ALL' && log.enterpriseType !== enterpriseType) return false
    if (stage !== 'ALL' && log.stage !== stage) return false
    if (operationType !== 'ALL' && log.operationType !== operationType) return false
    if (dataCategory !== 'ALL' && log.dataCategory !== dataCategory) return false
    if (result !== 'ALL' && log.result !== result) return false
    if (timeRange && timeRange[0] && timeRange[1]) {
      const start = new Date(
        timeRange[0].getFullYear(),
        timeRange[0].getMonth(),
        timeRange[0].getDate(),
        0,
        0,
        0,
      ).getTime()
      const end = new Date(
        timeRange[1].getFullYear(),
        timeRange[1].getMonth(),
        timeRange[1].getDate(),
        23,
        59,
        59,
      ).getTime()
      const logTime = new Date(log.operationTime.replace(/-/g, '/')).getTime()
      if (logTime < start || logTime > end) return false
    }
    return true
  })
})
const filteredTotal = computed(() => filtered.value.length)

const currentPage = ref(1)
const pageSize = ref(10)
const totalPages = computed(() => Math.max(1, Math.ceil(filteredTotal.value / pageSize.value)))
const pagedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filtered.value.slice(start, start + pageSize.value)
})

const goPage = (p: number) => {
  if (p < 1 || p > totalPages.value) return
  currentPage.value = p
}
const onPageSizeChange = (val: string) => {
  const n = parseInt(val)
  if (!Number.isNaN(n) && n > 0) {
    pageSize.value = n
    currentPage.value = 1
  }
}

// 导出 CSV
const exportCsv = () => {
  const headers = [
    '企业名称',
    '企业代码',
    '企业类型',
    '处理阶段',
    '操作类型',
    '操作详情',
    '操作时间',
    '操作人',
    '来源IP',
    '数据类别',
    '数据量',
    '目标系统',
    '操作结果',
    '耗时',
    '错误信息',
    '追踪ID',
  ]
  const rows = filtered.value.map((log) => [
    log.enterprise,
    log.enterpriseCode,
    log.enterpriseType,
    log.stage,
    log.operationType,
    log.operationDetail.replace(/\n/g, ' '),
    log.operationTime,
    log.operator || '',
    log.sourceIP || '',
    log.dataCategory || '',
    log.dataSize || '',
    log.targetSystem || '',
    log.result,
    log.duration || '',
    log.errorMsg || '',
    log.traceId || '',
  ])
  const content = [headers, ...rows].map((arr) => arr.map(csvEscape).join(',')).join('\n')
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `云端操作日志_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const csvEscape = (s: string) => {
  const needsQuote = /[",\n]/.test(s)
  const body = s.replace(/"/g, '""')
  return needsQuote ? `"${body}"` : body
}
</script>
