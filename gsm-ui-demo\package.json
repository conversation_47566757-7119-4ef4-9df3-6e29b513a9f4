{"name": "gsm-ui-demo", "version": "0.1.1", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"predev": "node scripts/sync-vendors.mjs", "prebuild": "node scripts/sync-vendors.mjs", "dev": "vite", "build": "run-p type-check \"build-only {@}\" -- && node scripts/optimize-iframe.mjs", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "lint:check": "eslint .", "format": "prettier --write src/", "format:check": "prettier -c \"src/**/*.{ts,tsx,vue,js,json,md,css,scss,html}\"", "deploy": "tcb framework deploy", "deploy:cloudbase": "cloudbase framework deploy", "deploy:cbcli": "tcb hosting deploy dist / -e cloud1-0gc8cbzg3efd6a99", "hosting:config": "tcb framework deploy", "deploy:all": "npm run build && npm run hosting:config"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@vee-validate/zod": "^4.15.1", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vueuse/core": "^13.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "echarts": "^5.6.0", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "lucide": "0.539.0", "lucide-vue-next": "^0.539.0", "pinia": "^3.0.3", "radix-vue": "^1.9.17", "reka-ui": "^2.5.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vee-validate": "^4.15.1", "vue": "^3.5.18", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "vue-sonner": "^2.0.8", "vue3-puzzle-vcode": "^1.1.7", "yup": "^1.7.0", "zod": "^3.25.76"}, "devDependencies": {"@cloudbase/cli": "^2.9.3", "@iconify-json/radix-icons": "^1.2.4", "@iconify/vue": "^5.0.0", "@musistudio/claude-code-router": "^1.0.36", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "3.6.2", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}