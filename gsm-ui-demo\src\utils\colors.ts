// 颜色工具类 - 解决 TypeScript 颜色数组类型问题

export type ColorArray = string[]
export type ReadonlyColorArray = readonly string[]

/**
 * 将只读颜色数组转换为可变数组
 * 解决 TypeScript 中 readonly 数组无法赋值给 string[] 的问题
 */
export function toMutableColorArray(colors: ReadonlyColorArray): ColorArray {
  return [...colors]
}

/**
 * 确保颜色数组为可变类型
 */
export function ensureMutableColors(colors: ReadonlyColorArray | ColorArray): ColorArray {
  if (Array.isArray(colors)) {
    return Array.from(colors)
  }
  return colors as ColorArray
}

/**
 * 预定义的颜色方案
 */
export const COLOR_SCHEMES = {
  // 蓝色系
  blue: ['#1E5A8E', '#4472C4', '#5B9BD5', '#8FAADC', '#85B8E0', '#B4C7E7'],

  // 蓝色系 (5色)
  blueCompact: ['#1E5A8E', '#4472C4', '#5B9BD5', '#8FAADC', '#85B8E0'],

  // 深蓝色系
  darkBlue: ['#0F3A5F', '#1E5A8E', '#2E7CD6', '#4472C4', '#5B9BD5', '#8FAADC', '#85B8E0'],

  // 扩展色彩
  extended: [
    '#0F3A5F',
    '#1E5A8E',
    '#4472C4',
    '#5B9BD5',
    '#8FAADC',
    '#85B8E0',
    '#B4C7E7',
    '#C5D9F1',
  ],

  // 政府主题色
  government: ['#7c2946', '#3c2a4d', '#d97a2a', '#a34e2d', '#e6b422'],

  // 图表专用色彩
  chart: {
    pie: ['#d97a2a', '#e6b422', '#3c2a4d'],
    line: ['#7c2946', '#3c2a4d', '#d97a2a'],
    bar: ['#d97a2a', '#e6b422', '#3c2a4d'],
  },
} as const

/**
 * 获取可变的颜色方案
 */
export function getColorScheme(scheme: keyof typeof COLOR_SCHEMES): ColorArray {
  const colors = COLOR_SCHEMES[scheme]
  return ensureMutableColors(colors as ReadonlyColorArray)
}

/**
 * 获取图表特定颜色方案
 */
export function getChartColors(type: 'pie' | 'line' | 'bar'): ColorArray {
  return ensureMutableColors(COLOR_SCHEMES.chart[type])
}

/**
 * 动态生成颜色数组
 */
export function generateColorArray(baseColors: string[], count: number): ColorArray {
  const result: string[] = []
  for (let i = 0; i < count; i++) {
    result.push(baseColors[i % baseColors.length])
  }
  return result
}
