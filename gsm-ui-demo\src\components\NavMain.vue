<script setup lang="ts">
import type { LucideIcon } from 'lucide-vue-next'
import {
  ChevronRight,
  SquareTerminal,
  ClipboardCheck,
  ClipboardList,
  Activity,
  Settings2,
  AlertTriangle,
  Inbox,
  Users,
  LifeBuoy,
  Send,
  FileText,
  ShieldAlert,
  Car,
  Cloud,
  CloudLightning,
  Route,
  CloudCog,
  FileCog,
  ScrollText,
  Megaphone,
  UserCog,
  MapPinned,
  Settings,
  ShieldCheck,
  FileClock,
  FileEdit,
  Building2,
  Shield,
  Wrench,
  Database,
  FolderCog,
  BarChart3,
  Bell,
  ListTodo,
} from 'lucide-vue-next'

import { useRoute, useRouter } from 'vue-router'
// icon name -> component map (hoisted top-level)
const iconMap = {
  ChevronRight,
  SquareTerminal,
  ClipboardCheck,
  ClipboardList,
  Activity,
  Settings2,
  AlertTriangle,
  Inbox,
  Users,
  LifeBuoy,
  Send,
  FileText,
  ShieldAlert,
  Car,
  Cloud,
  CloudLightning,
  Route,
  CloudCog,
  FileCog,
  ScrollText,
  Megaphone,
  UserCog,
  MapPinned,
  <PERSON><PERSON>s,
  ShieldCheck,
  FileClock,
  FileEdit,
  Building2,
  Shield,
  Wrench,
  Database,
  FolderCog,
  BarChart3,
  Bell,
  ListTodo,
} as const

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import type { SidebarMenuItem as MenuItemType } from '@/config/sidebar-menu'

interface NavMainProps {
  items: {
    title: string
    url: string
    icon: LucideIcon
    isActive?: boolean
    items?: MenuItemType[]
  }[]
}

const props = defineProps<NavMainProps>()
const router = useRouter()
const route = useRoute()

// 检查路径是否匹配当前路由
function isPathActive(path: string): boolean {
  return route.path === path || route.path.startsWith(path + '/')
}

// 检查菜单项是否有活动的子项
function hasActiveChild(item: MenuItemType): boolean {
  if (item.to && isPathActive(item.to)) return true
  if (item.children) {
    // icon name -> component map (hoisted top-level)

    return item.children.some((child) => hasActiveChild(child))
  }
  return false
}

// 处理导航点击
function handleNavigation(item: MenuItemType) {
  if (item.to) {
    router.push(item.to)
  } else if (item.children && item.children.length > 0) {
    // 如果没有直接路由但有子项，导航到第一个子项
    const firstChildWithRoute = findFirstRouteInChildren(item.children)
    if (firstChildWithRoute) {
      router.push(firstChildWithRoute)
    }
  }
}

// 递归查找第一个有路由的子项
function findFirstRouteInChildren(children: MenuItemType[]): string | null {
  for (const child of children) {
    if (child.to) return child.to
    if (child.children) {
      const found = findFirstRouteInChildren(child.children)
      if (found) return found
    }
  }
  return null
}

// 获取菜单项对应的图标
function getMenuIcon(
  menuItem: Partial<import('@/config/sidebar-menu').SidebarMenuItem>,
  mainItem: { icon?: unknown },
) {
  const itemIconName = menuItem.icon as keyof typeof iconMap | undefined
  if (itemIconName && iconMap[itemIconName]) {
    return iconMap[itemIconName]
  }
  return (mainItem.icon as typeof SquareTerminal | undefined) || SquareTerminal
}
</script>

<template>
  <SidebarGroup>
    <SidebarGroupLabel data-sidebar="group-label">
      {{ props.items[0]?.title || '菜单' }}
    </SidebarGroupLabel>

    <SidebarMenu>
      <!-- 遍历主菜单的子项，每个子项都是一级菜单 -->
      <template v-for="mainItem in items" :key="mainItem.title">
        <template v-for="menuItem in mainItem.items" :key="menuItem.title">
          <!-- 有子项的一级菜单项（如：风险管理） -->
          <Collapsible
            v-if="menuItem.children && menuItem.children.length > 0"
            as-child
            :default-open="true"
          >
            <SidebarMenuItem>
              <SidebarMenuButton
                as-child
                size="lg"
                data-sidebar="menu-button"
                :data-active="hasActiveChild(menuItem)"
              >
                <button
                  type="button"
                  class="flex w-full items-center gap-3 px-3 py-3"
                  @click="handleNavigation(menuItem)"
                >
                  <component :is="getMenuIcon(menuItem, mainItem)" class="h-4 w-4 shrink-0" />
                  <span class="truncate font-medium text-base">{{ menuItem.title }}</span>
                </button>
              </SidebarMenuButton>

              <CollapsibleTrigger as-child>
                <SidebarMenuAction
                  class="data-[state=open]:rotate-90 transition-transform duration-200"
                >
                  <ChevronRight class="h-4 w-4" />
                  <span class="sr-only">切换</span>
                </SidebarMenuAction>
              </CollapsibleTrigger>

              <CollapsibleContent>
                <SidebarMenuSub class="space-y-1 mt-1">
                  <!-- 二级菜单项（如：车端风险管理） -->
                  <SidebarMenuSubItem
                    v-for="subItem in menuItem.children"
                    :key="subItem.title"
                    class="flex items-center"
                  >
                    <SidebarMenuSubButton
                      v-if="subItem.to"
                      as-child
                      data-sidebar="menu-sub-button"
                      :is-active="isPathActive(subItem.to)"
                      :data-active="isPathActive(subItem.to)"
                    >
                      <RouterLink :to="subItem.to" class="flex items-center gap-2 px-3 py-2.5">
                        <span class="truncate font-medium">{{ subItem.title }}</span>
                      </RouterLink>
                    </SidebarMenuSubButton>
                    <SidebarMenuBadge v-if="subItem.badge" class="ml-2 mr-1">
                      {{ subItem.badge }}
                    </SidebarMenuBadge>
                  </SidebarMenuSubItem>
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>

          <!-- 无子项的一级菜单项 -->
          <SidebarMenuItem v-else-if="menuItem.to">
            <SidebarMenuButton
              as-child
              size="lg"
              data-sidebar="menu-button"
              :is-active="isPathActive(menuItem.to)"
              :data-active="isPathActive(menuItem.to)"
            >
              <RouterLink :to="menuItem.to" class="flex items-center gap-3 px-3 py-3">
                <component :is="getMenuIcon(menuItem, mainItem)" class="h-4 w-4 shrink-0" />
                <span class="truncate font-medium text-base">{{ menuItem.title }}</span>
              </RouterLink>
            </SidebarMenuButton>
            <SidebarMenuBadge v-if="menuItem.badge" class="ml-2 mr-1">
              {{ menuItem.badge }}
            </SidebarMenuBadge>
          </SidebarMenuItem>

          <!-- 无路由无子项的菜单项 -->
          <SidebarMenuItem v-else>
            <SidebarMenuButton
              disabled
              size="lg"
              class="opacity-60 cursor-not-allowed"
              data-sidebar="menu-button"
            >
              <component :is="getMenuIcon(menuItem, mainItem)" class="h-4 w-4 shrink-0" />
              <span class="truncate px-3 py-3 text-base">{{ menuItem.title }}</span>
            </SidebarMenuButton>
            <SidebarMenuBadge v-if="menuItem.badge" class="ml-2 mr-1">
              {{ menuItem.badge }}
            </SidebarMenuBadge>
          </SidebarMenuItem>
        </template>
      </template>
    </SidebarMenu>
  </SidebarGroup>
</template>
