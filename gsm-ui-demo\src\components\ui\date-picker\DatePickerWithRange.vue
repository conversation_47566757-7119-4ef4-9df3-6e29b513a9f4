<template>
  <Popover v-model:open="isOpen">
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="
          cn(
            'w-full justify-start text-left font-normal',
            !modelValue && 'text-muted-foreground',
            className,
          )
        "
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        <template v-if="modelValue && modelValue[0] && modelValue[1]">
          {{ formatDate(modelValue[0]) }} - {{ formatDate(modelValue[1]) }}
        </template>
        <template v-else>
          {{ placeholder }}
        </template>
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0" align="start">
      <div class="p-3 space-y-3">
        <!-- 快速选择按钮 -->
        <div class="grid grid-cols-2 gap-2">
          <Button
            v-for="preset in presets"
            :key="preset.label"
            variant="outline"
            size="sm"
            @click.stop="selectPreset(preset)"
          >
            {{ preset.label }}
          </Button>
        </div>

        <!-- 自定义日期选择 -->
        <div class="border-t pt-3">
          <div class="grid grid-cols-2 gap-3">
            <div class="space-y-2">
              <Label class="text-sm">开始日期</Label>
              <Popover v-model:open="startDateOpen">
                <PopoverTrigger as-child>
                  <Button
                    variant="outline"
                    size="sm"
                    class="w-full justify-start text-left font-normal"
                    @click.stop
                  >
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ startDate ? formatDate(startDate) : '选择日期' }}
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  class="w-auto p-0"
                  align="start"
                  @interact-outside="(e: Event) => e.preventDefault()"
                >
                  <Calendar
                    v-model="startDate as any"
                    :max-date="endDate || undefined"
                    mode="single"
                    class="rounded-md border"
                    @update:model-value="() => (startDateOpen = false)"
                    :default-placeholder="undefined as any"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div class="space-y-2">
              <Label class="text-sm">结束日期</Label>
              <Popover v-model:open="endDateOpen">
                <PopoverTrigger as-child>
                  <Button
                    variant="outline"
                    size="sm"
                    class="w-full justify-start text-left font-normal"
                    @click.stop
                  >
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ endDate ? formatDate(endDate) : '选择日期' }}
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  class="w-auto p-0"
                  align="start"
                  @interact-outside="(e: Event) => e.preventDefault()"
                >
                  <Calendar
                    v-model="endDate as any"
                    :min-date="startDate || undefined"
                    mode="single"
                    class="rounded-md border"
                    @update:model-value="() => (endDateOpen = false)"
                    :default-placeholder="undefined as any"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-3 border-t mt-3">
            <Button variant="outline" size="sm" @click.stop="clearSelection"> 清除 </Button>
            <div class="flex space-x-2">
              <Button variant="outline" size="sm" @click.stop="handleCancel"> 取消 </Button>
              <Button size="sm" @click.stop="confirmSelection" :disabled="!isValidRange">
                确定
              </Button>
            </div>
          </div>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { CalendarIcon } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'

interface Props {
  modelValue?: [Date, Date] | null
  placeholder?: string
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择日期范围',
  className: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: [Date, Date] | null]
}>()

// 响应式状态
const isOpen = ref(false)
const startDate = ref<Date | null>(null)
const endDate = ref<Date | null>(null)
const startDateOpen = ref(false)
const endDateOpen = ref(false)

// 预设日期范围
const presets = [
  {
    label: '今天',
    getValue: () => {
      const today = new Date()
      return [today, today] as [Date, Date]
    },
  },
  {
    label: '昨天',
    getValue: () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      return [yesterday, yesterday] as [Date, Date]
    },
  },
  {
    label: '最近7天',
    getValue: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      return [start, end] as [Date, Date]
    },
  },
  {
    label: '最近30天',
    getValue: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      return [start, end] as [Date, Date]
    },
  },
  {
    label: '本月',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), 1)
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      return [start, end] as [Date, Date]
    },
  },
  {
    label: '上月',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const end = new Date(now.getFullYear(), now.getMonth(), 0)
      return [start, end] as [Date, Date]
    },
  },
  {
    label: '本季度',
    getValue: () => {
      const now = new Date()
      const quarter = Math.floor(now.getMonth() / 3)
      const start = new Date(now.getFullYear(), quarter * 3, 1)
      const end = new Date(now.getFullYear(), quarter * 3 + 3, 0)
      return [start, end] as [Date, Date]
    },
  },
  {
    label: '本年',
    getValue: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), 0, 1)
      const end = new Date(now.getFullYear(), 11, 31)
      return [start, end] as [Date, Date]
    },
  },
]

// 计算属性
const isValidRange = computed(() => {
  return startDate.value && endDate.value && startDate.value <= endDate.value
})

// 方法
const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const selectPreset = (preset: (typeof presets)[0]) => {
  const [start, end] = preset.getValue()
  startDate.value = start
  endDate.value = end
  confirmSelection()
}

const confirmSelection = () => {
  if (isValidRange.value) {
    emit('update:modelValue', [startDate.value!, endDate.value!])
    // 使用 nextTick 确保状态更新后再关闭
    setTimeout(() => {
      isOpen.value = false
    }, 0)
  }
}

const clearSelection = () => {
  startDate.value = null
  endDate.value = null
  emit('update:modelValue', null)
  setTimeout(() => {
    isOpen.value = false
  }, 0)
}

const handleCancel = () => {
  // 恢复到原始值
  if (props.modelValue && props.modelValue[0] && props.modelValue[1]) {
    startDate.value = new Date(props.modelValue[0])
    endDate.value = new Date(props.modelValue[1])
  } else {
    startDate.value = null
    endDate.value = null
  }
  setTimeout(() => {
    isOpen.value = false
  }, 0)
}

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue[0] && newValue[1]) {
      startDate.value = new Date(newValue[0])
      endDate.value = new Date(newValue[1])
    } else {
      startDate.value = null
      endDate.value = null
    }
  },
  { immediate: true },
)

// 监听开始日期变化，确保结束日期不早于开始日期
watch(startDate, (newStart) => {
  if (newStart && endDate.value && newStart > endDate.value) {
    endDate.value = null
  }
})

// 监听结束日期变化，确保开始日期不晚于结束日期
watch(endDate, (newEnd) => {
  if (newEnd && startDate.value && startDate.value > newEnd) {
    startDate.value = null
  }
})
</script>
