<template>
  <div :class="containerClass">
    <div v-if="showSummary" class="text-sm text-muted-foreground">
      显示第 {{ start }} - {{ end }} 条， 共 {{ total }} 条记录
    </div>
    <div class="flex items-center gap-4">
      <div class="pagination-size-control">
        <span>每页显示</span>
        <Select :model-value="pageSize.toString()" @update:model-value="onPageSizeChange">
          <SelectTrigger class="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="opt in pageSizeOptions" :key="opt" :value="String(opt)">{{ opt }}</SelectItem>
          </SelectContent>
        </Select>
        <span>条</span>
      </div>

      <Pagination :page="page" :total="total" :items-per-page="pageSize" :sibling-count="1" :show-edges="true" @update:page="onPageChange">
        <PaginationContent v-slot="{ items }">
          <PaginationFirst />
          <PaginationPrevious />
          <template v-for="(it, idx) in items" :key="idx">
            <PaginationItem v-if="it.type === 'page'" :value="it.value" :is-active="it.value === page" />
            <PaginationEllipsis v-else />
          </template>
          <PaginationNext />
          <PaginationLast />
        </PaginationContent>
      </Pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Pagination, PaginationContent, PaginationFirst, PaginationLast, PaginationNext, PaginationPrevious, PaginationItem, PaginationEllipsis } from '@/components/ui/pagination'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Props {
  page: number
  total: number
  pageSize: number
  pageSizeOptions?: number[]
  showSummary?: boolean
  align?: 'between' | 'end'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  pageSizeOptions: () => [10, 20, 50, 100],
  showSummary: true,
  align: 'between',
  class: '',
})

const emit = defineEmits<{
  'update:page': [value: number]
  'update:pageSize': [value: number]
}>()

const start = computed(() => (props.total === 0 ? 0 : (props.page - 1) * props.pageSize + 1))
const end = computed(() => Math.min(props.page * props.pageSize, props.total))
const containerClass = computed(
  () => `flex items-center ${props.align === 'between' ? 'justify-between' : 'justify-end'} mt-4 px-4 pb-4 ${props.class ?? ''}`,
)

function onPageSizeChange(v: string) {
  const n = Number(v)
  if (!Number.isNaN(n) && n > 0) {
    emit('update:pageSize', n)
    // 统一规范：切换每页条数时回到第1页
    emit('update:page', 1)
  }
}

function onPageChange(p: number) {
  emit('update:page', p)
}
</script>

