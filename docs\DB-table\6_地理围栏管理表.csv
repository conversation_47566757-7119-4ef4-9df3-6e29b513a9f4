﻿表名: geographic_fences,,,,,,,
,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment)
1,id,主键ID,BIGINT,,○,●,自增主键
2,fence_name,围栏名称,VARCHAR,128,○,,
3,fence_type,围栏类型,SMALLINT,,○,,"1: 允许区域, 2: 禁止区域"
4,geometry_wkt,地理边界WKT,TEXT,,○,,WKT格式存储地理边界。若用PostGIS可改为geometry类型。
5,description,描述,TEXT,,,,
6,is_active,是否启用,BOOLEAN,,○,,
7,create_time,创建时间,TIMESTAMP,,○,,
8,update_time,更新时间,TIMESTAMP,,○,,
