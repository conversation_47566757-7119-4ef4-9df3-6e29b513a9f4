<template>
  <ContentPageLayout>
    <section class="py-24">
      <div class="mx-auto max-w-2xl text-center px-6">
        <h1 class="text-7xl font-bold tracking-tight">404</h1>
        <p class="mt-4 text-muted-foreground">抱歉，您访问的页面不存在或已被移动。</p>
        <p class="mt-1 text-sm text-muted-foreground">
          将在
          <span class="font-medium text-foreground">{{ seconds }}</span>
          秒后自动返回首页
        </p>
        <div class="mt-8 flex items-center justify-center gap-3">
          <Button @click="goHome">立即返回首页</Button>
        </div>
      </div>
    </section>
  </ContentPageLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import ContentPageLayout from './ContentPageLayout.vue'
import { Button } from '@/components/ui/button'

const router = useRouter()
const seconds = ref(5)
let timer: number | undefined

function goHome() {
  router.replace('/')
}

onMounted(() => {
  timer = window.setInterval(() => {
    if (seconds.value <= 1) {
      clearInterval(timer)
      goHome()
    } else {
      seconds.value -= 1
    }
  }, 1000)
})

onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
})
</script>

<style scoped>
/* 复用现有设计系统语义色，无额外自定义变量 */
</style>
