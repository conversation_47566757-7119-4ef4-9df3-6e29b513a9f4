<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">请如实填写企业基本信息，所填信息应与营业执照保持一致</p>
      </div>
      <Badge variant="outline" class="text-sm"> 步骤 1 / 5 </Badge>
    </div>

    <!-- 进度条 -->
    <div class="w-full bg-muted rounded-full h-2">
      <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 20%"></div>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Building class="w-5 h-5" />
          企业基本信息
        </CardTitle>
        <CardDescription> 请填写企业的基本信息，确保与营业执照上的信息完全一致 </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 企业名称 -->
          <div class="space-y-2">
            <Label for="companyName" class="text-base font-semibold">
              企业名称 <span class="text-red-500">*</span>
            </Label>
            <Input
              id="companyName"
              v-model="formData.companyName"
              placeholder="请输入企业全称（与营业执照一致）"
              :class="{ 'border-red-500': errors.companyName }"
            />
            <p v-if="errors.companyName" class="text-sm text-red-500">{{ errors.companyName }}</p>
            <p class="text-xs text-muted-foreground">
              注意：企业名称必须与营业执照上的名称完全一致
            </p>
          </div>

          <!-- 统一社会信用代码 -->
          <div class="space-y-2">
            <Label for="creditCode" class="text-base font-semibold">
              统一社会信用代码 <span class="text-red-500">*</span>
            </Label>
            <Input
              id="creditCode"
              v-model="formData.creditCode"
              placeholder="请输入18位统一社会信用代码"
              maxlength="18"
              :class="{ 'border-red-500': errors.creditCode }"
            />
            <p v-if="errors.creditCode" class="text-sm text-red-500">{{ errors.creditCode }}</p>
            <p class="text-xs text-muted-foreground">格式：18位数字和字母组合</p>
          </div>

          <!-- 地址信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="registeredAddress" class="text-base font-semibold">
                注册地址 <span class="text-red-500">*</span>
              </Label>
              <Textarea
                id="registeredAddress"
                v-model="formData.registeredAddress"
                placeholder="请输入企业注册地址"
                rows="3"
                :class="{ 'border-red-500': errors.registeredAddress }"
              />
              <p v-if="errors.registeredAddress" class="text-sm text-red-500">
                {{ errors.registeredAddress }}
              </p>
            </div>
            <div class="space-y-2">
              <Label for="businessAddress" class="text-base font-semibold">
                经营地址 <span class="text-red-500">*</span>
              </Label>
              <Textarea
                id="businessAddress"
                v-model="formData.businessAddress"
                placeholder="请输入企业经营地址"
                rows="3"
                :class="{ 'border-red-500': errors.businessAddress }"
              />
              <p v-if="errors.businessAddress" class="text-sm text-red-500">
                {{ errors.businessAddress }}
              </p>
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="sameAsRegistered"
                  v-model:checked="sameAsRegistered"
                  @update:checked="handleSameAsRegistered"
                />
                <Label for="sameAsRegistered" class="text-sm">与注册地址相同</Label>
              </div>
            </div>
          </div>

          <!-- 企业类型 -->
          <div class="space-y-2">
            <Label for="companyType" class="text-base font-semibold">
              企业类型 <span class="text-red-500">*</span>
            </Label>
            <Select v-model="formData.companyType">
              <SelectTrigger id="companyType" :class="{ 'border-red-500': errors.companyType }">
                <SelectValue placeholder="请选择企业类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="整车生产企业">整车生产企业</SelectItem>
                <SelectItem value="平台运营商">平台运营商</SelectItem>
                <SelectItem value="智驾方案提供商">智驾方案提供商</SelectItem>
                <SelectItem value="地图服务商">地图服务商</SelectItem>
                <SelectItem value="其他">其他</SelectItem>
              </SelectContent>
            </Select>
            <p v-if="errors.companyType" class="text-sm text-red-500">{{ errors.companyType }}</p>
          </div>

          <!-- 成立日期与注册资本 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="establishDate" class="text-base font-semibold">
                成立日期 <span class="text-red-500">*</span>
              </Label>
              <Input
                id="establishDate"
                v-model="formData.establishDate"
                type="date"
                :class="{ 'border-red-500': errors.establishDate }"
              />
              <p v-if="errors.establishDate" class="text-sm text-red-500">
                {{ errors.establishDate }}
              </p>
            </div>
            <div class="space-y-2">
              <Label for="registeredCapital" class="text-base font-semibold">
                注册资本 <span class="text-red-500">*</span>
              </Label>
              <div class="flex">
                <Input
                  id="registeredCapital"
                  v-model="formData.registeredCapital"
                  placeholder="请输入注册资本"
                  type="number"
                  step="0.01"
                  min="0"
                  class="flex-1"
                  :class="{ 'border-red-500': errors.registeredCapital }"
                />
                <div class="flex items-center px-3 border border-l-0 rounded-r-md bg-muted">
                  <span class="text-sm text-muted-foreground">万元</span>
                </div>
              </div>
              <p v-if="errors.registeredCapital" class="text-sm text-red-500">
                {{ errors.registeredCapital }}
              </p>
            </div>
          </div>

          <!-- 法人代表信息 -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">法人代表信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="space-y-2">
                <Label for="legalPersonName" class="text-base font-semibold">
                  法人姓名 <span class="text-red-500">*</span>
                </Label>
                <Input
                  id="legalPersonName"
                  v-model="formData.legalPersonName"
                  placeholder="请输入法人姓名"
                  :class="{ 'border-red-500': errors.legalPersonName }"
                />
                <p v-if="errors.legalPersonName" class="text-sm text-red-500">
                  {{ errors.legalPersonName }}
                </p>
              </div>
              <div class="space-y-2">
                <Label for="legalPersonPhone" class="text-base font-semibold">
                  联系电话 <span class="text-red-500">*</span>
                </Label>
                <Input
                  id="legalPersonPhone"
                  v-model="formData.legalPersonPhone"
                  placeholder="请输入联系电话"
                  :class="{ 'border-red-500': errors.legalPersonPhone }"
                />
                <p v-if="errors.legalPersonPhone" class="text-sm text-red-500">
                  {{ errors.legalPersonPhone }}
                </p>
              </div>
              <div class="space-y-2">
                <Label for="legalPersonEmail" class="text-base font-semibold">
                  电子邮箱 <span class="text-red-500">*</span>
                </Label>
                <Input
                  id="legalPersonEmail"
                  v-model="formData.legalPersonEmail"
                  placeholder="请输入电子邮箱"
                  type="email"
                  :class="{ 'border-red-500': errors.legalPersonEmail }"
                />
                <p v-if="errors.legalPersonEmail" class="text-sm text-red-500">
                  {{ errors.legalPersonEmail }}
                </p>
              </div>
            </div>
          </div>

          <!-- 营业执照上传 -->
          <div class="space-y-2">
            <Label class="text-base font-semibold">
              营业执照扫描件 <span class="text-red-500">*</span>
            </Label>
            <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              <div v-if="!uploadedFile" class="text-center">
                <Upload class="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                <p class="text-xs text-muted-foreground mb-4">
                  支持 JPG、PNG、PDF 格式，文件大小不超过 10MB
                </p>
                <Button type="button" variant="outline" @click="triggerFileUpload">
                  选择文件
                </Button>
                <input
                  ref="fileInput"
                  type="file"
                  accept=".jpg,.jpeg,.png,.pdf"
                  class="hidden"
                  @change="handleFileUpload"
                />
              </div>
              <div v-else class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <FileText class="w-8 h-8 text-blue-500" />
                  <div>
                    <p class="text-base font-semibold">{{ uploadedFile.name }}</p>
                    <p class="text-xs text-muted-foreground">
                      {{ formatFileSize(uploadedFile.size) }}
                    </p>
                  </div>
                </div>
                <Button type="button" variant="outline" size="sm" @click="removeFile">
                  <X class="w-4 h-4" />
                </Button>
              </div>
            </div>
            <p v-if="errors.businessLicense" class="text-sm text-red-500">
              {{ errors.businessLicense }}
            </p>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" disabled>
              <ChevronLeft class="w-4 h-4 mr-2" />
              上一步
            </Button>
            <div class="flex gap-2">
              <Button type="button" variant="outline" @click="handleSave">
                <Save class="w-4 h-4 mr-2" />
                保存（暂存）
              </Button>
              <Button type="submit" @click="handleNext">
                下一步
                <ChevronRight class="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 保存提示弹窗 -->
    <Dialog v-model:open="saveDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>保存成功</DialogTitle>
          <DialogDescription> 您的企业基本信息已保存，可稍后继续填报。 </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end">
          <Button @click="saveDialogOpen = false">确定</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Building, ChevronLeft, ChevronRight, FileText, Save, Upload, X } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const router = useRouter()

// 表单数据
const formData = reactive({
  companyName: '',
  creditCode: '',
  registeredAddress: '',
  businessAddress: '',
  companyType: '',
  establishDate: '',
  registeredCapital: '',
  legalPersonName: '',
  legalPersonPhone: '',
  legalPersonEmail: '',
})

// 表单验证错误
const errors = reactive({
  companyName: '',
  creditCode: '',
  registeredAddress: '',
  businessAddress: '',
  companyType: '',
  establishDate: '',
  registeredCapital: '',
  legalPersonName: '',
  legalPersonPhone: '',
  legalPersonEmail: '',
  businessLicense: '',
})

// 其他状态
const sameAsRegistered = ref(false)
const uploadedFile = ref<File | null>(null)
const fileInput = ref<HTMLInputElement>()
const saveDialogOpen = ref(false)

// 处理地址同步
const handleSameAsRegistered = (checked: boolean) => {
  if (checked && formData.registeredAddress) {
    formData.businessAddress = formData.registeredAddress
  }
}

// 文件上传处理
const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      errors.businessLicense = '文件格式不支持，请上传 JPG、PNG 或 PDF 格式的文件'
      return
    }

    if (file.size > maxSize) {
      errors.businessLicense = '文件大小超过限制，请上传小于 10MB 的文件'
      return
    }

    uploadedFile.value = file
    errors.businessLicense = ''
  }
}

const removeFile = () => {
  uploadedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表单验证（原型演示版本 - 校验已禁用）
const validateForm = () => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => {
    errors[key as keyof typeof errors] = ''
  })

  // 原型演示模式：直接返回true，跳过所有校验
  console.log('原型演示模式：表单校验已禁用，直接通过')
  return true

  /* 原始校验逻辑（原型完成后可恢复）
  let isValid = true

  // 企业名称验证
  if (!formData.companyName.trim()) {
    errors.companyName = '请输入企业名称'
    isValid = false
  }

  // 统一社会信用代码验证
  if (!formData.creditCode.trim()) {
    errors.creditCode = '请输入统一社会信用代码'
    isValid = false
  } else if (!/^[A-Z0-9]{18}$/i.test(formData.creditCode)) {
    errors.creditCode = '统一社会信用代码格式不正确'
    isValid = false
  }

  // 注册地址验证
  if (!formData.registeredAddress.trim()) {
    errors.registeredAddress = '请输入注册地址'
    isValid = false
  }

  // 经营地址验证
  if (!formData.businessAddress.trim()) {
    errors.businessAddress = '请输入经营地址'
    isValid = false
  }

  // 企业类型验证
  if (!formData.companyType) {
    errors.companyType = '请选择企业类型'
    isValid = false
  }

  // 成立日期验证
  if (!formData.establishDate) {
    errors.establishDate = '请选择成立日期'
    isValid = false
  }

  // 注册资本验证
  if (!formData.registeredCapital) {
    errors.registeredCapital = '请输入注册资本'
    isValid = false
  } else if (parseFloat(formData.registeredCapital) <= 0) {
    errors.registeredCapital = '注册资本必须大于0'
    isValid = false
  }

  // 法人姓名验证
  if (!formData.legalPersonName.trim()) {
    errors.legalPersonName = '请输入法人姓名'
    isValid = false
  }

  // 法人电话验证
  if (!formData.legalPersonPhone.trim()) {
    errors.legalPersonPhone = '请输入联系电话'
    isValid = false
  } else if (!/^1[3-9]\d{9}$/.test(formData.legalPersonPhone)) {
    errors.legalPersonPhone = '请输入正确的手机号码'
    isValid = false
  }

  // 法人邮箱验证
  if (!formData.legalPersonEmail.trim()) {
    errors.legalPersonEmail = '请输入电子邮箱'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.legalPersonEmail)) {
    errors.legalPersonEmail = '请输入正确的邮箱地址'
    isValid = false
  }

  // 营业执照验证
  if (!uploadedFile.value) {
    errors.businessLicense = '请上传营业执照扫描件'
    isValid = false
  }

  return isValid
  */
}

// 保存（暂存）
const handleSave = () => {
  console.log('保存表单数据:', formData)
  console.log('上传文件:', uploadedFile.value)

  // 保存到本地存储
  localStorage.setItem(
    'enterprise_basic_info',
    JSON.stringify({
      formData,
      uploadedFileName: uploadedFile.value?.name,
    }),
  )

  saveDialogOpen.value = true
}

// 下一步
const handleNext = () => {
  if (validateForm()) {
    handleSave()
    // 跳转到下一步（测绘资质信息）
    router.push('/corp/filing/form/qualification')
  }
}

// 表单提交
const handleSubmit = () => {
  // 阻止默认提交行为，由handleNext处理
}

// 页面加载时尝试恢复数据
const loadSavedData = () => {
  const saved = localStorage.getItem('enterprise_basic_info')
  if (saved) {
    try {
      const data = JSON.parse(saved)
      Object.assign(formData, data.formData)
    } catch (e) {
      console.warn('Failed to load saved data:', e)
    }
  }
}

// 组件挂载时加载保存的数据
loadSavedData()
</script>

<style scoped>
/* 自定义样式 */
.border-red-500 {
  border-color: rgb(239 68 68);
}
</style>
