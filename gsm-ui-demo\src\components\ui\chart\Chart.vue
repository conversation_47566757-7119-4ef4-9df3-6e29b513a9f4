<template>
  <ChartContainer
    :option="chartOption"
    :height="height"
    :class="class"
  />
</template>

<script setup lang="ts" generic="<PERSON><PERSON> extends Record<string, unknown>">
import { computed } from 'vue'
import ChartContainer from '@/components/charts/ChartContainer.vue'

interface Props {
  /**
   * The data for the chart
   */
  data: Datum[]
  /**
   * Chart configuration (ECharts option)
   */
  config: any
  /**
   * Chart type (not used in ECharts, but kept for compatibility)
   */
  type?: 'xy' | 'single'
  /**
   * Chart height
   */
  height?: number
  /**
   * Additional CSS classes
   */
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'single',
  height: 280
})

// Convert config to ECharts option
const chartOption = computed(() => {
  // If config is already a valid ECharts option, use it directly
  if (props.config && typeof props.config === 'object') {
    return {
      ...props.config,
      // Ensure data is properly set if not already in config
      ...(props.data && !props.config.series ? {
        series: [{
          type: 'bar', // default type
          data: props.data
        }]
      } : {})
    }
  }

  // Fallback configuration
  return {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'bar',
      data: props.data || []
    }]
  }
})
</script>