<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理系统数据备份与恢复，支持自动定时备份、手动备份和增量备份策略
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="handleCreateBackup" class="flex items-center gap-2">
          <Plus class="w-4 h-4" />
          创建备份
        </Button>
        <Button @click="handleBackupSettings" variant="outline" class="flex items-center gap-2">
          <Settings class="w-4 h-4" />
          备份设置
        </Button>
      </div>
    </div>

    <!-- 备份概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">总备份数</p>
              <p class="text-2xl font-bold">{{ backupStats.total }}</p>
              <p class="text-xs text-muted-foreground mt-1">
                +{{ backupStats.thisMonth }}个 本月新增
              </p>
            </div>
            <Database class="w-8 h-8 text-primary" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">存储占用</p>
              <p class="text-2xl font-bold">{{ formatBytes(backupStats.storageUsed) }}</p>
              <div class="w-full bg-muted rounded-full h-2 mt-2">
                <div
                  class="bg-blue-500 rounded-full h-2 transition-all duration-300"
                  :style="{
                    width: `${Math.min((backupStats.storageUsed / backupStats.storageTotal) * 100, 100)}%`,
                  }"
                ></div>
              </div>
            </div>
            <HardDrive class="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">成功率</p>
              <p class="text-2xl font-bold text-green-600">{{ backupStats.successRate }}%</p>
              <p class="text-xs text-muted-foreground mt-1">最近30天统计</p>
            </div>
            <CheckCircle class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">下次备份</p>
              <p class="text-lg font-bold">{{ nextBackupTime }}</p>
              <div class="flex items-center gap-1 mt-1">
                <div
                  :class="`w-2 h-2 rounded-full ${backupStatus === 'running' ? 'bg-blue-500 animate-pulse' : 'bg-green-500'}`"
                ></div>
                <p class="text-xs text-muted-foreground">
                  {{ backupStatus === 'running' ? '备份中...' : '系统空闲' }}
                </p>
              </div>
            </div>
            <Clock class="w-8 h-8 text-orange-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 备份策略配置 -->
    <Card v-if="showBackupSettings">
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Settings class="w-5 h-5" />
            备份策略配置
          </div>
          <Button @click="showBackupSettings = false" variant="ghost" size="sm">
            <X class="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 自动备份设置 -->
          <div class="space-y-4">
            <h4 class="font-medium">自动备份设置</h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <Label>启用自动备份</Label>
                <Switch v-model:checked="backupConfig.autoBackupEnabled" />
              </div>
              <div class="grid grid-cols-2 gap-2">
                <div>
                  <Label>备份频率</Label>
                  <Select v-model="backupConfig.frequency">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">每日备份</SelectItem>
                      <SelectItem value="weekly">每周备份</SelectItem>
                      <SelectItem value="monthly">每月备份</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>备份时间</Label>
                  <Input v-model="backupConfig.backupTime" type="time" />
                </div>
              </div>
              <div>
                <Label>保留策略 (天)</Label>
                <Input
                  v-model.number="backupConfig.retentionDays"
                  type="number"
                  min="1"
                  max="365"
                />
              </div>
            </div>
          </div>

          <!-- 备份存储设置 -->
          <div class="space-y-4">
            <h4 class="font-medium">存储设置</h4>
            <div class="space-y-3">
              <div>
                <Label>存储位置</Label>
                <Select v-model="backupConfig.storageLocation">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="local">本地存储</SelectItem>
                    <SelectItem value="oss">阿里云OSS</SelectItem>
                    <SelectItem value="s3">AWS S3</SelectItem>
                    <SelectItem value="ftp">FTP服务器</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="flex items-center justify-between">
                <Label>启用压缩</Label>
                <Switch v-model:checked="backupConfig.compressionEnabled" />
              </div>
              <div class="flex items-center justify-between">
                <Label>启用加密</Label>
                <Switch v-model:checked="backupConfig.encryptionEnabled" />
              </div>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-2 pt-4 border-t">
          <Button @click="saveBackupConfig">
            <Save class="w-4 h-4 mr-2" />
            保存设置
          </Button>
          <Button @click="testBackupConfig" variant="outline">
            <TestTube class="w-4 h-4 mr-2" />
            测试配置
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 备份记录列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>备份记录</span>
          <Badge variant="outline"> 共 {{ filteredBackups.length }} 条记录 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <!-- 筛选条件 - 紧贴表格 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="false"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
        />
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>备份名称</TableHead>
                <TableHead>备份类型</TableHead>
                <TableHead>数据范围</TableHead>
                <TableHead>文件大小</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>存储位置</TableHead>
                <TableHead class="w-[140px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedBackups.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无备份记录
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(backup, index) in pagedBackups"
                :key="backup.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component :is="getBackupTypeIcon(backup.type)" class="w-4 h-4 text-primary" />
                    <div>
                      <div class="font-medium">{{ backup.name }}</div>
                      <div class="text-xs text-muted-foreground">{{ backup.id }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getBackupTypeVariant(backup.type)">
                    {{ backup.type }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div class="text-sm">
                    {{ backup.dataScope }}
                  </div>
                </TableCell>
                <TableCell>
                  <div class="font-mono text-sm">{{ formatBytes(backup.fileSize) }}</div>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">
                  <div>{{ backup.createdAt }}</div>
                  <div class="text-xs text-muted-foreground">耗时: {{ backup.duration }}</div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <Badge :variant="getStatusVariant(backup.status) as any">
                      {{ backup.status }}
                    </Badge>
                    <component
                      :is="getStatusIcon(backup.status)"
                      :class="`w-3 h-3 ${getStatusIconColor(backup.status)}`"
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-1 text-sm">
                    <component :is="getStorageIcon(backup.storage)" class="w-4 h-4" />
                    {{ backup.storage }}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleViewDetail(backup.id)">
                        <Eye class="w-4 h-4 mr-2" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="backup.status === '成功'"
                        @click="handleDownload(backup.id)"
                      >
                        <Download class="w-4 h-4 mr-2" />
                        下载备份
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="backup.status === '成功'"
                        @click="handleRestore(backup.id)"
                        class="text-blue-600"
                      >
                        <RotateCcw class="w-4 h-4 mr-2" />
                        恢复数据
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleVerify(backup.id)">
                        <CheckCircle class="w-4 h-4 mr-2" />
                        验证完整性
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDuplicate(backup.id)">
                        <Copy class="w-4 h-4 mr-2" />
                        复制备份
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDelete(backup.id)" class="text-red-600">
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除备份
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页：统一 PaginationBar -->
        <PaginationBar
          v-model:page="currentPage"
          v-model:pageSize="pageSize"
          :total="filteredBackups.length"
        />
      </CardContent>
    </Card>

    <!-- 创建备份抽屉 -->
    <Sheet :open="showCreateDialog" @update:open="showCreateDialog = $event">
      <SheetContent side="right" class="z-[60] w-[50vw] min-w-[520px] max-w-[900px]">
        <SheetHeader>
          <SheetTitle>创建备份</SheetTitle>
          <SheetDescription> 选择备份类型和数据范围，创建新的数据备份任务 </SheetDescription>
        </SheetHeader>

        <div class="space-y-6 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label>备份名称</Label>
              <Input v-model="createForm.name" placeholder="请输入备份名称" />
            </div>
            <div>
              <Label>备份类型</Label>
              <Select v-model="createForm.type">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="完整备份">完整备份</SelectItem>
                  <SelectItem value="增量备份">增量备份</SelectItem>
                  <SelectItem value="差异备份">差异备份</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label>数据范围</Label>
            <div class="mt-2 space-y-2">
              <div v-for="scope in dataScopes" :key="scope.key" class="flex items-center space-x-2">
                <Checkbox
                  :id="scope.key"
                  :checked="createForm.scopes.includes(scope.key)"
                  @update:checked="toggleScope(scope.key, $event)"
                />
                <Label :for="scope.key" class="flex items-center gap-2">
                  <component :is="scope.icon" class="w-4 h-4" />
                  {{ scope.label }}
                  <span class="text-xs text-muted-foreground">({{ scope.size }})</span>
                </Label>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label>存储位置</Label>
              <Select v-model="createForm.storage">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="本地存储">本地存储</SelectItem>
                  <SelectItem value="阿里云OSS">阿里云OSS</SelectItem>
                  <SelectItem value="AWS S3">AWS S3</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="compression"
                  :checked="createForm.compression"
                  @update:checked="createForm.compression = $event"
                />
                <Label for="compression">启用压缩</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="encryption"
                  :checked="createForm.encryption"
                  @update:checked="createForm.encryption = $event"
                />
                <Label for="encryption">启用加密</Label>
              </div>
            </div>
          </div>

          <div>
            <Label>备份描述</Label>
            <Textarea
              v-model="createForm.description"
              placeholder="请输入备份描述（可选）"
              rows="3"
            />
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showCreateDialog = false">取消</Button>
          <Button @click="confirmCreateBackup">
            <Plus class="w-4 h-4 mr-2" />
            创建备份
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>

    <!-- 备份详情抽屉 -->
    <Sheet :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <SheetContent
        side="right"
        class="z-[60] w-[66vw] min-w-[640px] max-w-[1100px] max-h-[100vh] overflow-y-auto"
      >
        <SheetHeader>
          <SheetTitle>备份详情 - {{ currentBackup?.name }}</SheetTitle>
          <SheetDescription> 查看备份的详细信息和元数据 </SheetDescription>
        </SheetHeader>

        <div v-if="currentBackup" class="space-y-4 py-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label class="text-base font-semibold text-muted-foreground">备份ID</Label>
              <p class="text-sm font-mono">{{ currentBackup.id }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">创建时间</Label>
              <p class="text-sm">{{ currentBackup.createdAt }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">备份类型</Label>
              <Badge :variant="getBackupTypeVariant(currentBackup.type)">
                {{ currentBackup.type }}
              </Badge>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">文件大小</Label>
              <p class="text-sm">{{ formatBytes(currentBackup.fileSize) }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">备份时长</Label>
              <p class="text-sm">{{ currentBackup.duration }}</p>
            </div>
            <div>
              <Label class="text-base font-semibold text-muted-foreground">存储位置</Label>
              <div class="flex items-center gap-1">
                <component :is="getStorageIcon(currentBackup.storage)" class="w-4 h-4" />
                <span class="text-sm">{{ currentBackup.storage }}</span>
              </div>
            </div>
            <div class="col-span-2">
              <Label class="text-base font-semibold text-muted-foreground">数据范围</Label>
              <p class="text-sm">{{ currentBackup.dataScope }}</p>
            </div>
          </div>

          <div v-if="currentBackup.description" class="p-3 bg-muted rounded">
            <Label class="text-base font-semibold text-muted-foreground">备份描述</Label>
            <p class="text-sm mt-1">{{ currentBackup.description }}</p>
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showDetailDialog = false">关闭</Button>
          <Button
            v-if="currentBackup?.status === '成功'"
            @click="handleRestore(currentBackup.id)"
            variant="default"
          >
            <RotateCcw class="w-4 h-4 mr-2" />
            恢复数据
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  CheckCircle,
  Clock,
  Cloud,
  Copy,
  Cpu,
  Database,
  Download,
  Eye,
  HardDrive,
  MoreHorizontal,
  Plus,
  RotateCcw,
  Save,
  Settings,
  TestTube,
  Trash2,
  Users,
  X,
  XCircle,
} from 'lucide-vue-next'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { PaginationBar } from '@/components/ui/pagination'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type BackupType = '完整备份' | '增量备份' | '差异备份'
type BackupStatus = '成功' | '失败' | '进行中' | '已取消'
type StorageType = '本地存储' | '阿里云OSS' | 'AWS S3' | 'FTP服务器'

interface BackupRecord {
  id: string
  name: string
  type: BackupType
  status: BackupStatus
  dataScope: string
  fileSize: number
  createdAt: string
  duration: string
  storage: StorageType
  description?: string
}

interface BackupConfig {
  autoBackupEnabled: boolean
  frequency: string
  backupTime: string
  retentionDays: number
  storageLocation: string
  compressionEnabled: boolean
  encryptionEnabled: boolean
}

interface CreateForm {
  name: string
  type: BackupType
  scopes: string[]
  storage: StorageType
  compression: boolean
  encryption: boolean
  description: string
}

// 状态管理
const backupStatus = ref<'idle' | 'running'>('idle')
const nextBackupTime = ref('明日 02:00')
const showBackupSettings = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentBackup = ref<BackupRecord | null>(null)

// 筛选和分页
const currentPage = ref(1)
const pageSize = ref(10)
const filters = ref({
  name: '',
  type: 'ALL' as 'ALL' | BackupType,
  status: 'ALL' as 'ALL' | BackupStatus,
  storage: 'ALL' as 'ALL' | StorageType,
  dateRange: null as [Date, Date] | null,
})

// 备份统计
const backupStats = ref({
  total: 45,
  thisMonth: 8,
  storageUsed: 512 * 1024 * 1024 * 1024, // 512GB
  storageTotal: 2 * 1024 * 1024 * 1024 * 1024, // 2TB
  successRate: 98.2,
})

// 备份配置
const backupConfig = ref<BackupConfig>({
  autoBackupEnabled: true,
  frequency: 'daily',
  backupTime: '02:00',
  retentionDays: 30,
  storageLocation: 'local',
  compressionEnabled: true,
  encryptionEnabled: true,
})

// 创建表单
const createForm = ref<CreateForm>({
  name: '',
  type: '完整备份',
  scopes: [],
  storage: '本地存储',
  compression: true,
  encryption: true,
  description: '',
})

// 数据范围选项
const dataScopes = [
  { key: 'database', label: '数据库', icon: Database, size: '2.5GB' },
  { key: 'users', label: '用户数据', icon: Users, size: '156MB' },
  { key: 'files', label: '上传文件', icon: HardDrive, size: '8.9GB' },
  { key: 'config', label: '系统配置', icon: Settings, size: '2.3MB' },
  { key: 'logs', label: '系统日志', icon: Copy, size: '450MB' },
]

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '备份名称',
    type: 'input',
    placeholder: '请输入备份名称',
  },
  {
    key: 'type',
    label: '备份类型',
    type: 'select',
    placeholder: '请选择类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '完整备份', value: '完整备份' },
      { label: '增量备份', value: '增量备份' },
      { label: '差异备份', value: '差异备份' },
    ],
  },
  {
    key: 'status',
    label: '备份状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '成功', value: '成功' },
      { label: '失败', value: '失败' },
      { label: '进行中', value: '进行中' },
    ],
  },
  {
    key: 'storage',
    label: '存储位置',
    type: 'select',
    placeholder: '请选择位置',
    options: [
      { label: '全部位置', value: 'ALL' },
      { label: '本地存储', value: '本地存储' },
      { label: '阿里云OSS', value: '阿里云OSS' },
      { label: 'AWS S3', value: 'AWS S3' },
    ],
  },
  {
    key: 'dateRange',
    label: '创建时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// Mock 数据
const backupRecords = ref<BackupRecord[]>([
  {
    id: 'backup-20250825-001',
    name: '系统完整备份_20250825',
    type: '完整备份',
    status: '成功',
    dataScope: '数据库, 用户数据, 上传文件, 系统配置',
    fileSize: 12 * 1024 * 1024 * 1024, // 12GB
    createdAt: '2025-08-25 02:00:15',
    duration: '2小时15分钟',
    storage: '阿里云OSS',
    description: '每日例行完整备份',
  },
  {
    id: 'backup-20250824-002',
    name: '数据库备份_20250824',
    type: '增量备份',
    status: '成功',
    dataScope: '数据库',
    fileSize: 256 * 1024 * 1024, // 256MB
    createdAt: '2025-08-24 14:30:22',
    duration: '18分钟',
    storage: '本地存储',
  },
  {
    id: 'backup-20250824-001',
    name: '系统完整备份_20250824',
    type: '完整备份',
    status: '成功',
    dataScope: '数据库, 用户数据, 上传文件, 系统配置, 系统日志',
    fileSize: 11.8 * 1024 * 1024 * 1024, // 11.8GB
    createdAt: '2025-08-24 02:00:08',
    duration: '2小时8分钟',
    storage: '阿里云OSS',
  },
  {
    id: 'backup-20250823-003',
    name: '用户数据备份_20250823',
    type: '差异备份',
    status: '失败',
    dataScope: '用户数据',
    fileSize: 0,
    createdAt: '2025-08-23 16:45:33',
    duration: '3分钟',
    storage: 'AWS S3',
    description: '备份过程中网络连接中断',
  },
  {
    id: 'backup-20250823-002',
    name: '配置文件备份_20250823',
    type: '增量备份',
    status: '成功',
    dataScope: '系统配置',
    fileSize: 2.3 * 1024 * 1024, // 2.3MB
    createdAt: '2025-08-23 10:15:45',
    duration: '2分钟',
    storage: '本地存储',
  },
  {
    id: 'backup-20250823-001',
    name: '系统完整备份_20250823',
    type: '完整备份',
    status: '成功',
    dataScope: '数据库, 用户数据, 上传文件, 系统配置, 系统日志',
    fileSize: 11.5 * 1024 * 1024 * 1024, // 11.5GB
    createdAt: '2025-08-23 02:00:12',
    duration: '1小时58分钟',
    storage: '阿里云OSS',
  },
])

// 计算属性
const filteredBackups = computed(() => {
  return backupRecords.value.filter((backup) => {
    if (filters.value.name && !backup.name.includes(filters.value.name)) {
      return false
    }
    if (filters.value.type !== 'ALL' && backup.type !== filters.value.type) {
      return false
    }
    if (filters.value.status !== 'ALL' && backup.status !== filters.value.status) {
      return false
    }
    if (filters.value.storage !== 'ALL' && backup.storage !== filters.value.storage) {
      return false
    }
    if (filters.value.dateRange && filters.value.dateRange[0] && filters.value.dateRange[1]) {
      const startDate = new Date(
        filters.value.dateRange[0].getFullYear(),
        filters.value.dateRange[0].getMonth(),
        filters.value.dateRange[0].getDate(),
      )
      const endDate = new Date(
        filters.value.dateRange[1].getFullYear(),
        filters.value.dateRange[1].getMonth(),
        filters.value.dateRange[1].getDate(),
        23,
        59,
        59,
      )
      const backupDate = new Date(backup.createdAt.replace(/-/g, '/'))
      if (backupDate < startDate || backupDate > endDate) return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredBackups.value.length / pageSize.value)),
)

const pagedBackups = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredBackups.value.slice(start, start + pageSize.value)
})

// 辅助函数
const formatBytes = (bytes: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

const getBackupTypeIcon = (type: BackupType) => {
  const icons = {
    完整备份: Database,
    增量备份: Copy,
    差异备份: Clock,
  }
  return icons[type] || Database
}

import type { BadgeVariants } from '@/components/ui/badge'
const getBackupTypeVariant = (type: BackupType): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<BackupType, NonNullable<BadgeVariants['variant']>> = {
    完整备份: 'default',
    增量备份: 'secondary',
    差异备份: 'outline',
  }
  return variants[type] ?? 'outline'
}

const getStatusIcon = (status: BackupStatus) => {
  const icons = {
    成功: CheckCircle,
    失败: XCircle,
    进行中: Clock,
    已取消: X,
  }
  return icons[status] || CheckCircle
}

const getStatusVariant = (status: BackupStatus) => {
  const variants = {
    成功: 'default',
    失败: 'destructive',
    进行中: 'secondary',
    已取消: 'outline',
  }
  return variants[status] || 'outline'
}

const getStatusIconColor = (status: BackupStatus) => {
  const colors = {
    成功: 'text-green-500',
    失败: 'text-red-500',
    进行中: 'text-blue-500',
    已取消: 'text-gray-500',
  }
  return colors[status] || 'text-gray-500'
}

const getStorageIcon = (storage: StorageType) => {
  const icons = {
    本地存储: HardDrive,
    阿里云OSS: Cloud,
    'AWS S3': Cloud,
    FTP服务器: Database,
  }
  return icons[storage] || HardDrive
}

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    name: '',
    type: 'ALL',
    status: 'ALL',
    storage: 'ALL',
    dateRange: null,
  }
  currentPage.value = 1
}

const handleCreateBackup = () => {
  createForm.value = {
    name: `手动备份_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`,
    type: '完整备份',
    scopes: ['database', 'users'],
    storage: '本地存储',
    compression: true,
    encryption: true,
    description: '',
  }
  showCreateDialog.value = true
}

const handleBackupSettings = () => {
  showBackupSettings.value = !showBackupSettings.value
}

const confirmCreateBackup = () => {
  const newBackup: BackupRecord = {
    id: `backup-${Date.now()}`,
    name: createForm.value.name,
    type: createForm.value.type,
    status: '进行中',
    dataScope: createForm.value.scopes
      .map((key) => dataScopes.find((scope) => scope.key === key)?.label)
      .filter(Boolean)
      .join(', '),
    fileSize: 0,
    createdAt: new Date().toLocaleString('zh-CN'),
    duration: '计算中...',
    storage: createForm.value.storage,
    description: createForm.value.description,
  }

  backupRecords.value.unshift(newBackup)
  showCreateDialog.value = false

  // 模拟备份过程
  setTimeout(() => {
    newBackup.status = '成功'
    newBackup.fileSize = Math.random() * 10 * 1024 * 1024 * 1024 // 随机大小
    newBackup.duration = `${Math.floor(Math.random() * 120 + 10)}分钟`
  }, 3000)
}

const toggleScope = (key: string, checked: boolean) => {
  if (checked) {
    createForm.value.scopes.push(key)
  } else {
    const index = createForm.value.scopes.indexOf(key)
    if (index > -1) {
      createForm.value.scopes.splice(index, 1)
    }
  }
}

const saveBackupConfig = () => {
  console.log('保存备份配置:', backupConfig.value)
}

const testBackupConfig = () => {
  console.log('测试备份配置')
}

const handleViewDetail = (id: string) => {
  const backup = backupRecords.value.find((b) => b.id === id)
  if (backup) {
    currentBackup.value = backup
    showDetailDialog.value = true
  }
}

const handleDownload = (id: string) => {
  console.log('下载备份:', id)
}

const handleRestore = (id: string) => {
  console.log('恢复数据:', id)
  showDetailDialog.value = false
}

const handleVerify = (id: string) => {
  console.log('验证完整性:', id)
}

const handleDuplicate = (id: string) => {
  console.log('复制备份:', id)
}

const handleDelete = (id: string) => {
  const index = backupRecords.value.findIndex((b) => b.id === id)
  if (index > -1) {
    backupRecords.value.splice(index, 1)
  }
}
</script>
