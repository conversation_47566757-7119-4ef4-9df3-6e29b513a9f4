<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理企业内部角色和权限分配，支持角色创建、权限配置和员工分配
        </p>
      </div>
      <Button @click="handleCreateRole" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增角色
      </Button>
    </div>

    <!-- 角色列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>角色列表</span>
          <Badge variant="outline"> 共 {{ filteredRoles.length }} 个角色 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>角色名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>权限数量</TableHead>
                <TableHead>员工数量</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead class="w-[140px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedRoles.length === 0">
                <TableCell :colspan="8" class="h-24 text-center text-muted-foreground">
                  暂无角色数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(role, index) in pagedRoles"
                :key="role.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component :is="getRoleIcon(role.name)" class="w-4 h-4 text-primary" />
                    <div>
                      <div class="font-medium">{{ role.name }}</div>
                      <div class="text-xs text-muted-foreground">ID: {{ role.id }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="max-w-[300px] text-sm text-muted-foreground">
                    {{ role.description }}
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-1">
                    <Badge variant="outline" class="text-xs">
                      {{ role.permissionCount }}
                    </Badge>
                    <Button variant="ghost" size="sm" @click="handleViewPermissions(role.id)">
                      <Eye class="w-3 h-3" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary" class="text-xs"> {{ role.userCount }} 人 </Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(role.status)">{{ role.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ role.createdAt }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleEdit(role.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑角色
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleConfigPermissions(role.id)">
                        <Shield class="w-4 h-4 mr-2" />
                        配置权限
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleAssignUsers(role.id)">
                        <Users class="w-4 h-4 mr-2" />
                        分配员工
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="role.status === '启用'"
                        @click="handleDisable(role.id)"
                        class="text-orange-600"
                      >
                        <XCircle class="w-4 h-4 mr-2" />
                        禁用角色
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="role.status === '禁用'"
                        @click="handleEnable(role.id)"
                        class="text-green-600"
                      >
                        <CheckCircle class="w-4 h-4 mr-2" />
                        启用角色
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        @click="handleDelete(role.id)"
                        class="text-red-600"
                        :disabled="role.userCount > 0"
                      >
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除角色
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredRoles.length) }} 条， 共
            {{ filteredRoles.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 权限配置对话框 -->
    <Dialog :open="showPermissionDialog" @update:open="showPermissionDialog = $event">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>权限配置 - {{ currentRole?.name }}</DialogTitle>
          <DialogDescription> 为角色配置企业功能权限，支持按模块分组管理 </DialogDescription>
        </DialogHeader>

        <div class="space-y-6 py-4">
          <!-- 权限搜索 -->
          <div class="flex items-center gap-2">
            <Search class="w-4 h-4 text-muted-foreground" />
            <Input v-model="permissionSearch" placeholder="搜索权限..." class="max-w-sm" />
          </div>

          <!-- 权限树 -->
          <div v-for="module in filteredPermissions" :key="module.id" class="space-y-2">
            <div class="flex items-center gap-2 py-2 border-b">
              <Checkbox
                :id="`module-${module.id}`"
                :checked="isModuleChecked(module)"
                @update:checked="handleModuleCheck(module, $event)"
              />
              <Label :for="`module-${module.id}`" class="font-medium">
                {{ module.name }}
              </Label>
              <Badge variant="outline" class="text-xs ml-2">
                {{ module.permissions?.length || 0 }} 项
              </Badge>
            </div>
            <div class="ml-6 space-y-2">
              <div
                v-for="permission in module.permissions"
                :key="permission.id"
                class="flex items-center gap-2 py-1"
              >
                <Checkbox
                  :id="`perm-${permission.id}`"
                  :checked="selectedPermissions.includes(permission.id)"
                  @update:checked="handlePermissionCheck(permission.id, $event)"
                />
                <Label :for="`perm-${permission.id}`" class="text-sm">
                  {{ permission.name }}
                </Label>
                <span class="text-xs text-muted-foreground ml-2">
                  {{ permission.code }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showPermissionDialog = false">取消</Button>
          <Button @click="handleSavePermissions">保存权限</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 员工分配对话框 -->
    <Dialog :open="showUserDialog" @update:open="showUserDialog = $event">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>员工分配 - {{ currentRole?.name }}</DialogTitle>
          <DialogDescription> 为角色分配或移除企业员工，支持批量操作 </DialogDescription>
        </DialogHeader>

        <div class="space-y-4 py-4">
          <!-- 员工搜索 -->
          <div class="flex items-center gap-2">
            <Search class="w-4 h-4 text-muted-foreground" />
            <Input v-model="userSearch" placeholder="搜索员工..." class="flex-1" />
          </div>

          <!-- 员工列表 -->
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <div
              v-for="user in filteredUsers"
              :key="user.id"
              class="flex items-center justify-between p-2 border rounded hover:bg-muted/50"
            >
              <div class="flex items-center gap-2">
                <Checkbox
                  :id="`user-${user.id}`"
                  :checked="selectedUsers.includes(user.id)"
                  @update:checked="handleUserCheck(user.id, $event)"
                />
                <div>
                  <div class="font-medium text-sm">{{ user.realName }}</div>
                  <div class="text-xs text-muted-foreground">
                    {{ user.username }} - {{ user.department }}
                  </div>
                </div>
              </div>
              <Badge variant="outline">
                {{ user.position }}
              </Badge>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showUserDialog = false">取消</Button>
          <Button @click="handleSaveUsers">保存分配</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  CheckCircle,
  Edit,
  Eye,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  ShieldCheck,
  Trash2,
  Users,
  XCircle,
  Crown,
  UserCheck,
  Settings,
  Building2,
  Database,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type RoleStatus = '启用' | '禁用'

interface Role {
  id: number
  name: string
  description: string
  permissionCount: number
  userCount: number
  status: RoleStatus
  createdAt: string
}

interface Permission {
  id: number
  name: string
  code: string
  parentId?: number
}

interface PermissionModule {
  id: number
  name: string
  permissions: Permission[]
}

interface User {
  id: string
  username: string
  realName: string
  department: string
  position: string
}

// 筛选条件
const filters = ref({
  name: '',
  status: 'ALL' as 'ALL' | RoleStatus,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '角色名称',
    type: 'input',
    placeholder: '请输入角色名称',
  },
  {
    key: 'status',
    label: '角色状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '启用', value: '启用' },
      { label: '禁用', value: '禁用' },
    ],
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const showPermissionDialog = ref(false)
const showUserDialog = ref(false)
const currentRole = ref<Role | null>(null)
const permissionSearch = ref('')
const userSearch = ref('')
const selectedPermissions = ref<number[]>([])
const selectedUsers = ref<string[]>([])

// Mock 数据
const roles = ref<Role[]>([
  {
    id: 1,
    name: '企业管理员',
    description: '企业最高管理员，拥有企业内所有功能权限',
    permissionCount: 25,
    userCount: 2,
    status: '启用',
    createdAt: '2025-01-01 00:00:00',
  },
  {
    id: 2,
    name: '安全监测员',
    description: '负责企业安全风险监测和事件处理，具备监测相关功能权限',
    permissionCount: 18,
    userCount: 5,
    status: '启用',
    createdAt: '2025-01-15 09:30:00',
  },
  {
    id: 3,
    name: '审核专员',
    description: '负责企业内部备案信息审核和管理，具备审核相关权限',
    permissionCount: 12,
    userCount: 3,
    status: '启用',
    createdAt: '2025-02-01 14:20:00',
  },
  {
    id: 4,
    name: '风险处置专员',
    description: '负责风险事件处置和应急响应，具备处置相关功能权限',
    permissionCount: 8,
    userCount: 4,
    status: '启用',
    createdAt: '2025-02-15 11:45:00',
  },
  {
    id: 5,
    name: '数据分析员',
    description: '负责企业数据统计分析和报表生成，具备数据查看和导出权限',
    permissionCount: 6,
    userCount: 0,
    status: '禁用',
    createdAt: '2025-03-01 16:00:00',
  },
])

const permissions = ref<PermissionModule[]>([
  {
    id: 1,
    name: '综合概览',
    permissions: [
      { id: 101, name: '企业端首页', code: 'corp:dashboard:view' },
      { id: 102, name: '统计数据查看', code: 'corp:dashboard:stats' },
    ],
  },
  {
    id: 2,
    name: '注册备案',
    permissions: [
      { id: 201, name: '企业基本信息填报', code: 'corp:filing:basic:manage' },
      { id: 202, name: '测绘资质信息填报', code: 'corp:filing:qualification:manage' },
      { id: 203, name: '防控制度措施填报', code: 'corp:filing:policy:manage' },
      { id: 204, name: '防控技术措施填报', code: 'corp:filing:tech:manage' },
      { id: 205, name: '车辆信息填报', code: 'corp:filing:vehicle:manage' },
      { id: 206, name: '数据处理活动填报', code: 'corp:filing:activity:manage' },
      { id: 207, name: '备案记录管理', code: 'corp:filing:records:view' },
    ],
  },
  {
    id: 3,
    name: '风险事件',
    permissions: [
      { id: 301, name: '风险统计查看', code: 'corp:risk:stats:view' },
      { id: 302, name: '事件统计查看', code: 'corp:event:stats:view' },
      { id: 303, name: '风险处置', code: 'corp:risk:handle' },
      { id: 304, name: '事件处置', code: 'corp:event:handle' },
    ],
  },
  {
    id: 4,
    name: '通知待办',
    permissions: [
      { id: 401, name: '通知信息查看', code: 'corp:notice:view' },
      { id: 402, name: '任务管理', code: 'corp:task:manage' },
      { id: 403, name: '待办处理', code: 'corp:todo:handle' },
    ],
  },
  {
    id: 5,
    name: '企业管理',
    permissions: [
      { id: 501, name: '用户管理', code: 'corp:management:users:manage' },
      { id: 502, name: '角色权限管理', code: 'corp:management:roles:manage' },
      { id: 503, name: '企业设置', code: 'corp:management:settings:manage' },
    ],
  },
])

const users = ref<User[]>([
  {
    id: 'C-2025-001',
    username: 'corp_admin',
    realName: '张总经理',
    department: '总经理办公室',
    position: '总经理',
  },
  {
    id: 'C-2025-002',
    username: 'safety_manager',
    realName: '李安全员',
    department: '安全管理部',
    position: '安全管理员',
  },
  {
    id: 'C-2025-003',
    username: 'data_analyst',
    realName: '王分析师',
    department: '技术部',
    position: '数据分析师',
  },
  {
    id: 'C-2025-004',
    username: 'auditor_01',
    realName: '刘审核员',
    department: '合规部',
    position: '合规审核员',
  },
])

// 计算属性
const filteredRoles = computed(() => {
  return roles.value.filter((role) => {
    if (filters.value.name && !role.name.includes(filters.value.name)) {
      return false
    }
    if (filters.value.status !== 'ALL' && role.status !== filters.value.status) {
      return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredRoles.value.length / pageSize.value)),
)

const pagedRoles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredRoles.value.slice(start, start + pageSize.value)
})

const filteredPermissions = computed(() => {
  if (!permissionSearch.value) return permissions.value

  return permissions.value
    .map((module) => ({
      ...module,
      permissions: module.permissions.filter(
        (perm) =>
          perm.name.includes(permissionSearch.value) ||
          perm.code.toLowerCase().includes(permissionSearch.value.toLowerCase()),
      ),
    }))
    .filter((module) => module.permissions.length > 0)
})

const filteredUsers = computed(() => {
  if (!userSearch.value) return users.value

  return users.value.filter(
    (user) =>
      user.realName.includes(userSearch.value) ||
      user.username.includes(userSearch.value) ||
      user.department.includes(userSearch.value) ||
      user.position.includes(userSearch.value),
  )
})

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    name: '',
    status: 'ALL',
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = ['序号', '角色名称', '描述', '权限数量', '员工数量', '状态', '创建时间']
  const rows = filteredRoles.value.map((role, index) => [
    (index + 1).toString(),
    role.name,
    role.description,
    role.permissionCount.toString(),
    role.userCount.toString(),
    role.status,
    role.createdAt,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `角色管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleCreateRole = () => {
  console.log('创建新角色')
}

const handleEdit = (id: number) => {
  console.log('编辑角色:', id)
}

const handleConfigPermissions = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    currentRole.value = role
    selectedPermissions.value = [101, 102, 201, 202] // Mock selected permissions
    showPermissionDialog.value = true
  }
}

const handleAssignUsers = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    currentRole.value = role
    selectedUsers.value = ['C-2025-001', 'C-2025-002'] // Mock selected users
    showUserDialog.value = true
  }
}

const handleViewPermissions = (id: number) => {
  console.log('查看权限:', id)
}

const handleEnable = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    role.status = '启用'
  }
}

const handleDisable = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    role.status = '禁用'
  }
}

const handleDelete = (id: number) => {
  const index = roles.value.findIndex((r) => r.id === id)
  if (index > -1) {
    roles.value.splice(index, 1)
  }
}

// 权限相关
const isModuleChecked = (module: PermissionModule) => {
  const modulePermIds = module.permissions.map((p) => p.id)
  return modulePermIds.every((id) => selectedPermissions.value.includes(id))
}

const handleModuleCheck = (module: PermissionModule, checked: boolean) => {
  const modulePermIds = module.permissions.map((p) => p.id)
  if (checked) {
    selectedPermissions.value = [...new Set([...selectedPermissions.value, ...modulePermIds])]
  } else {
    selectedPermissions.value = selectedPermissions.value.filter(
      (id) => !modulePermIds.includes(id),
    )
  }
}

const handlePermissionCheck = (id: number, checked: boolean) => {
  if (checked) {
    if (!selectedPermissions.value.includes(id)) {
      selectedPermissions.value.push(id)
    }
  } else {
    selectedPermissions.value = selectedPermissions.value.filter((permId) => permId !== id)
  }
}

const handleUserCheck = (id: string, checked: boolean) => {
  if (checked) {
    if (!selectedUsers.value.includes(id)) {
      selectedUsers.value.push(id)
    }
  } else {
    selectedUsers.value = selectedUsers.value.filter((userId) => userId !== id)
  }
}

const handleSavePermissions = () => {
  console.log('保存权限配置:', selectedPermissions.value)
  showPermissionDialog.value = false
}

const handleSaveUsers = () => {
  console.log('保存员工分配:', selectedUsers.value)
  showUserDialog.value = false
}

// 样式函数
const getStatusVariant = (status: RoleStatus) => {
  switch (status) {
    case '启用':
      return 'default'
    case '禁用':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getRoleIcon = (roleName: string) => {
  if (roleName.includes('企业管理员')) return Crown
  if (roleName.includes('安全监测员')) return Shield
  if (roleName.includes('审核专员')) return UserCheck
  if (roleName.includes('风险处置专员')) return ShieldCheck
  if (roleName.includes('数据分析员')) return Database
  return Building2
}
</script>
