﻿表名: log_main,,,,,,,,,,
No.,项目ID (Field),项目名 (Name),类型 (Type),长度 (Length),必须 (Required),主键 (PK),备注 (Comment),,,
1,id,主键ID,BIGINT,,○,●,自增主键。,,,
2,log_id,日志ID,VARCHAR,64,○,,"""全局唯一的日志ID", 业务主键",,
3,data_package_id,数据包唯一标识,VARCHAR,64,,,"""【修订】核心溯源ID。云端日志(0x04-0x0a)必填。对于批量操作，采用拆分策略，每条记录对应一个ID。""",,,
4,enterprise_id,企业ID,VARCHAR,32,○,,关联 enterprise_info。,,,
5,vin,车辆VIN,VARCHAR,17,,,"""【修订】关联 vehicle_info。仅车端日志(0x01-0x03)需要填写。""",,,
6,log_source,日志来源,SMALLINT,,○,,"""1: 车端", 2: 企业端(云端)",,
7,processing_stage,处理阶段,SMALLINT,,○,,"""协议定义的信息类型标志", e.g., 0x01:车端收集, 0x04:云端收集..."
8,log_timestamp,日志时间戳,BIGINT,,○,,事件发生的毫秒级时间戳。建议作为分区键。,,,
9,is_success,是否成功,BOOLEAN,,○,,操作是否成功,,,
10,error_code,错误码,VARCHAR,16,,,,,,
11,create_time,创建时间,TIMESTAMP,,○,,日志记录入库时间,,,
,,,,,,,,,,
,,,,,,,,,,
