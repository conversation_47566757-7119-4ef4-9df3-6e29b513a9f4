<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">集中管理企业各类申请，包含待办任务、已办任务和发起跟踪</p>
      </div>
    </div>

    <!-- 任务统计概览 -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <!-- 待办任务 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">待办任务</CardTitle>
          <Clock class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.pendingTasks }}</div>
          <p class="text-xs text-muted-foreground">+{{ stats.newTasks }} 今日新增</p>
          <div class="mt-3">
            <PieChart
              :data="pendingTasksChartData"
              :center-text="stats.pendingTasks.toString()"
              :center-sub-text="'待办任务'"
              :height="240"
              :show-legend="true"
              color-scheme="primary"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 已办任务 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">已办任务</CardTitle>
          <CheckCircle class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.completedTasks }}</div>
          <p class="text-xs text-muted-foreground">本月完成 {{ stats.monthlyCompleted }} 个</p>
          <div class="mt-3 h-[320px]">
            <BarChart
              :data="completedTasksChartData"
              :height="320"
              :show-legend="false"
              color-scheme="oceanDepths"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 跟踪任务 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">跟踪任务</CardTitle>
          <Eye class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.trackingTasks }}</div>
          <p class="text-xs text-muted-foreground">{{ stats.urgentTasks }} 个需催办</p>
          <div class="mt-3">
            <PieChart
              :data="trackingTasksChartData"
              :center-text="stats.trackingTasks.toString()"
              :center-sub-text="'跟踪任务'"
              :height="240"
              :show-legend="true"
              color-scheme="enterprise"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 审批效率 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">审批效率</CardTitle>
          <TrendingUp class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <!-- 顶部关键指标 -->
          <div class="flex items-baseline gap-2">
            <div class="text-2xl font-bold">{{ completionRate }}%</div>
            <span class="text-xs text-muted-foreground">审批完成率</span>
          </div>

          <!-- 完成率进度条 -->
          <div class="mt-3 space-y-2">
            <div class="flex justify-between text-xs">
              <span>审批完成率</span>
              <span>{{ completionRate }}%</span>
            </div>
            <div class="w-full bg-muted rounded-full h-2">
              <div
                class="h-2 rounded-full bg-primary transition-all"
                :style="{ width: completionRate + '%' }"
              ></div>
            </div>
          </div>

          <!-- 额外统计信息 -->
          <div class="grid grid-cols-2 gap-3 mt-4 text-xs">
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">今日新增待审</span>
                <span class="font-medium">{{ stats.newTasks }}</span>
              </div>
            </div>
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">本周已完成</span>
                <span class="font-medium">{{ stats.weekApproved }}</span>
              </div>
            </div>
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">平均审批时长</span>
                <span class="font-medium">{{ stats.avgProcessTime }} 天</span>
              </div>
            </div>
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">超时待处理</span>
                <span class="font-medium text-orange-600 dark:text-orange-400">{{
                  stats.overduePending
                }}</span>
              </div>
            </div>
          </div>

          <!-- 状态摘要 -->
          <div class="mt-3 grid grid-cols-3 gap-2 text-xs">
            <div class="flex items-center justify-between rounded-md border px-2 py-1">
              <span class="text-muted-foreground">在办</span>
              <span class="font-medium">{{ stats.pendingTasks }}</span>
            </div>
            <div class="flex items-center justify-between rounded-md border px-2 py-1">
              <span class="text-muted-foreground">已完成</span>
              <span class="font-medium">{{ stats.completedTasks }}</span>
            </div>
            <div class="flex items-center justify-between rounded-md border px-2 py-1">
              <span class="text-muted-foreground">需催办</span>
              <span class="font-medium">{{ stats.urgentTasks }}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 任务管理主界面 -->
    <Card>
      <CardHeader>
        <CardTitle>任务管理</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs v-model="activeTab" class="w-full">
          <TabsList class="grid w-full grid-cols-3">
            <TabsTrigger value="pending">待办任务</TabsTrigger>
            <TabsTrigger value="completed">已办任务</TabsTrigger>
            <TabsTrigger value="tracking">发起跟踪</TabsTrigger>
          </TabsList>

          <!-- 待办任务 -->
          <TabsContent value="pending" class="space-y-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">待办任务列表</h3>
                <Badge variant="outline">共 {{ pendingTasks.length }} 条记录</Badge>
              </div>
              <!-- 查询条件 -->
              <CompactFilterForm
                :filter-fields="pendingFilterFields"
                :initial-values="pendingSearchForm"
                @search="handlePendingSearch"
                @reset="resetPendingSearch"
              />

              <!-- 待办任务列表 -->
              <div class="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>序号</TableHead>
                      <TableHead>企业名称</TableHead>
                      <TableHead>申请类型</TableHead>
                      <TableHead>申请材料</TableHead>
                      <TableHead>申请时间</TableHead>
                      <TableHead>剩余时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow v-for="(task, index) in pendingPagedTasks" :key="task.id">
                      <TableCell>{{ (pendingPage - 1) * pendingPageSize + index + 1 }}</TableCell>
                      <TableCell>{{ task.enterpriseName }}</TableCell>
                      <TableCell>
                        <Badge :variant="getApplicationTypeVariant(task.applicationType)">
                          {{ task.applicationType }}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline" @click="viewMaterials(task)">
                          <FileText class="w-4 h-4 mr-1" />
                          查看材料
                        </Button>
                      </TableCell>
                      <TableCell>{{ formatDateTime(task.applicationTime) }}</TableCell>
                      <TableCell>
                        <div :class="task.isUrgent ? 'text-red-600 font-medium' : ''">
                          {{ task.remainingTime }}
                          <AlertTriangle
                            v-if="task.isUrgent"
                            class="w-4 h-4 inline ml-1 text-red-500"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" @click="handleApproval(task)">审批</Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <!-- 分页：待办任务 -->
              <div class="flex items-center justify-between mt-4 px-2">
                <div class="pagination-size-control">
                  <span>每页显示</span>
                  <Select
                    :model-value="pendingPageSize.toString()"
                    @update:model-value="onPendingPageSizeChange"
                  >
                    <SelectTrigger class="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span>条</span>
                </div>

                <Pagination
                  v-model:page="pendingPage"
                  :total="pendingTasks.length"
                  :items-per-page="pendingPageSize"
                  :sibling-count="1"
                  :show-edges="true"
                >
                  <PaginationContent v-slot="{ items }">
                    <PaginationFirst />
                    <PaginationPrevious />
                    <template v-for="(p, idx) in items" :key="idx">
                      <PaginationItem
                        v-if="p.type === 'page'"
                        :value="p.value"
                        :is-active="p.value === pendingPage"
                      />
                      <PaginationEllipsis v-else />
                    </template>
                    <PaginationNext />
                    <PaginationLast />
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          </TabsContent>

          <!-- 已办任务 -->
          <TabsContent value="completed" class="space-y-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">已办任务列表</h3>
                <Badge variant="outline">共 {{ completedTasks.length }} 条记录</Badge>
              </div>
              <!-- 已办任务查询 -->
              <CompactFilterForm
                :filter-fields="completedFilterFields"
                :initial-values="completedSearchForm"
                @search="handleCompletedSearch"
                @reset="resetCompletedSearch"
              />

              <!-- 已办任务列表 -->
              <div class="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>序号</TableHead>
                      <TableHead>企业名称</TableHead>
                      <TableHead>申请类型</TableHead>
                      <TableHead>申请材料</TableHead>
                      <TableHead>申请时间</TableHead>
                      <TableHead>审批时间</TableHead>
                      <TableHead>审批结果</TableHead>
                      <TableHead>操作人员</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow v-for="(task, index) in completedPagedTasks" :key="task.id">
                      <TableCell>{{
                        (completedPage - 1) * completedPageSize + index + 1
                      }}</TableCell>
                      <TableCell>{{ task.enterpriseName }}</TableCell>
                      <TableCell>
                        <Badge :variant="getApplicationTypeVariant(task.applicationType)">
                          {{ task.applicationType }}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline" @click="viewMaterials(task)">
                          <FileText class="w-4 h-4 mr-1" />
                          查看材料
                        </Button>
                      </TableCell>
                      <TableCell>{{ formatDateTime(task.applicationTime) }}</TableCell>
                      <TableCell>{{ formatDateTime(task.approvalTime!) }}</TableCell>
                      <TableCell>
                        <Badge :variant="getApprovalResultVariant(task.approvalResult!)">
                          {{ task.approvalResult }}
                        </Badge>
                      </TableCell>
                      <TableCell>{{ task.operator }}</TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline" @click="viewApprovalHistory(task)">
                          审批记录
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <!-- 分页：已办任务 -->
              <div class="flex items-center justify-between mt-4 px-2">
                <div class="pagination-size-control">
                  <span>每页显示</span>
                  <Select
                    :model-value="completedPageSize.toString()"
                    @update:model-value="onCompletedPageSizeChange"
                  >
                    <SelectTrigger class="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span>条</span>
                </div>

                <Pagination
                  v-model:page="completedPage"
                  :total="completedTasks.length"
                  :items-per-page="completedPageSize"
                  :sibling-count="1"
                  :show-edges="true"
                >
                  <PaginationContent v-slot="{ items }">
                    <PaginationFirst />
                    <PaginationPrevious />
                    <template v-for="(p, idx) in items" :key="idx">
                      <PaginationItem
                        v-if="p.type === 'page'"
                        :value="p.value"
                        :is-active="p.value === completedPage"
                      />
                      <PaginationEllipsis v-else />
                    </template>
                    <PaginationNext />
                    <PaginationLast />
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          </TabsContent>

          <!-- 发起跟踪 -->
          <TabsContent value="tracking" class="space-y-4">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">发起跟踪列表</h3>
                <div class="flex items-center gap-2">
                  <Badge variant="outline">共 {{ trackingTasks.length }} 条记录</Badge>
                  <Button @click="createTrackingTask">
                    <Plus class="w-4 h-4 mr-2" />
                    发起跟踪
                  </Button>
                </div>
              </div>

              <div class="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>序号</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>标题</TableHead>
                      <TableHead>提交时间</TableHead>
                      <TableHead>当前待办人</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow v-for="(task, index) in trackingTasks" :key="task.id">
                      <TableCell>{{ index + 1 }}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{{ task.type }}</Badge>
                      </TableCell>
                      <TableCell>{{ task.title }}</TableCell>
                      <TableCell>{{ formatDateTime(task.submitTime) }}</TableCell>
                      <TableCell>{{ task.currentHandler }}</TableCell>
                      <TableCell>
                        <Badge :variant="getTrackingStatusVariant(task.status)">
                          {{ task.status }}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div class="flex gap-1">
                          <Button size="sm" variant="outline" @click="viewTrackingDetail(task)">
                            查看
                          </Button>
                          <Button
                            v-if="task.canUrge"
                            size="sm"
                            variant="outline"
                            @click="urgeTask(task)"
                          >
                            催办
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <Sheet v-model:open="approvalDialogOpen">
          <SheetContent
            side="right"
            class="z-[60] w-[66vw] min-w-[640px] max-w-[1100px] max-h-[90vh] overflow-y-auto"
          >
            <!-- 审批操作区域 -->
            <div class="mt-6 space-y-4">
              <h3 class="text-lg font-semibold">审批操作</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-4">
                  <div class="space-y-2">
                    <Label>审批决定</Label>
                    <Select v-model="approvalDecision">
                      <SelectTrigger>
                        <SelectValue placeholder="请选择审批结果" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="approve">通过</SelectItem>
                        <SelectItem value="reject">驳回</SelectItem>
                        <SelectItem value="conditional">有条件通过</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div class="space-y-2">
                    <Label>审批意见</Label>
                    <Textarea v-model="approvalComments" placeholder="请输入审批意见..." rows="4" />
                  </div>
                </div>

                <div class="space-y-4">
                  <div class="space-y-2">
                    <Label>常见问题</Label>
                    <div class="space-y-2">
                      <div
                        v-for="issue in commonIssues"
                        :key="issue"
                        class="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          :id="issue"
                          v-model="selectedIssues"
                          :value="issue"
                        />
                        <label :for="issue" class="text-sm">{{ issue }}</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-between mt-6">
              <div class="flex gap-2">
                <Button @click="saveApprovalDraft">
                  <Save class="w-4 h-4 mr-2" />
                  保存草稿
                </Button>
                <Button @click="submitApproval" :disabled="!approvalDecision">
                  <CheckCircle class="w-4 h-4 mr-2" />
                  提交审批
                </Button>
              </div>

              <Button variant="outline" @click="approvalDialogOpen = false"> 关闭 </Button>
            </div>
          </SheetContent>
        </Sheet>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Clock,
  CheckCircle,
  Eye,
  TrendingUp,
  FileText,
  AlertTriangle,
  Plus,
  Save,
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent } from '@/components/ui/sheet'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from '@/components/ui/table'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { PieChart, BarChart } from '@/components/charts'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import {
  Pagination,
  PaginationContent,
  PaginationFirst,
  PaginationPrevious,
  PaginationNext,
  PaginationLast,
  PaginationItem,
  PaginationEllipsis,
} from '@/components/ui/pagination'

// 接口定义
interface ApprovalTask {
  id: string
  enterpriseName: string
  creditCode: string
  applicationType: '新注册' | '更新' | '申诉' | '风险评估'
  applicationTime: string
  approvalTime?: string
  approvalResult?: '通过' | '驳回' | '有条件通过'
  operator?: string
  remainingTime?: string
  isUrgent?: boolean
}

interface TrackingTask {
  id: string
  type: string
  title: string
  submitTime: string
  currentHandler: string
  status: '进行中' | '已完成' | '已暂停'
  canUrge: boolean
}

interface SearchForm {
  enterpriseName: string
  creditCode?: string
  applicationType: string
  applicationTimeRange?: string
  approvalResult?: string
}

// 响应式数据
const currentTime = ref('')
const activeTab = ref('pending')
const approvalDialogOpen = ref(false)
const selectedTask = ref<ApprovalTask | null>(null)
const approvalActiveTab = ref('basic')
const approvalDecision = ref('')
const approvalComments = ref('')
const selectedIssues = ref<string[]>([])

// 统计数据
const stats = ref({
  pendingTasks: 23,
  newTasks: 5,
  completedTasks: 156,
  monthlyCompleted: 89,
  trackingTasks: 12,
  urgentTasks: 3,
  efficiency: 85,
  avgProcessTime: 4.2,
  weekApproved: 57,
  overduePending: 2,
})

// 搜索表单
const pendingSearchForm = ref<SearchForm>({
  enterpriseName: '',
  creditCode: '',
  applicationType: '',
  applicationTimeRange: '',
})

const completedSearchForm = ref<SearchForm>({
  enterpriseName: '',
  applicationType: '',
  approvalResult: '',
})

// 待办任务筛选字段配置
const pendingFilterFields: FilterField[] = [
  {
    key: 'enterpriseName',
    label: '企业名称',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'creditCode',
    label: '信用代码',
    type: 'input',
    placeholder: '请输入信用代码',
  },
  {
    key: 'applicationType',
    label: '申请类型',
    type: 'select',
    placeholder: '请选择申请类型',
    options: [
      { label: '全部类型', value: 'all' },
      { label: '新注册', value: '新注册' },
      { label: '更新', value: '更新' },
      { label: '申诉', value: '申诉' },
      { label: '风险评估', value: '风险评估' },
    ],
    defaultValue: 'all',
  },
  {
    key: 'applicationTimeRange',
    label: '申请时间范围',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 已办任务筛选字段配置
const completedFilterFields: FilterField[] = [
  {
    key: 'enterpriseName',
    label: '企业名称',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'applicationType',
    label: '申请类型',
    type: 'select',
    placeholder: '请选择申请类型',
    options: [
      { label: '全部类型', value: 'all' },
      { label: '新注册', value: '新注册' },
      { label: '更新', value: '更新' },
      { label: '申诉', value: '申诉' },
    ],
    defaultValue: 'all',
  },
  {
    key: 'approvalResult',
    label: '审批结果',
    type: 'select',
    placeholder: '请选择审批结果',
    options: [
      { label: '全部结果', value: 'all' },
      { label: '通过', value: '通过' },
      { label: '驳回', value: '驳回' },
      { label: '有条件通过', value: '有条件通过' },
    ],
    defaultValue: 'all',
  },
]

// 模拟数据生成
import { randomInt } from '@/lib/mock-data-generator'
const appTypes: ApprovalTask['applicationType'][] = ['新注册', '更新', '申诉', '风险评估']
const operators = ['张审核员', '李审核员', '王审核员', '赵审核员']
const companyPrefixes = ['北京', '上海', '深圳', '杭州', '南京', '成都', '重庆', '武汉']
const companySuffixes = ['智行', '车联', '自动驾驶', '地图', '数据', '科技']

function randomCompany() {
  const p = companyPrefixes[randomInt(0, companyPrefixes.length - 1)]
  const s = companySuffixes[randomInt(0, companySuffixes.length - 1)]
  return `${p}${s}有限公司`
}
function randomCredit() {
  const chars = '0123456789ABCDEFGHJKLMNPQRTUWXY'
  let t = ''
  for (let i = 0; i < 18; i++) t += chars.charAt(randomInt(0, chars.length - 1))
  return t
}
function isoDate() {
  const y = randomInt(2023, 2025)
  const m = String(randomInt(1, 12)).padStart(2, '0')
  const d = String(randomInt(1, 28)).padStart(2, '0')
  const hh = String(randomInt(0, 23)).padStart(2, '0')
  const mm = String(randomInt(0, 59)).padStart(2, '0')
  return `${y}-${m}-${d}T${hh}:${mm}:00`
}

function generatePending(n = 40): ApprovalTask[] {
  return Array.from({ length: n }).map((_, i) => ({
    id: `${i + 1}`,
    enterpriseName: randomCompany(),
    creditCode: randomCredit(),
    applicationType: appTypes[randomInt(0, appTypes.length - 1)],
    applicationTime: isoDate(),
    remainingTime: `${randomInt(1, 5)}天`,
    isUrgent: Math.random() < 0.2,
  }))
}

function generateCompleted(n = 80): ApprovalTask[] {
  const results: ApprovalTask['approvalResult'][] = ['通过', '驳回', '有条件通过']
  return Array.from({ length: n }).map((_, i) => ({
    id: `${i + 1000}`,
    enterpriseName: randomCompany(),
    creditCode: randomCredit(),
    applicationType: appTypes[randomInt(0, appTypes.length - 1)],
    applicationTime: isoDate(),
    approvalTime: isoDate(),
    approvalResult: results[randomInt(0, results.length - 1)],
    operator: operators[randomInt(0, operators.length - 1)],
  }))
}

const pendingTasks = ref<ApprovalTask[]>(generatePending(42))

const completedTasks = ref<ApprovalTask[]>(generateCompleted(96))

const trackingTasks = ref<TrackingTask[]>([
  {
    id: '1',
    type: '审批跟踪',
    title: '智能网联汽车测绘资质审核跟踪',
    submitTime: '2024-01-10T09:00:00',
    currentHandler: '李审核员',
    status: '进行中',
    canUrge: true,
  },
])

const commonIssues = ref([
  '材料不完整',
  '资质证书过期',
  '技术方案不符合要求',
  '安全措施不完善',
  '企业信息不一致',
])

// 分页状态与切片
const pendingPage = ref(1)
const pendingPageSize = ref(10)
const completedPage = ref(1)
const completedPageSize = ref(10)

const pendingPagedTasks = computed(() => {
  const start = (pendingPage.value - 1) * pendingPageSize.value
  return pendingTasks.value.slice(start, start + pendingPageSize.value)
})
const completedPagedTasks = computed(() => {
  const start = (completedPage.value - 1) * completedPageSize.value
  return completedTasks.value.slice(start, start + completedPageSize.value)
})

const onPendingPageSizeChange = (val: string) => {
  const n = Number(val)
  if (!Number.isNaN(n)) {
    pendingPageSize.value = n
    pendingPage.value = 1
  }
}
const onCompletedPageSizeChange = (val: string) => {
  const n = Number(val)
  if (!Number.isNaN(n)) {
    completedPageSize.value = n
    completedPage.value = 1
  }
}

// 计算属性
// const selectedTaskEnterprise = computed((): Enterprise | null => {
//   if (!selectedTask.value) return null
//
//   return {
//     id: '1',
//     name: selectedTask.value.enterpriseName,
//     type: '整车生产企业',
//     creditCode: selectedTask.value.creditCode,
//     registrationStatus: '待审核',
//     registrationTime: selectedTask.value.applicationTime,
//     contactPerson: '张三',
//     contactPhone: '13800138000',
//     address: '北京市朝阳区科技园区',
//     vehicleCount: 1250,
//     riskLevel: '低',
//   }
// })

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getApplicationTypeVariant = (type: string) => {
  switch (type) {
    case '新注册':
      return 'default'
    case '更新':
      return 'secondary'
    case '申诉':
      return 'outline'
    case '风险评估':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getApprovalResultVariant = (result: string) => {
  switch (result) {
    case '通过':
      return 'default'
    case '驳回':
      return 'destructive'
    case '有条件通过':
      return 'secondary'
    default:
      return 'outline'
  }
}

const getTrackingStatusVariant = (status: string) => {
  switch (status) {
    case '进行中':
      return 'secondary'
    case '已完成':
      return 'default'
    case '已暂停':
      return 'destructive'
    default:
      return 'outline'
  }
}

const handlePendingSearch = (filters?: Record<string, string | number | boolean>) => {
  if (filters) {
    Object.assign(pendingSearchForm.value, filters)
  }
  console.log('搜索待办任务:', pendingSearchForm.value)
}

const resetPendingSearch = () => {
  pendingSearchForm.value = {
    enterpriseName: '',
    creditCode: '',
    applicationType: 'all',
    applicationTimeRange: '',
  }
}

const handleCompletedSearch = (filters?: Record<string, string | number | boolean>) => {
  if (filters) {
    Object.assign(completedSearchForm.value, filters)
  }
  console.log('搜索已办任务:', completedSearchForm.value)
}

const resetCompletedSearch = () => {
  completedSearchForm.value = {
    enterpriseName: '',
    applicationType: 'all',
    approvalResult: 'all',
  }
}

const handleApproval = (task: ApprovalTask) => {
  selectedTask.value = task
  approvalActiveTab.value = 'basic'
  approvalDecision.value = ''
  approvalComments.value = ''
  selectedIssues.value = []
  approvalDialogOpen.value = true
}

const viewMaterials = (task: ApprovalTask) => {
  console.log('查看申请材料:', task.id)
}

const viewApprovalHistory = (task: ApprovalTask) => {
  console.log('查看审批记录:', task.id)
}

const createTrackingTask = () => {
  console.log('发起跟踪任务')
}

const viewTrackingDetail = (task: TrackingTask) => {
  console.log('查看跟踪详情:', task.id)
}

const urgeTask = (task: TrackingTask) => {
  console.log('催办任务:', task.id)
}

const saveApprovalDraft = () => {
  console.log('保存审批草稿')
}

const submitApproval = () => {
  console.log('提交审批:', {
    decision: approvalDecision.value,
    comments: approvalComments.value,
    issues: selectedIssues.value,
  })
  approvalDialogOpen.value = false
}

// 图表数据
const pendingTasksChartData = computed(() => [
  { name: '新注册', value: 12, color: '#0f4c75' },
  { name: '更新', value: 7, color: '#3282b8' },
  { name: '申诉', value: 3, color: '#2e8b57' },
  { name: '风险评估', value: 1, color: '#4682b4' },
])

const completedTasksChartData = computed(() => [
  { name: '本周', value: 45, color: '#0f4c75' },
  { name: '上周', value: 38, color: '#3282b8' },
  { name: '两周前', value: 42, color: '#2e8b57' },
  { name: '三周前', value: 31, color: '#4682b4' },
])

const trackingTasksChartData = computed(() => [
  { name: '需催办', value: stats.value.urgentTasks, color: '#1b262c' },
  {
    name: '正常跟踪',
    value: stats.value.trackingTasks - stats.value.urgentTasks,
    color: '#0f4c75',
  },
])

// 审批完成率（动态计算）
const completionRate = computed(() => stats.value.efficiency)

// 审批效率图表数据已移除，如需要可重新添加
// const efficiencyChartData = computed(() => [
//   {
//     name: '审批效率',
//     data: [
//       { name: '本月', value: 92 },
//       { name: '上月', value: 87 },
//       { name: '两月前', value: 89 },
//       { name: '三月前', value: 84 },
//     ],
//     color: '#2ecc71',
//   },
// ])

// 生命周期
let timeInterval: ReturnType<typeof setInterval>

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>
