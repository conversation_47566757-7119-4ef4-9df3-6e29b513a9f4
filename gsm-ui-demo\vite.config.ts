import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { fileURLToPath } from 'node:url'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const base = env.VITE_BASE || env.BASE_URL || '/'
  return {
    base,
    plugins: [
      vue({
        template: {
          compilerOptions: {
            // 移除生产环境中的注释
            comments: false,
            // 启用更激进的优化
            hoistStatic: true,
            cacheHandlers: true,
          },
        },
        // 启用响应式转换优化
        reactivityTransform: true,
      }),
      vueJsx({
        // 优化JSX编译
        optimize: true,
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      port: parseInt(env.VITE_DEV_PORT) || 4173, // 使用环境变量中的端口
      host: true, // 允许外部访问
      cors: true, // 启用 CORS
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers':
          'Origin, X-Requested-With, Content-Type, Accept, Authorization',
      },
      proxy: {
        // 代理高德地图API请求以解决CORS问题
        '/api/amap': {
          target: 'https://restapi.amap.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/amap/, ''),
          secure: true,
          headers: {
            Referer: `http://localhost:${parseInt(env.VITE_DEV_PORT) || 4173}`,
          },
        },
        // 代理高德地图JS API
        '/amap-js': {
          target: 'https://webapi.amap.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/amap-js/, ''),
          secure: true,
          headers: {
            Referer: `http://localhost:${parseInt(env.VITE_DEV_PORT) || 4173}`,
          },
        },
      },
    },
    build: {
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          // 完全禁用文件合并，每个模块独立输出
          manualChunks: (id) => {
            // 为每个依赖创建独立的chunk，进一步细分
            if (id.includes('node_modules')) {
              const parts = id.split('node_modules/')[1].split('/')
              const packageName = parts[0]
              
              // 对大型库进行更细粒度的分割
              if (packageName === 'vue' || packageName === '@vue') {
                const subModule = parts[1] || 'core'
                return `vendor-vue-${subModule}`
              }
              if (packageName === 'echarts') {
                const subModule = parts[1] || 'core'
                return `vendor-echarts-${subModule}`
              }
              if (packageName === 'radix-vue') {
                const subModule = parts[1] || 'core'
                return `vendor-radix-${subModule}`
              }
              if (packageName === 'lucide-vue-next') {
                return `vendor-lucide`
              }
              
              return `vendor-${packageName}`
            }
            
            // 为每个Vue组件创建独立的chunk，按目录分组
            if (id.includes('.vue')) {
              const pathParts = id.split('/src/')[1]?.split('/') || []
              const directory = pathParts.length > 1 ? pathParts[pathParts.length - 2] : 'root'
              const componentName = id.split('/').pop()?.replace('.vue', '') || 'component'
              return `${directory}-${componentName}`
            }
            
            // 为每个TypeScript/JavaScript文件创建独立的chunk，按目录分组
            if (id.includes('/src/') && (id.endsWith('.ts') || id.endsWith('.js'))) {
              const pathParts = id.split('/src/')[1]?.split('/') || []
              const directory = pathParts.length > 1 ? pathParts[pathParts.length - 2] : 'root'
              const fileName = id.split('/').pop()?.replace(/\.(ts|js)$/, '') || 'module'
              return `${directory}-${fileName}`
            }
            
            return null
          },
          // 使用更短的文件名，减小文件大小
          chunkFileNames: 'assets/[name]-[hash:8].js',
          entryFileNames: 'assets/[name]-[hash:8].js',
          assetFileNames: 'assets/[name]-[hash:8].[ext]',
        },
        // 启用更激进的代码分割
        external: [],
        // 优化依赖预构建
        treeshake: {
          preset: 'smallest',
          manualPureFunctions: ['console.log', 'console.info', 'console.warn'],
        },
      },
      chunkSizeWarningLimit: 500, // 降低警告限制，鼓励更小的chunk
      // 优化构建目标和压缩
      target: 'es2020', // 使用更现代的目标，减小polyfill
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 移除console.log
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info'], // 移除特定函数调用
          passes: 2, // 多次压缩优化
        },
        mangle: {
          toplevel: true, // 压缩顶级作用域
        },
        format: {
          comments: false, // 移除注释
        },
      },
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 减小资源内联阈值
      assetsInlineLimit: 2048, // 2KB以下的资源内联
      // 启用源码映射优化
      sourcemap: false, // 禁用源码映射以减小文件大小
    },
  }
})
