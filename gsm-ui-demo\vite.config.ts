import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { fileURLToPath } from 'node:url'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const base = env.VITE_BASE || env.BASE_URL || '/'
  return {
    base,
    plugins: [vue(), vueJsx()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      port: parseInt(env.VITE_DEV_PORT) || 4173, // 使用环境变量中的端口
      host: true, // 允许外部访问
      cors: true, // 启用 CORS
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers':
          'Origin, X-Requested-With, Content-Type, Accept, Authorization',
      },
      proxy: {
        // 代理高德地图API请求以解决CORS问题
        '/api/amap': {
          target: 'https://restapi.amap.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/amap/, ''),
          secure: true,
          headers: {
            Referer: `http://localhost:${parseInt(env.VITE_DEV_PORT) || 4173}`,
          },
        },
        // 代理高德地图JS API
        '/amap-js': {
          target: 'https://webapi.amap.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/amap-js/, ''),
          secure: true,
          headers: {
            Referer: `http://localhost:${parseInt(env.VITE_DEV_PORT) || 4173}`,
          },
        },
      },
    },
    build: {
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          manualChunks: {
            // 将小文件合并到更大的 chunk 中
            vendor: ['vue', 'vue-router', 'pinia'],
            ui: ['radix-vue', 'lucide-vue-next', 'class-variance-authority'],
            utils: ['clsx', 'tailwind-merge'],
          },
          // 确保chunk文件名稳定，避免缓存问题
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
        },
      },
      chunkSizeWarningLimit: 1000, // 增加警告限制
      // 确保构建时正确处理动态导入
      target: 'es2015',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
        },
      },
    },
  }
})
