<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理全国行政区域信息，支持区域层级配置、管辖范围定义和权限分配
        </p>
      </div>
      <Button @click="handleCreateRegion" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增区域
      </Button>
    </div>

    <!-- 区域列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>区域列表</span>
          <Badge variant="outline"> 共 {{ filteredRegions.length }} 个区域 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <!-- 筛选条件 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>区域名称</TableHead>
                <TableHead>区域代码</TableHead>
                <TableHead>区域级别</TableHead>
                <TableHead>上级区域</TableHead>
                <TableHead>负责人</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead class="w-[120px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedRegions.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无区域数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(item, index) in pagedRegions"
                :key="item.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="font-medium">{{ item.name }}</div>
                  <div class="text-sm text-muted-foreground">{{ item.fullName }}</div>
                </TableCell>
                <TableCell class="font-mono text-sm">{{ item.code }}</TableCell>
                <TableCell>
                  <Badge :variant="getLevelVariant(item.level)">{{ item.level }}</Badge>
                </TableCell>
                <TableCell>{{ item.parentName || '-' }}</TableCell>
                <TableCell>
                  <div v-if="item.supervisor">
                    <div class="font-medium text-sm">{{ item.supervisor.name }}</div>
                    <div class="text-xs text-muted-foreground">{{ item.supervisor.phone }}</div>
                  </div>
                  <span v-else class="text-muted-foreground">未分配</span>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(item.status)">{{ item.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ item.updatedAt }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleEdit(item.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleAssignSupervisor(item.id)">
                        <UserCheck class="w-4 h-4 mr-2" />
                        分配负责人
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleViewSubRegions(item.id)">
                        <MapPin class="w-4 h-4 mr-2" />
                        下级区域
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="item.status === '启用'"
                        @click="handleDisable(item.id)"
                      >
                        <XCircle class="w-4 h-4 mr-2" />
                        停用
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="item.status === '停用'"
                        @click="handleEnable(item.id)"
                      >
                        <CheckCircle class="w-4 h-4 mr-2" />
                        启用
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredRegions.length) }} 条， 共
            {{ filteredRegions.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  CheckCircle,
  Edit,
  MapPin,
  MoreHorizontal,
  Plus,
  UserCheck,
  XCircle,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type RegionLevel = '省级' | '市级' | '县级' | '区级'
type RegionStatus = '启用' | '停用'

interface RegionSupervisor {
  name: string
  phone: string
  email: string
}

interface RegionItem {
  id: string
  name: string
  fullName: string
  code: string
  level: RegionLevel
  parentId?: string
  parentName?: string
  supervisor?: RegionSupervisor
  status: RegionStatus
  createdAt: string
  updatedAt: string
}

// 筛选条件
const filters = ref({
  name: '',
  code: '',
  level: 'ALL' as 'ALL' | RegionLevel,
  status: 'ALL' as 'ALL' | RegionStatus,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '区域名称',
    type: 'input',
    placeholder: '请输入区域名称',
  },
  {
    key: 'code',
    label: '区域代码',
    type: 'input',
    placeholder: '请输入区域代码',
  },
  {
    key: 'level',
    label: '区域级别',
    type: 'select',
    placeholder: '请选择级别',
    options: [
      { label: '全部级别', value: 'ALL' },
      { label: '省级', value: '省级' },
      { label: '市级', value: '市级' },
      { label: '县级', value: '县级' },
      { label: '区级', value: '区级' },
    ],
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '启用', value: '启用' },
      { label: '停用', value: '停用' },
    ],
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// Mock数据
const regions = ref<RegionItem[]>([
  {
    id: 'R-2025-001',
    name: '北京市',
    fullName: '北京市',
    code: '110000',
    level: '省级',
    supervisor: {
      name: '李主管',
      phone: '138****0001',
      email: '<EMAIL>',
    },
    status: '启用',
    createdAt: '2025-01-10 09:00',
    updatedAt: '2025-08-15 14:30',
  },
  {
    id: 'R-2025-002',
    name: '海淀区',
    fullName: '北京市海淀区',
    code: '110108',
    level: '区级',
    parentId: 'R-2025-001',
    parentName: '北京市',
    supervisor: {
      name: '王区长',
      phone: '138****0002',
      email: '<EMAIL>',
    },
    status: '启用',
    createdAt: '2025-01-10 09:15',
    updatedAt: '2025-08-10 16:45',
  },
  {
    id: 'R-2025-003',
    name: '朝阳区',
    fullName: '北京市朝阳区',
    code: '110105',
    level: '区级',
    parentId: 'R-2025-001',
    parentName: '北京市',
    supervisor: {
      name: '张区长',
      phone: '138****0003',
      email: '<EMAIL>',
    },
    status: '启用',
    createdAt: '2025-01-10 09:20',
    updatedAt: '2025-07-25 11:20',
  },
  {
    id: 'R-2025-004',
    name: '上海市',
    fullName: '上海市',
    code: '310000',
    level: '省级',
    supervisor: {
      name: '陈主管',
      phone: '138****0004',
      email: '<EMAIL>',
    },
    status: '启用',
    createdAt: '2025-01-12 10:00',
    updatedAt: '2025-08-18 09:30',
  },
  {
    id: 'R-2025-005',
    name: '浦东新区',
    fullName: '上海市浦东新区',
    code: '310115',
    level: '区级',
    parentId: 'R-2025-004',
    parentName: '上海市',
    status: '停用',
    createdAt: '2025-01-12 10:15',
    updatedAt: '2025-06-30 16:00',
  },
])

// 过滤后的数据
const filteredRegions = computed(() => {
  return regions.value.filter((region) => {
    if (
      filters.value.name &&
      !region.name.includes(filters.value.name) &&
      !region.fullName.includes(filters.value.name)
    ) {
      return false
    }
    if (filters.value.code && !region.code.includes(filters.value.code)) {
      return false
    }
    if (filters.value.level !== 'ALL' && region.level !== filters.value.level) {
      return false
    }
    if (filters.value.status !== 'ALL' && region.status !== filters.value.status) {
      return false
    }
    return true
  })
})

// 分页数据
const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredRegions.value.length / pageSize.value)),
)
const pagedRegions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredRegions.value.slice(start, start + pageSize.value)
})

// 搜索处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
  console.log('搜索条件:', filters.value)
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    name: '',
    code: '',
    level: 'ALL',
    status: 'ALL',
  }
  currentPage.value = 1
}

// 导出数据
const exportData = () => {
  const headers = [
    '序号',
    '区域名称',
    '区域代码',
    '区域级别',
    '上级区域',
    '负责人',
    '负责人电话',
    '状态',
    '更新时间',
  ]
  const rows = filteredRegions.value.map((region, index) => [
    (index + 1).toString(),
    region.fullName,
    region.code,
    region.level,
    region.parentName || '',
    region.supervisor?.name || '',
    region.supervisor?.phone || '',
    region.status,
    region.updatedAt,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `区域管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

// 操作处理函数
const handleCreateRegion = () => {
  console.log('新增区域')
}

const handleEdit = (id: string) => {
  console.log('编辑区域:', id)
}

const handleAssignSupervisor = (id: string) => {
  console.log('分配负责人:', id)
}

const handleViewSubRegions = (id: string) => {
  console.log('查看下级区域:', id)
}

const handleDisable = (id: string) => {
  const region = regions.value.find((r) => r.id === id)
  if (region) {
    region.status = '停用'
    region.updatedAt = new Date().toLocaleString('zh-CN').replace(/\//g, '-')
  }
  console.log('停用区域:', id)
}

const handleEnable = (id: string) => {
  const region = regions.value.find((r) => r.id === id)
  if (region) {
    region.status = '启用'
    region.updatedAt = new Date().toLocaleString('zh-CN').replace(/\//g, '-')
  }
  console.log('启用区域:', id)
}

// Badge样式
const getLevelVariant = (level: RegionLevel) => {
  switch (level) {
    case '省级':
      return 'default'
    case '市级':
      return 'secondary'
    case '县级':
      return 'outline'
    case '区级':
      return 'outline'
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: RegionStatus) => {
  switch (status) {
    case '启用':
      return 'default'
    case '停用':
      return 'destructive'
    default:
      return 'outline'
  }
}
</script>
