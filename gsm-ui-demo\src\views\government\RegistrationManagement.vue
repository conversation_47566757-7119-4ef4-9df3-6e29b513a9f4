<template>
  <div class="card-container">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">管理已注册备案的企业与车辆信息库，提供统计、搜索与导出</p>
      </div>
    </div>

    <!-- 顶端统计 -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <!-- 企业注册状态统计 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">企业注册状态</CardTitle>
          <Building class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.totalEnterprises }}</div>
          <p class="text-xs text-muted-foreground">+{{ stats.newEnterprises }} 本月新增</p>
          <div class="mt-3">
            <PieChart
              :data="enterpriseStatusChartData"
              :center-text="stats.totalEnterprises.toString()"
              :center-sub-text="'企业总数'"
              :height="240"
              :show-legend="true"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 企业类型占比 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">企业类型占比</CardTitle>
          <Factory class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="mt-3 h-[320px]">
            <PieChart :data="enterpriseTypeChartData" :height="320" :show-legend="true" />
          </div>
        </CardContent>
      </Card>

      <!-- 车辆类型统计 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">车辆类型统计</CardTitle>
          <Car class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.totalVehicles }}</div>
          <p class="text-xs text-muted-foreground">
            {{ stats.vehicleGrowth > 0 ? '+' : '' }}{{ stats.vehicleGrowth }}% 环比增长
          </p>
          <div class="mt-3 h-[320px]">
            <BarChart :data="vehicleTypeChartData" :height="320" :show-legend="false" />
          </div>
        </CardContent>
      </Card>

      <!-- 审批进度 -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-semibold">审批进度</CardTitle>
          <ClipboardCheck class="h-8 w-8 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <!-- 顶部关键指标 -->
          <div class="flex items-baseline gap-2">
            <div class="text-2xl font-bold">{{ stats.pendingApprovals }}</div>
            <span class="text-xs text-muted-foreground">待审批企业</span>
          </div>

          <!-- 完成率进度条 -->
          <div class="mt-3 space-y-2">
            <div class="flex justify-between text-xs">
              <span>审批完成率</span>
              <span>{{ completionRate }}%</span>
            </div>
            <div class="w-full bg-muted rounded-full h-2">
              <div
                class="h-2 rounded-full bg-primary transition-all"
                :style="{ width: completionRate + '%' }"
              ></div>
            </div>
          </div>

          <!-- 额外统计信息 -->
          <div class="grid grid-cols-2 gap-3 mt-4 text-xs">
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">今日新增待审</span>
                <span class="font-medium">{{ stats.todayPending }}</span>
              </div>
            </div>
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">本周已完成</span>
                <span class="font-medium">{{ stats.weekApproved }}</span>
              </div>
            </div>
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">平均审批时长</span>
                <span class="font-medium">{{ stats.avgApprovalHours }} 小时</span>
              </div>
            </div>
            <div class="p-2 rounded-md border">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">超时待处理</span>
                <span class="font-medium text-orange-600 dark:text-orange-400">{{
                  stats.overduePending
                }}</span>
              </div>
            </div>
          </div>

          <!-- 状态摘要 -->
          <div class="mt-3 grid grid-cols-3 gap-2 text-xs">
            <div class="flex items-center justify-between rounded-md border px-2 py-1">
              <span class="text-muted-foreground">通过</span>
              <span class="font-medium">{{ stats.approvedCount }}</span>
            </div>
            <div class="flex items-center justify-between rounded-md border px-2 py-1">
              <span class="text-muted-foreground">未通过</span>
              <span class="font-medium">{{ stats.rejectedCount }}</span>
            </div>
            <div class="flex items-center justify-between rounded-md border px-2 py-1">
              <span class="text-muted-foreground">注册中</span>
              <span class="font-medium">{{ stats.registeringCount }}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 企业信息列表 -->
    <Card>
      <CardHeader>
        <CardTitle>企业信息列表</CardTitle>
        <CardDescription>
          共 {{ pagination.total }} 条记录，当前显示第
          {{ pagination.pageSize * (pagination.current - 1) + 1 }} -
          {{ Math.min(pagination.pageSize * pagination.current, pagination.total) }} 条
        </CardDescription>
      </CardHeader>
      <CardContent>
        <!-- 查询表单 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="searchForm"
          @search="handleSearch"
          @reset="resetSearch"
          @export="exportData"
        />
        <!-- 官方Table组件显示 -->
        <div class="space-y-4 mt-4">
          <div class="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>序号</TableHead>
                  <TableHead>企业名称</TableHead>
                  <TableHead>企业类型</TableHead>
                  <TableHead>注册状态</TableHead>
                  <TableHead>注册时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="(item, index) in displayedEnterprises" :key="item.id">
                  <TableCell>{{
                    pagination.pageSize * (pagination.current - 1) + index + 1
                  }}</TableCell>
                  <TableCell>{{ item.name }}</TableCell>
                  <TableCell>{{ item.type }}</TableCell>
                  <TableCell>
                    <Badge :variant="getStatusVariant(item.registrationStatus)">
                      {{ item.registrationStatus }}
                    </Badge>
                  </TableCell>
                  <TableCell>{{ item.registrationTime }}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline" @click="viewDetails(item)">
                      查看详情
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <PaginationBar
            :page="regCurrentPage"
            :total="pagination.total"
            :page-size="pagination.pageSize"
            @update:page="(p: number) => (pagination.current = p)"
            @update:pageSize="
              (ps: number) => {
                pagination.pageSize = ps
                pagination.current = 1
              }
            "
          />
        </div>
      </CardContent>
    </Card>

    <!-- 详情弹窗 -->
    <Dialog v-model:open="detailDialogOpen">
      <DialogContent class="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{{ selectedEnterprise?.name }} - 详细信息</DialogTitle>
          <DialogDescription> 查看企业完整注册信息和审批记录 </DialogDescription>
        </DialogHeader>

        <Tabs v-if="selectedEnterprise" v-model="activeTab" class="w-full">
          <TabsList class="grid w-full grid-cols-5">
            <TabsTrigger value="basic">企业基本信息</TabsTrigger>
            <TabsTrigger value="qualification">测绘资质信息</TabsTrigger>
            <TabsTrigger value="vehicles">车辆信息</TabsTrigger>
            <TabsTrigger value="security">安全防护措施</TabsTrigger>
            <TabsTrigger value="activities">处理活动信息</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" class="space-y-4">
            <div class="p-4 card-subtle rounded-lg">
              <h3 class="font-semibold mb-3">企业基本信息</h3>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div><strong>企业名称：</strong>{{ selectedEnterprise.name }}</div>
                <div><strong>企业类型：</strong>{{ selectedEnterprise.type }}</div>
                <div><strong>统一社会信用代码：</strong>{{ selectedEnterprise.creditCode }}</div>
                <div><strong>联系人：</strong>{{ selectedEnterprise.contactPerson }}</div>
                <div><strong>联系电话：</strong>{{ selectedEnterprise.contactPhone }}</div>
                <div><strong>企业地址：</strong>{{ selectedEnterprise.address }}</div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="qualification" class="space-y-4">
            <MappingQualificationInfo
              :enterprise="selectedEnterprise"
              :mode="isAuditMode ? 'audit' : 'view'"
              :disable-card-variants="true"
            />
          </TabsContent>

          <TabsContent value="vehicles" class="space-y-4">
            <div class="p-4 card-subtle rounded-lg">
              <h3 class="font-semibold mb-3">车辆信息</h3>
              <p class="text-sm">
                <strong>车辆总数：</strong>{{ selectedEnterprise.vehicleCount }} 辆
              </p>
            </div>
          </TabsContent>

          <TabsContent value="security" class="space-y-4">
            <div class="p-4 card-subtle rounded-lg">
              <h3 class="font-semibold mb-3">安全防护措施</h3>
              <p class="text-sm text-muted-foreground">安全防护措施相关信息...</p>
            </div>
          </TabsContent>

          <TabsContent value="activities" class="space-y-4">
            <div class="p-4 card-subtle rounded-lg">
              <h3 class="font-semibold mb-3">数据处理活动信息</h3>
              <p class="text-sm text-muted-foreground">数据处理活动相关信息...</p>
            </div>
          </TabsContent>
        </Tabs>

        <!-- 审批记录 -->
        <div class="mt-6 space-y-4">
          <h3 class="text-lg font-semibold">审批记录</h3>
          <div class="p-4 card-surface rounded-lg">
            <p class="text-sm text-muted-foreground">审批历史记录...</p>
          </div>
        </div>

        <!-- 导航按钮 -->
        <div class="flex items-center justify-between mt-6">
          <div class="flex gap-2">
            <Button
              variant="outline"
              @click="navigateToPrevious"
              :disabled="!hasPrevious"
              class="flex items-center gap-2"
            >
              <ChevronLeft class="w-4 h-4" />
              上一页
            </Button>
            <Button
              variant="outline"
              @click="navigateToNext"
              :disabled="!hasNext"
              class="flex items-center gap-2"
            >
              下一页
              <ChevronRight class="w-4 h-4" />
            </Button>
          </div>

          <Button variant="outline" @click="detailDialogOpen = false"> 关闭 </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Building, Factory, Car, ClipboardCheck, ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from '@/components/ui/table'
import { PieChart, BarChart } from '@/components/charts'
import MappingQualificationInfo from '@/components/enterprise-info/MappingQualificationInfo.vue'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'
import { PaginationBar } from '@/components/ui/pagination'

// 子组件导入（这些组件需要单独创建）
// import DataTable from '@/components/ui/data-table/DataTable.vue'
// import DatePickerWithRange from '@/components/ui/date-picker/DatePickerWithRange.vue'
// import EnterpriseBasicInfo from '@/components/enterprise-info/EnterpriseBasicInfo.vue'
// import VehicleInfo from '@/components/enterprise-info/VehicleInfo.vue'
// import SecurityMeasuresInfo from '@/components/enterprise-info/SecurityMeasuresInfo.vue'
// import DataProcessingActivitiesInfo from '@/components/enterprise-info/DataProcessingActivitiesInfo.vue'
// import ApprovalHistory from '@/components/enterprise-info/ApprovalHistory.vue'

// 接口定义
interface Enterprise {
  id: string
  name: string
  type: string
  creditCode: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  contactPerson: string
  contactPhone: string
  address: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

interface SearchForm {
  enterpriseName: string
  enterpriseType: string
  registrationStatus: string
  registrationDateRange: string
}

interface Pagination {
  current: number
  pageSize: number
  total: number
}

// 响应式数据
const loading = ref(false)
const detailDialogOpen = ref(false)
const selectedEnterprise = ref<Enterprise | null>(null)
const activeTab = ref('basic')
const isAuditMode = ref(false) // 控制是否为审核模式

// 统计数据
const stats = ref({
  totalEnterprises: 346,
  newEnterprises: 12,
  totalVehicles: 15832,
  vehicleGrowth: 8.5,
  pendingApprovals: 23,
  approvedCount: 298,
  rejectedCount: 15,
  registeringCount: 23,
  todayPending: 6,
  weekApproved: 57,
  avgApprovalHours: 28,
  overduePending: 2,
})

// 企业类型统计
const enterpriseTypes = ref([
  { name: '整车生产企业', count: 156, color: '#3498db' },
  { name: '平台运营商', count: 89, color: '#9b59b6' },
  { name: '智驾方案提供商', count: 67, color: '#17a2b8' },
  { name: '地图服务商', count: 34, color: '#28a745' },
])

// 查询表单
const searchForm = ref<SearchForm>({
  enterpriseName: '',
  enterpriseType: '',
  registrationStatus: '',
  registrationDateRange: '',
})

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'enterpriseName',
    label: '企业名称',
    type: 'input',
    placeholder: '请输入企业名称',
  },
  {
    key: 'enterpriseType',
    label: '企业类型',
    type: 'select',
    placeholder: '请选择企业类型',
    options: [
      { label: '全部类型', value: 'all' },
      { label: '整车生产企业', value: '整车生产企业' },
      { label: '平台运营商', value: '平台运营商' },
      { label: '智驾方案提供商', value: '智驾方案提供商' },
      { label: '地图服务商', value: '地图服务商' },
      { label: '其他', value: '其他' },
    ],
    defaultValue: 'all',
  },
  {
    key: 'registrationStatus',
    label: '注册状态',
    type: 'select',
    placeholder: '请选择注册状态',
    options: [
      { label: '全部状态', value: 'all' },
      { label: '注册中', value: '注册中' },
      { label: '通过', value: '通过' },
      { label: '未通过', value: '未通过' },
      { label: '待审核', value: '待审核' },
    ],
    defaultValue: 'all',
  },
  {
    key: 'registrationDateRange',
    label: '注册时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页配置
const pagination = ref<Pagination>({
  current: 1,
  pageSize: 10,
  total: 346,
})

// 表格数据 - 扩充为更多 mock 数据
const tableData = ref<Enterprise[]>([])

// 随机工具与数据来源
import { randomInt } from '@/lib/mock-data-generator'
const enterpriseTypeOpts = [
  '整车生产企业',
  '平台运营商',
  '智驾方案提供商',
  '地图服务商',
  '其他',
] as const
const registrationStatusOpts: Enterprise['registrationStatus'][] = [
  '注册中',
  '通过',
  '未通过',
  '待审核',
]
const riskLevelOpts: Enterprise['riskLevel'][] = ['低', '中', '高']
const cityPool = ['北京', '上海', '深圳', '杭州', '南京', '广州', '成都', '武汉', '西安', '重庆']
const streetPool = ['科技园区', '高新区', '软件园', '产业园', '开发区']
const contactPool = ['张三', '李四', '王五', '赵六', '钱七', '孙八']

function pad(n: number) {
  return n.toString().padStart(2, '0')
}
function randomDateYMD(startYear = 2023, endYear = 2025) {
  const y = randomInt(startYear, endYear)
  const m = randomInt(1, 12)
  const d = randomInt(1, 28)
  return `${y}-${pad(m)}-${pad(d)}`
}
function randomCreditCode() {
  const chars = '0123456789ABCDEFGHJKLMNPQRTUWXY'
  let s = ''
  for (let i = 0; i < 18; i++) s += chars.charAt(randomInt(0, chars.length - 1))
  return s
}
function randomPhone() {
  return `1${randomInt(30, 99)}${randomInt(*********, *********)}`
}
function randomCompanyName() {
  const city = cityPool[randomInt(0, cityPool.length - 1)]
  const suffix = ['科技', '智能', '汽车', '地图', '信息', '数据', '网络'][randomInt(0, 6)]
  return `${city}${suffix}有限公司`
}
function randomAddress() {
  const city = cityPool[randomInt(0, cityPool.length - 1)]
  const street = streetPool[randomInt(0, streetPool.length - 1)]
  return `${city}市${street}${randomInt(1, 999)}号`
}

function generateEnterprises(n = 50): Enterprise[] {
  const list: Enterprise[] = []
  for (let i = 1; i <= n; i++) {
    const type = enterpriseTypeOpts[randomInt(0, enterpriseTypeOpts.length - 1)]
    const status = registrationStatusOpts[randomInt(0, registrationStatusOpts.length - 1)]
    const risk = riskLevelOpts[randomInt(0, riskLevelOpts.length - 1)]
    list.push({
      id: `${i}`,
      name: randomCompanyName(),
      type,
      creditCode: randomCreditCode(),
      registrationStatus: status,
      registrationTime: randomDateYMD(2023, 2025),
      contactPerson: contactPool[randomInt(0, contactPool.length - 1)],
      contactPhone: randomPhone(),
      address: randomAddress(),
      vehicleCount: randomInt(50, 3000),
      riskLevel: risk,
    })
  }
  return list
}

// 初始化生成 50 条数据
const initialEnterprises = generateEnterprises(50)
tableData.value = initialEnterprises
pagination.value.total = tableData.value.length

// 表格列配置已移至模板中直接定义

// 计算属性
const displayedEnterprises = computed(() => {
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return tableData.value.slice(start, end)
})

const regCurrentPage = computed({
  get: () => pagination.value.current,
  set: (val: number) => {
    pagination.value.current = val
  },
})

const enterpriseStatusChartData = computed(() => [
  { name: '注册中', value: stats.value.registeringCount, color: '#f59e0b' },
  { name: '通过', value: stats.value.approvedCount, color: '#10b981' },
  { name: '未通过', value: stats.value.rejectedCount, color: '#ef4444' },
  { name: '待审核', value: stats.value.pendingApprovals, color: '#8b5cf6' },
])

const enterpriseTypeChartData = computed(() =>
  enterpriseTypes.value.map((item) => ({
    name: item.name,
    value: item.count,
    color: item.color,
  })),
)

const vehicleTypeChartData = computed(() => [
  { name: '智能网联车', value: 8956, color: '#3b82f6' },
  { name: '自动驾驶车', value: 3245, color: '#8b5cf6' },
  { name: '传统车联网', value: 2134, color: '#06b6d4' },
  { name: '测试车辆', value: 1497, color: '#84cc16' },
])

// 审批完成率（动态计算）
const completionRate = computed(() =>
  Math.round((stats.value.approvedCount / stats.value.totalEnterprises) * 100),
)

const hasPrevious = computed(() => {
  if (!selectedEnterprise.value) return false
  const currentIndex = tableData.value.findIndex((item) => item.id === selectedEnterprise.value!.id)
  return currentIndex > 0
})

const hasNext = computed(() => {
  if (!selectedEnterprise.value) return false
  const currentIndex = tableData.value.findIndex((item) => item.id === selectedEnterprise.value!.id)
  return currentIndex < tableData.value.length - 1
})

// 方法
// 移除未使用的时间更新逻辑

const handleSearch = (filters?: Record<string, string | number | boolean>) => {
  loading.value = true
  if (filters) {
    // 如果传入了过滤参数，更新searchForm
    Object.assign(searchForm.value, filters)
  }
  console.log('搜索条件:', searchForm.value)
  // 这里实现搜索逻辑
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const resetSearch = () => {
  searchForm.value = {
    enterpriseName: '',
    enterpriseType: 'all',
    registrationStatus: 'all',
    registrationDateRange: '',
  }
  handleSearch()
}

const exportData = () => {
  console.log('导出Excel')
  // 这里实现导出功能
}

const getStatusVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (status) {
    case '通过':
      return 'default'
    case '注册中':
      return 'secondary'
    case '待审核':
      return 'outline'
    case '未通过':
      return 'destructive'
    default:
      return 'outline'
  }
}

// 分页
// 移除未使用的 onRegPageSizeChange 函数

// 分页处理函数已移至模板中直接使用

const loadData = () => {
  loading.value = true
  // 这里实现数据加载逻辑
  setTimeout(() => {
    loading.value = false
  }, 500)
}

const viewDetails = (enterprise: Enterprise) => {
  selectedEnterprise.value = enterprise
  activeTab.value = 'basic'
  detailDialogOpen.value = true
}

const navigateToPrevious = () => {
  if (!selectedEnterprise.value || !hasPrevious.value) return
  const currentIndex = tableData.value.findIndex((item) => item.id === selectedEnterprise.value!.id)
  if (currentIndex > 0) {
    selectedEnterprise.value = tableData.value[currentIndex - 1]
  }
}

const navigateToNext = () => {
  if (!selectedEnterprise.value || !hasNext.value) return
  const currentIndex = tableData.value.findIndex((item) => item.id === selectedEnterprise.value!.id)
  if (currentIndex < tableData.value.length - 1) {
    selectedEnterprise.value = tableData.value[currentIndex + 1]
  }
}

// 生命周期
onMounted(() => {
  loadData()
})

// 暴露方法给模板使用
defineExpose({
  viewDetails,
})
</script>
