@echo off
echo =============================================
echo 腾讯云 CloudBase 自动部署脚本
echo =============================================
echo.

echo [1/4] 检查项目构建状态...
if not exist "dist\index.html" (
    echo 错误: 未找到构建产物，请先运行 npm run build
    pause
    exit /b 1
)
echo ✅ 构建产物检查完成

echo.
echo [2/4] 检查 CloudBase CLI...
tcb --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装 CloudBase CLI...
    npm install -g @cloudbase/cli
)
echo ✅ CloudBase CLI 准备完成

echo.
echo [3/4] 开始部署到腾讯云...
echo 环境ID: cloud1-0gc8cbzg3efd6a99
echo 区域: ap-shanghai
echo.

REM 检查是否已登录
tcb login --status >nul 2>&1
if %errorlevel% neq 0 (
    echo 需要登录到腾讯云账号...
    echo 请在浏览器中完成登录流程
    tcb login
)

echo 正在部署...
tcb framework deploy

echo.
echo [4/4] 部署完成检查...
if %errorlevel% equ 0 (
    echo ✅ 部署成功！
    echo.
    echo 访问地址:
    echo https://cloud1-0gc8cbzg3efd6a99.ap-shanghai.tcb.qcloud.la
    echo.
    echo 可在腾讯云控制台查看详细信息:
    echo https://console.cloud.tencent.com/tcb
) else (
    echo ❌ 部署失败，请检查错误信息
    echo.
    echo 常见解决方案:
    echo 1. 检查网络连接
    echo 2. 确认腾讯云账号权限
    echo 3. 查看上方错误日志
)

echo.
echo 按任意键退出...
pause >nul