﻿.ax_default {
  font-family:'';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
._形状 {
}
._图片_ {
}
.button {
}
.primary_button {
  font-size:14px;
  color:#409EFF;
}
._一级标题 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._二级标题 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._三级标题 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._四级标题 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._五级标题 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._六级标题 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
._文本段落 {
  text-align:left;
}
._文本链接 {
  color:#0000FF;
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
.box_1 {
}
.el-button-primary {
  font-family:'';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:center;
}
.el-button-primary-plain {
  font-family:'';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#409EFF;
  text-align:center;
}
.box_2 {
}
._形状1 {
  font-family:'';
  font-weight:700;
  font-style:normal;
  color:#FF0066;
}
.h14-black {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#333333;
  text-align:left;
}
.radio-check {
}
.radio-uncheck {
  color:#333333;
}
.radio-label-check {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#409EFF;
  text-align:left;
}
.radio-label-uncheck {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#6B6B6B;
  text-align:left;
}
.checkbox-check {
}
.checkbox-label-check {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#409EFF;
  text-align:left;
}
.checkbox-uncheck {
}
.checkbox-label-uncheck {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:left;
}
.input-placeholder {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#C0C4CC;
  text-align:left;
}
.inputed {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:left;
}
.input-textarea {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#C0C4CC;
  text-align:left;
}
.tag {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#409EFF;
  text-align:center;
}
.tag-success {
  font-family:'';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#67C23A;
  text-align:center;
}
.tag-warning {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#E6A23C;
  text-align:center;
}
.tag-info {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:center;
}
.tag-danger {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F56C6C;
  text-align:center;
}
.el-shadow {
}
._流程形状 {
}
.line {
}
.text_field {
  color:#606266;
  text-align:left;
}
.table_cell {
}
.marker {
  color:#FFFFFF;
}
._线段 {
}
._连接 {
}
.paragraph {
  text-align:left;
}
.heading_2 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:'';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
.box_3 {
}
.shape {
}
._图片 {
}
.image {
}
.line1 {
}
.el-button {
  font-family:'';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:center;
}
.ellipse {
}
.label {
  font-size:14px;
  text-align:left;
}
.link_button {
  color:#169BD5;
}
.text_field1 {
  color:#000000;
  text-align:left;
}
.text_area {
  color:#000000;
  text-align:left;
}
.droplist {
  color:#000000;
  text-align:left;
}
.radio_button {
  text-align:left;
}
.tree_node {
  text-align:left;
}
.flow_shape {
}
.checkbox {
  text-align:left;
}
.icon {
}
.label1 {
  font-family:'';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:left;
}
.table_cell1 {
  font-family:'';
  font-weight:normal;
  font-style:normal;
  font-size:13px;
  color:#000000;
}
.menu_item {
}
._默认样式 {
}
._鼠标按下文本链接时 {
}
._鼠标按下文本链接时1 {
}
._图片_1 {
  color:#000000;
}
.refs-chart-data {
  font-family:'';
  font-weight:400;
  font-style:normal;
}
.flow_shape1 {
}
.text_field2 {
  color:#000000;
  text-align:left;
}
.box_11 {
}
.placeholder {
}
textarea, select, input, button { outline: none; }
