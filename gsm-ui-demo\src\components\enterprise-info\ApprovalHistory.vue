<template>
  <div class="space-y-6">
    <!-- 审批流程概览 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Clock class="h-5 w-5" />
            <span>审批流程概览</span>
          </div>
          <Badge
            :variant="
              currentStatus === '已通过'
                ? 'default'
                : currentStatus === '审批中'
                  ? 'secondary'
                  : 'destructive'
            "
          >
            {{ currentStatus }}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <!-- 流程步骤 -->
          <div class="flex items-center justify-between">
            <div v-for="(step, index) in approvalSteps" :key="step.name" class="flex items-center">
              <div class="flex flex-col items-center">
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                  :class="getStepStatusClass(step.status)"
                >
                  <Check v-if="step.status === 'completed'" class="w-4 h-4" />
                  <Clock v-else-if="step.status === 'current'" class="w-4 h-4" />
                  <span v-else>{{ index + 1 }}</span>
                </div>
                <div class="text-xs mt-1 text-center max-w-20">{{ step.name }}</div>
              </div>
              <div
                v-if="index < approvalSteps.length - 1"
                class="w-16 h-px mx-2"
                :class="step.status === 'completed' ? 'bg-green-300' : 'bg-gray-300'"
              ></div>
            </div>
          </div>

          <!-- 当前进度信息 -->
          <div class="p-3 bg-blue-50 rounded-lg">
            <div class="flex items-center space-x-2">
              <Info class="w-5 h-5 text-blue-600" />
              <div class="text-sm text-blue-800">
                <strong>当前状态：</strong>{{ progressInfo.current }}
                <span class="mx-2">|</span>
                <strong>预计完成时间：</strong>{{ progressInfo.estimatedCompletion }}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 审批记录时间线 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <History class="h-5 w-5" />
          <span>审批记录时间线</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div v-for="(record, index) in approvalRecords" :key="record.id" class="relative">
            <!-- 时间线连接线 -->
            <div
              v-if="index < approvalRecords.length - 1"
              class="absolute left-4 top-8 w-px h-16 bg-border"
            ></div>

            <div class="flex items-start space-x-4">
              <!-- 状态图标 -->
              <div
                class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                :class="getRecordStatusClass(record.result)"
              >
                <Check v-if="record.result === '通过'" class="w-4 h-4" />
                <X v-else-if="record.result === '驳回'" class="w-4 h-4" />
                <Clock v-else-if="record.result === '处理中'" class="w-4 h-4" />
                <AlertCircle v-else class="w-4 h-4" />
              </div>

              <!-- 记录内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-2">
                    <h4 class="font-medium">{{ record.stage }}</h4>
                    <Badge :variant="getRecordBadgeVariant(record.result)">
                      {{ record.result }}
                    </Badge>
                  </div>
                  <div class="text-sm text-muted-foreground">
                    {{ formatDateTime(record.processTime) }}
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div class="space-y-1">
                    <div class="text-sm">
                      <span class="font-medium">处理人：</span>
                      {{ record.processor }}
                    </div>
                    <div class="text-sm">
                      <span class="font-medium">处理部门：</span>
                      {{ record.department }}
                    </div>
                  </div>
                  <div class="space-y-1">
                    <div class="text-sm">
                      <span class="font-medium">处理时长：</span>
                      {{ record.processingDuration }}
                    </div>
                    <div v-if="record.deadline" class="text-sm">
                      <span class="font-medium">处理期限：</span>
                      {{ formatDate(record.deadline) }}
                    </div>
                  </div>
                </div>

                <!-- 审批意见 -->
                <div v-if="record.comments" class="space-y-2">
                  <Label class="text-sm font-medium">审批意见</Label>
                  <div class="p-3 bg-muted rounded-lg text-sm">
                    {{ record.comments }}
                  </div>
                </div>

                <!-- 问题清单 -->
                <div v-if="record.issues && record.issues.length > 0" class="space-y-2 mt-3">
                  <Label class="text-sm font-medium text-red-700">发现问题</Label>
                  <div class="space-y-1">
                    <div
                      v-for="issue in record.issues"
                      :key="issue"
                      class="flex items-start space-x-2"
                    >
                      <AlertTriangle class="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <span class="text-sm text-red-700">{{ issue }}</span>
                    </div>
                  </div>
                </div>

                <!-- 附件 -->
                <div
                  v-if="record.attachments && record.attachments.length > 0"
                  class="space-y-2 mt-3"
                >
                  <Label class="text-sm font-medium">相关附件</Label>
                  <div class="flex flex-wrap gap-2">
                    <Button
                      v-for="attachment in record.attachments"
                      :key="attachment.name"
                      size="sm"
                      variant="outline"
                      @click="viewAttachment(attachment)"
                      class="h-auto p-2"
                    >
                      <Paperclip class="w-3 h-3 mr-1" />
                      <span class="text-xs">{{ attachment.name }}</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 催办记录 -->
    <Card v-if="urgeRecords.length > 0">
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Bell class="h-5 w-5" />
          <span>催办记录</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-3">
          <div v-for="urge in urgeRecords" :key="urge.id" class="p-3 border rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <Badge variant="outline">催办</Badge>
                <span class="font-medium">{{ urge.reason }}</span>
              </div>
              <div class="text-sm text-muted-foreground">
                {{ formatDateTime(urge.urgeTime) }}
              </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div><span class="font-medium">催办人：</span>{{ urge.urger }}</div>
              <div><span class="font-medium">催办对象：</span>{{ urge.target }}</div>
            </div>
            <div v-if="urge.response" class="mt-2 p-2 bg-green-50 rounded text-sm">
              <span class="font-medium text-green-800">回复：</span>
              <span class="text-green-700">{{ urge.response }}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 操作按钮 -->
    <div class="flex items-center space-x-2">
      <Button
        v-if="canUrge"
        @click="handleUrge"
        variant="outline"
        class="flex items-center space-x-2"
      >
        <Bell class="w-4 h-4" />
        <span>发起催办</span>
      </Button>
      <Button @click="exportHistory" variant="outline" class="flex items-center space-x-2">
        <Download class="w-4 h-4" />
        <span>导出记录</span>
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Clock,
  Check,
  X,
  AlertCircle,
  AlertTriangle,
  History,
  Info,
  Bell,
  Paperclip,
  Download,
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

interface Props {
  enterpriseId: string
}

const props = defineProps<Props>()

// 当前审批状态
const currentStatus = computed(() => '审批中')

// 审批流程步骤
const approvalSteps = computed(() => [
  { name: '提交申请', status: 'completed' },
  { name: '初步审核', status: 'completed' },
  { name: '资料审查', status: 'completed' },
  { name: '专家评审', status: 'current' },
  { name: '最终审批', status: 'pending' },
])

// 进度信息
const progressInfo = computed(() => ({
  current: '专家评审阶段，正在进行技术方案评估',
  estimatedCompletion: '2024-02-15',
}))

// 审批记录
const approvalRecords = computed(() => [
  {
    id: '1',
    stage: '申请提交',
    result: '通过',
    processor: '系统自动',
    department: '政务服务中心',
    processTime: '2024-01-15T09:00:00',
    processingDuration: '即时',
    comments: '申请材料提交成功，系统自动受理',
    attachments: [],
  },
  {
    id: '2',
    stage: '初步审核',
    result: '通过',
    processor: '李审核员',
    department: '市交通委员会',
    processTime: '2024-01-16T14:30:00',
    processingDuration: '1天5小时',
    deadline: '2024-01-18',
    comments: '企业基本资质符合要求，材料完整，同意进入下一阶段审核。',
    attachments: [{ name: '初审意见书.pdf', size: '256KB' }],
  },
  {
    id: '3',
    stage: '资料审查',
    result: '通过',
    processor: '王高级审核员',
    department: '市测绘地理信息局',
    processTime: '2024-01-20T16:45:00',
    processingDuration: '4天2小时',
    deadline: '2024-01-22',
    comments: '测绘资质、车辆信息、安全防护措施等材料审查合格，技术方案可行。',
    issues: [],
    attachments: [
      { name: '资料审查报告.pdf', size: '1.2MB' },
      { name: '技术方案评估.docx', size: '890KB' },
    ],
  },
  {
    id: '4',
    stage: '专家评审',
    result: '处理中',
    processor: '专家评审组',
    department: '智能网联汽车专家委员会',
    processTime: '2024-01-22T10:00:00',
    processingDuration: '进行中...',
    deadline: '2024-02-05',
    comments: '专家组正在对企业的自动驾驶技术方案、数据安全措施进行深度评估。',
    issues: ['需要补充L3级自动驾驶系统的安全评估报告', '地理围栏技术的具体实现方案需要进一步说明'],
    attachments: [{ name: '专家评审意见征集表.pdf', size: '445KB' }],
  },
])

// 催办记录
const urgeRecords = computed(() => [
  {
    id: '1',
    reason: '审批时间超期',
    urger: '企业法人代表',
    target: '专家评审组',
    urgeTime: '2024-02-01T14:00:00',
    response: '已收到催办，评审工作将在本周内完成。',
  },
])

// 判断是否可以催办
const canUrge = computed(() => {
  // 如果当前状态是处理中且超过预定时间3天以上，允许催办
  const now = new Date()
  const lastRecord = approvalRecords.value[approvalRecords.value.length - 1]
  const processTime = new Date(lastRecord.processTime)
  const daysDiff = Math.floor((now.getTime() - processTime.getTime()) / (1000 * 60 * 60 * 24))
  return daysDiff >= 3 && lastRecord.result === '处理中'
})

// 方法
const getStepStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500 text-white'
    case 'current':
      return 'bg-blue-500 text-white'
    case 'pending':
      return 'bg-gray-200 text-gray-600'
    default:
      return 'bg-gray-200 text-gray-600'
  }
}

const getRecordStatusClass = (result: string) => {
  switch (result) {
    case '通过':
      return 'bg-green-500 text-white'
    case '驳回':
      return 'bg-red-500 text-white'
    case '处理中':
      return 'bg-blue-500 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

const getRecordBadgeVariant = (result: string) => {
  switch (result) {
    case '通过':
      return 'default'
    case '驳回':
      return 'destructive'
    case '处理中':
      return 'secondary'
    default:
      return 'outline'
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const viewAttachment = (attachment: any) => {
  console.log('查看附件:', attachment.name)
  // 这里实现查看附件的逻辑
}

const handleUrge = () => {
  console.log('发起催办')
  // 这里实现催办的逻辑
}

const exportHistory = () => {
  console.log('导出审批历史')
  // 这里实现导出的逻辑
}
</script>
