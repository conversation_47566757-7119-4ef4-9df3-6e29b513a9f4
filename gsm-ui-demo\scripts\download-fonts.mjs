#!/usr/bin/env node
import { mkdir, writeFile } from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

async function downloadFont(url, outputPath) {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const buffer = await response.arrayBuffer()
    await writeFile(outputPath, new Uint8Array(buffer))
    console.log(`Downloaded: ${path.basename(outputPath)}`)
  } catch (error) {
    console.error(`Failed to download ${url}:`, error.message)
  }
}

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const projectRoot = path.resolve(__dirname, '..')

async function main() {
  const fontsDir = path.join(projectRoot, 'public', 'assets', 'fonts', 'inter')

  // 创建字体目录
  await mkdir(fontsDir, { recursive: true })

  // Inter 字体的 Google Fonts API URL
  const googleFontsApiUrl =
    'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap'

  try {
    // 获取 CSS 文件内容
    const response = await fetch(googleFontsApiUrl)
    const cssContent = await response.text()

    // 解析 CSS 中的字体文件 URL
    const fontUrls = cssContent.match(/url\(([^)]+)\)/g)

    if (fontUrls) {
      for (let i = 0; i < fontUrls.length; i++) {
        const url = fontUrls[i].replace(/url\(([^)]+)\)/, '$1')
        const cleanUrl = url.replace(/['"]/g, '')

        // 从 URL 中提取文件名
        const urlParts = cleanUrl.split('/')
        const fileName = urlParts[urlParts.length - 1] || `inter-${i}.woff2`

        const outputPath = path.join(fontsDir, fileName)
        await downloadFont(cleanUrl, outputPath)
      }
    }

    // 保存本地化的 CSS 文件
    const localCss = cssContent.replace(
      /url\(https:\/\/fonts\.gstatic\.com\/s\/inter\/[^)]+\)/g,
      (match) => {
        const url = match.replace(/url\(([^)]+)\)/, '$1')
        const fileName = url.split('/').pop()
        return `url('/assets/fonts/inter/${fileName}')`
      },
    )

    const cssPath = path.join(fontsDir, 'inter.css')
    await writeFile(cssPath, localCss)
    console.log('Created local CSS file: inter.css')
  } catch (error) {
    console.error('Failed to process Google Fonts:', error.message)
  }
}

// 下载 Leaflet 图标
async function downloadLeafletIcons() {
  const leafletDir = path.join(projectRoot, 'public', 'assets', 'leaflet')
  await mkdir(leafletDir, { recursive: true })

  const leafletIcons = [
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
  ]

  for (const iconUrl of leafletIcons) {
    const fileName = iconUrl.split('/').pop()
    const outputPath = path.join(leafletDir, fileName)
    await downloadFont(iconUrl, outputPath)
  }
}

async function mainWithLeaflet() {
  await main()
  await downloadLeafletIcons()
}

mainWithLeaflet().catch((err) => {
  console.error('[download-fonts] Failed:', err)
  process.exit(1)
})
