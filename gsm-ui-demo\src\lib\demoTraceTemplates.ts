// demoTraceTemplates.ts
// 演示阶段：统一的溯源数据模板（车辆端/云端）
// 说明：后续对接真实数据时，请替换为真实的接口返回结构，并删除/调整本模板的调用。

export function buildVehicleDemoTrace(base: any = {}) {
  const now = Date.now()
  const t1 = now - 60_000
  const t2 = now - 30_000
  const t3 = now
  const vin = base.vin || 'LSVAP2A17PXXXXXXXX'
  const enterprise = base.enterprise || '演示企业（车辆端）'

  const traceData = [
    {
      logId: `LOG-V-COL-${now}-VIN`,
      processingStage: '数据收集',
      stageTimestamp: t1,
      isRisk: false,
      vin,
      positionStatus: 0x01,
      longitude: base.eventLongitude || 116334567,
      latitude: base.eventLatitude || 39912345,
      altitude: 5000,
      dataType: 0x0007,
      businessForm: 0x01,
      securityTech: 0x0000,
      securityTechType: 0x00,
      mapAuditNum: '0x00',
      riskDescription: null,
    },
    {
      logId: `LOG-V-STO-${now}-VIN`,
      processingStage: '数据存储',
      stageTimestamp: t2,
      isRisk: true,
      vin,
      associatedMileage: 1250,
      coordinateProcessing: 0x00,
      accessControlStatus: 0x01,
      encryptionStorageStatus: 0x00,
      riskDescription: '存储前未进行国家认定的地理信息保密处理技术处理，直接明文存储',
    },
    {
      logId: `LOG-V-TRN-${now}-VIN`,
      processingStage: '数据传输',
      stageTimestamp: t3,
      isRisk: true,
      vin,
      transmissionDestination: '91310000000000002B',
      destinationIP: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 192, 168, 1, 100],
      transmissionMileage: 1250,
      coordinateProcessedFlag: 0x02,
      coordinateProcessingMethod: 0x00,
      transmissionAreaType: 0x01,
      networkType: 0x01,
      securityProtocol: 0x01,
      externalTransmission: 0x01,
      riskDescription: '传输未经保密处理的真实坐标数据',
    },
  ]

  const riskPoints = [
    {
      stage: '数据存储',
      description: '存储前未进行保密处理技术处理',
      field: 'coordinateProcessing',
    },
    {
      stage: '数据传输',
      description: '传输真实坐标数据，未经脱敏处理',
      field: 'coordinateProcessedFlag',
    },
  ]

  const dataTypes = base.dataTypes || ['点云数据', '影像数据', '轨迹数据']

  return { traceData, riskPoints, dataTypes, enterprise }
}

export function buildCloudDemoTrace(base: any = {}) {
  const now = Date.now()
  const t1 = now - 90_000
  const t2 = now - 45_000
  const t3 = now

  const dataPacketIds = base.dataPacketIds || [
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
  ]

  const traceData = [
    {
      logId: `LOG-C-COL-${now}-ENT`,
      processingStage: '数据收集',
      stageTimestamp: t1,
      isRisk: false,
      dataPacketId: dataPacketIds[0],
      dataImportance: 0x02,
      dataSourceType: 0x02,
      dataSourceId: base.vin || 'LSVAP2A17PXXXXXXXX',
      dataUsage: 0x02,
      operatorIdentity: 0x01,
      riskDescription: null,
    },
    {
      logId: `LOG-C-STO-${now}-ENT`,
      processingStage: '数据存储',
      stageTimestamp: t2,
      isRisk: false,
      dataPacketId: dataPacketIds[0],
      dataImportance: 0x02,
      storageDeviceType: 0x01,
      storageAreaFlag: 0x01,
      storageDeviceIP: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 10, 0, 1, 100],
      storageZoneType: 0x01,
      dataPartitionFlag: 0x01,
      storageSecurityBitmap: 0x07,
      desensitizationFlag: 0x02,
      operatorIdentity: 0x01,
      riskDescription: null,
    },
    {
      logId: `LOG-C-PRO-${now}-ENT`,
      processingStage: '数据提供',
      stageTimestamp: t3,
      sourceLogId: `LOG-C-STO-${now}-ENT`,
      isRisk: true,
      dataPacketIds,
      providedPacketCount: dataPacketIds.length,
      receiverType: 0x01,
      receiverSecurityCapacity: 0x02,
      exemptionCondition: 0x02,
      securityProcessingTech: 0x0000,
      contractAgreementStatus: 0x02,
      operatorIdentity: 0x01,
      riskDescription: '向第三方提供重要数据，但未采用安全处理技术，且接收方安全能力不足',
    },
  ]

  const riskPoints = [
    {
      stage: '数据提供阶段',
      description: '未采用任何安全处理技术直接提供重要数据',
      field: 'securityProcessingTech',
    },
    {
      stage: '数据提供阶段',
      description: '接收方安全能力不足但仍提供数据',
      field: 'receiverSecurityCapacity',
    },
  ]

  const dataTypes = base.dataTypes || ['点云数据', '影像数据', '轨迹数据']
  const dataImportance = base.dataImportance || '重要'

  return {
    traceData,
    riskPoints,
    dataTypes,
    dataPacketIds,
    dataImportance,
    operatorId: base.operatorId || 'ops_user_demo',
    receiverId: base.receiverId || '91310000000000002B',
  }
}

