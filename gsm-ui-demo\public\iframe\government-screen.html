<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <title>数据安全监测-综合大屏(内嵌)</title>
    <style>
      :root {
        --bg: #0b1220;
        --bg-2: #0e1a2f;
        --panel: #0c1a2b;
        --panel-2: #0f223a;
        --border: #1f3b61;
        --glow: #1ad3ff;
        --text: #d8e7ff;
        --muted: #e0e8f1;
        --high: #ce7979;
        --mid: #f4c542;
        --low: #2de26d;
        /* 深海主题（蓝色系为主） - 本页图表配色统一原则 */
        --sea-1: #2ce7ff;
        --sea-2: #67b7ff;
        --sea-3: #3d91e6;
        --sea-4: #1e6fbf;
        --sea-5: #0fa9d7;
        --sea-6: #6ee7b7; /* 点缀 */
        --sea-1-d: #0fa9d7;
        --sea-2-d: #3d91e6;
        --sea-3-d: #2b6fbf;
        --sea-4-d: #184f8e;
        --sea-5-d: #0b7fa5;
        --sea-6-d: #4ccf9e;
      }
      * {
        box-sizing: border-box;
      }
      html,
      body {
        height: 100%;
        margin: 0;
        color: var(--text);
        font-family:
          Inter,
          'HarmonyOS Sans SC',
          'PingFang SC',
          'Hiragino Sans GB',
          'Microsoft YaHei',
          system-ui,
          -apple-system,
          Segoe UI,
          Roboto,
          Arial,
          sans-serif;
      }

      /* 背景地图层（全局铺底）与内容层叠放关系 */
      #bg-map {
        position: fixed;
        inset: 0;
        z-index: 0;
      }
      #bg-map .map-wrap {
        position: absolute;
        inset: 0;
        border: none;
        border-radius: 0;
      }
      .grid {
        position: relative;
        z-index: 999;
      }
      /* 中列中间作为视野留白（穿透背景地图） */
      #map.panel {
        background: transparent !important;
        box-shadow: none !important;
        border: none !important;
        pointer-events: none;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
      }
      #map .content {
        padding: 0;
      }
      #map .content > * {
        display: block;  
      }
      body {
        padding: 12px;
      }
      /* 列大标题（车端/云端） */
      .col-title {
        position: fixed;
        top: 10px;
        z-index: 2;
        pointer-events: none;
        font-size: 28px;
        font-weight: 800;
        letter-spacing: 2px;
        color: rgba(223, 246, 255, 0.9);
        text-shadow: 0 2px 10px rgba(0, 180, 255, 0.35);
      }
      .col-title.left {
        left: 16px;
      }
      .col-title.right {
        right: 16px;
      }

      .grid {
        height: 100%;
        width: 100%;
        display: grid;
        grid-template-columns: minmax(320px, 1fr) minmax(0, 1.6fr) minmax(320px, 1fr); /* 左 中 右，防止首尾列被压缩为0 */
        /* grid-template-rows: auto auto auto; 由内部面板自身撑开 */
        gap: 0px;
        pointer-events: none; /* 让非面板区域不拦截事件，穿透到下层背景地图 */
      }

      /* 三列布局：列容器包裹，DOM 彻底隔离 */
      .col {
        display: grid;
        grid-auto-rows: max-content;
        gap: 12px;
        align-content: start;
        min-width: 0; /* 防止列根据子元素的最小内容宽度被撑大 */
      }
      .col-left {
        grid-column: 1;
        /* 左列与中列一致的三行布局：顶部、填充、中底部 */
        height: 100%;
        align-content: stretch;
        grid-template-rows: max-content 1fr max-content;
      }
      .col-mid {
        grid-column: 2;
        /* 让中间列撑满可视高度，并将面板分为三行：顶部统计、地图（填充）、底部预警 */
        height: 100%;
        align-content: stretch;
        grid-template-rows: max-content 1fr max-content;
      }
      /* 使中间列的地图面板在第二行可撑满空间 */
      #map.panel {
        min-height: 0;
      }
      #map .content {
        height: 100%;
      }
      .col-right {
        grid-column: 3;
        /* 右列与中列一致的三行布局：顶部、填充、中底部 */
        height: 100%;
        align-content: stretch;
        grid-template-rows: max-content 1fr max-content;
      }
      /* 防止中间行被内部flex内容撑破，确保可伸缩 */
      .col-left > .panel:nth-child(2),
      .col-right > .panel:nth-child(2) {
        min-height: 0;
      }

      .panel {
        position: relative;
        display: flex;
        flex-direction: column;
        min-height: 0;
        min-width: 0; /* 避免面板的最小内容宽度影响列宽 */
        border: none !important;
        background: transparent !important;
        box-shadow: none !important;
        border-radius: 0;
        overflow: visible; /* 允许标题/头部的装饰效果外扩 */
        backdrop-filter: none !important;
        pointer-events: auto; /* 面板继续可交互 */
      }
      .content {
        background: transparent !important;
      }

      /* Charts: floating/naked look */
      .chart {
        background: transparent !important;
        box-shadow: none !important;
        border: 0 !important;
      }
      .chart canvas {
        background: transparent !important;
      }

      /* 左右列标题占位，避免被遮挡（目前未使用） */
      .col-title-slot {
        position: relative;
        height: 0px;
      }
      .panel::before {
        /* 顶部霓虹线（设计更新：禁用） */
        content: none;
        display: none;
      }
      .panel .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 25px 10px 25px;
        border-bottom: none;
        background: url('./image/subtitle_bg.png') center/100% 100% no-repeat;
        background-position-y: 15px;
        background-position-x: -5px;
        border-radius: 0 0 6px 6px;
      }
      .title {
        /* 文本与背景样式 */
        font-size: 20px;
        font-weight: 700;
        font-style: italic;
        letter-spacing: 0px;
        line-height: 27.44px;
        color: rgb(255, 255, 255);
        text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42), 0px 0px 7px rgba(75, 180, 229, 0.25);
        display: inline-block;
      }
      .sub {
        color: var(--muted);
        font-size: 12px;
        margin-left: 8px;
      }
      .content {
        flex: 1;
        min-height: 0;
        padding: 10px 12px;
        display: flex;
        flex-direction: column;
      }
      .content.scroll {
        overflow: auto;
      }
      .seg {
        display: inline-flex;
        background: rgba(26, 211, 255, 0.08);
        border: 1px solid rgba(26, 211, 255, 0.25);
        border-radius: 6px;
        overflow: hidden;
      }
      .seg button {
        font: inherit;
        color: var(--muted);
        background: transparent;
        border: 0;
        padding: 6px 10px;
        cursor: pointer;
      }
      .seg button.active {
        color: #07131f;
        background: linear-gradient(180deg, #2ce7ff, #11bfff);
      }

      /* 时间周期切换按钮组统一样式 */
      .seg.time, .seg[data-scope*="proc"] {
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        background: rgba(6, 18, 32, 0.8);
        border: 1px solid rgba(26, 211, 255, 0.3);
        border-radius: 8px;
        padding: 4px;
        box-shadow:
          inset 0 1px 4px rgba(26, 211, 255, 0.1),
          0 4px 12px rgba(0, 0, 0, 0.3);
        transform: scale(0.9);
        transform-origin: center;
      }

      .seg.time button, .seg[data-scope*="proc"] button {
        font-size: 11px;
        padding: 4px 8px;
        min-width: 32px;
        height: 24px;
        border-radius: 4px;
        transition: all 0.2s ease;
      }

      .seg.time button.active, .seg[data-scope*="proc"] button.active {
        background: linear-gradient(180deg, #2ce7ff, #11bfff);
        color: #07131f;
        box-shadow: 0 2px 6px rgba(44, 231, 255, 0.4);
        transform: translateY(-1px);
      }

      .seg.time button:not(.active), .seg[data-scope*="proc"] button:not(.active) {
        background: rgba(26, 211, 255, 0.1);
        color: #9feaff;
        border: 1px solid rgba(26, 211, 255, 0.2);
      }

      .seg.time button:not(.active):hover, .seg[data-scope*="proc"] button:not(.active):hover {
        background: rgba(26, 211, 255, 0.2);
        border-color: rgba(26, 211, 255, 0.4);
        transform: translateY(-1px);
      }
      .kpi-bar {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;
      }
      .kpi {
        background: linear-gradient(180deg, rgba(26, 211, 255, 0.06), rgba(26, 211, 255, 0.02));
        border: 1px dashed rgba(26, 211, 255, 0.25);
        border-radius: 6px;
        padding: 10px;
      }
      .kpi .v {
        font-size: 20px;
        font-weight: 700;
        color: #2cd8ff;
      }

      /* 顶端状态卡（大图标、内发光）- 增加宽度 */
      .kpi-bar.status {
        grid-template-columns: repeat(6, minmax(140px, 1fr));
        gap: 8px;
        max-width: 980px;
        margin: 0 auto;
        justify-content: center;
        /* 科幻整体背景 */
        background: linear-gradient(135deg,
          rgba(26, 211, 255, 0.08) 0%,
          rgba(44, 231, 255, 0.12) 25%,
          rgba(17, 191, 255, 0.08) 50%,
          rgba(26, 211, 255, 0.12) 75%,
          rgba(44, 231, 255, 0.08) 100%);
        border: 1px solid rgba(26, 211, 255, 0.3);
        border-radius: 12px;
        padding: 12px 14px;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow:
          inset 0 0 30px rgba(26, 211, 255, 0.1),
          0 0 40px rgba(26, 211, 255, 0.15),
          0 0 80px rgba(26, 211, 255, 0.08);
        position: relative;
        overflow: hidden;
      }

      /* 添加科幻装饰元素 - 缩小尺寸 */
      .kpi-bar.status::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(26, 211, 255, 0.8) 20%,
          rgba(44, 231, 255, 1) 50%,
          rgba(26, 211, 255, 0.8) 80%,
          transparent 100%);
        box-shadow: 0 0 15px rgba(26, 211, 255, 0.6);
      }

      .kpi-bar.status::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(26, 211, 255, 0.6) 20%,
          rgba(44, 231, 255, 0.8) 50%,
          rgba(26, 211, 255, 0.6) 80%,
          transparent 100%);
        box-shadow: 0 0 15px rgba(26, 211, 255, 0.4);
      }
      /* 移除独立背景，融入整体设计 - 缩小尺寸 */
      .kpi.status-item {
        position: relative;
        background: transparent;
        border: 0;
        border-radius: 0px;
        box-shadow: none;
        /* 添加微妙的分割线 */
        border-right: 1px solid rgba(26, 211, 255, 0.2);
      }

      /* 最后一个项目不显示右边框 */
      .kpi.status-item:last-child {
        border-right: none;
      }

      .kpi.status-item .meta .v {
        font-size: 28px;
        letter-spacing: 0.2px;
      }

      .status-item {
        display: grid;
        grid-template-columns: 32px 1fr;
        grid-template-rows: auto auto;
        grid-template-areas:
          "icon value"
          "icon label";
        align-items: center;
        justify-content: start;
        gap: 4px 8px; /* 行间距4，列间距8 */
        position: relative;
        background: transparent;
        border: none;
        border-radius: 4px;
        padding: 8px 6px; /* 缩小内边距 */
        box-shadow: none;
        min-height: 52px; /* 略缩小高度 */
        transition: all 0.3s ease;
      }

      .status-item:hover {
        transform: translateY(-1px);
      }
      .status-item .icon {
        grid-area: icon;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        background: none;
        border-radius: 6px;
        box-shadow: none;
        order: 0;
      }
      .status-item .icon svg {
        width: 36px;
        height: 36px;
        stroke: #ffffff;
        fill: none;
        stroke-width: 1.8;
        stroke-linecap: round;
        stroke-linejoin: round;
      }
      .status-item .meta {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
        grid-template-areas:
          "value"
          "label";
        align-items: start;
        justify-items: start;
        gap: 0;
        text-align: left;
      }
      .status-item .meta .v {
        grid-area: value;
        font-size: 18px;
        line-height: 1.05;
        color: #04BA19;
        font-weight: 700;
        letter-spacing: 0.1px;
      }
      .status-item .meta .label {
        grid-area: label;
        color: var(--muted);
        font-size: 15px;
        font-weight: 500;
        line-height: 1.2;
      }

      .legend {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
      }

      .dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 6px;
      }
      .badge {
        padding: 2px 6px;
        border-radius: 999px;
        background: rgba(26, 211, 255, 0.1);
        border: 1px solid rgba(26, 211, 255, 0.3);
        color: var(--muted);
        font-size: 12px;
      }

      /* 表格（告警） */
      table {
        width: 100%;
        border-collapse: collapse;
      }
      thead th {
        text-align: left;
        font-weight: 600;
        color: var(--muted);
        padding: 6px 8px;
        border-bottom: 1px solid rgba(26, 211, 255, 0.2);
        position: sticky;
        top: 0;
        z-index: 4; /* 确保表头在任何粘性单元格之上 */
        background: linear-gradient(180deg, rgba(6, 18, 32, 0.9), rgba(6, 18, 32, 0.75));
        backdrop-filter: blur(6px);
      }
      /* Lucide 图标样式重置，走系统样式 */
      i[data-lucide] {
        display: inline-flex;
      }

      tbody td {
        padding: 6px 8px;
        border-bottom: 1px dashed rgba(26, 211, 255, 0.15);
      }
      .level {
        font-weight: 600;
      }
      .level.high {
        color: var(--high);
      }
      .level.mid {
        color: var(--mid);
      }
      .level.low {
        color: var(--low);
      }
      /* 风险预警列表：滚动、省略、多列样式增强 */
      #alerts .table-viewport.scroll {
        overflow: auto; /* 允许编程滚动 */
        position: relative;
        height: 360px; /* 提高初始高度，显示更多数据，JS会覆盖为精确行高 */
        flex: 0 0 auto; /* 取消flex:1撑高，按固定高度显示 */
        padding: 0; /* 表头紧贴容器顶部 */
        -ms-overflow-style: none; /* IE/Edge 隐藏滚动条 */
        scrollbar-width: none; /* Firefox 隐藏滚动条 */
      }
      #alerts .table-viewport.scroll::-webkit-scrollbar { display: none; } /* Chrome/Safari 隐藏滚动条 */
      #alerts table {
        table-layout: fixed;
      }
      #alerts thead th,
      #alerts tbody td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      /* 调小表格数据字号，压缩内边距，保证更多行可见 */
      #alerts thead th {
        font-size: 11px;
        padding: 2px 6px;
      }
      #alerts tbody td {
        font-size: 11px;
        padding: 2px 6px;
      }
      /* 确保最后一列（告警时间）完整展示，并右对齐 */
      #alerts thead th.time {
        position: sticky;
        right: 0;
        z-index: 6; /* 高于普通表头与内容右侧粘性列 */
        background: linear-gradient(180deg, rgba(6, 18, 32, 0.96), rgba(6, 18, 32, 0.88));
        box-shadow: -1px 0 0 rgba(26, 211, 255, 0.2);
        white-space: nowrap;
        overflow: visible;
        text-overflow: clip;
        text-align: right;
      }
      #alerts tbody td:last-child {
        position: sticky;
        right: 0;
        z-index: 3; /* 低于表头，但高于普通单元格 */
        background: rgba(8, 20, 34, 0.75);
        box-shadow: -1px 0 0 rgba(26, 211, 255, 0.12);
        white-space: nowrap;
        overflow: visible;
        text-overflow: clip;
        text-align: right;
        font-variant-numeric: tabular-nums;
      }
      #alerts tbody tr {
        cursor: pointer;
      }
      #alerts tbody tr:hover {
        background: rgba(44, 231, 255, 0.06);
      }
      /* 列表中仅在“风险等级”字段使用红/橙/白配色（低风险设为白色） */
      #alerts .level.low {
        color: #ffffff;
      }
      /* 风险描述列可多行截断时的样式（如未来改为2行） */
      .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        white-space: normal;
      }
      /* 小型时间分段按钮（与上方风格一致，稍微紧凑） */
      .mini-time.segp {
        display: inline-flex;
        gap: 6px;
      }
      .mini-time .seg {
        transform: scale(0.92);
        transform-origin: left center;
      }

      /* 布局占位/图表与地图 */
      .chart {
        width: 100%;
        height: 100%;
        background: rgba(8, 20, 34, 0.35);
        border: 1px solid rgba(26, 211, 255, 0.18);
        border-radius: 8px;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        box-shadow:
          inset 0 0 0 1px rgba(26, 211, 255, 0.06),
          0 8px 24px rgba(0, 0, 0, 0.25);
      }
      .map {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #7ecfff;
        border: 1px dashed rgba(26, 211, 255, 0.2);
        border-radius: 6px;
        background: radial-gradient(
          400px 240px at 50% 30%,
          rgba(0, 180, 255, 0.18),
          transparent 70%
        );
      }
      .map-wrap {
        position: relative;
        flex: 1;
        min-height: 0;
        border: 1px dashed rgba(26, 211, 255, 0.2);
        border-radius: 6px;
        overflow: visible; /* 允许溢出显示，避免放大被裁剪 */
      }
      /* AMap background container fills the bg area */
      #amap-bg.amap-bg {
        position: absolute;
        inset: 0;
        z-index: 0;
      }
      #bg-map .map-wrap{position:absolute;inset:0;}
      #amap-bg.amap-bg{position:absolute;inset:0;z-index:0;}
      /* 无边框模式：顶端统计栏 & 中间地图区域 */
      #top-stats.panel,
      #map.panel {
        border: none;
        background: transparent;
        box-shadow: none;
      }
      #top-stats.panel::before,
      #map.panel::before {
        content: none;
      }
      #top-stats .header,
      #map .header {
        border-bottom: none;
        background: url('./image/subtitle_bg.png') center/100% 100% no-repeat;
      }
      #map .content .map-wrap {
        border: none;
      }

      .map-canvas {
        position: absolute;
        inset: 0;
        background: url('../axure/images/u554.png') center/contain no-repeat;
        transform-origin: center center;
        cursor: grab;
      }
      .map-dots {
        position: absolute;
        inset: 0;
        pointer-events: auto;
      }
      .map-dot {
        position: absolute;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: radial-gradient(
          circle at 30% 30%,
          #fff,
          rgba(255, 255, 255, 0.6) 30%,
          rgba(0, 200, 255, 0.4) 60%,
          rgba(0, 200, 255, 0.08) 100%
        );
        box-shadow: 0 0 12px rgba(0, 200, 255, 0.75);
        transform: translate(-50%, -50%);
      }
      .heatmap {
        position: absolute;
        inset: 0;
        pointer-events: none;
      }
      .map-tooltip {
        position: absolute;
        z-index: 5;
        pointer-events: none;
        transform: translate(12px, -10px);
        background: rgba(10, 24, 40, 0.85);
        border: 1px solid rgba(26, 211, 255, 0.35);
        color: #dff6ff;
        border-radius: 8px;
        padding: 8px 10px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.35);
        display: none;
        min-width: 220px;
      }
      .map-tooltip .title {
        font-weight: 700;
        color: #8fe9ff;
      }
      .map-tooltip .row {
        display: flex;
        justify-content: space-between;
        gap: 12px;
        color: #9ec9ff;
      }
      .map-ctrl {
        position: absolute;
        right: 8px;
        top: 8px;
        display: flex;
        flex-direction: column;
        gap: 6px;
        z-index: 3;
      }
      .map-ctrl button {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        border: 1px solid rgba(26, 211, 255, 0.4);
        background: rgba(6, 18, 32, 0.6);
        color: #9feaff;
        cursor: pointer;
      }

      /* 地图图层控制面板 */
      .map-layer-control {
        display: none;
        position: fixed;
        right: 20px;
        top: 200px; /* 改为固定像素位置，避免transform问题 */
        background: rgba(6, 18, 32, 0.98); /* 提高背景透明度 */
        border: 3px solid rgba(44, 231, 255, 0.8); /* 更明显的边框 */
        border-radius: 12px;
        padding: 16px;
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.6); /* 更强的阴影 */
        z-index: 2000; /* 大幅提高z-index */
        min-width: 220px;
        max-width: 300px;
        pointer-events: auto;
        opacity: 1; /* 确保完全不透明 */
        visibility: visible; /* 确保可见 */
      }

      .map-layer-control h4 {
        margin: 0 0 16px 0;
        color: #2ce7ff;
        font-size: 16px;
        font-weight: 700;
        text-align: center;
        border-bottom: 2px solid rgba(26, 211, 255, 0.4);
        padding-bottom: 10px;
        text-shadow: 0 0 8px rgba(44, 231, 255, 0.5);
      }

      /* 风险点图例 */
      #map .content .risk-legend {
        position: absolute;
        left: 16px;
        bottom: 16px;
        display: flex !important;
        gap: 12px;
        align-items: center;
        padding: 8px 10px;
        background: rgba(6, 18, 32, 0.85);
        border: 2px solid rgba(44, 231, 255, 0.6);
        border-radius: 10px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
        z-index: 2000;
        pointer-events: auto;
      }
      .risk-legend .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #cbefff;
        white-space: nowrap;
      }
      .risk-legend .legend-icon {
        width: 18px;
        height: 18px;
        display: block;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.6));
      }
      .risk-legend .legend-label.high { color: #ff6161; }
      .risk-legend .legend-label.medium { color: #f4c542; }
      .risk-legend .legend-label.low { color: #2de26d; }

      .layer-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 6px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
      }

      .layer-item:hover {
        background: rgba(26, 211, 255, 0.1);
      }

      .layer-item:last-child {
        margin-bottom: 0;
      }

      .layer-label {
        color: #d8e7ff;
        font-size: 12px;
        font-weight: 500;
        flex: 1;
      }

      .layer-toggle {
        position: relative;
        width: 36px;
        height: 20px;
        background: rgba(26, 211, 255, 0.2);
        border-radius: 10px;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      .layer-toggle.active {
        background: rgba(44, 231, 255, 0.6);
      }

      .layer-toggle::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background: #ffffff;
        border-radius: 50%;
        transition: transform 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .layer-toggle.active::after {
        transform: translateX(16px);
      }

      .layer-status {
        font-size: 10px;
        color: #9feaff;
        margin-left: 8px;
        opacity: 0.7;
      }
      .two-col {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        height: 100%;
      }
      .stack {
        display: grid;
        grid-template-rows: 1fr 1fr;
        gap: 10px;
        height: 100%;
      }

      /* 风险/事件 两列分割布局 */
      .risk-split {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        align-items: start;
      }

      /* 风险统计工具栏 */
      .chart-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        margin-bottom: 8px;
      }
      .chart-toolbar .seg.time {
        transform: scale(0.9);
        transform-origin: left center;
      }
      .chart-toolbar .modes {
        display: inline-flex;
        gap: 6px;
      }
      .chart-toolbar .modes .mode {
        width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        border: 1px solid rgba(26, 211, 255, 0.3);
        background: rgba(6, 18, 32, 0.5);
        color: #9feaff;
        cursor: pointer;
      }
      .chart-toolbar .modes .mode.active {
        background: linear-gradient(180deg, rgba(44, 231, 255, 0.25), rgba(17, 191, 255, 0.2));
        border-color: rgba(44, 231, 255, 0.6);
        box-shadow: 0 0 8px rgba(44, 231, 255, 0.4);
      }
      /* 让标题栏右侧的模式按钮使用与工具栏相同的外观 */
      .panel .header .modes {
        display: inline-flex;
        gap: 6px;
      }
      .panel .header .modes .mode {
        width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        border: 1px solid rgba(26, 211, 255, 0.3);
        background: rgba(6, 18, 32, 0.5);
        color: #9feaff;
        cursor: pointer;
      }
      .panel .header .modes .mode.active {
        background: linear-gradient(180deg, rgba(44, 231, 255, 0.25), rgba(17, 191, 255, 0.2));
        border-color: rgba(44, 231, 255, 0.6);
        box-shadow: 0 0 8px rgba(44, 231, 255, 0.4);
      }
      .panel .header .seg.time { transform: scale(0.92); transform-origin: right center; }
      .panel .header .year-select {
        appearance: none;
        background: rgba(6, 18, 32, 0.6);
        border: 1px solid rgba(26, 211, 255, 0.35);
        color: #cfeaff;
        border-radius: 6px;
        padding: 4px 8px;
        font-size: 12px;
      }
      .chart-toolbar .year-select {
        appearance: none;
        background: rgba(6, 18, 32, 0.6);
        border: 1px solid rgba(26, 211, 255, 0.35);
        color: #cfeaff;
        border-radius: 6px;
        padding: 4px 8px;
        font-size: 12px;
      }

      /* 车辆信息轮播 */
      .carousel {
        position: relative;
        overflow: hidden;
        height: 260px;
        margin-bottom: 12px;
        border-radius: 6px;
      }
      .carousel-track {
        display: flex;
        width: 200%;
        height: 100%;
        will-change: transform;
        transition: transform 500ms ease;
      }
      .carousel-slide {
        flex: 0 0 50%;
        height: 100%;
      }
      /* 居中车辆信息轮播内的图表 */
      #vehicle-info .carousel-slide {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .carousel-indicators {
        position: absolute;
        left: 50%;
        bottom: 8px;
        transform: translateX(-50%);
        display: flex;
        gap: 6px;
        pointer-events: auto;
      }
      .carousel-indicators .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(26, 211, 255, 0.35);
        border: 1px solid rgba(26, 211, 255, 0.6);
        cursor: pointer;
      }
      .carousel-indicators .dot.active {
        background: #2cd8ff;
        border-color: #2cd8ff;
        box-shadow: 0 0 8px rgba(44, 216, 255, 0.6);
      }

      /* 左右列顶端统计卡片：更紧凑 + 数字更醒目（仅车辆信息、企业信息） */
      #vehicle-info .kpi-bar,
      #enterprise-info .kpi-bar {
        display: grid;
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 6px;
        margin-top: 8px;
      }
      #vehicle-info .kpi,
      #enterprise-info .kpi {
        padding: 8px 8px;
        background: linear-gradient(180deg, rgba(26, 211, 255, 0.12), rgba(26, 211, 255, 0.08));
        border: 1px solid rgba(26, 211, 255, 0.25);
        border-radius: 8px;
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        box-shadow:
          inset 0 2px 8px rgba(26, 211, 255, 0.15),
          0 4px 16px rgba(0, 0, 0, 0.15);
        min-height: 64px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
      }
      /* 数字在上、文字在下（通过排序实现，无需改 HTML） */
      #vehicle-info .kpi .v,
      #enterprise-info .kpi .v {
        order: 1;
        display: inline-flex;
        align-items: baseline;
        gap: 2px;
        font-size: 22px;
        line-height: 1;
        font-weight: 900;
        color: #ffffff;
      }
      #vehicle-info .kpi > div:first-child,
      #enterprise-info .kpi > div:first-child {
        order: 2;
        font-size: 12px;
        font-weight: 500;
        color: #d8e7ff;
        opacity: 0.9;
        margin-top: 4px;
      }
      #vehicle-info .kpi .unit,
      #enterprise-info .kpi .unit {
        margin-left: 0;
        font-size: 11px;
        color: #99c9ee;
        opacity: 0.9;
      }

      /* 小屏兼容 */
      @media (max-width: 1200px) {
        .grid {
          grid-template-columns: 1fr;
          grid-template-rows: repeat(6, auto);
        }
      }
      /* 强制 root 三列最小宽度，防止首列为 0 */
      .grid#root {
        grid-template-columns: minmax(320px, 1fr) minmax(0, 1.6fr) minmax(320px, 1fr) !important;
        grid-auto-columns: minmax(0, 1fr);
      }
    </style>
    <style>
    /* 港铁系统 大屏图表区标题背景统一样式 - 应用于大屏内嵌 iframe */
    section.panel > .header,
    .panel > .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      height: 38px;

      box-sizing: border-box;
      padding: 0px 16px 8px 28px;
      background: transparent;
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAbCAYAAABiFp9rAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGqADAAQAAAABAAAAGwAAAABxqZ+6AAAEy0lEQVRIDb1WTWwbRRT+ZnYd21k7iZ0fNw5toSg/NCRtIFIRoqopQqKABKrUguDAoZxQOXDoBQnZ6QWJ3pBAoiCBiCqk9IDEnzhAMFKlFtE0pDRuo7ZREUmbJo5/4n/v7gxvE5s4id20AnUP3vXMm/e9+d6b7w1wnx62GY6UctlmaAjL72AQkjEmN1u3fn5ToFBI8jDAu9rB4h6w3laIyQXIkUMQ9wK4KVAgJNUHt0ONCdg4hyIETC+HfuMvGAFAhEJMrI++2n9ebbA8ZtHm9EJJAA6hosFU4JE2eDIa3Ft3wH6zHYq14zK95XXV3mq1wcox1xbwggG7ItBomnBZc8Ui0jmJZFRDKr4T+cOnYRLYHalcs6P+U1c+PfDDVXslUDIPrhJtxI92fl/yUGQw/RxM+BmHjzF4sjo0LQMbgd1xZ2uApJBvzi4av+4ameoogzWmwPIMCiWzTnDZOv1IJnjumfgRCPhtJrbYdHgX7XDl47APhVeoLK+tfK8BsiaobveIohjrH57aa/13uCEVE0QMGJfgYJLf7ii8Gn4pGkzVmzt0BX4ya2FuuCcjcNTK2wYgyzmh+cDE6O5TV44mcgQiYZKhLhQirfSkGo3BMy/GTsxtLQzAICoF2nIuNFKhOCd3Ql1PZXUgC0tCFRIf3qif+iSt5JgBZLon3N/Vp5WJMljRbnaM7028HxnIHCD7DitvuRp5U8qLrLfv4NFQ5f/Sd3+eLe0Xgv/kLWjxByLaWLJFd2UbRCfNM9INW7xNfyLWamjt1x3X6ZxZFIuihJGLQVz7ESIcHpI1d1QJSNztTvGFb+cx021XEH1y1PNl55/aSSZYfsVO8oX2/MHwy9Hjaa+5jQSqiQDrzSaoROOyKNwVUMlZc1ZmPkuIRR/RVOyZcI13XnSNVAaUbTAGftsXP0aFY6eDrTp0cJKsewViixq0I028+Tadn7rIrvRjV/vSr1QCaUvqhT2jnhOUqwLTYeRtK7po2WyqDCVH4w2s9S0vvGaBDuvYs/E3ou3F56lkSlrJRNus/evBn5u+IIrmaTShcGRJu4zeGORpclIyXHHXN3x5g/yTQg97se1dTTibljxmz1gg9l7WZfaXAgA3Wfahy66Pev/QRmnxvI1jsQAstRaRo1LXyypfc0cUgS4Zf2d7putz6YCHpEGbGki9UAlSl1dm+842fOCfsV8iuhaEiZiTIaXUIZ+xwSyDWEHVKoY5pvD9F1/v+rjJCdoUtQdQmzDx73FwJ2y/P/W99xiBXKAE3KT6nXemkSTdy/VGYFSCVAUip+dUlT0+8Vr3GcsgT1pH7YFRxCTPpHCSCd+M86vAN83H3VllmiK4RWZRmUKql5Tcfwum1aPWN8X11J3srut5+/RhVrRArCdJWuciCdKpO6iSzT98SQs9Ou46KzhiOuWDKZSPAuXDAz0YoK77dPU2v6YYVlyv/loNjTTLQf2ogbSsudyPFAVp+k7STlMuygetWJOPVQ+rX+t3tDpT+krPQdjdKBgcSaI1R8MmNb5ci4qsM4Oi/xrMu7mw1CqGZRiLZ9IrUhLkuYElhwMxOohxH1XW39Mo1MrHhmhpYNMdBegCEqaLCN2CTMQBfxekdQv6Jfg/34KsPFkR/td7neXjvjz/APCpIWo4RLN4AAAAAElFTkSuQmCC),url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAc4AAAALCAYAAAD2tG0DAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAABzqADAAQAAAABAAAACwAAAAB9iK53AAAEvUlEQVR4Ae2cTWvcVhSGj77GduwYagjUq6z6gRfZdVFa2h9QsksIdJFtlvkJSX9ClukPKJR020Ugq9KuuigEWgKFQiFgQ8EBe8YzGWmkPK9kzcgmkGRsEck91whJM9LMed4j33PP/ZjA2i5FEdh3FtgOm8pfx/u2v/c8Pn/HCuwtyo+6xz4IquP6s2s2nYvvIrGJqe98YpD/Xuc7vefFFXAFXIElFIiXuOftb1HF+8hCbghszyLLOF5nG3G+1tEAukFF+x8Wr7N/bjlHM7ac4J8TSCA5Dp5Nti3YnveADRAbw/UmNl23CJryXwhfNPfdFV4ZdtR/Tb742H+PYC6KfO478XlxBVwBV2BJBdoNnMo0t6l0Ny22IxvYxBK7xHFMJaxg2sUyxKhYFS0Bc2IpZyn2pwSNjOCpIFplnU02g+sAvq6zSe8m2xZc2WvYdF3FF9HIiWFLSt/F7OW7rgbNJt8BZKul76b2ovSd/Hayx0DXe3EFXAFX4B0VaDdwKlPZpaJdJaiE5JjrbDlnCp0Fr8w6GDwjLFOmooASlOFizLnZS17bKTOYWuIFW3bMltoK1ya9YJtBdEhz5jI4u1h8kq3qet7DR+JJyv6BtTIU9YGvwNJ1fFeQX0/hO+CoCprqQfDiCrgCrsCZFCgDZ6FuuRZK8D2h54DAmRI4IyrfmW0Qei7xVQPe6WbWqVxT2WZEYMkIGyucDTmasu3brNZqzhYQVsawJbAZbHkP2AwadZiHsMGE3ZnGZ2s2PQqBMk5DhQFkKzR2XsIXwzfjrOu+E5+aP3rCcixWb8Hmwnfi8+IKuAKuwDIKBAzXlQHzs7s/tNKFxaAS9VdkeRDbjO3GV5/arS8+WsbW93LPg5+f2q9P/7WoSIkxDNBqmAwilTlbGMGW2PXPP7bbX3/yXuxc5ksfPv7TnvzxT8VWZETNvIyj9WcxEwq/hXDKd4l9ee2q3f3mWv125/c//va3/fTLM/jkN/iIn2oneHEFXAFX4CwK/P7g26CVTHNu1MOiGvtLyVSUkZ3OOBV9ulZUuy4yziPyq0PylyHbGFOndj/ISpNrNk1zGtPhKb5mNt1ltiojU8Y5JJPUqO7RCTYB3i/UGzHgb41tg+sun8g4u84XwqTR2JQt4XgT390JNGbtxRVwBVyBMynQ7hinxs40K3WVSkvjTVXXmdK3hNer2bZnMr+Fm5VSkmxhteycEDAmhAxVuDP7sOSpvnTBppHNcdkEyMt7us9W2TmBcmxb+GafIy29aRad78GsyVGHaBCWSZvmRfeDT13teuZC+EZwjE7xNVn92BVwBVyBd1Cg3cCpHs3tstKaUn0Z1W9G7qkJGxHVb0AG09WiHspqglBK4KhmnubzNZ2V1Qs2TXXa7wlbiq0DfCImNQi0l4/q9aq1R3S+jQYj3lezYcCVMR7UpC41errqu5pvhL2aVTvgafsAvl1YvLgCroArcA4KtNtVWq911A8DaK2j8hUNNWkdZ1fXAjbXcTI8y+Smah2nGgH3CBun13FeRDY9WKfXcW7iv775To0fTX5S9nyT49p35/CP4x/hCrgC/18F2g2cta51JdynXw+quy7f9MszF5lN/rvofPUz6ntXwBVwBd5SgVe0tAyhPHzagAAAAABJRU5ErkJggg==);
      background-repeat: no-repeat,no-repeat;
      background-position: -2px 1px,left 32px;
      background-size: 26px 27px,100% 11px;
    }
    /* 港铁系统 标题文字样式（仅限面板标题文字） */
      .panel .header .clock {
        margin-left: auto;
        color: #9feaff;
        font-weight: 600;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        letter-spacing: 0.3px;
        opacity: 0.95;
        transform: scale(0.96);
        transform-origin: right center;
      }

    section.panel > .header > .title,
    .panel > .header > .title {
      font-size: 24px;
      line-height: 30px;
      color: #fff;
      text-shadow: 0 5px 5px rgba(116, 140, 204, 0.96);
      font-style: normal;
    }
  </style>
</head>
  <body>
    <!-- 全局铺底天津地图-高德地图 -->
    <div id="bg-map">
      <div class="map-wrap">
        <!-- Real AMap background container -->
        <div id="amap-bg" class="amap-bg"></div>
      </div>
      </div>
    <!-- 渐变遮罩层 -->
    <div class="map-overlay" style="
      position: absolute;
      inset: 0;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(15, 23, 42, 0.98) 0%,
        rgba(15, 23, 42, 0.98) 19%,
        transparent 36%,
        transparent 65%,
        rgba(15, 23, 42, 0.98) 80%,
        rgba(15, 23, 42, 0.98) 100%
      ),
      linear-gradient(
        to bottom,
        rgba(15, 23, 42, 0.98) 0%,
        rgba(15, 23, 42, 0.98) 5%,
        transparent 15%,
        transparent 75%,
        rgba(15, 23, 42, 0.98) 92%,
        rgba(15, 23, 42, 0.98) 100%
      );
      z-index: 30;
    "></div>
    <div class="grid" id="root">
      <div class="col col-left">
        <section class="panel" id="vehicle-info">
          <div class="header"><div class="title">车端 · 车辆信息</div><div class="clock" id="veh-clock" aria-label="大屏当前时间"></div></div>
          <div class="content">
            <div class="carousel" id="veh-carousel">
              <div class="carousel-track">
                <div class="carousel-slide">
                  <div id="chart-veh-type" class="chart" style="height: 260px"></div>
                </div>
                <div class="carousel-slide">
                  <div id="chart-veh-brand" class="chart" style="height: 260px"></div>
                </div>
              </div>
              <div class="carousel-indicators" aria-hidden="true">
                <span class="dot active"></span>
                <span class="dot"></span>
              </div>
            </div>
            <div class="kpi-bar">
              <div class="kpi">
                <div>车辆总数</div>
                <div class="v">
                  <span id="kpi-veh-total">12,345</span><span class="unit">辆</span>
                </div>
              </div>
              <div class="kpi">
                <div>品牌</div>
                <div class="v"><span id="kpi-veh-brands">16</span><span class="unit">个</span></div>
              </div>
              <div class="kpi">
                <div>在线车辆</div>
                <div class="v">
                  <span id="kpi-veh-online">8,906</span><span class="unit">辆</span>
                </div>
              </div>
              <div class="kpi">
                <div>今日新增</div>
                <div class="v"><span id="kpi-veh-new">128</span><span class="unit">辆</span></div>
              </div>
            </div>
          </div>
        </section>

        <section class="panel" id="proc-left">
          <div class="header">
            <div class="title">处理活动统计</div>
            <div class="seg" data-scope="proc-left">
              <button class="active" data-range="day">日</button
              ><button data-range="month">月</button><button data-range="year">年</button>
            </div>
          </div>
          <div class="content">
            <div id="chart-proc-left" class="chart"></div>
          </div>
        </section>

        <section class="panel" id="risk-left">
          <div class="header">
            <div class="title">车端风险事件</div>
            <div style="display: flex; align-items: center; gap: 8px">
              <div class="modes" data-scope="risk-left-mode">
                <button class="mode active" data-mode="bar" aria-label="柱状图">
                  <i data-lucide="bar-chart-3"></i>
                </button>
                <button class="mode" data-mode="pie" aria-label="饼图">
                  <i data-lucide="pie-chart"></i>
                </button>
                <button class="mode" data-mode="line" aria-label="折线图">
                  <i data-lucide="line-chart"></i>
                </button>
              </div>
              <div class="seg time" data-scope="risk-left-time">
                <button class="active" data-range="day">日</button>
                <button data-range="week">周</button>
                <button data-range="month">月</button>
                <button data-range="year">年</button>
              </div>
              <select class="year-select" id="risk-left-year" style="display: none">
                <option>2023</option>
                <option>2024</option>
                <option selected>2025</option>
              </select>
            </div>
          </div>
          <div class="content">
            <div class="risk-split">
              <!-- 左侧：风险统计（工具栏已移至标题栏右侧） -->
              <div>
                <div id="chart-risk-left" class="chart" style="height: 360px"></div>
              </div>
              <!-- 右侧：事件统计（按事件类型分类） -->
              <div>
                <div id="chart-event-left" class="chart" style="height: 360px"></div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <div class="col col-mid">
        <section class="panel" id="top-stats">
          <div class="content">
            <div class="kpi-bar status">
              <div class="kpi status-item">
                <div class="icon" aria-hidden="true"><i data-lucide="shield-alert"></i></div>
                <div class="meta">
                  <div class="label">风险总数</div>
                  <div class="v" id="kpi-veh-total">2,345</div>
                </div>
              </div>
              <div class="kpi status-item">
                <div class="icon" aria-hidden="true"><i data-lucide="car-front"></i></div>
                <div class="meta">
                  <div class="label">车端风险</div>
                  <div class="v" id="kpi-veh-risk">168</div>
                </div>
              </div>
              <div class="kpi status-item">
                <div class="icon" aria-hidden="true"><i data-lucide="cloud-lightning"></i></div>
                <div class="meta">
                  <div class="label">云端风险</div>
                  <div class="v" id="kpi-cloud-risk">73</div>
                </div>
              </div>
              <div class="kpi status-item">
                <div class="icon" aria-hidden="true"><i data-lucide="alert-triangle"></i></div>
                <div class="meta">
                  <div class="label">车端事件</div>
                  <div class="v" id="kpi-veh-events">256</div>
                </div>
              </div>
              <div class="kpi status-item">
                <div class="icon" aria-hidden="true"><i data-lucide="zap"></i></div>
                <div class="meta">
                  <div class="label">云端事件</div>
                  <div class="v" id="kpi-cloud-events">89</div>
                </div>
              </div>
              <div class="kpi status-item">
                <div class="icon" aria-hidden="true"><i data-lucide="check-circle-2"></i></div>
                <div class="meta">
                  <div class="label">处置率</div>
                  <div class="v" id="kpi-done">92%</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section class="panel" id="map">
            <div class="content">
              <div style="height: 100%; position: relative; display: block;">

                <!-- 风险点图例 -->
                <div class="risk-legend" id="risk-legend">
                  <div class="legend-item">
                    <img class="legend-icon" src="../assets/icons/co-red.svg" alt="高风险">
                    <span class="legend-label high">高风险</span>
                  </div>
                  <div class="legend-item">
                    <img class="legend-icon" src="../assets/icons/co-orange.svg" alt="中风险">
                    <span class="legend-label medium">中风险</span>
                  </div>
                  <div class="legend-item">
                    <img class="legend-icon" src="../assets/icons/co-yellow.svg" alt="低风险">
                    <span class="legend-label low">低风险</span>
                  </div>
                </div>

                <!-- 地图图层控制面板 -->
                <div class="map-layer-control" id="map-layer-control">
                  <h4>地图图层控制</h4>
                  <div class="layer-item">
                    <span class="layer-label">卫星图层</span>
                    <div class="layer-toggle active" data-layer="satellite">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">路网图层</span>
                    <div class="layer-toggle active" data-layer="roadnet">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">风险点标记</span>
                    <div class="layer-toggle active" data-layer="risk-markers">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">敏感区域</span>
                    <div class="layer-toggle active" data-layer="sensitive-areas">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">热力图</span>
                    <div class="layer-toggle active" data-layer="heatmap">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">3D建筑</span>
                    <div class="layer-toggle active" data-layer="buildings">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">天津市边界</span>
                    <div class="layer-toggle active" data-layer="tianjin-boundary">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">天津市遮罩</span>
                    <div class="layer-toggle active" data-layer="tianjin-mask">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                  <div class="layer-item">
                    <span class="layer-label">区县边界</span>
                    <div class="layer-toggle active" data-layer="tianjin-districts">
                      <span class="layer-status">显示</span>
                    </div>
                  </div>
                </div>

              </div>
            </div>
         </section>

        <section class="panel" id="alerts">
          <div class="content two-col">
            <!-- 中下左 - 车端风险预警 -->
            <div class="panel" id="alerts-car" style="margin: 0; backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); background: rgba(8, 20, 34, 0.75); border: 1px solid rgba(26, 211, 255, 0.25); box-shadow: inset 0 0 0 1px rgba(26, 211, 255, 0.08), 0 8px 24px rgba(0, 0, 0, 0.25);">
              <div class="header">
                <div class="title">车端风险预警</div>
                <div class="seg time" data-scope="alerts-car">
                  <button class="active" data-range="day">日</button>
                  <button data-range="month">月</button>
                  <button data-range="year">年</button>
                </div>
              </div>
              <div class="content"><div class="table-viewport scroll">
                <table id="tbl-car">
                  <thead>
                    <tr>
                      <th style="width: 30px">序号</th>
                      <th style="width: 100px">VIN码</th>
                      <th style="width: 40px">等级</th>
                      <th style="width: 40px">阶段</th>
                      <th style="width: 80px">风险事件</th>
                      <th style="width: 80px">风险类型</th>
                      <th class="time" style="width: 100px">告警时间</th>
                    </tr>
                  </thead>
                  <tbody></tbody>
                </table>
              </div></div>
            </div>

            <!-- 中下右 - 云端风险预警 -->
            <div class="panel" id="alerts-cloud" style="margin: 0; backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); background: rgba(8, 20, 34, 0.75); border: 1px solid rgba(26, 211, 255, 0.25); box-shadow: inset 0 0 0 1px rgba(26, 211, 255, 0.08), 0 8px 24px rgba(0, 0, 0, 0.25);">
              <div class="header">
                <div class="title">云端风险预警</div>
                <div class="seg time" data-scope="alerts-cloud">
                  <button class="active" data-range="day">日</button>
                  <button data-range="month">月</button>
                  <button data-range="year">年</button>
                </div>
              </div>
              <div class="content"><div class="table-viewport scroll">
                <table id="tbl-cloud">
                  <thead>
                    <tr>
                      <th style="width: 30px">序号</th>
                      <th style="width: 100px">企业名称</th>
                      <th style="width: 40px">等级</th>
                      <th style="width: 40px">阶段</th>
                      <th style="width: 80px">风险事件</th>
                      <th style="width: 80px">风险类型</th>
                      <th class="time" style="width: 100px">告警时间</th>
                    </tr>
                  </thead>
                  <tbody></tbody>
                </table>
              </div>
            </div>
          </div>
        </section>
      </div>

      <div class="col col-right">
        <section class="panel" id="enterprise-info">
          <div class="header">
            <div class="title">云端 · 企业信息</div>
          </div>
          <div class="content">
            <div id="chart-ent-pie" class="chart" style="height: 260px"></div>
            <div class="kpi-bar">
              <div class="kpi">
                <div>企业总数</div>
                <div class="v">
                  <span id="kpi-ent-total">2,316</span><span class="unit">家</span>
                </div>
              </div>
              <div class="kpi">
                <div>合作平台</div>
                <div class="v"><span id="kpi-ent-plat">12</span><span class="unit">个</span></div>
              </div>
              <div class="kpi">
                <div>今日新增</div>
                <div class="v"><span id="kpi-ent-new">9</span><span class="unit">家</span></div>
              </div>
              <div class="kpi">
                <div>告警企业</div>
                <div class="v"><span id="kpi-ent-alarm">34</span><span class="unit">家</span></div>
              </div>
            </div>
          </div>
        </section>

        <section class="panel" id="proc-right">
          <div class="header">
            <div class="title">处理活动统计</div>
            <div class="seg" data-scope="proc-right">
              <button class="active" data-range="day">日</button
              ><button data-range="month">月</button><button data-range="year">年</button>
            </div>
          </div>
          <div class="content">
            <div id="chart-proc-right" class="chart"></div>
          </div>
        </section>

        <section class="panel" id="risk-right">
          <div class="header">
            <div class="title">云端风险事件</div>
            <div style="display: flex; align-items: center; gap: 8px">
              <div class="modes" data-scope="risk-right-mode">
                <button class="mode active" data-mode="bar" aria-label="柱状图">
                  <i data-lucide="bar-chart-3"></i>
                </button>
                <button class="mode" data-mode="pie" aria-label="饼图">
                  <i data-lucide="pie-chart"></i>
                </button>
                <button class="mode" data-mode="line" aria-label="折线图">
                  <i data-lucide="line-chart"></i>
                </button>
              </div>
              <div class="seg time" data-scope="risk-right-time">
                <button class="active" data-range="day">日</button>
                <button data-range="week">周</button>
                <button data-range="month">月</button>
                <button data-range="year">年</button>
              </div>
              <select class="year-select" id="risk-right-year" style="display: none">
                <option>2023</option>
                <option>2024</option>
                <option selected>2025</option>
              </select>
            </div>
          </div>
          <div class="content">
            <div class="risk-split">
              <!-- 左侧：风险统计 -->
              <div>
                <div id="chart-risk-right" class="chart" style="height: 360px"></div>
              </div>
              <!-- 右侧：事件统计（按事件类型分类） -->
              <div>
                <div id="chart-event-right" class="chart" style="height: 360px"></div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
    <!-- Lucide 图标（本地化版本，失败则走 CDN 兜底） -->
<script src="/assets/icons/lucide.min.js" onerror="this.onerror=null;this.remove();var s=document.createElement('script');s.src='https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.min.js';s.onload=function(){try{window.lucide&&lucide.createIcons&&lucide.createIcons()}catch(e){console.warn('[icons] lucide CDN 加载但初始化失败',e)}};document.head.appendChild(s);"></script>
<!-- 本地 ECharts（vendor，同步自 node_modules/echarts/dist/echarts.min.js） -->
    <script src="/iframe/vendor/echarts.min.js"></script>

    <script>
      // ECharts 初始化助手：透明背景 + 容器 ResizeObserver 自适应 + Canvas性能优化
      function initEchart(el, option) {
        if (!window.echarts || !el) return null

        // Canvas性能优化：添加 willReadFrequently 属性
        const canvas = el.querySelector('canvas') || el.appendChild(document.createElement('canvas'))
        if (canvas && canvas.getContext) {
          try {
            const ctx = canvas.getContext('2d', { willReadFrequently: true })
            if (ctx) {
              canvas._context2d = ctx
            }
          } catch (e) {
            console.warn('Canvas context optimization failed:', e)
          }
        }

        const chart = echarts.init(el, null, {
          renderer: 'canvas',
          useDirtyRect: true,
          devicePixelRatio: window.devicePixelRatio || 1
        })

        // 强制透明背景（双保险：即使个别 option 未覆盖）
        if (option && typeof option === 'object') {
          const safeOption = { ...option, backgroundColor: 'transparent' }
          chart.setOption(safeOption)
        }

        // 监听容器尺寸变化，解决非 window.resize 场景下的宽度错位
        try {
          const ro = new ResizeObserver(() => {
            // 延迟执行，避免频繁resize
            clearTimeout(el._resizeTimer)
            el._resizeTimer = setTimeout(() => chart.resize(), 100)
          })
          ro.observe(el)
        } catch (e) {
          // ResizeObserver 不可用时，至少监听 window.resize
          window.addEventListener('resize', () => chart.resize())
        }

        return chart
      }

      // 简单分段切换（仅高亮）
      document.querySelectorAll('.seg').forEach((seg) => {

        seg.addEventListener('click', (e) => {
          if (e.target.tagName === 'BUTTON') {
            ;[...seg.querySelectorAll('button')].forEach((b) => b.classList.remove('active'))
            e.target.classList.add('active')
            window.parent?.postMessage(
              { type: 'segmentChange', scope: seg.dataset.scope, value: e.target.textContent },
              '*',
            )
          }
        })
      })

      // 向父窗口暴露 API（兼容直开）
      window.Dashboard = {
        setKPIs(payload) {
          // { id:value }
          Object.entries(payload || {}).forEach(([k, v]) => {
            const el = document.getElementById(k)
            if (el) el.textContent = v
          })
        },

      // 实时钟显示（用于“车端 · 车辆信息”标题右侧）
      initRealtimeClock: (function () {
        function startRealtimeClock(elId) {
          const el = document.getElementById(elId)
          if (!el) return
          const pad = (n) => String(n).padStart(2, '0')
          function tick() {
            const d = new Date()
            const s = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
            el.textContent = s
            el.title = s
          }
          tick()
          setInterval(tick, 1000)
        }
        document.addEventListener('DOMContentLoaded', () => startRealtimeClock('veh-clock'))
      })(),

        setTable(id, rows) {
          // rows: Array<Array|string>
          const tb = document.querySelector(`#${id} tbody`)
          if (!tb) return
          tb.innerHTML = (rows || [])
            .map(
              (r) =>
                `<tr>${r
                  .map((c, i) => {
                    if (i === 2) {
                      // 风险等级列
                      const cls = c === '高' ? 'high' : c === '中' ? 'mid' : 'low'
                      return `<td class="level ${cls}">${c}</td>`
                    }
                    return `<td>${c}</td>`
                  })
                  .join('')}</tr>`,
            )
            .join('')
        },
      }

      // === 风险预警列表（车端/云端）数据与交互 ===
      const ALERT_TICKERS = new Map()
      const ALERT_STATE = {
        car: { period: 'day' },
        cloud: { period: 'day' },
      }

      function randPick(arr) {
        return arr[Math.floor(Math.random() * arr.length)]
      }
      function pad2(n) {
        return n < 10 ? '0' + n : '' + n
      }
      function formatTime(date) {
        const y = date.getFullYear()
        const m = pad2(date.getMonth() + 1)
        const d = pad2(date.getDate())
        const hh = pad2(date.getHours())
        const mm = pad2(date.getMinutes())
        return `${y}-${m}-${d} ${hh}:${mm}`
      }

      const SAMPLE_DESC_CAR = [
        '车辆在受限区域进行高频图像数据收集，疑似违规采集敏感地理信息。',
        '存储策略未按要求进行地理信息脱敏，存在少量原始坐标残留。',
        '夜间传输日志发现少量未加密流量，可能由设备降级导致。',
        '车辆在未报备测试/采集区域进行高精度地图数据采集，违反测绘法规。',
      ]
      const SAMPLE_DESC_CLOUD = [
        '数据收集未按要求脱敏，存在敏感数据暴露风险。',
        '云端存储访问控制不当，可能被未授权访问。',
        '传输加密强度不足，存在被中间人攻击风险。',
        '对第三方提供了未脱敏的原始数据，存在泄露风险。',
      ]
      const CAR_STAGES = ['收集', '存储', '传输']
      const CLOUD_OPS = ['收集', '存储', '传输', '加工', '提供', '公开', '销毁']
      const LEVELS = ['高', '中', '低']
      // 风险类型（根据 /gov/monitor/risk/vehicle 与 /gov/monitor/risk/cloud 页面描述提炼）
      const CAR_TYPES = ['违规收集', '脱敏不充分', '明文传输', '违法测绘']
      const CLOUD_TYPES = [
        '违规收集',
        '访问控制',
        '传输安全',
        '日志完整性',
        '数据泄露',
        '信息泄露',
        'API安全',
        '算法安全',
        '合规风险',
        '销毁不彻底',
      ]

      function randomVIN() {
        const letters = 'ABCDEFGHJKLMNPRstuvwxyz0123456789'
        let s = ''
        for (let i = 0; i < 17; i++) s += letters[Math.floor(Math.random() * letters.length)]
        return s.toUpperCase()
      }
      function randomEnterprise() {
        const ents = [
          '上汽集团股份有限公司',
          '东风汽车集团有限公司',
          '中国第一汽车集团有限公司',
          '北京汽车集团有限公司',
          '广州汽车集团股份有限公司',
          '长安汽车股份有限公司',
          '吉利汽车控股有限公司',
          '长城汽车股份有限公司',
          '比亚迪股份有限公司',
          '奇瑞汽车股份有限公司',
          '江淮汽车集团股份有限公司',
          '海马汽车集团股份有限公司',
          '广汽集团股份有限公司',
          '一汽解放汽车有限公司',
          '东风商用车有限公司',
          '北京奔驰汽车有限公司',
          '上汽大众汽车有限公司',
          '上海通用汽车有限公司',
          '一汽-大众汽车有限公司',
          '神龙汽车有限公司',
          '华晨宝马汽车有限公司',
          '北京现代汽车有限公司',
          '东风日产乘用车公司',
          '广州丰田汽车有限公司',
          '一汽丰田汽车有限公司',
        ]
        return randPick(ents)
      }

      function genAlertsCar(period = 'day') {
        const now = new Date()
        const count = period === 'day' ? 24 : period === 'month' ? 36 : 42
        const rows = []
        for (let i = 0; i < count; i++) {
          const level = randPick(LEVELS)
          const stage = randPick(CAR_STAGES)
          const desc = randPick(SAMPLE_DESC_CAR)
          const rtype = randPick(CAR_TYPES)
          const dt = new Date(now)
          if (period === 'day') dt.setHours(now.getHours() - Math.floor(Math.random() * 12))
          else if (period === 'month') dt.setDate(now.getDate() - Math.floor(Math.random() * 20))
          else dt.setMonth(now.getMonth() - Math.floor(Math.random() * 6))
          rows.push([
            String(i + 1),
            randomVIN(),
            level,
            stage,
            `<span class="line-clamp-1" title="${desc}">${desc}</span>`,
            rtype,
            formatTime(dt),
          ])
        }
        return rows
      }
      function genAlertsCloud(period = 'day') {
        const now = new Date()
        const count = period === 'day' ? 24 : period === 'month' ? 36 : 42
        const rows = []
        for (let i = 0; i < count; i++) {
          const level = randPick(LEVELS)
          const op = randPick(CLOUD_OPS)
          const ent = randomEnterprise()
          const desc = randPick(SAMPLE_DESC_CLOUD)
          const rtype = randPick(CLOUD_TYPES)
          const dt = new Date(now)
          if (period === 'day') dt.setHours(now.getHours() - Math.floor(Math.random() * 12))
          else if (period === 'month') dt.setDate(now.getDate() - Math.floor(Math.random() * 20))
          else dt.setMonth(now.getMonth() - Math.floor(Math.random() * 6))
          rows.push([
            String(i + 1),
            ent,
            level,
            op,
            `<span class="line-clamp-1" title="${desc}">${desc}</span>`,
            rtype,
            formatTime(dt),
          ])
        }
        return rows
      }

      function setAlertTable(which, period) {
        const id = which === 'car' ? 'tbl-car' : 'tbl-cloud'
        const data = which === 'car' ? genAlertsCar(period) : genAlertsCloud(period)
        Dashboard.setTable(id, data)
        // 调整展示区高度，保证始终显示 5 条
        ensureVisibleRows(which)
        // 重置并启动滚动
        resetTicker(which)
        startTicker(which)
      }

      function resetTicker(which) {
        const panel = document.getElementById(which === 'car' ? 'alerts-car' : 'alerts-cloud')
        const viewport = panel?.querySelector('.table-viewport.scroll')
        const tb = panel?.querySelector('tbody')
        if (!panel || !viewport || !tb) return
        // 清理重复
        const clones = panel.querySelectorAll('tbody.__dup')
        clones.forEach((c) => c.parentElement?.removeChild(c))
        viewport.scrollTop = 0
      }

      function ensureVisibleRows(which) {
        // 改为显示 10 条
        const panel = document.getElementById(which === 'car' ? 'alerts-car' : 'alerts-cloud')
        const viewport = panel?.querySelector('.table-viewport.scroll')
        const thead = panel?.querySelector('thead')
        const row = panel?.querySelector('tbody tr')
        if (!panel || !viewport || !thead) return
        const rh = row ? row.getBoundingClientRect().height : 30
        const hh = thead.getBoundingClientRect().height || 24
        viewport.style.height = Math.round(rh * 10 + hh + 8) + 'px'
      }

      function startTicker(which) {
        const panel = document.getElementById(which === 'car' ? 'alerts-car' : 'alerts-cloud')
        const viewport = panel?.querySelector('.table-viewport.scroll')
        const tb = panel?.querySelector('tbody')
        if (!panel || !viewport || !tb) return
        // 复制一份内容进行无缝滚动
        const dup = tb.cloneNode(true)
        dup.classList.add('__dup')
        tb.parentElement?.appendChild(dup)
        let paused = false
        let raf = 0
        let last = 0
        const baseSpeed = 12 // px 每秒（更慢的上升）

        const run = () => {
          // 初始化位置，确保产生滚动
          viewport.scrollTop = 1
          const half = tb.scrollHeight || 1
          const tick = (ts) => {
            if (!last) last = ts
            const dt = ts - last
            last = ts
            if (!paused) {
              viewport.scrollTop += (baseSpeed * dt) / 1000
              if (viewport.scrollTop >= half) viewport.scrollTop = 0
            }
            raf = requestAnimationFrame(tick)
          }
          raf = requestAnimationFrame(tick)
        }

        // 悬停暂停
        const onEnter = () => (paused = true)
        const onLeave = () => (paused = false)
        viewport.addEventListener('mouseenter', onEnter)
        viewport.addEventListener('mouseleave', onLeave)
        // 行点击跳转
        const tbody = panel.querySelector('tbody')
        const onClick = (e) => {
          const tr = e.target.closest('tr')
          if (!tr) return
          if (which === 'car') window.parent?.location && (window.parent.location.href = '/gov/monitor/risk/vehicle')
          else window.parent?.location && (window.parent.location.href = '/gov/monitor/risk/cloud')
        }
        panel.addEventListener('click', onClick)

        // 若已存在旧 ticker，先清理
        const old = ALERT_TICKERS.get(which)
        if (old) old.cleanup?.()
        // 延迟启动，等待布局/高度计算稳定
        setTimeout(run, 50)
        ALERT_TICKERS.set(which, {
          cleanup() {
            cancelAnimationFrame(raf)
            viewport.removeEventListener('mouseenter', onEnter)
            viewport.removeEventListener('mouseleave', onLeave)
            panel.removeEventListener('click', onClick)
          },
        })
      }

      function initAlertTables() {
        // 初始渲染
        setAlertTable('car', ALERT_STATE.car.period)
        setAlertTable('cloud', ALERT_STATE.cloud.period)
        // 窗口变化时重新计算可见行高
        window.addEventListener('resize', () => {
          ensureVisibleRows('car')
          ensureVisibleRows('cloud')
        })
        // 时间筛选按钮
        const scopes = [
          { scope: 'alerts-car', which: 'car' },
          { scope: 'alerts-cloud', which: 'cloud' },
        ]
        scopes.forEach(({ scope, which }) => {
          const seg = document.querySelector(`.seg.time[data-scope="${scope}"]`)
          seg?.addEventListener('click', (e) => {
            if (e.target.tagName !== 'BUTTON') return
            ;[...seg.querySelectorAll('button')].forEach((b) => b.classList.remove('active'))
            e.target.classList.add('active')
            const period = e.target.getAttribute('data-range') || 'day'
            ALERT_STATE[which].period = period
            setAlertTable(which, period)
          })
        })
      }

      // 接收父窗 postMessage 以更新数据
      // 初始化 Lucide 图标（增加兜底与错误日志）
      try {
        window.renderLucideFallback = function () {
          const ICONS = {
            'shield-alert': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2l7 4v6c0 5-3.5 9-7 10-3.5-1-7-5-7-10V6l7-4z"/><path d="M12 8v5"/><path d="M12 16h.01"/></svg>`,
            'car-front': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="5" y="11" width="14" height="6" rx="2"/><circle cx="8" cy="18" r="1"/><circle cx="16" cy="18" r="1"/><path d="M7 11a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3"/></svg>`,
            'cloud-lightning': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 18H18a4 4 0 0 0 0-8 5 5 0 0 0-9-1 4 4 0 0 0-2 9z"/><path d="M13 11l-2 4h3l-2 4"/></svg>`,
            'alert-triangle': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3l9 16H3l9-16z"/><path d="M12 9v5"/><path d="M12 18h.01"/></svg>`,
            'zap': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 2L3 14h7l-1 8 10-12h-7z"/></svg>`,
            'check-circle-2': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M9 12l2 2 4-4"/></svg>`,
            'bar-chart-3': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 20V6"/><path d="M10 20V4"/><path d="M16 20v-8"/><path d="M22 20H2"/></svg>`,
            'pie-chart': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 12V3a9 9 0 1 1-9 9h9z"/></svg>`,
            'line-chart': `<svg class="lucide" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 17l6-6 4 4 8-8"/></svg>`
          }
          document.querySelectorAll('i[data-lucide]').forEach(el => {
            const name = el.getAttribute('data-lucide') || ''
            const svg = ICONS[name]
            if (svg) {
              const span = document.createElement('span')
              span.innerHTML = svg
              const svgEl = span.firstElementChild
              if (svgEl) {
                svgEl.setAttribute('width', '20')
                svgEl.setAttribute('height', '20')
                el.replaceWith(svgEl)
              }
            }
          })
        }

        if (window.lucide && lucide.createIcons) {
          lucide.createIcons()
        } else {
          console.warn('[icons] lucide 未加载或无 createIcons，可尝试延迟初始化')
          setTimeout(() => {
            if (window.lucide && lucide.createIcons) {
              lucide.createIcons()
            } else {
              // 仍不可用则使用内置 SVG 进行降级渲染
              window.renderLucideFallback && window.renderLucideFallback()
            }
          }, 300)
        }
      } catch (err) {
        console.error('[icons] lucide 初始化失败', err)
      }

      // 页面完全加载后再做一次保险检查
      window.addEventListener('load', () => {
        setTimeout(() => {
          if (!document.querySelector('svg.lucide')) {
            window.renderLucideFallback && window.renderLucideFallback()
          }
        }, 600)
      })


      // 过滤未处理的Promise拒绝
      window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && typeof event.reason === 'string' && (
          event.reason.includes('MetaMask') ||
          event.reason.includes('Failed to connect')
        )) {
          event.preventDefault()
          console.warn('MetaMask connection rejected - continuing without wallet features')
          return false
        }
      })


      // 背景地图：使用 AMap 实例作为全局铺底
      ;(function initAmapBackground() {
        function loadScript(src) {
          return new Promise((resolve, reject) => {
            const s = document.createElement('script')
            s.src = src
            s.async = true
            s.onload = resolve
            s.onerror = reject
            document.head.appendChild(s)
          })
        }




        // 监听来自父窗口的消息，获取API密钥
        let amapKey = ''
        window.addEventListener('message', (event) => {
          console.log('🔍 [Iframe Debug] 收到消息:', event.data, '来源:', event.origin)

          // 检查消息来源是否安全
          if (event.origin !== window.location.origin) {
            console.warn('⚠️ [Iframe Debug] 收到来自不安全来源的消息，忽略')
            return
          }

          if (event.data && event.data.type === 'amapConfig') {
            amapKey = event.data.key || ''
            console.log('🔑 [Iframe Debug] 收到API密钥:', amapKey ? '有效' : '空值')

            if (amapKey) {
              console.log('🚀 [Iframe Debug] 开始初始化AMap...')
              bootstrap()
            } else {
              console.error('❌ [Iframe Debug] 未收到有效的AMap API密钥')
            }
          }
        })

        // 向父窗口请求API密钥
        if (window.parent && window.parent !== window) {
          try {
            window.parent.postMessage({ type: 'requestAmapKey' }, '*')
          } catch (error) {
            console.error('向父窗口请求API密钥失败:', error.message)
          }
        }

        async function bootstrap() {
          console.log('🔄 [Bootstrap] 开始初始化，API Key状态:', amapKey ? '已设置' : '未设置')

          if (!amapKey) {
            console.warn('⏳ [Bootstrap] 等待 AMap API Key...')
            return
          }

          console.log('📡 [Bootstrap] 正在加载AMap SDK，API Key:', amapKey.substring(0, 8) + '...')

          try {
            // v2.0 API，按需加载插件：比例尺、控件条、行政区查询、图层、行政区图层
            const mapScriptUrl = `https://webapi.amap.com/maps?v=2.0&key=${encodeURIComponent(amapKey)}&plugin=AMap.Scale,AMap.ControlBar,AMap.DistrictSearch,AMap.DistrictLayer,AMap.HeatMap,AMap.Buildings,AMap.TileLayer`
            console.log('🌐 [Bootstrap] 请求URL:', mapScriptUrl)
            await loadScript(mapScriptUrl)

            const container = document.getElementById('amap-bg')
            if (!container || !window.AMap) throw new Error('AMap 容器或对象不可用')

            const map = new AMap.Map('amap-bg', {
              viewMode: '3D',
              zoom: 9, // 进一步缩小比例，让天津区域居中显示
              center: [117.200983, 39.084158], // 天津
              mapStyle: 'amap://styles/darkblue', // 深色主题（大屏常态）
              dragEnable: true,
              scrollWheel: true,
              doubleClickZoom: true,
              zooms: [3, 20],
              pitchEnable: false, // 关闭倾斜
              rotateEnable: false, // 关闭旋转
              pitch: 0,
              rotation: 0,
              showLabel: true, // 启用内置地名标注
            })


            // 添加卫星图层和路网图层
            try {
              if (AMap.TileLayer && AMap.TileLayer.Satellite && AMap.TileLayer.RoadNet) {
                const satelliteLayer = new AMap.TileLayer.Satellite({ zIndex: 5 })
                const roadNetLayer = new AMap.TileLayer.RoadNet({ zIndex: 10 })
                
                // 存储图层引用以便控制
                window.amapLayers = window.amapLayers || {}
                window.amapLayers.satellite = satelliteLayer
                window.amapLayers.roadnet = roadNetLayer
                
                // 默认显示卫星和路网图层
                map.add(satelliteLayer)
                map.add(roadNetLayer)
                roadNetLayer.setOpacity(0.8)
              }
            } catch (e) {
              console.warn('添加卫星和路网图层失败:', e.message)
            }

            // 定制地图显示要素：显示背景、道路、建筑和标注点
            try {
              map.setFeatures(['bg', 'road', 'building', 'point'])
            } catch (e) {
              console.warn('设置地图要素失败:', e.message)
            }

            // 添加图层控制功能
            initLayerControls(map)
            
            // 添加风险信息点标记
            addRiskMarkers(map)
            
            // 添加风险热力图
            addRiskHeatmap(map)
            
            // 添加地理围栏
            console.log('🔄 [初始化] 开始添加地理围栏')
            addGeofencing(map)
            console.log('✅ [初始化] 地理围栏添加完成')
            
            // 添加天津市行政区域边界
            console.log('🔄 [初始化] 开始添加天津市边界')
            addTianjinBoundary(map)
            // 注意：天津市边界查询是异步的，完成信息会在回调中输出
            
            // 添加天津市遮罩
            console.log('🔄 [初始化] 开始添加天津市遮罩')
            addTianjinMask(map)
            
            // 添加天津市区县边界
            console.log('🔄 [初始化] 开始添加天津市区县边界')
            addTianjinDistricts(map)
          } catch (err) {
            console.error('AMap 背景初始化失败:', err)
          }
        }
      })()
      // 初始化 ECharts 饼图（环状）
      function initDonuts() {
        const typeEl = document.getElementById('chart-veh-type')
        const brandEl = document.getElementById('chart-veh-brand')
        const entEl = document.getElementById('chart-ent-pie')

        // 左上 - 两个环形图分别渲染
        let typeChart, brandChart
        if (window.echarts && typeEl) {
          typeChart = initEchart(typeEl, makeDonutOption('车辆类型占比', mockVehPie()[1], undefined, { showLabelLine: true }))
        }
        if (window.echarts && brandEl) {
          brandChart = initEchart(brandEl, makeDonutOption('车辆品牌占比', mockVehPie()[0], undefined, { showLabelLine: true }))
        }

        // 右上（企业）
        if (entEl && window.echarts) {
          const entChart = initEchart(entEl, makeDonutOption('企业类型占比', mockEntPie(), undefined, { showLabelLine: true }))
        }

        // 轮播逻辑（横向循环）
        const carousel = document.getElementById('veh-carousel')
        const track = carousel?.querySelector('.carousel-track')
        const dots = carousel?.querySelectorAll('.carousel-indicators .dot')
        const total = track ? track.children.length : 2
        let index = 0
        function go(i) {
          if (!track || !dots) return
          index = ((i % total) + total) % total
          const step = 100 / total
          track.style.transform = `translateX(-${index * step}%)`
          dots.forEach((d, di) => d.classList.toggle('active', di === index))
          // 延迟触发 resize，确保 ECharts 重新计算尺寸
          setTimeout(() => {
            typeChart?.resize()
            brandChart?.resize()
          }, 320)
        }
        // 自动轮播（可暂停/恢复）
        let autoTimer = null
        function stopAuto() {
          if (autoTimer) {
            clearInterval(autoTimer)
            autoTimer = null
          }
        }
        function startAuto() {
          stopAuto()
          autoTimer = setInterval(() => go((index + 1) % total), 5000)
        }
        // 初始定位到第一个面板，消除浏览器计算差异
        go(0)
        startAuto()

        // 悬停暂停、移出恢复
        carousel?.addEventListener('mouseenter', stopAuto)
        carousel?.addEventListener('mouseleave', startAuto)

        // 指示点可点击
        if (dots && dots.length) {
          Array.from(dots).forEach((d, di) => {
            d.addEventListener('click', (ev) => {
              ev.preventDefault()
              ev.stopPropagation()
              go(di)
              stopAuto()
              startAuto()
            })
          })
        }

        // 窗口改变时，保持当前面板尺寸正确
        // 单独的 window.resize 监听由 initEchart 的 ResizeObserver 兜底，无需重复注册
      }
      function getSeaPalette() {
        const css = getComputedStyle(document.documentElement)
        const val = (name, fb) => (css.getPropertyValue(name) || fb).trim()
        const main = [
          val('--sea-1', '#2CE7FF'),
          val('--sea-2', '#67B7FF'),
          val('--sea-3', '#3D91E6'),
          val('--sea-4', '#1E6FBF'),
          val('--sea-5', '#0FA9D7'),
          val('--sea-6', '#6EE7B7'),
        ]
        const inner = [
          val('--sea-1-d', '#0FA9D7'),
          val('--sea-2-d', '#3D91E6'),
          val('--sea-3-d', '#2B6FBF'),
          val('--sea-4-d', '#184F8E'),
          val('--sea-5-d', '#0B7FA5'),
          val('--sea-6-d', '#4CCF9E'),
        ]
        return { main, inner }
      }
      function getEventTypeColorMap() {
        return {
          '数据泄露': '#1f3b61',
          '系统故障': '#112e5e',
          '访问异常': '#51A7FF',
          '传输异常': '#3d91e6',
          '违规处理': '#657597',
        }
      }
      function makeDonutOption(title, data, center = ['50%', '60%'], opts = {}) {
        const ringColors = ['#5fd9aa', '#6097fa', '#60d3da', '#657597']
        const pieColors = ['#5fd9aa', '#6097fa', '#60d3da', '#657597']
        const nameMap = opts.nameColorMap || opts.colorsByName
        const pieData = (data || []).map((d, i) => ({
          ...d,
          itemStyle: {
            color: (nameMap && nameMap[d.name]) ? nameMap[d.name] : pieColors[i % pieColors.length],
            borderRadius: 8,
            // 使用深色半透明描边模拟分段环与断点，贴近参考截图
            borderWidth: 2,
            borderColor: 'rgba(11,28,46,.65)'
          },
        }))
        return {
          backgroundColor: 'transparent',
          color: pieColors,
          title: {
            text: title,
            left: 'center',
            top: 0,
            textStyle: { color: '#9feaff', fontSize: 12, fontWeight: 500 },
          },
          tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
          legend: { show: false },
          series: [
            {
              name: title,
              type: 'pie',
              // 统一为较厚的环形（贴合参考风格）
              radius: ['46%', '74%'],
              center,
              padAngle: 3,
              minAngle: 3,
              avoidLabelOverlap: true,
              itemStyle: { borderRadius: 8, borderColor: 'transparent', borderWidth: 0 },
              // 标签：当需要引导线时放到外侧，否则保留内侧百分比
              label: opts.showLabelLine ? {
                show: true,
                position: 'outside',
                formatter: '{b} ({d}%)',
                color: '#cbefff',
                fontSize: 12,
              } : {
                show: true,
                position: 'inside',
                formatter: (p) => `${Math.round(p.percent)}%`,
                color: '#0b1c2e',
                fontSize: 12,
              },
              // 引导线样式
              labelLine: opts.showLabelLine ? {
                show: true,
                length: 12,
                length2: 8,
                smooth: 0.2,
              } : { show: false },
              emphasis: { scale: true, scaleSize: 6, itemStyle: { shadowBlur: 8, shadowColor: 'rgba(44,216,255,0.35)' } },
              data: pieData,
              z: 2,
            },
          ],
          animationEasing: 'cubicOut',
          animationDuration: 600,
        }
      }
      function mockVehPie() {
        return [
          [
            { name: '小米', value: 23 },
            { name: '华为', value: 19 },
            { name: '上汽', value: 15 },
            { name: '比亚迪', value: 21 },
            { name: '其他', value: 22 },
          ],
          [
            { name: 'M类', value: 34 },
            { name: 'N类', value: 28 },
            { name: 'O类', value: 14 },
            { name: '无人驾驶装备', value: 24 },
          ],
        ]
      }
      function mockEntPie() {
        return [
          { name: '地图服务商', value: 35 },
          { name: '汽车企业', value: 30 },
          { name: '智驾方案提供商', value: 20 },
          { name: '平台运营方', value: 15 },
        ]
      }

      ;(function waitE() {
        if (window.echarts) initDonuts()
        else setTimeout(waitE, 200)
      })()

      // 初始化处理活动（左右）
      function initProcCharts() {
        const leftStages = ['收集', '存储', '传输']
        const rightStages = ['收集', '存储', '传输', '加工', '提供', '公开', '销毁']
        setupProcChart({
          scope: 'proc-left',
          elId: 'chart-proc-left',
          stages: leftStages,
          title: '处理活动（车端）',
        })
        setupProcChart({
          scope: 'proc-right',
          elId: 'chart-proc-right',
          stages: rightStages,
          title: '处理活动（云端）',
        })
      }
      function setupProcChart({ scope, elId, stages, title }) {
        const el = document.getElementById(elId)
        if (!el || !window.echarts) return
        const chart = initEchart(el)
        let range = 'day'
        function render() {
          const option = makeProcOption(title, stages, range)
          chart.setOption(option, true)
          chart.resize()
        }
        const seg = document.querySelector(`.seg[data-scope="${scope}"]`)
        seg?.addEventListener('click', (e) => {
          if (e.target.tagName === 'BUTTON') {
            ;[...seg.querySelectorAll('button')].forEach((b) => b.classList.remove('active'))
            e.target.classList.add('active')
            range = e.target.getAttribute('data-range') || 'day'
            render()
          }
        })
        render()
      }
      function makeProcOption(title, stages, range) {
        const barStackColors = ['#475570', '#51BD97', '#5585df']
        const x =
          range === 'day'
            ? Array.from({ length: 24 }, (_, i) => (i < 10 ? `0${i}` : String(i)))
            : range === 'month'
              ? Array.from({ length: 30 }, (_, i) => String(i + 1))
              : [
                  '1月',
                  '2月',
                  '3月',
                  '4月',
                  '5月',
                  '6月',
                  '7月',
                  '8月',
                  '9月',
                  '10月',
                  '11月',
                  '12月',
                ]

        function genSeriesData(si, len) {
          const amp = range === 'day' ? 20 : range === 'month' ? 28 : 36
          let v = 40 + si * 10
          const arr = []
          for (let i = 0; i < len; i++) {
            const wave = Math.sin(i / 1.2 + si) + Math.cos(i / 2.1 + si * 0.5)
            const noise = (Math.random() - 0.5) * (amp + si * 6)
            v = Math.max(0, v + wave * (amp * 0.6) + noise)
            arr.push(Math.round(v))
          }
          return arr
        }

        const series = stages.map((s, si) => ({
          name: s,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 3,
          lineStyle: { width: 2 },
          itemStyle: { color: barStackColors[si % barStackColors.length] },
          areaStyle: {
            opacity: 0.18,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: barStackColors[si % barStackColors.length] },
              { offset: 1, color: 'rgba(0,0,0,0)' },
            ]),
          },
          data: genSeriesData(si, x.length),
        }))

        return {
          title: {
            text: title,
            left: 'center',
            top: 0,
            textStyle: { color: '#9feaff', fontSize: 12 },
          },
          backgroundColor: 'transparent',
          tooltip: { trigger: 'axis' },
          legend: { type: 'scroll', orient: 'horizontal', top: 20, left: 'center', itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect', textStyle: { color: '#9ec9ff', fontSize: 11 } },
          grid: { left: 36, right: 12, bottom: 32, top: 56 },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: x,
            axisLabel: { color: '#9ec9ff', hideOverlap: true },
          },
          yAxis: {
            type: 'value',
            axisLabel: { color: '#9ec9ff' },
            splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } },
          },
          series,
        }
      }

      // 新风险统计模块（左右）：时间筛选 + 模式切换（柱/饼/折线）
      function initRiskModules() {
        const VEH_STAGES = ['收集', '存储', '传输']
        const CLOUD_STAGES = ['收集', '存储', '传输', '加工', '提供', '公开', '销毁']
        setupRiskModule({ scope: 'risk-left', elId: 'chart-risk-left', stages: VEH_STAGES })
        setupRiskModule({ scope: 'risk-right', elId: 'chart-risk-right', stages: CLOUD_STAGES })
      }

      // 事件统计：生成按类型的数据（支持不同时间范围）
      function mockEventTypeData(scopeKey, range, year) {
        const vehTypes = ['数据泄露', '访问异常', '传输异常', '系统故障']
        const cloudTypes = ['数据泄露', '违规处理', '访问异常', '系统故障']
        const types = scopeKey === 'left' ? vehTypes : cloudTypes
        const amp = range === 'day' ? 10 : range === 'week' ? 24 : range === 'month' ? 40 : 60
        const base = 15 + Math.random() * 30
        return types.map((t, i) => ({ name: t, value: Math.max(1, Math.round((base + (i + 1) * 4 + (Math.random() - 0.5) * amp))) }))
      }

      // 为柱状图生成多系列堆叠数据：x 为时间维度分类，series 为各事件类型数组
      function mockEventTypeSeries(scopeKey, range, year, types) {
        const x = getTimeCategories(range)
        const series = types.map((t, li) => {
          const amp = range === 'day' ? 8 : range === 'week' ? 14 : range === 'month' ? 20 : 28
          let v = 12 + li * 5
          const arr = []
          for (let i = 0; i < x.length; i++) {
            const wave = Math.sin(i / 1.7 + li) + Math.cos(i / 2.2 + li * 0.6)
            const noise = (Math.random() - 0.5) * (amp + li * 3)
            v = Math.max(0, v + wave * (amp * 0.35) + noise)
            arr.push(Math.round(v))
          }
          return arr
        })
        return { x, series }
      }

      function makeEventBarOption(title, x, types, seriesData) {
        const cmap = getEventTypeColorMap()
        const fallbackColors = ['#51A7FF', '#2de26d', '#9a6bff', '#f4c542', '#ff6161']
        const series = types.map((t, i) => ({
          name: t,
          type: 'bar',
          stack: 'total',
          barMaxWidth: x.length <= 12 ? 16 : x.length <= 24 ? 12 : 8,
          barMinHeight: 1,
          itemStyle: {
            borderRadius: 2,
            color: cmap[t] || fallbackColors[i % fallbackColors.length],
          },
          emphasis: { focus: 'series' },
          data: seriesData[i] || Array.from({ length: x.length }, () => 0),
        }))
        const step = Math.max(1, Math.ceil(x.length / 8))
        return {
          backgroundColor: 'transparent',
          title: { text: title, left: 'center', top: 0, textStyle: { color: '#9feaff', fontSize: 12 } },
          tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
          legend: {
            type: 'scroll', orient: 'horizontal',
            top: 20, left: 'center',
            itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect',
            textStyle: { color: '#9ec9ff', fontSize: 11 },
          },
          grid: { left: 36, right: 12, bottom: 28, top: 56 },

          xAxis: {
            type: 'category',
            data: x,
            axisLabel: {
              color: '#9ec9ff',
              hideOverlap: true,
              rotate: x.length > 12 ? 25 : 0,
              margin: 8,
              interval: (index) => (index % step === 0 ? index : undefined),
            },
            axisTick: { show: false },
          },
          yAxis: {
            type: 'value',
            axisLabel: { color: '#9ec9ff' },
            splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } },
          },
          series,
          barCategoryGap: x.length <= 12 ? '30%' : x.length <= 24 ? '45%' : x.length <= 36 ? '60%' : '70%',
          barGap: '10%',
          animationDurationUpdate: 500,
          animationEasingUpdate: 'cubicOut',
        }
      }

      function getTimeCategories(range) {
        return range === 'day'
          ? Array.from({ length: 24 }, (_, i) => `${i}h`)
          : range === 'week'
          ? ['周一','周二','周三','周四','周五','周六','周日']
          : range === 'month'
          ? Array.from({ length: 30 }, (_, i) => String(i + 1))
          : ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
      }

      function makeEventLineOption(title, types, range) {
        const { main } = getSeaPalette()
        const cmap = getEventTypeColorMap()
        const x = getTimeCategories(range)
        function gen(li, len) {
          const amp = range === 'day' ? 10 : range === 'week' ? 16 : range === 'month' ? 24 : 28
          let v = 20 + li * 6
          const arr = []
          for (let i = 0; i < len; i++) {
            const wave = Math.sin(i / 1.6 + li) + Math.cos(i / 2.1 + li * 0.7)
            const noise = (Math.random() - 0.5) * (amp + li * 3)
            v = Math.max(0, v + wave * (amp * 0.35) + noise)
            arr.push(Math.round(v))
          }
          return arr
        }
        const series = types.map((t, li) => {
          const c = cmap[t] || main[(li + 3) % main.length]
          return ({
            name: t,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 3,
            lineStyle: { width: 2 },
            itemStyle: { color: c },
            areaStyle: {
              opacity: 0.16,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: c },
                { offset: 1, color: 'rgba(0,0,0,0)' },
              ]),
            },
            data: gen(li, x.length),
          })
        })
        return {
          backgroundColor: 'transparent',
          title: { text: title, left: 'center', top: 0, textStyle: { color: '#9feaff', fontSize: 12 } },
          tooltip: { trigger: 'axis' },
          legend: { type: 'plain', top: 18, left: 'center', itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect', textStyle: { color: '#9ec9ff', fontSize: 11 } },
          grid: { left: 36, right: 12, bottom: 30, top: 36 },
          xAxis: { type: 'category', data: x, axisLabel: { color: '#9ec9ff', hideOverlap: true } },
          yAxis: { type: 'value', axisLabel: { color: '#9ec9ff' }, splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } } },
          series,
        }
      }

      function mockRiskData(stages, levels, range, year) {
        // 调整数据结构：按风险等级分组，横轴为数据处理阶段
        const amp = range === 'day' ? 12 : range === 'week' ? 28 : range === 'month' ? 55 : 90
        const phase = Math.random() * Math.PI * 2
        const freq = 0.6 + Math.random() * 0.8 // 起伏频率
        const levelBase = levels.map(() => 30 + Math.random() * 60) // 每次渲染为各等级随机基线（30~90）
        const stageWeight = stages.map((st) =>
          st === '收集'
            ? 1.3 + Math.random() * 0.6
            : st === '存储'
              ? 1.0 + Math.random() * 0.5
              : 0.8 + Math.random() * 0.4,
        )
        const noise = () => (Math.random() - 0.5) * (amp * (0.6 + Math.random() * 0.6))
        const seasonal = (li) => Math.sin((li + 1) * freq + phase) * (amp * 0.4)
        const yb = (((year || new Date().getFullYear()) % 7) - 3) * 2 // 轻微年度偏移

        return levels.map((lv, li) =>
          stages.map((s, si) => {
            const base = levelBase[li] * stageWeight[si]
            const val = base + seasonal(li) + noise() + yb + Math.random() * (amp * 0.2)
            return { stage: s, level: lv, value: Math.max(1, Math.round(val)) }
          }),
        )
      }

      function makeRiskBarOption(title, stages, levels, data) {
        const levelColorMap = { '高': '#8b5a3c', '中': '#a68b5c', '低': '#5a7d5a' }
        // 调整：系列按风险等级，类目为数据处理阶段
        const series = levels.map((lv, li) => ({
          name: lv,
          type: 'bar',
          stack: 'total',
          barWidth: 28,
          itemStyle: { color: levelColorMap[lv] || ['#ff6161', '#f4c542', '#2de26d'][li % 3] },
          data: stages.map((st, si) => data[li][si].value),
        }))
        return {
          backgroundColor: 'transparent',
          title: {
            text: title,
            left: 'center',
            top: 0,
            textStyle: { color: '#9feaff', fontSize: 12 },
          },
          tooltip: { trigger: 'axis' },
          legend: { type: 'scroll', orient: 'horizontal', top: 20, left: 'center', itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect', textStyle: { color: '#9ec9ff', fontSize: 11 } },
          grid: { left: 36, right: 12, bottom: 38, top: 56 },
          xAxis: {
            type: 'category',
            data: stages, // 横轴改为数据处理阶段
            axisLabel: { color: '#9ec9ff', hideOverlap: true },
          },
          yAxis: {
            type: 'value',
            axisLabel: { color: '#9ec9ff', hideOverlap: true },
            splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } },
          },
          series,
        }
      }

      function makeRiskPieOption(title, stages, levels, data) {
        const sums = stages.map((st, si) => levels.reduce((acc, _, li) => acc + data[si][li].value, 0))
        const pieColors = ['#5fd9aa', '#6097fa', '#60d3da', '#657597']
        // 统一与 makeDonutOption 相同的描边与配色规则
        const pieData = stages.map((st, i) => ({
          name: st,
          value: sums[i],
          itemStyle: {
            color: pieColors[i % pieColors.length],
            borderRadius: 8,
            borderWidth: 2,
            borderColor: 'rgba(11,28,46,.65)'
          },
        }))
        return {
          backgroundColor: 'transparent',
          title: { text: title, left: 'center', top: 0, textStyle: { color: '#9feaff', fontSize: 12, fontWeight: 500 } },
          tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
          legend: {
            type: 'scroll', orient: 'horizontal', top: 22, left: 'center',
            itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect',
            textStyle: { color: '#9ec9ff', fontSize: 11 },
          },
          series: [
            {
              name: title,
              type: 'pie',
              // 统一为较厚的环形（贴合参考风格）
              radius: ['46%', '74%'],
              center: ['50%', '60%'],
              padAngle: 3,
              minAngle: 3,
              avoidLabelOverlap: true,
              itemStyle: { borderRadius: 8, borderColor: 'transparent', borderWidth: 0 },
              // 标签统一为环内仅显示百分比
              label: { show: true, position: 'inside', formatter: (p) => `${Math.round(p.percent)}%`, color: '#0b1c2e', fontSize: 12 },
              // 隐藏引导线，保持干净
              labelLine: { show: false },
              emphasis: { scale: true, scaleSize: 6, itemStyle: { shadowBlur: 8, shadowColor: 'rgba(44,216,255,0.35)' } },
              data: pieData,
              z: 2,
            },
          ],
        }
      }

      function makeRiskLineOption(title, stages, levels, data, range) {
        const { main } = getSeaPalette()
        const x =
          range === 'day'
            ? Array.from({ length: 24 }, (_, i) => `${i}h`)
            : range === 'week'
              ? ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
              : range === 'month'
                ? Array.from({ length: 30 }, (_, i) => String(i + 1))
                : [
                    '1月',
                    '2月',
                    '3月',
                    '4月',
                    '5月',
                    '6月',
                    '7月',
                    '8月',
                    '9月',
                    '10月',
                    '11月',
                    '12月',
                  ]

        function genData(li, len) {
          const ampBase = range === 'day' ? 20 : range === 'week' ? 30 : range === 'month' ? 50 : 60
          let v = 20 + li * 8
          const arr = []
          for (let i = 0; i < len; i++) {
            const wave = Math.sin(i / 1.5) + Math.cos(i / 2.3)
            const noise = (Math.random() - 0.5) * ampBase
            v = Math.max(0, v + wave * ampBase * 0.25 + noise)
            arr.push(Math.round(v))
          }
          return arr
        }

        const series = levels.map((lv, li) => ({
          name: lv,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          itemStyle: { color: main[(li + 2) % main.length] },
          areaStyle: {
            opacity: 0.18,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: main[(li + 2) % main.length] },
              { offset: 1, color: 'rgba(0,0,0,0)' },
            ]),
          },
          data: genData(li, x.length),
        }))
        return {
          backgroundColor: 'transparent',
          title: {
            text: title,
            left: 'center',
            top: 0,
            textStyle: { color: '#9feaff', fontSize: 12 },
          },
          tooltip: { trigger: 'axis' },
          legend: { type: 'scroll', orient: 'horizontal', top: 20, left: 'center', itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect', textStyle: { color: '#9ec9ff', fontSize: 11 } },
          grid: { left: 36, right: 12, bottom: 38, top: 56 },
          xAxis: { type: 'category', data: x, axisLabel: { color: '#9ec9ff', hideOverlap: true } },
          yAxis: {
            type: 'value',
            axisLabel: { color: '#9ec9ff', hideOverlap: true },
            splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } },
          },
          series,
        }
      }

      function setupRiskModule({ scope, elId, stages }) {
        const levels = ['高', '中', '低']
        const el = document.getElementById(elId)
        if (!el || !window.echarts) return
        const chart = initEchart(el)
        const evtElId = scope === 'risk-left' ? 'chart-event-left' : 'chart-event-right'
        const evEl = document.getElementById(evtElId)
        const evChart = evEl ? initEchart(evEl) : null

        let range = 'day'
        let mode = 'bar'
        let year = new Date().getFullYear()

        function render() {
          // 风险统计
          const data = mockRiskData(stages, levels, range, year)
          const title = scope === 'risk-left' ? '车端风险统计' : '云端风险统计'
          let option
          if (mode === 'bar') option = makeRiskBarOption(title, stages, levels, data)
          else if (mode === 'pie') {
            option = makeRiskPieOption(title, stages, levels, data)
            try {
              const s = option?.series?.[0]
              if (s) {
                s.radius = ['46%', '74%']
                s.center = ['50%', '62%']
                s.label = {
                  position: 'inside', show: true, fontSize: 12,
                  formatter: (p) => `${Math.round(p.percent)}%`,
                  color: '#0b1c2e',
                }
                s.labelLine = { show: false }
                option.legend = {
                  type: 'scroll', orient: 'horizontal', top: 20, left: 'center',
                  itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect',
                  textStyle: { color: '#9ec9ff', fontSize: 11 },
                }
              }
            } catch (e) { /* noop */ }
          } else option = makeRiskLineOption(title, stages, levels, data, range)
          chart.setOption(option, true)
          chart.resize()

          // 事件统计（按事件类型）
          if (evChart && evEl) {
            const scopeKey = scope === 'risk-left' ? 'left' : 'right'
            const evtTypes = scopeKey === 'left' ? ['数据泄露', '访问异常', '传输异常', '系统故障'] : ['数据泄露', '违规处理', '访问异常', '系统故障']
            const evtTitle = scopeKey === 'left' ? '车端事件类型占比' : '云端事件类型占比'
            let evOption
            if (mode === 'pie') {
                const evtData = mockEventTypeData(scopeKey, range, year)
                evOption = makeDonutOption(evtTitle, evtData, undefined, { nameColorMap: getEventTypeColorMap() })
                try {
                const s = evOption?.series?.[0]
                if (s) {
                  s.radius = ['46%', '74%']
                  s.center = ['50%', '62%']
                  s.label = {
                    position: 'inside', show: true, fontSize: 12,
                    formatter: (p) => `${Math.round(p.percent)}%`,
                    color: '#0b1c2e',
                  }
                  s.labelLine = { show: false }
                  evOption.legend = {
                    type: 'scroll', orient: 'horizontal', top: 20, left: 'center',
                    itemWidth: 10, itemHeight: 10, itemGap: 8, padding: 0, icon: 'rect',
                    textStyle: { color: '#9ec9ff', fontSize: 11 },
                  }
                }
              } catch (e) { /* noop */ }
            } else if (mode === 'bar') {
              const { x, series } = mockEventTypeSeries(scopeKey, range, year, evtTypes)
              evOption = makeEventBarOption(evtTitle, x, evtTypes, series)
            } else {
              evOption = makeEventLineOption(evtTitle, evtTypes, range)
            }
            evChart.setOption(evOption, true)
            evChart.resize()
          }
        }

        const timeSeg = document.querySelector(`#${scope} .header .seg.time`)
        const yearSelect = document.getElementById(`${scope}-year`)
        timeSeg?.addEventListener('click', (e) => {
          if (e.target.tagName === 'BUTTON') {
            ;[...timeSeg.querySelectorAll('button')].forEach((b) => b.classList.remove('active'))
            e.target.classList.add('active')
            range = e.target.getAttribute('data-range') || 'day'
            if (yearSelect) yearSelect.style.display = range === 'year' ? '' : 'none'
            render()
          }
        })
        yearSelect?.addEventListener('change', () => {
          year = Number(yearSelect.value)
          render()
        })

        const modeWrap = document.querySelector(`#${scope} .header .modes`)
        modeWrap?.addEventListener('click', (e) => {
          const btn = e.target.closest('button')
          if (!btn) return
          const m = btn.getAttribute('data-mode')
          if (!m) return
          ;[...modeWrap.querySelectorAll('button')].forEach((b) => b.classList.remove('active'))
          btn.classList.add('active')
          mode = m
          render()
        })

        render()
      }
      const L1 = document.getElementById('chart-risk-left-levels')
      const L2 = document.getElementById('chart-risk-left-events')
      const R1 = document.getElementById('chart-risk-right-levels')
      const R2 = document.getElementById('chart-risk-right-events')
      if (window.echarts) {
        if (L1) {
          const c = initEchart(L1, makeLevelOption('车端风险等级分布'))
        }
        if (L2) {
          const c = initEchart(L2, makeEventOption('车端事件信息统计'))
        }
        if (R1) {
          const c = initEchart(R1, makeLevelOption('云端风险等级分布'))
        }
        if (R2) {
          const c = initEchart(R2, makeEventOption('云端事件信息统计'))
        }
      }
      function makeLevelOption(title) {
        return {
          title: {
            text: title,
            left: 'center',
            top: 0,
            textStyle: { color: '#9feaff', fontSize: 12 },
          },
          tooltip: {},
          grid: { left: 30, right: 12, bottom: 24, top: 28 },
          xAxis: {
            type: 'category',
            data: ['高', '中', '低'],
            axisLabel: { color: '#ff9aa2', formatter: (v) => v },
          },
          yAxis: {
            type: 'value',
            axisLabel: { color: '#9ec9ff' },
            splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } },
          },
          series: [
            {
              type: 'bar',
              data: [35, 52, 78],
              barWidth: 22,
              itemStyle: { color: (p) => ['#ff6161', '#f4c542', '#2de26d'][p.dataIndex] },
            },
          ],
        }
      }
      function makeEventOption(title) {
        const { main } = getSeaPalette()
        return {
          title: {
            text: title,
            left: 'center',
            top: 0,
            textStyle: { color: '#9feaff', fontSize: 12 },
          },
          tooltip: { trigger: 'axis' },
          legend: { top: 18, left: 'center', textStyle: { color: '#9ec9ff' } },
          grid: { left: 36, right: 12, bottom: 18, top: 48 },
          xAxis: {
            type: 'category',
            data: ['收集', '存储', '使用', '传输', '提供', '公开'],
            axisLabel: { color: '#9ec9ff' },
          },
          yAxis: {
            type: 'value',
            axisLabel: { color: '#9ec9ff' },
            splitLine: { lineStyle: { color: 'rgba(26,211,255,.12)' } },
          },
          series: [
            {
              type: 'line',
              name: '事件数',
              data: [18, 22, 12, 28, 9, 5],
              smooth: true,
              itemStyle: { color: main[2] },
            },
            {
              type: 'bar',
              name: '处置完成',
              data: [12, 14, 8, 18, 6, 3],
              barWidth: 16,
              itemStyle: { color: main[4] },
            },
          ],
        }
      }

      // 图层控制功能
      function initLayerControls(map) {
        const layerControl = document.getElementById('map-layer-control')
        if (!layerControl) return
        
        // 卫星图层控制
        const satelliteToggle = layerControl.querySelector('[data-layer="satellite"]')
        if (satelliteToggle && window.amapLayers?.satellite) {
          satelliteToggle.addEventListener('click', () => {
            const isActive = satelliteToggle.classList.toggle('active')
            if (isActive) {
              map.add(window.amapLayers.satellite)
              satelliteToggle.querySelector('.layer-status').textContent = '显示'
            } else {
              map.remove(window.amapLayers.satellite)
              satelliteToggle.querySelector('.layer-status').textContent = '隐藏'
            }
          })
        }
        
        // 路网图层控制
        const roadnetToggle = layerControl.querySelector('[data-layer="roadnet"]')
        if (roadnetToggle && window.amapLayers?.roadnet) {
          roadnetToggle.addEventListener('click', () => {
            const isActive = roadnetToggle.classList.toggle('active')
            if (isActive) {
              map.add(window.amapLayers.roadnet)
              roadnetToggle.querySelector('.layer-status').textContent = '显示'
            } else {
              map.remove(window.amapLayers.roadnet)
              roadnetToggle.querySelector('.layer-status').textContent = '隐藏'
            }
          })
        }
        
        // 3D建筑图层控制
        const buildingsToggle = layerControl.querySelector('[data-layer="buildings"]')
        if (buildingsToggle && AMap.Buildings) {
          buildingsToggle.addEventListener('click', () => {
            const isActive = buildingsToggle.classList.toggle('active')
            if (isActive) {
              map.setFeatures(['bg', 'road', 'building'])
              buildingsToggle.querySelector('.layer-status').textContent = '显示'
            } else {
              map.setFeatures(['bg', 'road'])
              buildingsToggle.querySelector('.layer-status').textContent = '隐藏'
            }
          })
        }

        // 热力图图层控制
        const heatmapToggle = layerControl.querySelector('[data-layer="heatmap"]')
        if (heatmapToggle) {
          heatmapToggle.addEventListener('click', () => {
            const isActive = heatmapToggle.classList.toggle('active')
            if (window.heatmapLayer) {
              if (isActive) {
                window.heatmapLayer.show()
                heatmapToggle.querySelector('.layer-status').textContent = '显示'
              } else {
                window.heatmapLayer.hide()
                heatmapToggle.querySelector('.layer-status').textContent = '隐藏'
              }
            }
          })
        }

        // 敏感区域图层控制
        const sensitiveToggle = layerControl.querySelector('[data-layer="sensitive-areas"]')
        if (sensitiveToggle) {
          sensitiveToggle.addEventListener('click', () => {
            const isActive = sensitiveToggle.classList.toggle('active')
            if (window.geofencingPolygons) {
              window.geofencingPolygons.forEach(polygon => {
                if (isActive) {
                  polygon.show()
                } else {
                  polygon.hide()
                }
              })
              sensitiveToggle.querySelector('.layer-status').textContent = isActive ? '显示' : '隐藏'
            }
          })
        }

        // 风险标记图层控制
        const markersToggle = layerControl.querySelector('[data-layer="risk-markers"]')
        if (markersToggle) {
          markersToggle.addEventListener('click', () => {
            const isActive = markersToggle.classList.toggle('active')
            if (window.riskMarkers) {
              window.riskMarkers.forEach(marker => {
                if (isActive) {
                  marker.show()
                } else {
                  marker.hide()
                }
              })
              markersToggle.querySelector('.layer-status').textContent = isActive ? '显示' : '隐藏'
            }
          })
        }

        // 天津市边界图层控制
        const boundaryToggle = layerControl.querySelector('[data-layer="tianjin-boundary"]')
        if (boundaryToggle) {
          boundaryToggle.addEventListener('click', () => {
            const isActive = boundaryToggle.classList.toggle('active')
            if (window.tianjinBoundary) {
              if (isActive) {
                window.tianjinBoundary.show()
                boundaryToggle.querySelector('.layer-status').textContent = '显示'
              } else {
                window.tianjinBoundary.hide()
                boundaryToggle.querySelector('.layer-status').textContent = '隐藏'
              }
            }
          })
        }

        // 天津市遮罩图层控制
        const maskToggle = layerControl.querySelector('[data-layer="tianjin-mask"]')
        if (maskToggle) {
          maskToggle.addEventListener('click', () => {
            const isActive = maskToggle.classList.toggle('active')
            if (window.tianjinMask) {
              if (isActive) {
                window.tianjinMask.show()
                maskToggle.querySelector('.layer-status').textContent = '显示'
              } else {
                window.tianjinMask.hide()
                maskToggle.querySelector('.layer-status').textContent = '隐藏'
              }
            }
          })
        }

        // 天津市区县边界图层控制
        const districtsToggle = layerControl.querySelector('[data-layer="tianjin-districts"]')
        if (districtsToggle) {
          districtsToggle.addEventListener('click', () => {
            const isActive = districtsToggle.classList.toggle('active')
            if (window.tianjinDistricts && window.tianjinDistricts.length > 0) {
              window.tianjinDistricts.forEach(district => {
                if (isActive) {
                  district.show()
                } else {
                  district.hide()
                }
              })
              districtsToggle.querySelector('.layer-status').textContent = isActive ? '显示' : '隐藏'
            }
          })
        }
      }

      // 添加风险信息点标记（增加圆形背景与阴影）
      function addRiskMarkers(map) {
        if (!AMap.Marker) return
        
        // 风险点数据
        const riskPoints = [
          { lng: 117.20, lat: 39.13, level: '高', name: '武清区风险点', desc: '车辆数据泄露风险', count: 24 },
          { lng: 117.35, lat: 38.95, level: '中', name: '津南区风险点', desc: '数据传输异常', count: 12 },
          { lng: 117.12, lat: 39.22, level: '低', name: '北辰区风险点', desc: '访问控制问题', count: 6 },
          { lng: 117.40, lat: 39.05, level: '高', name: '滨海新区风险点', desc: '敏感数据收集', count: 36 },
          { lng: 117.05, lat: 39.18, level: '中', name: '西青区风险点', desc: '存储策略违规', count: 18 }
        ]
        
        const colorMap = { '高': '#ff6161', '中': '#f4c542', '低': '#2de26d' }
        const iconMap = { '高': '../assets/icons/co-red.svg', '中': '../assets/icons/co-orange.svg', '低': '../assets/icons/co-yellow.svg' }
        
        // 创建风险标记
        riskPoints.forEach((point, index) => {
          const size = point.count > 20 ? 32 : point.count > 10 ? 28 : 24
          const bgSize = size + 10
          
          // 自定义DOM内容：圆形背景 + 半透明泡泡 + SVG图标
          const content = document.createElement('div')
          content.style.cssText = `position: relative; width: ${bgSize}px; height: ${bgSize}px; transform: translate(-50%, -50%);`
          
          const circle = document.createElement('div')
          circle.style.cssText = `position: absolute; left: 50%; top: 50%; width: ${bgSize}px; height: ${bgSize}px; transform: translate(-50%, -50%); border-radius: 50%; background: radial-gradient(closest-side, rgba(0,0,0,0.35), rgba(0,0,0,0.1) 60%, transparent 70%); box-shadow: 0 4px 12px rgba(0,0,0,0.4);`
          
          const bubble = document.createElement('div')
          bubble.style.cssText = `position: absolute; left: 50%; top: 50%; width: ${size}px; height: ${size}px; transform: translate(-50%, -50%); display: flex; align-items: center; justify-content: center; border-radius: 50%; background: rgba(14,31,52,0.8); border: 2px solid ${colorMap[point.level]}; box-shadow: 0 8px 20px rgba(0,0,0,0.45), 0 0 10px ${colorMap[point.level]}33;`
          
          const img = document.createElement('img')
          img.src = iconMap[point.level]
          img.width = size - 6
          img.height = size - 6
          img.alt = `${point.level}风险`
          img.style.filter = 'drop-shadow(0 2px 4px rgba(0,0,0,0.35))'
          
          bubble.appendChild(img)
          content.appendChild(circle)
          content.appendChild(bubble)
          
          const marker = new AMap.Marker({
            position: [point.lng, point.lat],
            content,
            offset: new AMap.Pixel(0, 0),
            zIndex: 100 + index
          })
          
          marker.on('click', (e) => { showRiskInfoCard(point, e) })
          map.add(marker)
          
          window.riskMarkers = window.riskMarkers || []
          window.riskMarkers.push(marker)
        })
      }

      // 添加风险热力图
      function addRiskHeatmap(map) {
        if (!AMap.HeatMap) {
          console.warn('AMap HeatMap plugin not available')
          return
        }
        
        // 生成风险热力图数据
        const heatmapData = generateHeatmapData()
        
        // 创建热力图实例（弱化视觉强度）
        const heatmap = new AMap.HeatMap(map, {
          radius: 28, // 略增大半径，让热区更柔和
          opacity: [0, 0.35], // 降低整体透明度上限，避免抢视觉
          gradient: { // 使用偏冷的渐变，降低饱和度
            0.4: 'rgb(64,115,255)',
            0.65: 'rgb(44,209,158)',
            0.8: 'rgb(255,214,102)',
            1.0: 'rgb(255,102,102)'
          },
          zIndex: 50
        })
        
        // 设置热力图数据（提高max以拉低强度）
        heatmap.setDataSet({
          data: heatmapData,
          max: 180
        })
        
        // 存储热力图引用
        window.heatmapLayer = heatmap
        
        console.log('✅ 风险热力图加载完成，数据点:', heatmapData.length)
      }

      // 生成热力图数据
      function generateHeatmapData() {
        // 天津市主要区域坐标范围
        const tianjinBounds = {
          minLng: 116.7, maxLng: 118.0,
          minLat: 38.5, maxLat: 40.2
        }
        
        const heatmapData = []
        
        // 生成高风险区域数据点
        const highRiskAreas = [
          { center: [117.20, 39.13], radius: 0.08, count: 80 }, // 武清区
          { center: [117.40, 39.05], radius: 0.12, count: 120 }, // 滨海新区
          { center: [117.18, 39.15], radius: 0.06, count: 60 }  // 北辰区
        ]
        
        // 生成中风险区域数据点
        const mediumRiskAreas = [
          { center: [117.35, 38.95], radius: 0.07, count: 40 }, // 津南区
          { center: [117.05, 39.18], radius: 0.05, count: 35 }, // 西青区
          { center: [117.12, 39.08], radius: 0.04, count: 30 }  // 东丽区
        ]
        
        // 生成低风险区域数据点
        const lowRiskAreas = [
          { center: [117.45, 38.85], radius: 0.08, count: 20 }, // 静海区
          { center: [117.80, 39.30], radius: 0.06, count: 15 }, // 宁河区
          { center: [116.90, 39.40], radius: 0.05, count: 10 }  // 宝坻区
        ]
        
        // 生成高风险区域数据
        highRiskAreas.forEach(area => {
          for (let i = 0; i < area.count; i++) {
            const lng = area.center[0] + (Math.random() - 0.5) * area.radius
            const lat = area.center[1] + (Math.random() - 0.5) * area.radius
            const count = Math.floor(Math.random() * 30) + 70 // 70-100
            heatmapData.push({
              lng: lng,
              lat: lat,
              count: count
            })
          }
        })
        
        // 生成中风险区域数据
        mediumRiskAreas.forEach(area => {
          for (let i = 0; i < area.count; i++) {
            const lng = area.center[0] + (Math.random() - 0.5) * area.radius
            const lat = area.center[1] + (Math.random() - 0.5) * area.radius
            const count = Math.floor(Math.random() * 30) + 40 // 40-70
            heatmapData.push({
              lng: lng,
              lat: lat,
              count: count
            })
          }
        })
        
        // 生成低风险区域数据
        lowRiskAreas.forEach(area => {
          for (let i = 0; i < area.count; i++) {
            const lng = area.center[0] + (Math.random() - 0.5) * area.radius
            const lat = area.center[1] + (Math.random() - 0.5) * area.radius
            const count = Math.floor(Math.random() * 20) + 10 // 10-30
            heatmapData.push({
              lng: lng,
              lat: lat,
              count: count
            })
          }
        })
        
        return heatmapData
      }

      // 添加演示地理围栏（敏感区域）图层
      function addGeofencing(map) {
        console.log('🔒 [地理围栏] 开始添加地理围栏')
        if (!AMap.Polygon) {
          console.warn('AMap.Polygon not available')
          return
        }
        console.log('🔒 [地理围栏] AMap.Polygon 可用')
        try {
          const levelStyle = {
            '高': { stroke: '#ff6161' },
            '中': { stroke: '#f4c542' },
            '低': { stroke: '#2de26d' },
          }
          const areas = [
            { name: '滨海新区敏感区', level: '高', path: [[117.55, 39.00], [117.60, 38.98], [117.68, 39.05], [117.62, 39.12], [117.54, 39.08]] },
            { name: '中心城区数据保护区', level: '中', path: [[117.16, 39.10], [117.22, 39.10], [117.24, 39.16], [117.18, 39.18], [117.12, 39.16]] },
            { name: '武清周边缓冲区', level: '低', path: [[117.10, 39.27], [117.18, 39.24], [117.22, 39.28], [117.16, 39.34], [117.08, 39.32]] },
          ]
          const polygons = []
          areas.forEach((a, i) => {
            const style = levelStyle[a.level] || levelStyle['低']
            const polygon = new AMap.Polygon({
              path: a.path,
              fillOpacity: 0, // 完全透明的填充
              strokeColor: style.stroke,
              strokeOpacity: 0.9,
              strokeWeight: 2,
              zIndex: 60 + i,
              bubble: true,
            })
            polygon.on('mouseover', () => map.setDefaultCursor('pointer'))
            polygon.on('mouseout', () => map.setDefaultCursor('grab'))
            polygons.push(polygon)
          })
          map.add(polygons)
          window.geofencingPolygons = polygons
          console.log('✅ [地理围栏] 地理围栏添加成功，共', polygons.length, '个区域')
        } catch (e) {
          console.warn('addGeofencing failed:', e.message)
        }
      }

      // 添加天津市行政区域边界
      function addTianjinBoundary(map) {
        console.log('🏙️ [天津边界] 开始添加天津市行政区域边界')
        
        try {
          // 使用Web服务API查询行政区域边界
          const webServiceKey = '9010de7c7c0c094c6d4bc5ce2e1f82c3' // GSM-webservice密钥
          const apiUrl = `https://restapi.amap.com/v3/config/district?keywords=天津市&subdistrict=0&extensions=all&key=${webServiceKey}`
          
          console.log('🏙️ [天津边界] 使用Web服务API查询，URL:', apiUrl)
          
          fetch(apiUrl)
            .then(response => {
              console.log('🏙️ [天津边界] HTTP响应状态:', response.status)
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
              }
              return response.json()
            })
            .then(data => {
              console.log('🏙️ [天津边界] API响应数据:', data)
              
              if (data.status === '1' && data.districts && data.districts.length > 0) {
                const district = data.districts[0]
                console.log('🏙️ [天津边界] 找到行政区:', district.name)
                
                if (district.polyline) {
                  console.log('🏙️ [天津边界] 获取到边界数据，长度:', district.polyline.length)
                  
                  // 解析边界坐标字符串
                  const boundaryPaths = district.polyline.split('|').map(pathStr => {
                    return pathStr.split(';').map(pointStr => {
                      const [lng, lat] = pointStr.split(',').map(Number)
                      return [lng, lat]
                    })
                  })
                  
                  console.log('🏙️ [天津边界] 解析得到', boundaryPaths.length, '个边界路径')
                  
                  // 创建天津市边界多边形
                  const boundaryPolygon = new AMap.Polygon({
                    path: boundaryPaths,
                    strokeWeight: 4.5, // 恢复为4.5，保持外边界不加粗
                    strokeColor: '#ffffff', // 外边框改为白色
                    strokeOpacity: 1.0, // 增加不透明度，更明显
                    fillOpacity: 0, // 透明填充
                    zIndex: 40, // 提高层级，确保显示在区县边界之上
                    bubble: true
                  })
                  
                  // 添加到地图
                  map.add(boundaryPolygon)
                  
                  // 存储边界引用
                  window.tianjinBoundary = boundaryPolygon
                  
                  console.log('✅ [天津边界] 天津市行政区域边界加载完成')
                } else {
                  console.warn('❌ [天津边界] 未获取到边界坐标数据')
                }
              } else {
                console.warn('❌ [天津边界] API查询失败:', data.info || '未知错误')
              }
            })
            .catch(error => {
              console.error('❌ [天津边界] 网络请求失败:', error.message)
            })
        } catch (e) {
          console.error('❌ [天津边界] addTianjinBoundary 执行失败:', e.message)
          console.error('❌ [天津边界] 错误堆栈:', e.stack)
        }
      }

      // 添加天津市遮罩
      function addTianjinMask(map) {
        console.log('🎭 [天津遮罩] 开始添加天津市遮罩')
        
        try {
          // 使用Web服务API查询天津市边界数据
          const webServiceKey = '9010de7c7c0c094c6d4bc5ce2e1f82c3' // GSM-webservice密钥
          const apiUrl = `https://restapi.amap.com/v3/config/district?keywords=天津市&subdistrict=0&extensions=all&key=${webServiceKey}`
          
          console.log('🎭 [天津遮罩] 使用Web服务API查询边界数据')
          
          fetch(apiUrl)
            .then(response => {
              console.log('🎭 [天津遮罩] HTTP响应状态:', response.status)
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
              }
              return response.json()
            })
            .then(data => {
              console.log('🎭 [天津遮罩] API响应数据获取成功')
              
              if (data.status === '1' && data.districts && data.districts.length > 0) {
                const district = data.districts[0]
                console.log('🎭 [天津遮罩] 找到行政区:', district.name)
                
                if (district.polyline) {
                  console.log('🎭 [天津遮罩] 开始创建遮罩多边形')
                  
                  // 解析边界坐标字符串
                  const boundaryPaths = district.polyline.split('|').map(pathStr => {
                    return pathStr.split(';').map(pointStr => {
                      const [lng, lat] = pointStr.split(',').map(Number)
                      return new AMap.LngLat(lng, lat)
                    })
                  })
                  
                  // 创建外部大矩形（覆盖整个地图）
                  const outer = [
                    new AMap.LngLat(-180, 90),
                    new AMap.LngLat(-180, -90),
                    new AMap.LngLat(180, -90),
                    new AMap.LngLat(180, 90)
                  ]
                  
                  // 构建路径数组：外部矩形 + 天津市边界（作为洞）
                  const pathArray = [outer]
                  pathArray.push(...boundaryPaths)
                  
                  // 创建遮罩多边形
                  const maskPolygon = new AMap.Polygon({
                    path: pathArray,
                    strokeWeight: 0, // 无边框
                    fillColor: '#1a1a2e', // 深蓝灰色
                    fillOpacity: 0.4, // 半透明
                    zIndex: 15, // 在卫星图层(5)和路网图层(10)之上，在边界(20)之下
                    bubble: false // 不响应鼠标事件
                  })
                  
                  // 添加到地图
                  map.add(maskPolygon)
                  
                  // 存储遮罩引用
                  window.tianjinMask = maskPolygon
                  
                  console.log('✅ [天津遮罩] 天津市遮罩创建完成')
                } else {
                  console.warn('❌ [天津遮罩] 未获取到边界坐标数据')
                }
              } else {
                console.warn('❌ [天津遮罩] API查询失败:', data.info || '未知错误')
              }
            })
            .catch(error => {
              console.error('❌ [天津遮罩] 网络请求失败:', error.message)
            })
        } catch (e) {
          console.error('❌ [天津遮罩] addTianjinMask 执行失败:', e.message)
          console.error('❌ [天津遮罩] 错误堆栈:', e.stack)
        }
      }

      // 添加天津市区县边界（顺序请求 + 重试限流 + 精确匹配）
      function addTianjinDistricts(map) {
        console.log('🏘️ [区县边界] 开始添加天津市区县边界')

        try {
          // 天津市具体区县名称列表
          const districts = [
            '和平区', '河东区', '河西区', '南开区', '河北区', '红桥区',
            '东丽区', '西青区', '津南区', '北辰区',
            '武清区', '宝坻区', '宁河区', '静海区', '蓟州区',
            '滨海新区'
          ]

          const webServiceKey = '9010de7c7c0c094c6d4bc5ce2e1f82c3' // GSM-webservice密钥
          const districtPolygons = []

          const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

          async function fetchDistrictWithRetry(districtName, attempt = 1) {
            // 更精确：使用 keywords=区县名 + city=天津市 来限定城市范围
            const apiUrl = `https://restapi.amap.com/v3/config/district?keywords=${encodeURIComponent(districtName)}&city=${encodeURIComponent('天津市')}&subdistrict=0&extensions=all&key=${webServiceKey}`
            console.log(`🏘️ [区县边界] 查询: ${districtName} (第 ${attempt} 次)`)

            try {
              const response = await fetch(apiUrl)
              if (!response.ok) {
                throw new Error(`HTTP ${response.status}`)
              }
              const data = await response.json()

              // 命中限流，进行指数退避重试
              if (data && (data.info === 'CUQPS_HAS_EXCEEDED_THE_LIMIT' || data.infocode === '10006')) {
                if (attempt <= 3) {
                  const backoff = 600 * Math.pow(2, attempt - 1)
                  console.warn(`⚠️ [区县边界] ${districtName} 命中QPS限流，${backoff}ms后重试 (${attempt}/3)`)
                  await delay(backoff)
                  return fetchDistrictWithRetry(districtName, attempt + 1)
                } else {
                  console.error(`❌ [区县边界] ${districtName} 多次重试仍限流，跳过`)
                  return null
                }
              }

              if (data.status === '1' && Array.isArray(data.districts) && data.districts.length > 0) {
                // 选取与名称最匹配的一项
                const matched = data.districts.find(d => (d.name || '').includes(districtName)) || data.districts[0]
                console.log(`🏘️ [区县边界] ${districtName} 查询成功: ${matched.name}`)

                if (matched.polyline) {
                  // 解析区县边界坐标字符串
                  const boundaryPaths = matched.polyline.split('|').map(pathStr => {
                    return pathStr.split(';').map(pointStr => {
                      const [lng, lat] = pointStr.split(',').map(Number)
                      return [lng, lat]
                    })
                  })

                  // 创建区县边界多边形
                  const districtPolygon = new AMap.Polygon({
                    path: boundaryPaths,
                    strokeWeight: 2.5, // 市内行政区边框细线（微调为2.5）
                    strokeColor: '#91d5ff', // 浅蓝色
                    strokeOpacity: 0.9,
                    fillColor: '#0052a3',
                    fillOpacity: 0.0, // 去除填充，突出细线边界
                    zIndex: 25,
                    bubble: true
                  })

                  // 悬停显示区县名
                  districtPolygon.on('mouseover', (e) => {
                    const infoWindow = new AMap.InfoWindow({
                      content: `<div style="padding: 8px; font-size: 12px; color: #333;">${matched.name}</div>`,
                      offset: new AMap.Pixel(0, -10)
                    })
                    infoWindow.open(map, e.lnglat)
                    districtPolygon._infoWindow = infoWindow
                  })

                  districtPolygon.on('mouseout', () => {
                    if (districtPolygon._infoWindow) {
                      districtPolygon._infoWindow.close()
                      districtPolygon._infoWindow = null
                    }
                  })

                  map.add(districtPolygon)
                  districtPolygons.push(districtPolygon)
                  console.log(`✅ [区县边界] ${districtName} 边界多边形创建成功`)
                } else {
                  console.warn(`⚠️ [区县边界] ${districtName} 请求成功但未返回边界数据`)
                }
              } else {
                // info 为 OK 但没有数据时给出友好提示，而非失败
                if (data && data.info === 'OK') {
                  console.warn(`⚠️ [区县边界] ${districtName} 请求成功但未找到匹配的边界数据`)
                } else {
                  console.warn(`❌ [区县边界] ${districtName} 查询失败: ${data ? (data.info || '未知错误') : '无返回'}`)
                }
              }

              // 请求之间做节流，避免QPS超限
              await delay(300)
              return true
            } catch (err) {
              if (attempt <= 3) {
                const backoff = 600 * Math.pow(2, attempt - 1)
                console.warn(`⚠️ [区县边界] ${districtName} 网络异常，${backoff}ms后重试 (${attempt}/3): ${err.message}`)
                await delay(backoff)
                return fetchDistrictWithRetry(districtName, attempt + 1)
              } else {
                console.error(`❌ [区县边界] ${districtName} 请求失败（达重试上限）: ${err.message}`)
                return null
              }
            }
          }

          // 顺序执行，避免一次性并发导致限流
          ;(async () => {
            for (const name of districts) {
              try {
                await fetchDistrictWithRetry(name)
              } catch (e) {
                console.error(`❌ [区县边界] ${name} 处理异常: ${e.message}`)
              }
            }
            window.tianjinDistricts = districtPolygons
            console.log(`✅ [区县边界] 天津市区县边界查询完成，成功创建 ${districtPolygons.length}/${districts.length} 个区县边界`)
          })()
        } catch (e) {
          console.error('❌ [区县边界] addTianjinDistricts 执行失败:', e.message)
          console.error('❌ [区县边界] 错误堆栈:', e.stack)
        }
      }

      // 创建风险标记内容
      function createRiskMarkerContent(level, count) {
        const colors = {
          '高': '#ff6161',
          '中': '#f4c542', 
          '低': '#2de26d'
        }
        const size = count > 20 ? 32 : count > 10 ? 28 : 24
        
        // 企业大楼建筑风格的SVG图标
        const buildingIcon = `
          <svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="18" height="18" rx="2" stroke="${colors[level]}" stroke-width="2" fill="rgba(255,255,255,0.9)"/>
            <rect x="6" y="6" width="2" height="2" fill="${colors[level]}"/>
            <rect x="10" y="6" width="2" height="2" fill="${colors[level]}"/>
            <rect x="14" y="6" width="2" height="2" fill="${colors[level]}"/>
            <rect x="6" y="10" width="2" height="2" fill="${colors[level]}"/>
            <rect x="10" y="10" width="2" height="2" fill="${colors[level]}"/>
            <rect x="14" y="10" width="2" height="2" fill="${colors[level]}"/>
            <rect x="6" y="14" width="2" height="2" fill="${colors[level]}"/>
            <rect x="10" y="14" width="2" height="2" fill="${colors[level]}"/>
            <rect x="14" y="14" width="2" height="2" fill="${colors[level]}"/>
            <rect x="8" y="17" width="8" height="2" fill="${colors[level]}"/>
          </svg>
        `
        
        return `<div style="
          position: relative;
          width: ${size}px;
          height: ${size}px;
          cursor: pointer;
          filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3));
        ">
          ${buildingIcon}
          <div style="
            position: absolute;
            top: -8px;
            right: -8px;
            width: 18px;
            height: 18px;
            background: ${colors[level]};
            border: 2px solid #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 10px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          ">${count}</div>
        </div>`
      }

      // 显示风险信息卡
      function showRiskInfoCard(point, event) {
        let tooltip = document.getElementById('risk-tooltip')
        if (!tooltip) {
          tooltip = document.createElement('div')
          tooltip.id = 'risk-tooltip'
          tooltip.style.cssText = `
            position: absolute;
            background: rgba(10, 24, 40, 0.95);
            border: 1px solid rgba(26, 211, 255, 0.5);
            border-radius: 8px;
            padding: 12px;
            color: #dff6ff;
            font-size: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            z-index: 1000;
            min-width: 200px;
            pointer-events: none;
            display: none;
          `
          document.body.appendChild(tooltip)
        }
        
        const levelColors = {
          '高': '#ff6161',
          '中': '#f4c542',
          '低': '#2de26d'
        }
        
        tooltip.innerHTML = `
          <div style="margin-bottom: 8px; font-weight: bold; color: #8fe9ff;">${point.name}</div>
          <div style="display: flex; align-items: center; margin-bottom: 6px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${levelColors[point.level]}; margin-right: 6px;"></span>
            <span>风险等级: <span style="color: ${levelColors[point.level]};">${point.level}</span></span>
          </div>
          <div style="margin-bottom: 6px;">风险事件: ${point.count}起</div>
          <div style="color: #9ec9ff; font-size: 11px;">${point.desc}</div>
        `
        
        // 定位信息卡 - 使用事件的像素坐标
        const mapContainer = document.getElementById('amap-bg')
        if (mapContainer && event && event.pixel) {
          const rect = mapContainer.getBoundingClientRect()
          tooltip.style.left = (rect.left + event.pixel.x + 15) + 'px'
          tooltip.style.top = (rect.top + event.pixel.y - 30) + 'px'
        } else {
          // 备用定位方案：使用鼠标位置
          tooltip.style.left = (event.clientX || 100) + 15 + 'px'
          tooltip.style.top = (event.clientY || 100) - 30 + 'px'
        }
        tooltip.style.display = 'block'
        
        // 3秒后自动隐藏
        clearTimeout(window.riskTooltipTimer)
        window.riskTooltipTimer = setTimeout(() => {
          tooltip.style.display = 'none'
        }, 3000)
      }

      // 等 ECharts 可用后统一初始化
      ;(function waitCharts() {
        if (window.echarts) {
          initProcCharts()
          initRiskModules()
          initAlertTables()
        } else setTimeout(waitCharts, 200)
      })()

      window.addEventListener('message', (ev) => {
        const { type, payload } = ev.data || {}

        if (type === 'setKPIs') Dashboard.setKPIs(payload)
        if (type === 'setTable') Dashboard.setTable(payload?.id, payload?.rows)
      })
    </script>
  </body>
</html>
