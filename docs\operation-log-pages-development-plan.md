# P-020 车端操作信息 & P-021 云端操作信息页面开发计划

## 1. 需求分析

### P-020 车端操作信息

- **功能描述**：车端操作日志的记录、查询与分析，按协议字段全量展示
- **核心功能**：
  - 查询条件：注册企业、车辆 VIN、品牌、车型、处理阶段、操作时间范围
  - 列表字段：序号、注册企业、车辆 VIN、品牌、车型、处理阶段、操作时间（及其他协议字段）
  - 导出功能：导出操作信息
- **路由**：/gov/log/vehicle

### P-021 云端操作信息

- **功能描述**：云端操作日志的记录、查询与分析
- **核心功能**：
  - 查询条件：企业名称、企业类型、处理阶段（收集至销毁）、操作时间范围
  - 列表字段：序号、企业名称、企业类型、处理阶段、操作时间（及其他协议字段）
  - 导出功能：导出操作信息
- **路由**：/gov/log/cloud

## 2. 技术架构

### 使用现有组件

基于已有的 RiskVehicle.vue 和 CloudRisk.vue 页面架构，复用以下组件：

- Card、CardContent、CardHeader、CardTitle、CardDescription
- Table 相关组件
- Select、Input、Label、Button
- DatePickerWithRange
- Badge
- 分页组件

### 页面布局

1. 页面标题和描述
2. 查询表单区域（Card 包裹）
3. 数据列表区域（Card 包裹的 Table）
4. 分页控制

## 3. 组件文件结构

### P-020 车端操作信息

```
src/views/government/VehicleOperationLog.vue
```

### P-021 云端操作信息

```
src/views/government/CloudOperationLog.vue
```

## 4. 数据结构设计

### 车端操作日志数据结构

```typescript
interface VehicleOperationLog {
  id: string
  enterprise: string // 注册企业
  enterpriseCode: string // 企业代码
  vin: string // 车辆VIN
  brand: string // 品牌
  model: string // 车型
  stage: '收集' | '存储' | '传输' // 处理阶段
  operationType: string // 操作类型
  operationDetail: string // 操作详情
  operationTime: string // 操作时间
  operator?: string // 操作人
  sourceIP?: string // 来源IP
  dataSize?: string // 数据量
  result: '成功' | '失败' // 操作结果
  errorMsg?: string // 错误信息
  traceId?: string // 追踪ID
}
```

### 云端操作日志数据结构

```typescript
interface CloudOperationLog {
  id: string
  enterprise: string // 企业名称
  enterpriseCode: string // 企业代码
  enterpriseType: string // 企业类型
  stage: '收集' | '存储' | '传输' | '加工' | '提供' | '公开' | '销毁'
  operationType: string // 操作类型
  operationDetail: string // 操作详情
  operationTime: string // 操作时间
  operator?: string // 操作人
  sourceIP?: string // 来源IP
  dataCategory?: string // 数据类别
  dataSize?: string // 数据量
  targetSystem?: string // 目标系统
  result: '成功' | '失败' // 操作结果
  errorMsg?: string // 错误信息
  duration?: string // 耗时
  traceId?: string // 追踪ID
}
```

## 5. 功能实现细节

### 查询功能

- 支持多条件组合查询
- 时间范围选择器使用 DatePickerWithRange 组件
- 查询按钮触发数据过滤
- 重置按钮清空所有筛选条件

### 列表展示

- 使用 Table 组件展示数据
- 支持分页（10/20/50/100 条每页）
- 关键字段使用 Badge 组件美化显示
- 操作结果使用不同颜色区分（成功：绿色，失败：红色）

### 导出功能

- 导出为 CSV 格式
- 包含所有查询结果数据
- 文件命名：车端操作日志\_YYYY-MM-DD.csv / 云端操作日志\_YYYY-MM-DD.csv

### Mock 数据

- 每个页面准备 10-15 条示例数据
- 覆盖不同的操作类型和处理阶段
- 包含成功和失败的案例

## 6. 样式和交互

### 样式要求

- 保持与现有风险管理页面一致的视觉风格
- 使用港铁色系配色方案
- 响应式布局，适配不同屏幕尺寸

### 交互设计

- 查询时显示加载状态（原型可省略）
- 无数据时显示友好提示
- 分页切换平滑
- 导出操作有进度提示（原型可用简单提示）

## 7. 路由配置

需要在 router/index.ts 中更新路由配置：

```typescript
// 在GovMonitorOperation下添加子路由
{
  path: 'operation',
  name: 'GovMonitorOperation',
  redirect: '/gov/monitor/operation/vehicle',
  meta: { title: '操作信息管理' },
  children: [
    {
      path: 'vehicle',
      name: 'GovMonitorOperationVehicle',
      component: () => import('@/views/government/VehicleOperationLog.vue'),
      meta: { title: '车端操作信息' },
    },
    {
      path: 'cloud',
      name: 'GovMonitorOperationCloud',
      component: () => import('@/views/government/CloudOperationLog.vue'),
      meta: { title: '云端操作信息' },
    },
  ],
}
```

## 8. 开发步骤

1. **创建 VehicleOperationLog.vue 组件**

   - 复制 RiskVehicle.vue 作为基础模板
   - 修改页面标题和描述
   - 调整查询表单字段
   - 修改表格列定义
   - 更新数据结构和 Mock 数据
   - 移除不需要的统计图表

2. **创建 CloudOperationLog.vue 组件**

   - 复制 CloudRisk.vue 作为基础模板
   - 修改页面标题和描述
   - 调整查询表单字段（支持七阶段）
   - 修改表格列定义
   - 更新数据结构和 Mock 数据
   - 移除不需要的统计图表

3. **配置路由**

   - 更新 router/index.ts
   - 添加重定向规则

4. **测试验证**
   - 测试页面访问
   - 验证查询功能
   - 检查分页功能
   - 测试导出功能

## 9. 注意事项

1. **协议字段完整性**：确保按照通信协议要求展示所有必要字段
2. **性能优化**：日志数据量可能较大，需要考虑前端分页和虚拟滚动（原型可暂不实现）
3. **数据安全**：敏感字段需要脱敏显示（如 IP 地址部分隐藏）
4. **时间格式**：统一使用"YYYY-MM-DD HH:mm:ss"格式
5. **错误处理**：失败的操作需要显示具体错误信息

## 10. 后续优化

- 添加高级搜索功能（支持更多协议字段筛选）
- 实现日志详情查看弹窗
- 添加日志统计分析图表
- 支持批量导出和定时任务
- 集成实时日志推送（WebSocket）
