<template>
  <div class="content-page-layout">
    <!-- 页眉 -->
    <LandingHeader />
    
    <!-- 主要内容区域 -->
    <main class="content-main">
      <div class="content-container">
        <slot />
      </div>
    </main>
    
    <!-- 页脚 -->
    <LandingFooter />
  </div>
</template>

<script setup lang="ts">
import LandingHeader from './LandingHeader.vue'
import LandingFooter from './LandingFooter.vue'
</script>

<style scoped>
.content-page-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

.content-main {
  flex: 1;
  width: 100%;
  padding: 2rem 0;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-main {
    padding: 1rem 0;
  }
  
  .content-container {
    padding: 0 1rem;
  }
}
</style>