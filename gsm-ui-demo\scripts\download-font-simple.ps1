# PowerShell script to download Alimama font
$fontsDir = "public\assets\fonts"

# Create fonts directory if it doesn't exist
if (!(Test-Path $fontsDir)) {
    New-Item -ItemType Directory -Path $fontsDir -Force
}

Write-Host "正在下载阿里妈妈方圆体字体文件..." -ForegroundColor Green

# Try to download from multiple sources
$urls = @(
    @{
        url = "https://cdn.jsdelivr.net/npm/alimama-fonts@1.0.0/dist/AlimamaFangYuanTiVF.woff2"
        filename = "AlimamaFangYuanTiVF.woff2"
    },
    @{
        url = "https://unpkg.com/alimama-fonts@1.0.0/dist/AlimamaFangYuanTiVF.woff"
        filename = "AlimamaFangYuanTiVF.woff"
    }
)

$successCount = 0

foreach ($font in $urls) {
    $outputPath = Join-Path $fontsDir $font.filename
    
    try {
        Write-Host "正在下载: $($font.url)" -ForegroundColor Yellow
        Invoke-WebRequest -Uri $font.url -OutFile $outputPath -TimeoutSec 30
        
        # Check if file was downloaded and has content
        if ((Test-Path $outputPath) -and ((Get-Item $outputPath).Length -gt 1000)) {
            Write-Host "✅ 下载成功: $($font.filename)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "❌ 下载的文件无效: $($font.filename)" -ForegroundColor Red
            if (Test-Path $outputPath) { Remove-Item $outputPath }
        }
    }
    catch {
        Write-Host "❌ 下载失败: $($font.filename) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

if ($successCount -eq 0) {
    Write-Host "所有字体下载都失败，将使用系统字体作为替代" -ForegroundColor Yellow
    Write-Host "字体CSS已配置为使用系统字体，应用仍可正常运行" -ForegroundColor Cyan
} else {
    Write-Host "字体下载完成！成功下载 $successCount 个文件" -ForegroundColor Green
}
