<template>
  <div class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 基本信息 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center space-x-2">
            <Building class="h-5 w-5" />
            <span>企业基本信息</span>
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="font-medium text-sm">企业名称：</span>
              <span class="text-sm">{{ enterprise.enterprise_name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">统一社会信用代码：</span>
              <span class="text-sm font-mono">{{ enterprise.unified_social_credit_code }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">企业类型：</span>
              <Badge variant="secondary">{{ enterprise.type }}</Badge>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">注册资本：</span>
              <span class="text-sm">{{ formatCurrency(basicInfo.registeredCapital) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">注册日期：</span>
              <span class="text-sm">{{ formatDate(enterprise.registration_date) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">企业状态：</span>
              <Badge :variant="getEnterpriseStatusVariant(enterprise.status)">
                {{ enterprise.status === 1 ? '正常' : '注销' }}
              </Badge>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">注册状态：</span>
              <Badge :variant="getStatusVariant(enterprise.registrationStatus)">
                {{ enterprise.registrationStatus }}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 联系信息 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center space-x-2">
            <Phone class="h-5 w-5" />
            <span>联系信息</span>
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="font-medium text-sm">法人代表：</span>
              <span class="text-sm">{{ basicInfo.legalRepresentative }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">联系人：</span>
              <span class="text-sm">{{ enterprise.contact_person }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">联系电话：</span>
              <span class="text-sm font-mono">{{ enterprise.contact_phone }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">企业邮箱：</span>
              <span class="text-sm">{{ basicInfo.email }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">官网地址：</span>
              <a :href="basicInfo.website" target="_blank" class="text-sm text-primary hover:underline">
                {{ basicInfo.website }}
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 地址信息 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <MapPin class="h-5 w-5" />
          <span>地址信息</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="font-medium text-sm">注册地址：</span>
              <span class="text-sm">{{ basicInfo.registeredAddress }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">经营地址：</span>
              <span class="text-sm">{{ enterprise.address }}</span>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="font-medium text-sm">邮政编码：</span>
              <span class="text-sm">{{ basicInfo.postalCode }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">所属区域：</span>
              <span class="text-sm">{{ basicInfo.region }}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 经营范围 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <FileText class="h-5 w-5" />
          <span>经营范围</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm leading-relaxed">
          {{ enterprise.business_scope }}
        </div>
      </CardContent>
    </Card>

    <!-- 附件信息 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <Paperclip class="h-5 w-5" />
          <span>营业执照</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="font-medium text-sm">执照编号：</span>
              <span class="text-sm font-mono">{{ basicInfo.licenseNumber }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">发证机关：</span>
              <span class="text-sm">{{ basicInfo.licenseAuthority }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium text-sm">有效期限：</span>
              <span class="text-sm">{{ formatDate(basicInfo.licenseValidFrom) }} 至 {{ formatDate(basicInfo.licenseValidTo) }}</span>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center space-x-2">
              <span class="font-medium text-sm">扫描件：</span>
              <Button size="sm" variant="outline" @click="viewLicense">
                <Eye class="w-4 h-4 mr-2" />
                查看
              </Button>
              <Button size="sm" variant="outline" @click="downloadLicense">
                <Download class="w-4 h-4 mr-2" />
                下载
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 风险评级 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <AlertTriangle class="h-5 w-5" />
          <span>风险评级</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <Badge :variant="getRiskVariant(enterprise.riskLevel)" class="text-base px-3 py-1">
              {{ enterprise.riskLevel }}风险
            </Badge>
            <span class="text-sm text-muted-foreground">最后评估时间：{{ formatDate(basicInfo.lastRiskAssessment) }}</span>
          </div>
          <Button size="sm" variant="outline">
            查看详细评估报告
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Building, 
  Phone, 
  MapPin, 
  FileText, 
  Paperclip, 
  AlertTriangle,
  Eye,
  Download
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface Enterprise {
  id: string
  enterprise_name: string // 企业名称
  unified_social_credit_code: string // 统一社会信用代码
  address: string // 地址
  contact_person: string // 联系人
  contact_phone: string // 联系电话
  business_scope: string // 业务范围
  registration_date: string // 注册日期
  status: number // 状态 1:正常 0:注销
  create_time: string // 创建时间
  update_time: string // 更新时间
  
  // 扩展字段（用于UI显示）
  type: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

interface Props {
  enterprise: Enterprise
}

const props = defineProps<Props>()

// 模拟企业详细基本信息数据
const basicInfo = computed(() => ({
  registeredCapital: 50000000, // 5000万元
  establishedDate: '2018-03-15',
  legalRepresentative: '李明',
  email: '<EMAIL>',
  website: 'https://www.zhixing-tech.com',
  registeredAddress: '北京市朝阳区科技园区创新大厦A座1001室',
  postalCode: '100101',
  region: '北京市朝阳区',
  businessScope: '智能网联汽车技术研发；自动驾驶系统集成；车辆检测技术服务；软件开发；技术咨询、技术转让、技术服务；销售电子产品、通讯设备、计算机软硬件及配件；货物进出口、技术进出口、代理进出口。',
  licenseNumber: '91110000MA001234XX',
  licenseAuthority: '北京市朝阳区市场监督管理局',
  licenseValidFrom: '2018-03-15',
  licenseValidTo: '2028-03-14',
  lastRiskAssessment: '2024-01-15'
}))

// 方法
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const getEnterpriseStatusVariant = (status: number) => {
  switch (status) {
    case 1:
      return 'default' // 正常
    case 0:
      return 'destructive' // 注销
    default:
      return 'outline'
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case '通过':
      return 'default'
    case '注册中':
      return 'secondary'
    case '待审核':
      return 'outline'
    case '未通过':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getRiskVariant = (riskLevel: string) => {
  switch (riskLevel) {
    case '高':
      return 'destructive'
    case '中':
      return 'default'
    case '低':
      return 'secondary'
    default:
      return 'outline'
  }
}

const viewLicense = () => {
  console.log('查看营业执照')
  // 这里实现查看营业执照的逻辑
}

const downloadLicense = () => {
  console.log('下载营业执照')
  // 这里实现下载营业执照的逻辑
}
</script>