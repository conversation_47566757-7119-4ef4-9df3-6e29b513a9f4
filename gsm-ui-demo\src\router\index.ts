import { createRouter, createWebHashHistory } from 'vue-router'

const ROUTER_BASE = (import.meta.env.BASE_URL === '#/' || import.meta.env.BASE_URL === '#')
  ? '/'
  : (import.meta.env.BASE_URL || '/')

const router = createRouter({
<<<<<<< HEAD
  history: createWebHistory(ROUTER_BASE),
=======
  history: createWebHashHistory(import.meta.env.BASE_URL),
>>>>>>> 91736b6123c129d456d0d99bd4255f9e4b8d8c64
  routes: [
    // === 登录前页面路由（公开访问，独立模块） ===
    {
      path: '/',
      name: 'Landing',
      component: () => import('@/modules/public/components/LandingPage.vue'),
      meta: { layout: 'public', requiresAuth: false, title: '地理信息安全监测平台' },
    },
    {
      path: '/login/:type?',
      name: 'Login',
      component: () => import('@/modules/public/components/LoginPage.vue'),
      props: (route) => ({ userType: route.params.type || 'government' }),
      meta: { layout: 'public', requiresAuth: false, title: '地理信息安全监测平台' },
    },
    {
      path: '/auth/activate',
      name: 'AuthActivate',
      component: () => import('@/views/auth/PersonalVerification.vue'),
      meta: { requiresAuth: false, title: '个人实名认证' },
    },
    {
      path: '/password/reset',
      name: 'PasswordReset',
      component: () => import('@/views/auth/PasswordReset.vue'),
      meta: { requiresAuth: false, title: '找回密码' },
    },
    {
      path: '/policy/:id',
      name: 'PolicyDetail',
      component: () => import('@/modules/public/components/PolicyDetailPage.vue'),
      props: true,
      meta: { layout: 'public', requiresAuth: false, title: '政策详情' },
    },
    {
      path: '/compliance/apply',
      name: 'ComplianceApplication',
      component: () => import('@/modules/public/components/ComplianceApplicationDetail.vue'),
      meta: { layout: 'public', requiresAuth: false, title: '企业入驻申请' },
    },
    {
      path: '/news/:id?',
      name: 'NewsDetail',
      component: () => import('@/modules/public/components/ContentDetailPage.vue'),
      props: true,
      meta: { layout: 'public', requiresAuth: false, title: '新闻详情' },
    },
    {
      path: '/policies',
      name: 'PolicyList',
      component: () => import('@/modules/external/components/PolicyListPage.vue'),
      meta: { layout: 'public', requiresAuth: false, title: '政策法规' },
    },

    // === Content路径支持（兼容原项目链接） ===
    {
      path: '/content/policy/:id',
      name: 'ContentPolicyDetail',
      component: () => import('@/modules/public/components/PolicyDetailPage.vue'),
      props: true,
      meta: { layout: 'public', requiresAuth: false, title: '政策详情' },
    },
    {
      path: '/content/compliance/:type?',
      name: 'ContentComplianceApplication',
      component: () => import('@/modules/public/components/ComplianceApplicationDetail.vue'),
      props: true,
      meta: { layout: 'public', requiresAuth: false, title: '企业入驻申请' },
    },
    {
      path: '/content/news/:id?',
      name: 'ContentNewsDetail',
      component: () => import('@/modules/public/components/ContentDetailPage.vue'),
      props: true,
      meta: { layout: 'public', requiresAuth: false, title: '新闻详情' },
    },

    // === 登录后页面路由（需要认证，使用shadcn-vue） ===
    // 政府端根：/gov
    {
      path: '/gov',
      name: 'Government',
      meta: { requiresAuth: true, userType: 'government', title: '政府监管控制台' },
      redirect: '/gov/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'GovDashboard',
          component: () => import('@/views/government/Dashboard.vue'),
          meta: { title: '政府监管控制台' },
        },
        {
          path: 'record',
          name: 'GovRecord',
          meta: { title: '备案审核' },
          redirect: '/gov/record/registration-info',
          children: [
            {
              path: 'registration-info',
              name: 'GovRecordRegistrationInfo',
              component: () => import('@/views/government/RegistrationManagement.vue'),
              meta: { title: '注册信息管理' },
            },
            {
              path: 'approval',
              name: 'GovRecordApproval',
              component: () => import('@/views/government/ApprovalTaskManagement.vue'),
              meta: { title: '备案审批管理' },
            },
          ],
        },
        {
          path: 'monitor',
          name: 'GovMonitor',
          meta: { title: '实时监测' },
          redirect: '/gov/monitor/risk',
          children: [
            {
              path: 'risk',
              name: 'GovMonitorRisk',
              redirect: '/gov/monitor/risk/vehicle',
              meta: { title: '风险管理' },
              children: [
                {
                  path: 'vehicle',
                  name: 'GovMonitorRiskVehicle',
                  component: () => import('@/views/government/RiskVehicle.vue'),
                  meta: { title: '车端安全风险' },
                },
                {
                  path: 'cloud',
                  name: 'GovMonitorRiskCloud',
                  component: () => import('@/views/government/CloudRisk.vue'),
                  meta: { title: '云端安全风险' },
                },
              ],
            },
            {
              path: 'event',
              name: 'GovMonitorEvent',
              redirect: '/gov/monitor/event/vehicle',
              meta: { title: '事件管理' },
              children: [
                {
                  path: 'vehicle',
                  name: 'GovMonitorEventVehicle',
                  component: () => import('@/views/government/VehicleEventManagement.vue'),
                  meta: { title: '车端事件管理' },
                },
                {
                  path: 'cloud',
                  name: 'GovMonitorEventCloud',
                  component: () => import('@/views/government/CloudEventManagement.vue'),
                  meta: { title: '云端事件管理' },
                },
              ],
            },
            {
              path: 'traceback',
              name: 'GovMonitorTraceback',
              redirect: '/gov/monitor/traceback/vehicle',
              meta: { title: '应急溯源管理' },
              children: [
                {
                  path: 'vehicle',
                  name: 'GovMonitorTracebackVehicle',
                  component: () => import('@/views/government/VehicleTracebackRecords.vue'),
                  meta: { title: '车端溯源追踪' },
                },
                {
                  path: 'cloud',
                  name: 'GovMonitorTracebackCloud',
                  component: () => import('@/views/government/CloudTracebackRecords.vue'),
                  meta: { title: '云端溯源追踪' },
                },
              ],
            },
            {
              path: 'operation',
              name: 'GovMonitorOperation',
              redirect: '/gov/monitor/operation/vehicle',
              meta: { title: '操作信息管理' },
              children: [
                {
                  path: 'vehicle',
                  name: 'GovMonitorOperationVehicle',
                  component: () => import('@/views/government/VehicleOperationLog.vue'),
                  meta: { title: '车端操作信息' },
                },
                {
                  path: 'cloud',
                  name: 'GovMonitorOperationCloud',
                  component: () => import('@/views/government/CloudOperationLog.vue'),
                  meta: { title: '云端操作信息' },
                },
              ],
            },
          ],
        },
        {
          path: 'system',
          name: 'GovSystem',
          meta: { title: '系统管理' },
          redirect: '/gov/system/users',
          children: [
            {
              path: 'users',
              name: 'GovSystemUsers',
              component: () => import('@/views/government/UserManagement.vue'),
              meta: { title: '用户信息管理' },
            },
            {
              path: 'roles',
              name: 'GovSystemRoles',
              component: () => import('@/views/government/RoleManagement.vue'),
              meta: { title: '角色权限管理' },
            },
            {
              path: 'organization',
              name: 'GovSystemOrganization',
              component: () => import('@/views/government/OrganizationManagement.vue'),
              meta: { title: '组织架构管理' },
            },
            {
              path: 'monitor-areas',
              name: 'GovSystemMonitorAreas',
              component: () => import('@/views/government/MonitorAreas.vue'),
              meta: { title: '监测区域管理' },
            },
            {
              path: 'rules',
              name: 'GovSystemRules',
              meta: { title: '风险规则管理' },
              redirect: '/gov/system/rules/risk',
              children: [
                {
                  path: 'risk',
                  name: 'GovSystemRiskRules',
                  component: () => import('@/views/government/RiskRulesManagement.vue'),
                  meta: { title: '风险规则管理' },
                },
                {
                  path: 'risk/edit/:id?',
                  name: 'GovSystemRiskRuleEdit',
                  component: () => import('@/views/government/RiskRuleEditor.vue'),
                  props: true,
                  meta: { title: '规则编辑' },
                },
              ],
            },
            {
              path: 'logs',
              name: 'GovSystemLogs',
              meta: { title: '日志管理' },
              redirect: '/gov/system/logs/operation',
              children: [
                {
                  path: 'operation',
                  name: 'GovSystemOperationLogs',
                  component: () => import('@/views/government/OperationLogs.vue'),
                  meta: { title: '操作日志' },
                },
                {
                  path: 'login',
                  name: 'GovSystemLoginLogs',
                  component: () => import('@/views/government/LoginLogs.vue'),
                  meta: { title: '登录日志' },
                },
              ],
            },
            {
              path: 'dict',
              name: 'GovSystemDict',
              component: () => import('@/views/government/DictionaryManagement.vue'),
              meta: { title: '字典管理' },
            },
            {
              path: 'config',
              name: 'GovSystemConfig',
              component: () => import('@/views/government/SystemConfig.vue'),
              meta: { title: '参数配置' },
            },
            {
              path: 'monitoring',
              name: 'GovSystemMonitoring',
              component: () => import('@/views/government/SystemMonitoring.vue'),
              meta: { title: '系统监控' },
            },
            {
              path: 'security',
              name: 'GovSystemSecurity',
              component: () => import('@/views/government/SystemSecurityRisk.vue'),
              meta: { title: '系统安全风险' },
            },
            {
              path: 'information',
              name: 'GovSystemInformation',
              meta: { title: '信息发布' },
              children: [
                {
                  path: '',
                  name: 'GovInformationList',
                  component: () => import('@/views/government/InformationList.vue'),
                  meta: { title: '信息发布管理' },
                },
                {
                  path: 'edit/:id?',
                  name: 'GovInformationEdit',
                  component: () => import('@/views/government/InformationEdit.vue'),
                  props: true,
                  meta: { title: '信息发布编辑' },
                },
              ],
            },
            {
              path: 'chart-kit',
              name: 'ChartKit',
              component: () => import('@/views/ChartKit.vue'),
              meta: { title: '图表配色主题对比' },
            },
          ],
        },
      ],
    },
    // 企业端根：/corp
    {
      path: '/corp',
      name: 'Enterprise',
      meta: { requiresAuth: true, userType: 'enterprise', title: '企业端控制台' },
      redirect: '/corp/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'EnterpriseDashboard',
          component: () => import('@/views/enterprise/Dashboard.vue'),
          meta: { title: '企业端控制台' },
        },
        {
          path: 'filing',
          name: 'EntRegistration',
          meta: { title: '注册备案' },
          redirect: '/corp/filing/form',
          children: [
            {
              path: 'form',
              name: 'EntRegistrationFiling',
              meta: { title: '备案填报' },
              redirect: '/corp/filing/form/basic-info',
              children: [
                {
                  path: 'basic-info',
                  name: 'EntFilingBasicInfo',
                  component: () => import('@/views/enterprise/BasicInfoFiling.vue'),
                  meta: { title: '企业基本信息填报' },
                },
                {
                  path: 'qualification',
                  name: 'EntFilingQualification',
                  component: () => import('@/views/enterprise/QualificationFiling.vue'),
                  meta: { title: '测绘资质信息填报' },
                },
                {
                  path: 'security-policy',
                  name: 'EntFilingSecurityPolicy',
                  component: () => import('@/views/enterprise/SecurityPolicyFiling.vue'),
                  meta: { title: '数据安全防控措施-制度' },
                },
                {
                  path: 'security-tech',
                  name: 'EntFilingSecurityTech',
                  component: () => import('@/views/enterprise/SecurityTechFiling.vue'),
                  meta: { title: '数据安全防控措施-技术' },
                },
                {
                  path: 'vehicle-info',
                  name: 'EntFilingVehicleInfo',
                  component: () => import('@/views/enterprise/VehicleInfoFiling.vue'),
                  meta: { title: '车辆信息填报' },
                },
                {
                  path: 'data-activity',
                  name: 'EntFilingDataActivity',
                  component: () => import('@/views/enterprise/DataActivityFiling.vue'),
                  meta: { title: '数据处理活动信息' },
                },
              ],
            },
            {
              path: 'records',
              name: 'EntFilingRecords',
              component: () => import('@/views/enterprise/FilingRecordsManagement.vue'),
              meta: { title: '备案管理' },
            },
          ],
        },
        {
          path: 'risk-event',
          name: 'EntRiskEvent',
          meta: { title: '风险事件' },
          redirect: '/corp/risk-event/risk',
          children: [
            {
              path: 'risk',
              name: 'EntRiskStats',
              component: () => import('@/views/enterprise/RiskStats.vue'),
              meta: { title: '风险统计' },
            },
            {
              path: 'event',
              name: 'EntEventStats',
              component: () => import('@/views/enterprise/EventStats.vue'),
              meta: { title: '事件统计' },
            },
          ],
        },
        {
          path: 'notice',
          name: 'EntNotice',
          meta: { title: '通知待办' },
          redirect: '/corp/notice/notices',
          children: [
            {
              path: 'notices',
              name: 'EntNotices',
              component: () => import('@/views/enterprise/NotificationList.vue'),
              meta: { title: '通知信息页' },
            },
            {
              path: 'tasks',
              name: 'EntTasks',
              component: () => import('@/views/enterprise/TaskManagement.vue'),
              meta: { title: '任务管理页' },
            },
          ],
        },
        {
          path: 'management',
          name: 'EntManagement',
          meta: { title: '企业管理' },
          redirect: '/corp/management/users',
          children: [
            {
              path: 'users',
              name: 'EntUsers',
              component: () => import('@/views/enterprise/UserManagement.vue'),
              meta: { title: '用户管理' },
            },
            {
              path: 'roles',
              name: 'EntRoles',
              component: () => import('@/views/enterprise/RoleManagement.vue'),
              meta: { title: '角色权限管理' },
            },
          ],
        },
      ],
    },

    // 兼容外链重定向（/gov/risk/vehicle → /gov/monitor/risk/vehicle）
    {
      path: '/gov/risk/vehicle',
      redirect: '/gov/monitor/risk/vehicle',
    },
    // P-011 注册信息管理页重定向
    {
      path: '/gov/registration/management',
      redirect: '/gov/record/registration-info',
    },
    // P-013 审批任务管理页重定向
    {
      path: '/gov/approval/tasks',
      redirect: '/gov/record/approval',
    },
    // 溯源短链重定向
    {
      path: '/gov/trace/vehicle',
      redirect: '/gov/monitor/traceback/vehicle',
    },
    {
      path: '/gov/trace/cloud',
      redirect: '/gov/monitor/traceback/cloud',
    },
    // P-020 车端操作信息重定向
    {
      path: '/gov/log/vehicle',
      redirect: '/gov/monitor/operation/vehicle',
    },
    // P-021 云端操作信息重定向
    {
      path: '/gov/log/cloud',
      redirect: '/gov/monitor/operation/cloud',
    },
    // P-018 车端应急溯源重定向
    {
      path: '/gov/trace/vehicle',
      redirect: '/gov/monitor/traceback/vehicle',
    },
    // P-019 云端应急溯源重定向
    {
      path: '/gov/trace/cloud',
      redirect: '/gov/monitor/traceback/cloud',
    },
    // 系统管理兼容重定向（/admin/* → /gov/system/*）
    {
      path: '/admin/:pathMatch(.*)*',
      redirect: (to) => {
        const adminPath = to.params.pathMatch as string
        return `/gov/system/${adminPath}`
      },
    },
    // 企业端兼容重定向
    {
      path: '/corp/filing/basic-info',
      redirect: '/corp/filing/form/basic-info',
    },
    {
      path: '/corp/filing/qualification',
      redirect: '/corp/filing/form/qualification',
    },
    {
      path: '/corp/filing/security-policy',
      redirect: '/corp/filing/form/security-policy',
    },
    {
      path: '/corp/filing/security-tech',
      redirect: '/corp/filing/form/security-tech',
    },
    {
      path: '/corp/filing/vehicle-info',
      redirect: '/corp/filing/form/vehicle-info',
    },
    {
      path: '/corp/filing/data-activity',
      redirect: '/corp/filing/form/data-activity',
    },
    {
      path: '/corp/risk/stats',
      redirect: '/corp/risk-event/risk',
    },
    {
      path: '/corp/event/stats',
      redirect: '/corp/risk-event/event',
    },

    // === 404 页面与兜底路由 ===
    {
      path: '/404',
      name: 'NotFound',
      component: () => import('@/modules/public/components/NotFoundPage.vue'),
      meta: { layout: 'public', requiresAuth: false, title: '页面未找到' },
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404',
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = checkAuthStatus() // 检查用户登录状态

  if (to.meta.requiresAuth && !isAuthenticated) {
    // Demo 原型自动放行策略：
    // 统一对 /gov、/corp、/admin 的访问进行本地凭据注入并放行（仅原型环境）
    if (to.path.startsWith('/gov') || to.path.startsWith('/corp') || to.path.startsWith('/admin')) {
      const userType = to.path.startsWith('/corp') ? 'enterprise' : 'government'
      localStorage.setItem('auth_token', 'demo-token')
      localStorage.setItem('user_type', userType)
      next(to.fullPath) // 重新进入目标路由，触发下一轮守卫并通过
      return
    }
    // 其他情况仍按原逻辑跳转登录页
    next({ name: 'Login', query: { redirect: to.fullPath } })
    return
  } else if (to.meta.userType && isAuthenticated) {
    // 检查用户类型权限
    const userType = getUserType()
    if (to.meta.userType !== userType) {
      // 用户类型不匹配，重定向到对应的dashboard
      next({ name: userType === 'government' ? 'GovDashboard' : 'EnterpriseDashboard' })
      return
    }
  }
  next()
})

// 路由后置守卫 - 更新页面标题
router.afterEach((to) => {
  const baseTitle = '地理信息安全监测平台'
  const pageTitle = to.meta?.title as string

  if (pageTitle && pageTitle !== baseTitle) {
    document.title = `${pageTitle} - ${baseTitle}`
  } else {
    document.title = baseTitle
  }

  // 运行时清理可能被外部脚本/托管环境附加的 "#/" 或 "#"，确保始终使用 HTML5 History 模式
  try {
    const h = window.location.hash
    if (h === '#/' || h === '#') {
      const clean = window.location.pathname + window.location.search
      history.replaceState(null, '', clean)
    }
  } catch {
    // 忽略清理失败
  }
})

// 辅助函数
function checkAuthStatus(): boolean {
  // TODO: 实现真实的认证检查逻辑
  return localStorage.getItem('auth_token') !== null
}

function getUserType(): 'government' | 'enterprise' {
  // TODO: 从token或用户信息中获取用户类型
  return (localStorage.getItem('user_type') as 'government' | 'enterprise') || 'government'
}

// 动态导入失败的兜底处理：
// 典型报错包括 “Failed to fetch dynamically imported module”、“ChunkLoadError” 等。
// 发生时说明浏览器缓存或边缘节点缓存的按需 chunk 与入口文件不一致，触发一次性强制刷新以获取最新资源。
router.onError((error) => {
  try {
    const msg = error instanceof Error ? String(error.message ?? '') : String(error ?? '')
    const shouldReload =
      msg.includes('Failed to fetch dynamically imported module') ||
      msg.includes('Importing a module script failed') ||
      msg.includes('ChunkLoadError') ||
      msg.includes('Loading chunk')

    if (shouldReload) {
      const href = window.location.href
      if (!href.includes('reload=1')) {
        const url = new URL(href)
        url.searchParams.set('reload', '1')
        window.location.replace(url.toString())
      } else {
        window.location.reload()
      }
    }
  } catch {
    // 兜底：若解析错误信息失败，仍尝试一次性刷新
    const href = window.location.href
    if (!href.includes('reload=1')) {
      const url = new URL(href)
      url.searchParams.set('reload', '1')
      window.location.replace(url.toString())
    } else {
      window.location.reload()
    }
  }
})

export default router
