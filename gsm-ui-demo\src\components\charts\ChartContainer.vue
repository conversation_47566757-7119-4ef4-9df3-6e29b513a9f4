<template>
  <div class="chart-container relative overflow-hidden">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    <v-chart
      ref="chartRef"
      :class="['w-full transition-all duration-300', heightClass]"
      :style="heightStyle"
      :option="enhancedOption"
      :autoresize="true"
      :loading="loading"
      :loading-options="loadingOptions"
      @click="handleChartClick"
      @mouseover="handleMouseOver"
      @mouseout="handleMouseOut"
      @finished="handleRenderFinished"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, onMounted, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { useDark } from '@vueuse/core'

// Import ECharts components
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  DataZoomComponent,
  GraphicComponent,
  VisualMapComponent,
} from 'echarts/components'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>att<PERSON><PERSON><PERSON>,
  Radar<PERSON>hart,
  HeatmapChart,
} from 'echarts/charts'

// Import theme configuration
import {
  createEChartsTheme,
  CHART_ANIMATIONS,
  DARK_COLORS, // Add the missing import
} from '@/lib/chart-themes'
import { getUnifiedColorScheme } from '@/lib/unified-chart-colors'

// Register ECharts components
use([
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  DataZoomComponent,
  GraphicComponent,
  VisualMapComponent,
  PieChart,
  BarChart,
  LineChart,
  ScatterChart,
  RadarChart,
  HeatmapChart,
])

interface Props {
  option: any
  height?: string | number
  loading?: boolean
  enableToolbox?: boolean
  enableDataZoom?: boolean
  colorScheme?:
    | 'primary'
    | 'risk'
    | 'enterprise'
    | 'stages'
    | 'status'
    | 'vehicleTypes'
    | 'barChart'
  colors?: string[]
  onClick?: (params: any) => void
  onMouseOver?: (params: any) => void
  onMouseOut?: (params: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  height: 'auto',
  loading: false,
  enableToolbox: false,
  enableDataZoom: false,
  colorScheme: 'primary',
})

const emit = defineEmits<{
  click: [params: any]
  mouseover: [params: any]
  mouseout: [params: any]
  renderFinished: []
}>()

const chartRef = ref<InstanceType<typeof VChart>>()
const isDark = useDark()

const heightClass = computed(() => {
  if (typeof props.height === 'number') {
    // 使用内联样式代替动态 Tailwind 类
    return ''
  }
  if (props.height === 'auto') {
    return 'h-[320px] sm:h-[400px] lg:h-[480px]' // 响应式高度设置
  }
  return props.height
})

// 计算内联样式 - 优化高度设置
const heightStyle = computed(() => {
  if (typeof props.height === 'number') {
    // 数字高度：严格遵守传入值，避免比容器高造成溢出
    const h = `${props.height}px`
    return { height: h, minHeight: h }
  }
  if (props.height === 'auto') {
    // 响应式高度设置，确保图表有足够的展示空间
    return {
      height: '320px',
      minHeight: '256px',
      '@media (min-width: 640px)': { height: '400px' },
      '@media (min-width: 1024px)': { height: '480px' },
    }
  }
  if (typeof props.height === 'string' && props.height.includes('px')) {
    // 明确像素值：遵守传入值
    const h = props.height
    return { height: h, minHeight: h }
  }
  // 其他情况：保持一个合理的最小高度
  return { minHeight: '256px' }
})

// Loading options with dark theme styling
const loadingOptions = computed(() => ({
  text: '加载中...',
  color: DARK_COLORS.darkBlue2, // 使用暗蓝色主题
  textColor: isDark.value ? '#e2e8f0' : '#2d3748',
  maskColor: isDark.value ? 'rgba(26, 32, 44, 0.8)' : 'rgba(255, 255, 255, 0.8)',
  zlevel: 0,
  fontSize: 14,
  showSpinner: true,
  spinnerRadius: 12,
  lineWidth: 4,
}))

// Enhanced chart option with MTR theme
const enhancedOption = computed(() => {
  const baseTheme = createEChartsTheme(isDark.value)

  // Get color scheme
  const colors =
    props.colors && props.colors.length > 0 ? props.colors : getColorScheme(props.colorScheme)

  const enhancedOption = {
    ...baseTheme,
    ...props.option,

    // Override colors
    color: colors,

    // Enhanced animations
    ...CHART_ANIMATIONS,

    // Toolbox configuration
    toolbox: props.enableToolbox
      ? {
          show: true,
          orient: 'horizontal',
          right: 20,
          top: 20,
          feature: {
            saveAsImage: {
              title: '保存为图片',
              name: 'chart',
              backgroundColor: isDark.value ? '#1a202c' : '#ffffff',
            },
            restore: {
              title: '重置',
            },
          },
          iconStyle: {
            borderColor: isDark.value ? '#a0aec0' : '#4a5568',
          },
          emphasis: {
            iconStyle: {
              borderColor: DARK_COLORS.darkBlue2, // 使用暗蓝色主题
            },
          },
        }
      : undefined,

    // DataZoom configuration
    dataZoom: props.enableDataZoom
      ? [
          {
            type: 'slider',
            backgroundColor: isDark.value ? '#2d3748' : '#f7fafc',
            borderColor: isDark.value ? '#4a5568' : '#e2e8f0',
            fillerColor: DARK_COLORS.darkBlue2 + '20', // 使用暗蓝色主题
            handleColor: DARK_COLORS.darkBlue2, // 使用暗蓝色主题
            handleSize: 20,
            textStyle: {
              color: isDark.value ? '#e2e8f0' : '#2d3748',
            },
          },
        ]
      : undefined,
  }

  // Apply enhanced styling to series
  if (enhancedOption.series) {
    enhancedOption.series = enhancedOption.series.map((series: any, index: number) =>
      enhanceSeriesStyling(series, index, colors),
    )
  }

  // Merge tooltip configuration
  if (enhancedOption.tooltip) {
    enhancedOption.tooltip = {
      ...baseTheme.tooltip,
      ...enhancedOption.tooltip,
      confine: true,
      appendToBody: true,
      transitionDuration: 0.2,
      hideDelay: 100,
      trigger: enhancedOption.tooltip.trigger || 'item',
    }
  }

  // Ensure a reasonable grid to avoid label clipping & overflow
  if (!enhancedOption.grid) {
    const top = enhancedOption.title ? 48 : 20
    const bottom = enhancedOption.legend ? 40 : 20
    enhancedOption.grid = { left: 16, right: 16, top, bottom, containLabel: true }
  } else if (Array.isArray(enhancedOption.grid)) {
    enhancedOption.grid = enhancedOption.grid.map((g: any) => ({
      left: 16,
      right: 16,
      top: 20,
      bottom: 20,
      ...g,
      containLabel: true, // ensure labels are contained to avoid overflow
    }))
  } else {
    const top = enhancedOption.title ? 48 : 20
    const bottom = enhancedOption.legend ? 40 : 20
    enhancedOption.grid = {
      left: 16,
      right: 16,
      top,
      bottom,
      ...enhancedOption.grid,
      containLabel: true,
    }
  }

  // Improve axis label readability for bar charts in light theme
  if (
    !isDark.value &&
    Array.isArray(enhancedOption.series) &&
    enhancedOption.series.some((s: any) => s && s.type === 'bar')
  ) {
    const labelStyle = {
      color: '#334155', // slate-700 for better contrast in light mode
      fontSize: 14,
      fontWeight: 500,
      fontFamily: 'Segoe UI, Roboto, Inter, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
    }
    const applyAxis = (axis: any) => {
      if (!axis) return axis
      const applyOne = (ax: any) => ({
        ...ax,
        axisLabel: {
          ...(ax?.axisLabel || {}),
          ...labelStyle,
        },
      })
      return Array.isArray(axis) ? axis.map(applyOne) : applyOne(axis)
    }
    enhancedOption.xAxis = applyAxis(enhancedOption.xAxis)
    enhancedOption.yAxis = applyAxis(enhancedOption.yAxis)
  }

  return enhancedOption
})

// Get color scheme based on type
function getColorScheme(scheme: Props['colorScheme']): string[] {
  // Use unified color scheme
  return getUnifiedColorScheme(scheme || 'primary')
}

// Enhance series styling
function enhanceSeriesStyling(series: any, index: number, colors: string[]) {
  const baseColor = colors[index % colors.length]

  const commonEnhancements = {
    ...series,
    // Enhanced hover effects
    emphasis: {
      scale: series.type === 'pie' ? 1.05 : 1.02,
      shadowBlur: 20,
      shadowColor: baseColor + '40',
      ...series.emphasis,
    },
    // Smooth transitions
    universalTransition: {
      enabled: true,
      divideShape: 'clone',
    },
  }

  // Type-specific enhancements
  switch (series.type) {
    case 'pie':
      return {
        ...commonEnhancements,
        radius: series.radius || ['40%', '65%'], // 调整半径，避免标签重叠
        itemStyle: {
          borderRadius: 4,
          borderColor: isDark.value ? '#1a202c' : '#ffffff',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          ...series.itemStyle,
        },
        labelLine: {
          length: 20,
          length2: 10,
          lineStyle: {
            width: 2,
          },
          ...series.labelLine,
        },
      }

    case 'bar':
      return {
        ...commonEnhancements,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          shadowBlur: 6,
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          ...series.itemStyle,
        },
        backgroundStyle: {
          color: isDark.value ? '#2d3748' : '#f7fafc',
          borderRadius: [4, 4, 0, 0],
        },
      }

    case 'line':
      return {
        ...commonEnhancements,
        smooth: series.smooth !== false,
        symbol: series.symbol || 'circle',
        symbolSize: series.symbolSize || 6,
        lineStyle: {
          width: 3,
          shadowBlur: 8,
          shadowColor: baseColor + '40',
          cap: 'round',
          ...series.lineStyle,
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: isDark.value ? '#1a202c' : '#ffffff',
          shadowBlur: 4,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          ...series.itemStyle,
        },
        areaStyle: series.areaStyle
          ? {
              opacity: 0.3,
              ...series.areaStyle,
            }
          : undefined,
      }

    default:
      return commonEnhancements
  }
}

// Event handlers
const handleChartClick = (params: any) => {
  emit('click', params)
  props.onClick?.(params)
}

const handleMouseOver = (params: any) => {
  emit('mouseover', params)
  props.onMouseOver?.(params)
}

const handleMouseOut = (params: any) => {
  emit('mouseout', params)
  props.onMouseOut?.(params)
}

const handleRenderFinished = () => {
  emit('renderFinished')
}

// Expose chart instance for external manipulation
const getChartInstance = () => {
  return chartRef.value?.chart // Fix the method call
}

// Auto-refresh chart on theme change
watch(isDark, () => {
  nextTick(() => {
    const chart = getChartInstance()
    if (chart) {
      chart.setOption(enhancedOption.value, true)
    }
  })
})

// 添加调试日志
onMounted(() => {
  nextTick(() => {
    if (chartRef.value) {
      const el = chartRef.value.$el
      console.log('[ChartContainer] 组件已挂载，容器尺寸:', {
        clientWidth: el?.clientWidth,
        clientHeight: el?.clientHeight,
        offsetWidth: el?.offsetWidth,
        offsetHeight: el?.offsetHeight,
        computedStyle: el ? window.getComputedStyle(el) : null,
      })
    }
  })
})

defineExpose({
  getChartInstance,
  refresh: () => {
    const chart = getChartInstance()
    if (chart) {
      chart.resize()
    }
  },
})
</script>

<style scoped>
.chart-container {
  position: relative;
  background: transparent;
  border-radius: 8px;
  overflow: hidden;
  /*padding: 12px;*/
}

@media (min-width: 640px) {
  .chart-container {
    /*padding: 16px;*/
  }
}

@media (min-width: 1024px) {
  .chart-container {
    /*padding: 20px;*/
  }
}

/* Removed the ::before pseudo-element that was adding a gradient background */

.chart-container canvas {
  border-radius: 6px;
  position: relative;
  z-index: 2;
  background: transparent;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.dark .loading-overlay {
  background: rgba(26, 32, 44, 0.8);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #8b1538;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dark .loading-spinner {
  border-color: #4a5568;
  border-top-color: #8b1538;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Removed chart hover effects that were adding gradient background */

/* Responsive adjustments */
@media (max-width: 640px) {
  .chart-container {
    min-height: 256px; /* 移动端保持更好的高度 */
    padding: 12px;
  }
}

@media (min-width: 768px) {
  .chart-container {
    /*min-height: 320px;*/
  }
}

@media (min-width: 1024px) {
  .chart-container {
    /*min-height: 400px;*/
  }
}
</style>
