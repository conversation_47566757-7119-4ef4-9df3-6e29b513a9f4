<template>
  <div class="axure-dashboard-wrapper">
    <!-- Axure 内容层（地图背景改为由 iframe 内部的 #amap-bg 渲染） -->
    <div class="axure-container" ref="axureContainer">
      <iframe
        ref="axureFrame"
        :src="axureUrl"
        @load="onFrameLoaded"
        class="axure-frame"
        :style="{
          width: '100%',
          height: '100%',
          transform: 'none',
          left: '0',
          top: '0',
        }"
        frameborder="0"
        scrolling="no"
        sandbox="allow-scripts allow-popups allow-forms allow-same-origin"
      />
    </div>

    <!-- 加载指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载监控大屏...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 组件状态
const loading = ref(true)
const axureFrame = ref<HTMLIFrameElement>()

// Axure HTML路径
const axureUrl = '/iframe/government-screen.html'

// 处理来自iframe的消息
const handleIframeMessage = (event: MessageEvent) => {
  if (event.data && event.data.type === 'requestAmapKey') {
    // 向iframe发送API密钥
    const iframe = axureFrame.value
    if (iframe && iframe.contentWindow) {
      try {
        const apiKey = import.meta.env.VITE_AMAP_API_KEY || 'a139182760d79f73aa3b86826382da6f'
        console.log('🔍 [Parent Debug] 发送API Key到iframe:', apiKey)

        iframe.contentWindow.postMessage(
          {
            type: 'amapConfig',
            key: apiKey,
          },
          '*',
        )
        console.log('✅ [Parent Debug] API密钥已发送到iframe')
      } catch (error) {
        console.error('❌ [Parent Debug] 发送API密钥到iframe失败:', error)
      }
    }
  }
}

// iframe加载完成处理
const onFrameLoaded = () => {
  if (axureFrame.value && axureFrame.value.contentWindow) {
    try {
      const frameDocument = axureFrame.value.contentWindow.document
      const style = frameDocument.createElement('style')
      style.textContent = `
        html, body {
          margin: 0 !important;
          padding: 0 !important;
        }
      `
      frameDocument.head.appendChild(style)
    } catch {
      console.warn('Cannot access iframe content due to CORS')
    }
  }

  setTimeout(() => {
    loading.value = false
  }, 800)
}

onMounted(() => {
  if (axureFrame.value) {
    axureFrame.value.style.width = '100%'
    axureFrame.value.style.height = '100%'
  }
  window.addEventListener('message', handleIframeMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleIframeMessage)
})
</script>

<style scoped>
.axure-dashboard-wrapper {
  background: #0f172afa;
  position: relative;
  width: 100%;
  height: calc(100vh - 60px); /* 减去header高度 */
  overflow: hidden;
  padding: 10px 0 0 0; /* 上方留10px空间 */
  box-sizing: border-box; /* 确保padding包含在高度内 */
}

/* 地图背景层 */
.map-background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* 确保地图在底部 */
}

.axure-container {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 2; /* 确保 Axure 内容在地图上方 */
}

.axure-frame {
  width: 100%;
  height: 100%;
  background: transparent;
  box-shadow: none;
  border-radius: 0;
  border: none;
  outline: none;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 防止内部内容产生滚动条 */
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #020b1a;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(0, 237, 255, 0.1);
  border-top-color: #00edff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-overlay p {
  margin-top: 20px;
  color: #00edff;
  font-size: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 深色主题适配 */
:global(.dark) .axure-dashboard-wrapper {
  background: #0f172afa;
}
</style>
