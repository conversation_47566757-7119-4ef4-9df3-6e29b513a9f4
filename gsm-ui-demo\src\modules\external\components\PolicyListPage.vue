<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
    <!-- 导航栏 -->
    <LandingHeader />

    <!-- 主要内容区域 -->
    <main class="pt-20">
      <!-- 页面标题区域 -->
      <section class="py-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div class="container mx-auto px-6">
          <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">{{ $route.meta.title }}</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              智能网联汽车地理信息安全相关的政策法规、标准规范和通知公告
            </p>
          </div>
        </div>
      </section>

      <!-- 筛选和搜索区域 -->
      <section class="py-6 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm">
        <div class="container mx-auto px-6">
          <div class="max-w-6xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 border border-gray-100 dark:border-gray-700">
              <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- 搜索框 -->
                <div class="lg:col-span-2">
                  <div class="relative">
                    <Search
                      class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                    />
                    <input
                      v-model="searchKeyword"
                      type="text"
                      placeholder="搜索政策法规标题..."
                      class="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    />
                  </div>
                </div>

                <!-- 分类筛选 -->
                <div>
                  <select
                    v-model="selectedCategory"
                    class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">全部分类</option>
                    <option value="政策法规">政策法规</option>
                    <option value="标准规范">标准规范</option>
                    <option value="通知公告">通知公告</option>
                    <option value="新闻动态">新闻动态</option>
                  </select>
                </div>

                <!-- 搜索按钮 -->
                <div>
                  <button
                    @click="handleSearch"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
                  >
                    <Search class="w-4 h-4" />
                    搜索
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 政策列表区域 -->
      <section class="py-8">
        <div class="container mx-auto px-6">
          <div class="max-w-6xl mx-auto">
            <!-- 列表头部信息 -->
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center gap-4">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">政策文件</h2>
                <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium">
                  共 {{ filteredPolicies.length }} 条
                </span>
              </div>

              <!-- 排序 -->
              <div class="flex items-center gap-2">
                <span class="text-gray-600 dark:text-gray-400">排序：</span>
                <select
                  v-model="sortBy"
                  class="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="latest">最新发布</option>
                  <option value="title">标题排序</option>
                  <option value="category">分类排序</option>
                </select>
              </div>
            </div>

            <!-- 政策列表 -->
            <div class="space-y-6">
              <div
                v-for="policy in pagedPolicies"
                :key="policy.id"
                class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 overflow-hidden group"
              >
                <div class="p-6">
                  <div class="flex flex-col lg:flex-row lg:items-start gap-6">
                    <!-- 左侧内容 -->
                    <div class="flex-1">
                      <!-- 分类标签和发布时间 -->
                      <div class="flex items-center gap-3 mb-4">
                        <span
                          :class="getCategoryClass(policy.category)"
                          class="px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {{ policy.category }}
                        </span>
                        <span class="text-gray-500 text-sm flex items-center gap-1">
                          <Calendar class="w-4 h-4" />
                          {{ policy.publishDate }}
                        </span>
                      </div>

                      <!-- 标题 -->
                      <h3
                        class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors cursor-pointer leading-tight"
                      >
                        {{ policy.title }}
                      </h3>

                      <!-- 摘要 -->
                      <p class="text-gray-600 dark:text-gray-300 leading-relaxed mb-4 line-clamp-3">
                        {{ policy.summary }}
                      </p>

                      <!-- 标签 -->
                      <div class="flex flex-wrap gap-2 mb-4">
                        <span
                          v-for="tag in policy.tags"
                          :key="tag"
                          class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs"
                        >
                          {{ tag }}
                        </span>
                      </div>

                      <!-- 底部信息 -->
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                          <span class="flex items-center gap-1">
                            <Eye class="w-4 h-4" />
                            {{ policy.views }} 次浏览
                          </span>
                          <span class="flex items-center gap-1">
                            <Download class="w-4 h-4" />
                            {{ policy.downloads }} 次下载
                          </span>
                        </div>

                        <div class="flex items-center gap-3">
                          <button
                            @click="viewPolicy(policy)"
                            class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium flex items-center gap-1 transition-colors"
                          >
                            查看详情
                            <ExternalLink class="w-4 h-4" />
                          </button>
                          <button
                            v-if="policy.downloadUrl"
                            @click="downloadPolicy(policy)"
                            class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
                          >
                            <Download class="w-4 h-4" />
                            下载
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- 右侧图片（如果有） -->
                    <div v-if="policy.coverImage" class="lg:w-48 lg:flex-shrink-0">
                      <img
                        :src="policy.coverImage"
                        :alt="policy.title"
                        class="w-full h-32 lg:h-full object-cover rounded-lg"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredPolicies.length === 0" class="text-center py-12">
              <div
                class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center"
              >
                <FileText class="w-12 h-12 text-gray-400 dark:text-gray-500" />
              </div>
              <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">暂无相关政策</h3>
              <p class="text-gray-500 dark:text-gray-400">请尝试调整搜索条件或分类筛选</p>
            </div>

            <!-- 分页 -->
            <div v-if="totalPages > 1" class="flex justify-center mt-8">
              <div class="flex items-center gap-2">
                <button
                  @click="goToPage(currentPage - 1)"
                  :disabled="currentPage === 1"
                  class="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <ChevronLeft class="w-4 h-4" />
                </button>

                <span
                  v-for="page in visiblePages"
                  :key="page"
                  @click="goToPage(page)"
                  :class="[
                    'px-4 py-2 rounded-lg cursor-pointer transition-all',
                    page === currentPage
                      ? 'bg-blue-600 dark:bg-blue-700 text-white'
                      : 'border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white',
                  ]"
                >
                  {{ page }}
                </span>

                <button
                  @click="goToPage(currentPage + 1)"
                  :disabled="currentPage === totalPages"
                  class="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <ChevronRight class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 相关链接区域 -->
      <section class="py-12 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
        <div class="container mx-auto px-6">
          <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">相关服务</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <a
                href="/compliance/apply"
                class="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
              >
                <div
                  class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors"
                >
                  <FileCheck class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">企业入驻申请</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">申请加入智能网联汽车监管平台</p>
              </a>

              <a
                href="/login/government"
                class="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
              >
                <div
                  class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:bg-green-200 dark:group-hover:bg-green-800 transition-colors"
                >
                  <Shield class="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">监管部门登录</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">政府监管人员工作台入口</p>
              </a>

              <a
                href="/login/enterprise"
                class="p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
              >
                <div
                  class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:bg-purple-200 dark:group-hover:bg-purple-800 transition-colors"
                >
                  <Building class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">企业用户登录</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">企业数据管理和合规报告</p>
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 dark:bg-gray-950 text-white py-8">
      <div class="container mx-auto px-6">
        <div class="max-w-6xl mx-auto text-center">
          <h3 class="text-xl font-semibold mb-3">智能网联汽车地理信息安全监测平台</h3>
          <p class="text-gray-400 dark:text-gray-500 mb-4">保障智能网联汽车地理信息安全，促进行业健康发展</p>
          <div class="flex justify-center items-center gap-8 text-sm text-gray-400 dark:text-gray-500">
            <span>© 2025 地理信息安全监测平台</span>
            <span>|</span>
            <span>技术支持</span>
            <span>|</span>
            <span>联系我们</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LandingHeader from '@/modules/public/components/LandingHeader.vue'
import {
  Building,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Download,
  ExternalLink,
  Eye,
  FileCheck,
  FileText,
  Search,
  Shield,
} from 'lucide-vue-next'

const router = useRouter()

// 搜索和筛选状态
const searchKeyword = ref('')
const selectedCategory = ref('')
const sortBy = ref('latest')

// 分页状态
const currentPage = ref(1)
const pageSize = 12

// 政策数据类型
interface Policy {
  id: string
  title: string
  category: string
  summary: string
  publishDate: string
  tags: string[]
  views: number
  downloads: number
  downloadUrl?: string
  coverImage?: string
  content: string
}

// Mock 政策数据
const allPolicies = ref<Policy[]>([
  {
    id: 'POL-2025-001',
    title: '关于加强智能网联汽车地理信息安全管理的通知',
    category: '政策法规',
    summary:
      '进一步规范智能网联汽车地理信息采集、处理和使用行为，保障国家地理信息安全，维护国家安全和公共利益，现就有关事项通知如下...',
    publishDate: '2025-01-15',
    tags: ['地理信息安全', '智能网联汽车', '数据管理'],
    views: 2856,
    downloads: 437,
    downloadUrl: '/downloads/policy-001.pdf',
    content: '详细政策内容...',
  },
  {
    id: 'STD-2025-001',
    title: '智能网联汽车地理信息安全技术规范（试行版）',
    category: '标准规范',
    summary:
      '本规范规定了智能网联汽车地理信息安全技术要求、测试方法和评估流程，适用于智能网联汽车相关企业的地理信息安全管理...',
    publishDate: '2025-01-10',
    tags: ['技术规范', '安全标准', '测试方法'],
    views: 1924,
    downloads: 312,
    downloadUrl: '/downloads/standard-001.pdf',
    coverImage: '/images/policy-cover-1.jpg',
    content: '详细规范内容...',
  },
  {
    id: 'NOT-2025-001',
    title: '关于开展智能网联汽车地理信息安全专项检查的公告',
    category: '通知公告',
    summary:
      '为贯彻落实相关法律法规，保障地理信息安全，决定在全国范围内开展智能网联汽车地理信息安全专项检查工作...',
    publishDate: '2025-01-08',
    tags: ['专项检查', '监督管理', '合规要求'],
    views: 3127,
    downloads: 589,
    downloadUrl: '/downloads/notice-001.pdf',
    content: '详细公告内容...',
  },
  {
    id: 'NEWS-2025-001',
    title: '智能网联汽车地理信息安全监测平台正式上线运行',
    category: '新闻动态',
    summary:
      '经过为期半年的建设和测试，智能网联汽车地理信息安全监测平台今日正式上线运行，为行业提供统一的监测和管理服务...',
    publishDate: '2025-01-05',
    tags: ['平台上线', '监测服务', '行业动态'],
    views: 4562,
    downloads: 0,
    coverImage: '/images/policy-cover-2.jpg',
    content: '详细新闻内容...',
  },
  {
    id: 'POL-2024-008',
    title: '智能网联汽车数据安全管理若干规定（试行）',
    category: '政策法规',
    summary:
      '为了规范汽车数据处理活动，保护个人、组织的合法权益，维护国家安全和社会公共利益，促进汽车数据合理开发利用...',
    publishDate: '2024-12-20',
    tags: ['数据安全', '个人信息保护', '合规管理'],
    views: 5231,
    downloads: 762,
    downloadUrl: '/downloads/policy-008.pdf',
    content: '详细政策内容...',
  },
  {
    id: 'STD-2024-003',
    title: '车联网网络安全和数据安全标准体系建设指南',
    category: '标准规范',
    summary:
      '为加快车联网网络安全和数据安全标准化工作，构建完善的车联网安全标准体系，指导相关标准制定...',
    publishDate: '2024-12-15',
    tags: ['标准体系', '网络安全', '建设指南'],
    views: 1876,
    downloads: 294,
    downloadUrl: '/downloads/standard-003.pdf',
    content: '详细标准内容...',
  },
])

// 计算过滤后的政策
const filteredPolicies = computed(() => {
  let result = allPolicies.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (policy) =>
        policy.title.toLowerCase().includes(keyword) ||
        policy.summary.toLowerCase().includes(keyword) ||
        policy.tags.some((tag) => tag.toLowerCase().includes(keyword)),
    )
  }

  // 分类筛选
  if (selectedCategory.value) {
    result = result.filter((policy) => policy.category === selectedCategory.value)
  }

  // 排序
  switch (sortBy.value) {
    case 'latest':
      result.sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())
      break
    case 'title':
      result.sort((a, b) => a.title.localeCompare(b.title))
      break
    case 'category':
      result.sort((a, b) => a.category.localeCompare(b.category))
      break
  }

  return result
})

// 计算分页数据
const totalPages = computed(() => Math.ceil(filteredPolicies.value.length / pageSize))

const pagedPolicies = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return filteredPolicies.value.slice(start, start + pageSize)
})

// 计算可见页码
const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    } else if (current >= total - 3) {
      pages.push(1, '...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1, '...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    }
  }

  return pages
})

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  console.log('搜索条件:', {
    keyword: searchKeyword.value,
    category: selectedCategory.value,
    sortBy: sortBy.value,
  })
}

// 分页跳转
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

// 查看政策详情
const viewPolicy = (policy: Policy) => {
  router.push(`/policy/${policy.id}`)
}

// 下载政策文件
const downloadPolicy = (policy: Policy) => {
  if (policy.downloadUrl) {
    // 创建临时链接进行下载
    const link = document.createElement('a')
    link.href = policy.downloadUrl
    link.download = `${policy.title}.pdf`
    link.click()

    console.log('下载政策:', policy.title)
  }
}

// 获取分类样式
const getCategoryClass = (category: string) => {
  const baseClass = 'px-3 py-1 rounded-full text-sm font-medium'
  switch (category) {
    case '政策法规':
      return `${baseClass} bg-blue-100 text-blue-700`
    case '标准规范':
      return `${baseClass} bg-green-100 text-green-700`
    case '通知公告':
      return `${baseClass} bg-orange-100 text-orange-700`
    case '新闻动态':
      return `${baseClass} bg-purple-100 text-purple-700`
    default:
      return `${baseClass} bg-gray-100 text-gray-700`
  }
}

onMounted(() => {
  console.log('政策法规列表页已加载')
})
</script>

<style scoped>
/* 文本截断样式 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
