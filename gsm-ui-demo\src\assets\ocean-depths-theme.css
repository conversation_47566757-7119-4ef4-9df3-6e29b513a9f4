/**
 * Ocean Depths Theme CSS Variables
 * 深海主题的CSS变量定义，为整个系统提供一致的配色方案
 */

:root {
  /* ===========================================
   * OCEAN DEPTHS THEME COLORS - 深海主题配色
   * =========================================== */

  /* Primary Color Scale - 主色调（深海蓝系） */
  --ocean-primary-50: #bbe1fa; /* 浅蓝 - 最浅色调 */
  --ocean-primary-100: #87ceeb; /* 天空蓝 */
  --ocean-primary-200: #5f9ea0; /* 青灰 - 辅助色 */
  --ocean-primary-300: #4682b4; /* 钢蓝 - 中等色调 */
  --ocean-primary-400: #3282b8; /* 海洋蓝 - 常用色调 */
  --ocean-primary-500: #0f4c75; /* 深海蓝 - 主色调 */
  --ocean-primary-600: #0d3f63; /* 更深的蓝 */
  --ocean-primary-700: #0a334f; /* 深蓝 */
  --ocean-primary-800: #08263b; /* 极深蓝 */
  --ocean-primary-900: #051a27; /* 最深蓝 */

  /* Secondary Color Scale - 辅助色（海绿系） */
  --ocean-secondary-50: #20b2aa; /* 浅海绿 - 最浅色调 */
  --ocean-secondary-100: #2e8b57; /* 海绿 - 中等色调 */
  --ocean-secondary-200: #228b22; /* 森林绿 */
  --ocean-secondary-300: #1e7e34; /* 深绿 */
  --ocean-secondary-400: #1a6d2e; /* 更深绿 */
  --ocean-secondary-500: #155724; /* 深森林绿 */
  --ocean-secondary-600: #0f4419; /* 极深绿 */
  --ocean-secondary-700: #0a2f0f; /* 最深绿 */
  --ocean-secondary-800: #051a08; /* 极暗绿 */
  --ocean-secondary-900: #020c03; /* 黑绿 */

  /* Neutral Color Scale - 中性色（深灰蓝系） */
  --ocean-neutral-50: #f8fafc; /* 白色 */
  --ocean-neutral-100: #f1f5f9; /* 极浅灰 */
  --ocean-neutral-200: #e2e8f0; /* 浅灰 */
  --ocean-neutral-300: #cbd5e1; /* 中浅灰 */
  --ocean-neutral-400: #94a3b8; /* 中灰 */
  --ocean-neutral-500: #64748b; /* 标准灰 */
  --ocean-neutral-600: #475569; /* 深灰 */
  --ocean-neutral-700: #334155; /* 更深灰 */
  --ocean-neutral-800: #1e293b; /* 极深灰 */
  --ocean-neutral-900: #1b262c; /* 深灰蓝 - 主题特色 */

  /* Semantic Colors - 语义化颜色 */
  --ocean-success-light: #20b2aa; /* 成功-浅海绿 */
  --ocean-success-main: #2e8b57; /* 成功-海绿 */
  --ocean-success-dark: #155724; /* 成功-深绿 */

  --ocean-warning-light: #ffd700; /* 警告-金黄 */
  --ocean-warning-main: #ffa500; /* 警告-橙色 */
  --ocean-warning-dark: #ff8c00; /* 警告-深橙 */

  --ocean-error-light: #ff6b6b; /* 错误-珊瑚红 */
  --ocean-error-main: #dc3545; /* 错误-红色 */
  --ocean-error-dark: #c82333; /* 错误-深红 */

  --ocean-info-light: #87ceeb; /* 信息-天空蓝 */
  --ocean-info-main: #4682b4; /* 信息-钢蓝 */
  --ocean-info-dark: #0f4c75; /* 信息-深海蓝 */

  /* Risk Level Colors - 风险等级配色（保持警示性的深海色调） */
  --ocean-risk-critical: #8b2635; /* 严重风险 - 深海红 */
  --ocean-risk-high: #b85450; /* 高风险 - 海洋珊瑚红 */
  --ocean-risk-medium: #d4845a; /* 中风险 - 深海橙 */
  --ocean-risk-low: var(--ocean-info-main); /* 低风险 - 钢蓝 */
  --ocean-risk-minimal: var(--ocean-success-main); /* 极低风险 - 海绿 */

  /* Status Colors - 状态配色（警示色也融入深海调性） */
  --ocean-status-active: var(--ocean-success-main); /* 激活/正常 */
  --ocean-status-inactive: var(--ocean-neutral-500); /* 非激活 */
  --ocean-status-pending: #cc8c5c; /* 待处理 - 深海琥珀 */
  --ocean-status-error: #8b4366; /* 错误 - 深海玫红 */
  --ocean-status-processing: var(--ocean-info-main); /* 处理中 */

  /* Enterprise Type Colors - 企业类型配色 */
  --ocean-enterprise-manufacturer: var(--ocean-primary-500); /* 整车生产企业 */
  --ocean-enterprise-platform: var(--ocean-primary-400); /* 平台运营商 */
  --ocean-enterprise-solution: var(--ocean-secondary-100); /* 智驾方案提供商 */
  --ocean-enterprise-map: var(--ocean-secondary-50); /* 地图服务商 */
  --ocean-enterprise-other: var(--ocean-neutral-600); /* 其他 */

  /* Data Processing Stage Colors - 数据处理阶段配色 */
  --ocean-stage-collection: var(--ocean-primary-500); /* 收集 */
  --ocean-stage-storage: var(--ocean-primary-400); /* 存储 */
  --ocean-stage-transmission: var(--ocean-primary-300); /* 传输 */
  --ocean-stage-processing: var(--ocean-secondary-100); /* 加工 */
  --ocean-stage-provision: var(--ocean-secondary-50); /* 提供 */
  --ocean-stage-publication: var(--ocean-warning-main); /* 公开 */
  --ocean-stage-destruction: var(--ocean-neutral-600); /* 销毁 */

  /* ===========================================
   * COMPONENT SPECIFIC OVERRIDES - 组件特定覆盖
   * =========================================== */

  /* Update Tailwind CSS color mappings */
  --primary: var(--ocean-primary-500);
  --primary-foreground: var(--ocean-neutral-50);

  --secondary: var(--ocean-secondary-100);
  --secondary-foreground: var(--ocean-neutral-50);

  --muted: var(--ocean-neutral-100);
  --muted-foreground: var(--ocean-neutral-600);

  --accent: var(--ocean-primary-200);
  --accent-foreground: var(--ocean-neutral-900);

  --destructive: var(--ocean-error-main);
  --destructive-foreground: var(--ocean-neutral-50);

  --border: var(--ocean-neutral-300);
  --input: var(--ocean-neutral-300);
  --ring: var(--ocean-primary-400);

  --background: var(--ocean-neutral-50);
  --foreground: var(--ocean-neutral-900);

  --card: var(--ocean-neutral-50);
  --card-foreground: var(--ocean-neutral-900);

  --popover: var(--ocean-neutral-50);
  --popover-foreground: var(--ocean-neutral-900);
}

.dark {
  /* Dark Theme Overrides - 暗色主题覆盖 */
  --primary: var(--ocean-primary-400);
  --primary-foreground: var(--ocean-neutral-50);

  --secondary: var(--ocean-secondary-200);
  --secondary-foreground: var(--ocean-neutral-100);

  --muted: var(--ocean-neutral-800);
  --muted-foreground: var(--ocean-neutral-400);

  --accent: var(--ocean-primary-300);
  --accent-foreground: var(--ocean-neutral-100);

  --destructive: var(--ocean-error-light);
  --destructive-foreground: var(--ocean-neutral-50);

  --border: var(--ocean-neutral-700);
  --input: var(--ocean-neutral-700);
  --ring: var(--ocean-primary-300);

  --background: var(--ocean-neutral-900);
  --foreground: var(--ocean-neutral-100);

  --card: var(--ocean-neutral-800);
  --card-foreground: var(--ocean-neutral-100);

  --popover: var(--ocean-neutral-800);
  --popover-foreground: var(--ocean-neutral-100);
}

/* ===========================================
 * UTILITY CLASSES - 工具类
 * =========================================== */

/* Ocean Depths Text Colors */
.text-ocean-primary {
  color: var(--ocean-primary-500);
}
.text-ocean-secondary {
  color: var(--ocean-secondary-100);
}
.text-ocean-success {
  color: var(--ocean-success-main);
}
.text-ocean-warning {
  color: var(--ocean-warning-main);
}
.text-ocean-error {
  color: var(--ocean-error-main);
}
.text-ocean-info {
  color: var(--ocean-info-main);
}
.text-ocean-neutral {
  color: var(--ocean-neutral-500);
}

/* Risk Level Text Colors */
.text-risk-critical {
  color: var(--ocean-risk-critical);
}
.text-risk-high {
  color: var(--ocean-risk-high);
}
.text-risk-medium {
  color: var(--ocean-risk-medium);
}
.text-risk-low {
  color: var(--ocean-risk-low);
}
.text-risk-minimal {
  color: var(--ocean-risk-minimal);
}

/* Status Text Colors */
.text-status-active {
  color: var(--ocean-status-active);
}
.text-status-inactive {
  color: var(--ocean-status-inactive);
}
.text-status-pending {
  color: var(--ocean-status-pending);
}
.text-status-error {
  color: var(--ocean-status-error);
}
.text-status-processing {
  color: var(--ocean-status-processing);
}

/* Ocean Depths Background Colors */
.bg-ocean-primary {
  background-color: var(--ocean-primary-500);
}
.bg-ocean-secondary {
  background-color: var(--ocean-secondary-100);
}
.bg-ocean-success {
  background-color: var(--ocean-success-main);
}
.bg-ocean-warning {
  background-color: var(--ocean-warning-main);
}
.bg-ocean-error {
  background-color: var(--ocean-error-main);
}
.bg-ocean-info {
  background-color: var(--ocean-info-main);
}

/* Risk Level Background Colors */
.bg-risk-critical {
  background-color: var(--ocean-risk-critical);
}
.bg-risk-high {
  background-color: var(--ocean-risk-high);
}
.bg-risk-medium {
  background-color: var(--ocean-risk-medium);
}
.bg-risk-low {
  background-color: var(--ocean-risk-low);
}
.bg-risk-minimal {
  background-color: var(--ocean-risk-minimal);
}

/* Status Background Colors */
.bg-status-active {
  background-color: var(--ocean-status-active);
}
.bg-status-inactive {
  background-color: var(--ocean-status-inactive);
}
.bg-status-pending {
  background-color: var(--ocean-status-pending);
}
.bg-status-error {
  background-color: var(--ocean-status-error);
}
.bg-status-processing {
  background-color: var(--ocean-status-processing);
}

/* Ocean Depths Border Colors */
.border-ocean-primary {
  border-color: var(--ocean-primary-500);
}
.border-ocean-secondary {
  border-color: var(--ocean-secondary-100);
}
.border-ocean-success {
  border-color: var(--ocean-success-main);
}
.border-ocean-warning {
  border-color: var(--ocean-warning-main);
}
.border-ocean-error {
  border-color: var(--ocean-error-main);
}
.border-ocean-info {
  border-color: var(--ocean-info-main);
}

/* ===========================================
 * CHART THEME OVERRIDES - 图表主题覆盖
 * =========================================== */

/* ECharts and other chart libraries should use these colors */
.chart-ocean-depths {
  --chart-color-1: var(--ocean-primary-500); /* 深海蓝 */
  --chart-color-2: var(--ocean-primary-400); /* 海洋蓝 */
  --chart-color-3: var(--ocean-primary-50); /* 浅蓝 */
  --chart-color-4: var(--ocean-neutral-900); /* 深灰蓝 */
  --chart-color-5: var(--ocean-secondary-100); /* 海绿 */
  --chart-color-6: var(--ocean-secondary-50); /* 浅海绿 */
  --chart-color-7: var(--ocean-primary-300); /* 钢蓝 */
  --chart-color-8: var(--ocean-primary-200); /* 青灰 */
}

/* Apply Ocean Depths theme to all charts by default */
[class*='chart'],
[id*='chart'],
.echarts-container {
  /* Chart theme colors */
}

/* ===========================================
 * ANIMATION AND TRANSITIONS - 动画和过渡效果
 * =========================================== */

.ocean-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ocean-hover-primary:hover {
  color: var(--ocean-primary-400);
  transform: translateY(-1px);
}

.ocean-hover-secondary:hover {
  color: var(--ocean-secondary-50);
  transform: translateY(-1px);
}

/* ===========================================
 * RESPONSIVE DESIGN - 响应式设计
 * =========================================== */

@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    /* Dark theme variables when system prefers dark */
    --primary: var(--ocean-primary-400);
    --primary-foreground: var(--ocean-neutral-50);

    --secondary: var(--ocean-secondary-200);
    --secondary-foreground: var(--ocean-neutral-100);

    --muted: var(--ocean-neutral-800);
    --muted-foreground: var(--ocean-neutral-400);

    --accent: var(--ocean-primary-300);
    --accent-foreground: var(--ocean-neutral-100);

    --destructive: var(--ocean-error-light);
    --destructive-foreground: var(--ocean-neutral-50);

    --border: var(--ocean-neutral-700);
    --input: var(--ocean-neutral-700);
    --ring: var(--ocean-primary-300);

    --background: var(--ocean-neutral-900);
    --foreground: var(--ocean-neutral-100);

    --card: var(--ocean-neutral-800);
    --card-foreground: var(--ocean-neutral-100);

    --popover: var(--ocean-neutral-800);
    --popover-foreground: var(--ocean-neutral-100);
  }
}

/* ===========================================
 * ACCESSIBILITY - 可访问性
 * =========================================== */

@media (prefers-reduced-motion: reduce) {
  .ocean-transition {
    transition: none;
  }

  .animate-pulse {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --ocean-primary-500: #003d5c;
    --ocean-error-main: #b91c1c;
    --ocean-success-main: #166534;
    --ocean-warning-main: #d97706;
  }
}
