<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理系统角色和权限分配，支持角色创建、权限配置和用户分配
        </p>
      </div>
      <Button @click="handleCreateRole" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增角色
      </Button>
    </div>

    <!-- 角色列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>角色列表</span>
          <Badge variant="outline"> 共 {{ filteredRoles.length }} 个角色 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <!-- 筛选条件 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>角色名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>权限数量</TableHead>
                <TableHead>用户数量</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead class="w-[140px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedRoles.length === 0">
                <TableCell :colspan="8" class="h-24 text-center text-muted-foreground">
                  暂无角色数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(role, index) in pagedRoles"
                :key="role.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component :is="getRoleIcon(role.name)" class="w-4 h-4 text-primary" />
                    <div>
                      <div class="font-medium">{{ role.name }}</div>
                      <div class="text-xs text-muted-foreground">ID: {{ role.id }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="max-w-[300px] text-sm text-muted-foreground">
                    {{ role.description }}
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-1">
                    <Badge variant="outline" class="text-xs">
                      {{ role.permissionCount }}
                    </Badge>
                    <Button variant="ghost" size="sm" @click="handleViewPermissions(role.id)">
                      <Eye class="w-3 h-3" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary" class="text-xs"> {{ role.userCount }} 人 </Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(role.status)">{{ role.status }}</Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ role.createdAt }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleEdit(role.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑角色
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleConfigPermissions(role.id)">
                        <Shield class="w-4 h-4 mr-2" />
                        配置权限
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleAssignUsers(role.id)">
                        <Users class="w-4 h-4 mr-2" />
                        分配用户
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="role.status === '启用'"
                        @click="handleDisable(role.id)"
                        class="text-orange-600"
                      >
                        <XCircle class="w-4 h-4 mr-2" />
                        禁用角色
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="role.status === '禁用'"
                        @click="handleEnable(role.id)"
                        class="text-green-600"
                      >
                        <CheckCircle class="w-4 h-4 mr-2" />
                        启用角色
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        @click="handleDelete(role.id)"
                        class="text-red-600"
                        :disabled="role.userCount > 0"
                      >
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除角色
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredRoles.length) }} 条， 共
            {{ filteredRoles.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 权限配置对话框 -->
    <Sheet :open="showPermissionDialog" @update:open="showPermissionDialog = $event">
      <SheetContent
        side="right"
        class="z-[60] w-[66vw] min-w-[640px] max-w-[1100px] max-h-[100vh] overflow-y-auto"
      >
        <SheetHeader>
          <SheetTitle>权限配置 - {{ currentRole?.name }}</SheetTitle>
          <SheetDescription> 为角色配置系统功能权限，支持按模块分组管理 </SheetDescription>
        </SheetHeader>

        <div class="space-y-6 py-4">
          <!-- 权限搜索 -->
          <div class="flex items-center gap-2">
            <Search class="w-4 h-4 text-muted-foreground" />
            <Input v-model="permissionSearch" placeholder="搜索权限..." class="max-w-sm" />
          </div>

          <!-- 权限树 -->
          <div v-for="module in filteredPermissions" :key="module.id" class="space-y-2">
            <div class="flex items-center gap-2 py-2 border-b">
              <Checkbox
                :id="`module-${module.id}`"
                :checked="isModuleChecked(module)"
                @update:checked="handleModuleCheck(module, $event)"
              />
              <Label :for="`module-${module.id}`" class="font-medium">
                {{ module.name }}
              </Label>
              <Badge variant="outline" class="text-xs ml-2">
                {{ module.permissions?.length || 0 }} 项
              </Badge>
            </div>
            <div class="ml-6 space-y-2">
              <div
                v-for="permission in module.permissions"
                :key="permission.id"
                class="flex items-center gap-2 py-1"
              >
                <Checkbox
                  :id="`perm-${permission.id}`"
                  :checked="selectedPermissions.includes(permission.id)"
                  @update:checked="handlePermissionCheck(permission.id, $event)"
                />
                <Label :for="`perm-${permission.id}`" class="text-sm">
                  {{ permission.name }}
                </Label>
                <span class="text-xs text-muted-foreground ml-2">
                  {{ permission.code }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showPermissionDialog = false">取消</Button>
          <Button @click="handleSavePermissions">保存权限</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>

    <!-- 用户分配对话框 -->
    <Sheet :open="showUserDialog" @update:open="showUserDialog = $event">
      <SheetContent side="right" class="z-[60] w-[50vw] min-w-[520px] max-w-[900px]">
        <SheetHeader>
          <SheetTitle>用户分配 - {{ currentRole?.name }}</SheetTitle>
          <SheetDescription> 为角色分配或移除用户，支持批量操作 </SheetDescription>
        </SheetHeader>

        <div class="space-y-4 py-4">
          <!-- 用户搜索 -->
          <div class="flex items-center gap-2">
            <Search class="w-4 h-4 text-muted-foreground" />
            <Input v-model="userSearch" placeholder="搜索用户..." class="flex-1" />
          </div>

          <!-- 用户列表 -->
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <div
              v-for="user in filteredUsers"
              :key="user.id"
              class="flex items-center justify-between p-2 border rounded hover:bg-muted/50"
            >
              <div class="flex items-center gap-2">
                <Checkbox
                  :id="`user-${user.id}`"
                  :checked="selectedUsers.includes(user.id)"
                  @update:checked="handleUserCheck(user.id, $event)"
                />
                <div>
                  <div class="font-medium text-sm">{{ user.realName }}</div>
                  <div class="text-xs text-muted-foreground">
                    {{ user.username }} - {{ user.organization }}
                  </div>
                </div>
              </div>
              <Badge :variant="user.userType.includes('政府') ? 'default' : 'outline'">
                {{ user.userType }}
              </Badge>
            </div>
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showUserDialog = false">取消</Button>
          <Button @click="handleSaveUsers">保存分配</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  CheckCircle,
  Edit,
  Eye,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  ShieldCheck,
  Trash2,
  Users,
  XCircle,
  Crown,
  UserCheck,
  Settings,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  Sheet,
  SheetContent,
} from '@/components/ui/sheet'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type RoleStatus = '启用' | '禁用'

interface Role {
  id: number
  name: string
  description: string
  permissionCount: number
  userCount: number
  status: RoleStatus
  createdAt: string
}

interface Permission {
  id: number
  name: string
  code: string
  parentId?: number
}

interface PermissionModule {
  id: number
  name: string
  permissions: Permission[]
}

interface User {
  id: string
  username: string
  realName: string
  userType: string
  organization: string
}

// 筛选条件
const filters = ref({
  name: '',
  status: 'ALL' as 'ALL' | RoleStatus,
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '角色名称',
    type: 'input',
    placeholder: '请输入角色名称',
  },
  {
    key: 'status',
    label: '角色状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '启用', value: '启用' },
      { label: '禁用', value: '禁用' },
    ],
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const showPermissionDialog = ref(false)
const showUserDialog = ref(false)
const currentRole = ref<Role | null>(null)
const permissionSearch = ref('')
const userSearch = ref('')
const selectedPermissions = ref<number[]>([])
const selectedUsers = ref<string[]>([])

// Mock 数据
const roles = ref<Role[]>([
  {
    id: 1,
    name: '超级管理员',
    description: '系统最高权限管理员，拥有所有功能权限',
    permissionCount: 45,
    userCount: 2,
    status: '启用',
    createdAt: '2025-01-01 00:00:00',
  },
  {
    id: 2,
    name: '安全监管员',
    description: '负责安全风险监测和事件处理，具备监测相关功能权限',
    permissionCount: 28,
    userCount: 8,
    status: '启用',
    createdAt: '2025-01-15 09:30:00',
  },
  {
    id: 3,
    name: '审核专员',
    description: '负责企业备案审核和注册信息管理，具备审核相关权限',
    permissionCount: 15,
    userCount: 5,
    status: '启用',
    createdAt: '2025-02-01 14:20:00',
  },
  {
    id: 4,
    name: '监督检查员',
    description: '负责现场检查和合规监督，具备检查相关功能权限',
    permissionCount: 12,
    userCount: 3,
    status: '启用',
    createdAt: '2025-02-15 11:45:00',
  },
  {
    id: 5,
    name: '数据分析员',
    description: '负责数据统计分析和报表生成，具备数据查看和导出权限',
    permissionCount: 8,
    userCount: 0,
    status: '禁用',
    createdAt: '2025-03-01 16:00:00',
  },
])

const permissions = ref<PermissionModule[]>([
  {
    id: 1,
    name: '综合概览',
    permissions: [
      { id: 101, name: '政府端首页', code: 'gov:dashboard:view' },
      { id: 102, name: '统计数据查看', code: 'gov:dashboard:stats' },
    ],
  },
  {
    id: 2,
    name: '备案审核',
    permissions: [
      { id: 201, name: '注册信息管理', code: 'gov:registration:manage' },
      { id: 202, name: '注册信息查看', code: 'gov:registration:view' },
      { id: 203, name: '注册信息审批', code: 'gov:registration:approve' },
      { id: 204, name: '备案审批管理', code: 'gov:approval:manage' },
      { id: 205, name: '审批任务处理', code: 'gov:approval:process' },
    ],
  },
  {
    id: 3,
    name: '实时监测',
    permissions: [
      { id: 301, name: '车端风险管理', code: 'gov:risk:vehicle:manage' },
      { id: 302, name: '云端风险管理', code: 'gov:risk:cloud:manage' },
      { id: 303, name: '车端事件管理', code: 'gov:event:vehicle:manage' },
      { id: 304, name: '云端事件管理', code: 'gov:event:cloud:manage' },
      { id: 305, name: '车端应急溯源', code: 'gov:trace:vehicle:manage' },
      { id: 306, name: '云端应急溯源', code: 'gov:trace:cloud:manage' },
      { id: 307, name: '车端操作信息', code: 'gov:operation:vehicle:view' },
      { id: 308, name: '云端操作信息', code: 'gov:operation:cloud:view' },
    ],
  },
  {
    id: 4,
    name: '系统管理',
    permissions: [
      { id: 401, name: '用户信息管理', code: 'gov:system:users:manage' },
      { id: 402, name: '角色权限管理', code: 'gov:system:roles:manage' },
      { id: 403, name: '区域管理', code: 'gov:system:regions:manage' },
      { id: 404, name: '风险规则管理', code: 'gov:system:rules:manage' },
      { id: 405, name: '日志管理', code: 'gov:system:logs:view' },
      { id: 406, name: '系统监控', code: 'gov:system:monitoring:view' },
      { id: 407, name: '信息发布管理', code: 'gov:system:information:manage' },
    ],
  },
])

const users = ref<User[]>([
  {
    id: 'U-2025-001',
    username: 'gov_admin',
    realName: '李管理员',
    userType: '政府管理员',
    organization: '国家测绘地理信息局',
  },
  {
    id: 'U-2025-002',
    username: 'supervisor_01',
    realName: '王监管员',
    userType: '政府监管员',
    organization: '北京市测绘院',
  },
  {
    id: 'U-2025-006',
    username: 'auditor_01',
    realName: '赵审核员',
    userType: '政府监管员',
    organization: '国家测绘地理信息局',
  },
])

// 计算属性
const filteredRoles = computed(() => {
  return roles.value.filter((role) => {
    if (filters.value.name && !role.name.includes(filters.value.name)) {
      return false
    }
    if (filters.value.status !== 'ALL' && role.status !== filters.value.status) {
      return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredRoles.value.length / pageSize.value)),
)

const pagedRoles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredRoles.value.slice(start, start + pageSize.value)
})

const filteredPermissions = computed(() => {
  if (!permissionSearch.value) return permissions.value

  return permissions.value
    .map((module) => ({
      ...module,
      permissions: module.permissions.filter(
        (perm) =>
          perm.name.includes(permissionSearch.value) ||
          perm.code.toLowerCase().includes(permissionSearch.value.toLowerCase()),
      ),
    }))
    .filter((module) => module.permissions.length > 0)
})

const filteredUsers = computed(() => {
  if (!userSearch.value) return users.value

  return users.value.filter(
    (user) =>
      user.realName.includes(userSearch.value) ||
      user.username.includes(userSearch.value) ||
      user.organization.includes(userSearch.value),
  )
})

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    name: '',
    status: 'ALL',
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = ['序号', '角色名称', '描述', '权限数量', '用户数量', '状态', '创建时间']
  const rows = filteredRoles.value.map((role, index) => [
    (index + 1).toString(),
    role.name,
    role.description,
    role.permissionCount.toString(),
    role.userCount.toString(),
    role.status,
    role.createdAt,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `角色管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleCreateRole = () => {
  console.log('创建新角色')
}

const handleEdit = (id: number) => {
  console.log('编辑角色:', id)
}

const handleConfigPermissions = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    currentRole.value = role
    selectedPermissions.value = [101, 102, 201, 202] // Mock selected permissions
    showPermissionDialog.value = true
  }
}

const handleAssignUsers = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    currentRole.value = role
    selectedUsers.value = ['U-2025-001', 'U-2025-002'] // Mock selected users
    showUserDialog.value = true
  }
}

const handleViewPermissions = (id: number) => {
  console.log('查看权限:', id)
}

const handleEnable = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    role.status = '启用'
  }
}

const handleDisable = (id: number) => {
  const role = roles.value.find((r) => r.id === id)
  if (role) {
    role.status = '禁用'
  }
}

const handleDelete = (id: number) => {
  const index = roles.value.findIndex((r) => r.id === id)
  if (index > -1) {
    roles.value.splice(index, 1)
  }
}

// 权限相关
const isModuleChecked = (module: PermissionModule) => {
  const modulePermIds = module.permissions.map((p) => p.id)
  return modulePermIds.every((id) => selectedPermissions.value.includes(id))
}

const handleModuleCheck = (module: PermissionModule, checked: boolean) => {
  const modulePermIds = module.permissions.map((p) => p.id)
  if (checked) {
    selectedPermissions.value = [...new Set([...selectedPermissions.value, ...modulePermIds])]
  } else {
    selectedPermissions.value = selectedPermissions.value.filter(
      (id) => !modulePermIds.includes(id),
    )
  }
}

const handlePermissionCheck = (id: number, checked: boolean) => {
  if (checked) {
    if (!selectedPermissions.value.includes(id)) {
      selectedPermissions.value.push(id)
    }
  } else {
    selectedPermissions.value = selectedPermissions.value.filter((permId) => permId !== id)
  }
}

const handleUserCheck = (id: string, checked: boolean) => {
  if (checked) {
    if (!selectedUsers.value.includes(id)) {
      selectedUsers.value.push(id)
    }
  } else {
    selectedUsers.value = selectedUsers.value.filter((userId) => userId !== id)
  }
}

const handleSavePermissions = () => {
  console.log('保存权限配置:', selectedPermissions.value)
  showPermissionDialog.value = false
}

const handleSaveUsers = () => {
  console.log('保存用户分配:', selectedUsers.value)
  showUserDialog.value = false
}

// 样式函数
const getStatusVariant = (status: RoleStatus) => {
  switch (status) {
    case '启用':
      return 'default'
    case '禁用':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getRoleIcon = (roleName: string) => {
  if (roleName.includes('超级管理员')) return Crown
  if (roleName.includes('安全监管员')) return Shield
  if (roleName.includes('审核专员')) return UserCheck
  if (roleName.includes('监督检查员')) return ShieldCheck
  return Settings
}
</script>
