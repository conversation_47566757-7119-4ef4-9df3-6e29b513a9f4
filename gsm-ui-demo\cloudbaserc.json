{
  "envId": "cloud1-0gc8cbzg3efd6a99",
  "framework": {
    "name": "vite-spa",
    "plugins": {
      "frontend": {
        "use": "@cloudbase/framework-plugin-website",
        "inputs": {
          "outputPath": "dist",
          "cloudPath": "/",
          "ignore": [".git", ".github", "node_modules", "cloudbaserc.json"],
          "envVariables": {},
<<<<<<< HEAD
          "rewrites": [
            { "source": "/**", "destination": "/index.html" },
            { "source": "**", "destination": "/index.html" }
=======
          "cleanUrls": true,
          "trailingSlash": false,
          "redirects": [
            {
              "source": "/**",
              "destination": "/index.html",
              "type": 200
            }
          ],
          "rewrites": [
            {
              "source": "/government-screen",
              "destination": "/iframe/government-screen.html"
            }
>>>>>>> 91736b6123c129d456d0d99bd4255f9e4b8d8c64
          ],
          "headers": [
            {
              "source": "**/*.@(js|css)",
              "headers": [{ "key": "Cache-Control", "value": "public, max-age=600, immutable" }]
            },
            {
              "source": "**/*.@(png|jpg|jpeg|gif|svg|webp|ico)",
              "headers": [{ "key": "Cache-Control", "value": "public, max-age=600, immutable" }]
            },
            {
              "source": "**/*.@(woff|woff2|ttf|otf)",
              "headers": [
                { "key": "Cache-Control", "value": "public, max-age=31536000, immutable" }
              ]
            },
            {
              "source": "**/*.@(json|xml|txt|csv)",
              "headers": [
                { "key": "Cache-Control", "value": "public, max-age=600, must-revalidate" }
              ]
            },
            {
              "source": "**/*.map",
              "headers": [
                { "key": "Cache-Control", "value": "public, max-age=3600, must-revalidate" }
              ]
            },
            {
              "source": "**/*.html",
              "headers": [
                { "key": "Cache-Control", "value": "no-cache, no-store, must-revalidate" }
              ]
            },
            {
              "source": "index.html",
              "headers": [
                { "key": "Cache-Control", "value": "no-cache, no-store, must-revalidate" }
              ]
            }
          ]
        }
      }
    }
  },
  "functions": [],
  "region": "ap-shanghai"
}
