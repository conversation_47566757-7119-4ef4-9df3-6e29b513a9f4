{"envId": "cloud1-0gc8cbzg3efd6a99", "framework": {"name": "vite-spa", "plugins": {"frontend": {"use": "@cloudbase/framework-plugin-website", "inputs": {"outputPath": "dist", "cloudPath": "/", "ignore": [".git", ".github", "node_modules", "cloudbaserc.json"], "envVariables": {}, "cleanUrls": true, "trailingSlash": false, "redirects": [{"source": "/**", "destination": "/index.html", "type": 200}], "rewrites": [{"source": "/government-screen", "destination": "/iframe/government-screen.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "**/*.@(png|jpg|jpeg|gif|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, must-revalidate"}]}, {"source": "**/*.@(woff|woff2|ttf|otf)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(json|xml|txt|csv)", "headers": [{"key": "Cache-Control", "value": "no-cache, must-revalidate"}]}, {"source": "**/*.map", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}]}}}}, "functions": [], "region": "ap-shanghai"}