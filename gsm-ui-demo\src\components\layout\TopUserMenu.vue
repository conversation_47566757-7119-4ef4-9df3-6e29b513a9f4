<template>
  <div class="top-user-menu">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <button class="user-trigger" type="button" aria-label="用户菜单">
          <User class="h-4 w-4" />
        </button>
      </DropdownMenuTrigger>

      <!-- 下拉内容保留头像与信息 -->
      <DropdownMenuContent class="z-[60] w-56 rounded-lg" align="end" :sideOffset="8">
        <DropdownMenuLabel class="p-0 font-normal">
          <div class="user-card">
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage :src="user.avatar" :alt="user.name" />
              <AvatarFallback class="rounded-lg">
                <User v-if="!userInitials || userInitials === 'U'" class="h-4 w-4" />
                <span v-else>{{ userInitials }}</span>
              </AvatarFallback>
            </Avatar>
            <div class="info">
              <span class="name">{{ user.name }}</span>
              <span class="email">{{ user.email }}</span>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem class="cursor-pointer" @click="goAccount"> 个人中心 </DropdownMenuItem>
        <DropdownMenuItem class="cursor-pointer" @click="goSettings"> 设置 </DropdownMenuItem>
        <DropdownMenuItem class="cursor-pointer" @click="goActivate">
          个人实名认证
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem class="cursor-pointer text-red-600" @click="logout">
          退出登录
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { User } from 'lucide-vue-next'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

const router = useRouter()

const user = ref({
  name: '管理员',
  email: '<EMAIL>',
  avatar: '',
})

onMounted(() => {
  const name = localStorage.getItem('user_name') || user.value.name
  const email = localStorage.getItem('user_email') || user.value.email
  const avatar = localStorage.getItem('user_avatar') || user.value.avatar
  user.value = { name, email, avatar }
})

const userInitials = computed(() => {
  if (!user.value.name) return 'U'
  const s = user.value.name.trim()
  return s.length > 2 ? s.slice(-2) : s
})

function goAccount() {
  // 若后续提供个人中心路由，可替换为实际路径
  router.push('/app/account')
}

function goSettings() {
  router.push('/app/settings')
}

function goActivate() {
  router.push({ name: 'AuthActivate' })
}

function logout() {
  localStorage.removeItem('auth_token')
  router.push({ name: 'Login' })
}
</script>

<style scoped>
.top-user-menu {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.user-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 2rem;
  width: 2rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  background: transparent;
  transition: all 0.2s ease;
}

.user-trigger:hover {
  background: rgba(148, 163, 184, 0.12);
}

:global(html:not(.dark)) .user-trigger:hover {
  background: rgba(15, 23, 42, 0.06);
}

.user-card {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.5rem;
}

.info {
  display: grid;
  text-align: left;
  line-height: 1.2;
}

.name {
  font-weight: 600;
  font-size: 0.875rem;
}

.email {
  font-size: 0.75rem;
  opacity: 0.8;
}
</style>
