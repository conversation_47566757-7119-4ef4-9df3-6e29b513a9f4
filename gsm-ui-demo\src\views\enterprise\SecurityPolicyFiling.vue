<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          请填写企业数据安全管理制度相关信息，包含组织架构、分类分级、制度体系等内容
        </p>
      </div>
      <Badge variant="outline" class="text-sm"> 步骤 3 / 5 </Badge>
    </div>

    <!-- 进度条 -->
    <div class="w-full bg-muted rounded-full h-2">
      <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 60%"></div>
    </div>

    <!-- 表单内容 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Shield class="w-5 h-5" />
          数据安全防控措施 - 制度
        </CardTitle>
        <CardDescription> 企业数据安全管理相关制度信息，确保符合相关法规要求 </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-8">
          <!-- 组织架构 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg">组织架构</CardTitle>
              <CardDescription>数据安全管理的组织架构信息</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="space-y-2">
                  <Label for="securityDepartment" class="text-base font-semibold">
                    数据安全负责部门 <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="securityDepartment"
                    v-model="formData.organization.department"
                    placeholder="请输入部门名称"
                    :class="{ 'border-red-500': errors.organization_department }"
                  />
                  <p v-if="errors.organization_department" class="text-sm text-red-500">
                    {{ errors.organization_department }}
                  </p>
                </div>
                <div class="space-y-2">
                  <Label for="securityManager" class="text-base font-semibold">
                    负责人 <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="securityManager"
                    v-model="formData.organization.manager"
                    placeholder="请输入负责人姓名"
                    :class="{ 'border-red-500': errors.organization_manager }"
                  />
                  <p v-if="errors.organization_manager" class="text-sm text-red-500">
                    {{ errors.organization_manager }}
                  </p>
                </div>
                <div class="space-y-2">
                  <Label for="managerPosition" class="text-base font-semibold">
                    职位 <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="managerPosition"
                    v-model="formData.organization.position"
                    placeholder="请输入职位"
                    :class="{ 'border-red-500': errors.organization_position }"
                  />
                  <p v-if="errors.organization_position" class="text-sm text-red-500">
                    {{ errors.organization_position }}
                  </p>
                </div>
              </div>

              <div class="space-y-2">
                <Label class="text-base font-semibold">
                  组织架构管理制度 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.organization.uploadedFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('organization')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="organizationFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('organization', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.organization.uploadedFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.organization.uploadedFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('organization')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.organization_file" class="text-sm text-red-500">
                  {{ errors.organization_file }}
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 数据分类分级管理 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg">数据分类分级管理</CardTitle>
              <CardDescription>数据分类分级管理制度和核心数据目录</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-2">
                <Label class="text-base font-semibold">
                  分类分级管理制度 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.classification.policyFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('classification')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="classificationFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('classification', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.classification.policyFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.classification.policyFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('classification')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.classification_policy" class="text-sm text-red-500">
                  {{ errors.classification_policy }}
                </p>
              </div>

              <div class="space-y-2">
                <Label class="text-base font-semibold"> 重要核心数据目录（可选） </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.classification.catalogFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX、XLS、XLSX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('catalog')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="catalogFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx,.xls,.xlsx"
                      class="hidden"
                      @change="handleFileUpload('catalog', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.classification.catalogFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.classification.catalogFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('catalog')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p class="text-xs text-muted-foreground">
                  根据《数据安全法》要求，重要数据须建立数据目录，明确数据来源、类型、数量、保护要求等
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 制度体系 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg">制度体系</CardTitle>
              <CardDescription>数据处理各阶段及日志管理相关制度文件</CardDescription>
            </CardHeader>
            <CardContent class="space-y-6">
              <!-- 数据处理阶段制度 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div v-for="stage in dataStages" :key="stage.key" class="space-y-2">
                  <Label class="text-base font-semibold">
                    {{ stage.name }}制度 <span class="text-red-500">*</span>
                  </Label>
                  <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-3">
                    <div v-if="!formData.policies[stage.key]" class="text-center">
                      <Upload class="w-6 h-6 mx-auto text-muted-foreground mb-1" />
                      <p class="text-xs text-muted-foreground mb-2">上传{{ stage.name }}制度文件</p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        @click="triggerFileUpload(stage.key)"
                      >
                        选择文件
                      </Button>
                      <input
                        :ref="(el) => (policyFileInputs[stage.key] = el as HTMLInputElement | null)"
                        type="file"
                        accept=".pdf,.doc,.docx"
                        class="hidden"
                        @change="handleFileUpload(stage.key, $event)"
                      />
                    </div>
                    <div v-else class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <FileText class="w-5 h-5 text-blue-500" />
                        <div>
                          <p class="text-xs font-medium">{{ formData.policies[stage.key].name }}</p>
                          <p class="text-xs text-muted-foreground">
                            {{ formatFileSize(formData.policies[stage.key].size) }}
                          </p>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        @click="removeFile(stage.key)"
                      >
                        <X class="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <p v-if="errors[`policy_${stage.key}`]" class="text-sm text-red-500">
                    {{ errors[`policy_${stage.key}`] }}
                  </p>
                </div>
              </div>

              <!-- 日志管理制度 -->
              <div class="space-y-2">
                <Label class="text-base font-semibold">
                  日志管理制度 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.policies.logging" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('logging')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="loggingFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('logging', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">{{ formData.policies.logging.name }}</p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.policies.logging.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('logging')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.policy_logging" class="text-sm text-red-500">
                  {{ errors.policy_logging }}
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 安全风险评估 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg">安全风险评估</CardTitle>
              <CardDescription>数据安全风险评估方式及相关制度文件</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-3">
                <Label class="text-base font-semibold">
                  评估方式 <span class="text-red-500">*</span>
                </Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="self-assessment"
                      v-model:checked="formData.riskAssessment.selfAssessment"
                    />
                    <Label for="self-assessment" class="text-sm">自评估</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="third-party"
                      v-model:checked="formData.riskAssessment.thirdParty"
                    />
                    <Label for="third-party" class="text-sm">第三方评估</Label>
                  </div>
                </div>
                <p v-if="errors.assessment_method" class="text-sm text-red-500">
                  {{ errors.assessment_method }}
                </p>
              </div>

              <!-- 第三方评估信息 -->
              <div
                v-if="formData.riskAssessment.thirdParty"
                class="space-y-4 p-4 bg-muted rounded-lg"
              >
                <h4 class="font-medium">第三方评估机构信息</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div class="space-y-2">
                    <Label for="instituteName" class="text-base font-semibold">
                      机构名称 <span class="text-red-500">*</span>
                    </Label>
                    <Input
                      id="instituteName"
                      v-model="formData.riskAssessment.instituteName"
                      placeholder="请输入机构名称"
                      :class="{ 'border-red-500': errors.institute_name }"
                    />
                    <p v-if="errors.institute_name" class="text-sm text-red-500">
                      {{ errors.institute_name }}
                    </p>
                  </div>
                  <div class="space-y-2">
                    <Label for="instituteCode" class="text-base font-semibold">
                      信用代码 <span class="text-red-500">*</span>
                    </Label>
                    <Input
                      id="instituteCode"
                      v-model="formData.riskAssessment.instituteCode"
                      placeholder="请输入统一社会信用代码"
                      maxlength="18"
                      :class="{ 'border-red-500': errors.institute_code }"
                    />
                    <p v-if="errors.institute_code" class="text-sm text-red-500">
                      {{ errors.institute_code }}
                    </p>
                  </div>
                  <div class="space-y-2">
                    <Label for="instituteQualification" class="text-base font-semibold">
                      机构资质 <span class="text-red-500">*</span>
                    </Label>
                    <Input
                      id="instituteQualification"
                      v-model="formData.riskAssessment.instituteQualification"
                      placeholder="请输入资质信息"
                      :class="{ 'border-red-500': errors.institute_qualification }"
                    />
                    <p v-if="errors.institute_qualification" class="text-sm text-red-500">
                      {{ errors.institute_qualification }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- 评估制度文件 -->
              <div class="space-y-2">
                <Label class="text-base font-semibold">
                  评估制度 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.riskAssessment.policyFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('assessment-policy')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="assessmentPolicyFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('assessment-policy', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.riskAssessment.policyFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.riskAssessment.policyFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('assessment-policy')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.assessment_policy" class="text-sm text-red-500">
                  {{ errors.assessment_policy }}
                </p>
              </div>

              <!-- 评估报告（可选） -->
              <div class="space-y-2">
                <Label class="text-base font-semibold"> 评估报告（可选） </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.riskAssessment.reportFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 20MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('assessment-report')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="assessmentReportFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('assessment-report', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.riskAssessment.reportFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.riskAssessment.reportFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('assessment-report')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 监测预警与应急管理 -->
          <Card class="relative">
            <div
              class="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-slate-500 to-slate-400 rounded-full"
            ></div>
            <CardHeader>
              <CardTitle class="text-lg">监测预警与应急管理</CardTitle>
              <CardDescription>监测预警机制及应急预案管理制度</CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-2">
                <Label class="text-base font-semibold">
                  监测预警机制 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.emergency.monitoringFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('monitoring')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="monitoringFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('monitoring', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.emergency.monitoringFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.emergency.monitoringFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('monitoring')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.monitoring_file" class="text-sm text-red-500">
                  {{ errors.monitoring_file }}
                </p>
              </div>

              <div class="space-y-2">
                <Label class="text-base font-semibold">
                  应急预案及管理制度 <span class="text-red-500">*</span>
                </Label>
                <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
                  <div v-if="!formData.emergency.emergencyFile" class="text-center">
                    <Upload class="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p class="text-sm text-muted-foreground mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-xs text-muted-foreground mb-3">
                      支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="triggerFileUpload('emergency')"
                    >
                      选择文件
                    </Button>
                    <input
                      ref="emergencyFileInput"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      class="hidden"
                      @change="handleFileUpload('emergency', $event)"
                    />
                  </div>
                  <div v-else class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <FileText class="w-6 h-6 text-blue-500" />
                      <div>
                        <p class="text-base font-semibold">
                          {{ formData.emergency.emergencyFile.name }}
                        </p>
                        <p class="text-xs text-muted-foreground">
                          {{ formatFileSize(formData.emergency.emergencyFile.size) }}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      @click="removeFile('emergency')"
                    >
                      <X class="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <p v-if="errors.emergency_file" class="text-sm text-red-500">
                  {{ errors.emergency_file }}
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" @click="handlePrevious">
              <ChevronLeft class="w-4 h-4 mr-2" />
              上一步
            </Button>
            <div class="flex gap-2">
              <Button type="button" variant="outline" @click="handleSave">
                <Save class="w-4 h-4 mr-2" />
                保存（暂存）
              </Button>
              <Button type="submit" @click="handleNext">
                下一步
                <ChevronRight class="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 保存提示弹窗 -->
    <Dialog v-model:open="saveDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>保存成功</DialogTitle>
          <DialogDescription>
            您的数据安全防控措施制度信息已保存，可稍后继续填报。
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end">
          <Button @click="saveDialogOpen = false">确定</Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ChevronLeft, ChevronRight, FileText, Save, Shield, Upload, X } from 'lucide-vue-next'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const router = useRouter()

// 数据处理阶段
const dataStages = [
  { key: 'collection', name: '收集' },
  { key: 'storage', name: '存储' },
  { key: 'processing', name: '加工' },
  { key: 'provision', name: '提供' },
  { key: 'disclosure', name: '公开' },
  { key: 'destruction', name: '销毁' },
]

// 表单数据
const formData = reactive({
  organization: {
    department: '',
    manager: '',
    position: '',
    uploadedFile: null as File | null,
  },
  classification: {
    policyFile: null as File | null,
    catalogFile: null as File | null,
  },
  policies: {
    collection: null as File | null,
    storage: null as File | null,
    processing: null as File | null,
    provision: null as File | null,
    disclosure: null as File | null,
    destruction: null as File | null,
    logging: null as File | null,
  },
  riskAssessment: {
    selfAssessment: false,
    thirdParty: false,
    instituteName: '',
    instituteCode: '',
    instituteQualification: '',
    policyFile: null as File | null,
    reportFile: null as File | null,
  },
  emergency: {
    monitoringFile: null as File | null,
    emergencyFile: null as File | null,
  },
})

// 表单验证错误
const errors = reactive<Record<string, string>>({})

// 其他状态
const saveDialogOpen = ref(false)

// 文件输入引用
const organizationFileInput = ref<HTMLInputElement>()
const classificationFileInput = ref<HTMLInputElement>()
const catalogFileInput = ref<HTMLInputElement>()
const policyFileInputs = ref<Record<string, HTMLInputElement | null>>({})
const loggingFileInput = ref<HTMLInputElement>()
const assessmentPolicyFileInput = ref<HTMLInputElement>()
const assessmentReportFileInput = ref<HTMLInputElement>()
const monitoringFileInput = ref<HTMLInputElement>()
const emergencyFileInput = ref<HTMLInputElement>()

// 文件上传处理
const triggerFileUpload = (type: string) => {
  switch (type) {
    case 'organization':
      organizationFileInput.value?.click()
      break
    case 'classification':
      classificationFileInput.value?.click()
      break
    case 'catalog':
      catalogFileInput.value?.click()
      break
    case 'logging':
      loggingFileInput.value?.click()
      break
    case 'assessment-policy':
      assessmentPolicyFileInput.value?.click()
      break
    case 'assessment-report':
      assessmentReportFileInput.value?.click()
      break
    case 'monitoring':
      monitoringFileInput.value?.click()
      break
    case 'emergency':
      emergencyFileInput.value?.click()
      break
    default:
      // 数据处理阶段制度文件
      policyFileInputs.value[type]?.click()
      break
  }
}

const handleFileUpload = (type: string, event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 验证文件类型和大小
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ]
    if (type === 'catalog') {
      allowedTypes.push(
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      )
    }

    const maxSize = type === 'assessment-report' ? 20 * 1024 * 1024 : 10 * 1024 * 1024 // 20MB or 10MB

    if (!allowedTypes.includes(file.type)) {
      const errorKey = getErrorKey(type)
      errors[errorKey] = '文件格式不支持，请上传正确格式的文件'
      return
    }

    if (file.size > maxSize) {
      const errorKey = getErrorKey(type)
      const sizeMB = type === 'assessment-report' ? '20MB' : '10MB'
      errors[errorKey] = `文件大小超过限制，请上传小于 ${sizeMB} 的文件`
      return
    }

    // 保存文件
    switch (type) {
      case 'organization':
        formData.organization.uploadedFile = file
        delete errors.organization_file
        break
      case 'classification':
        formData.classification.policyFile = file
        delete errors.classification_policy
        break
      case 'catalog':
        formData.classification.catalogFile = file
        break
      case 'logging':
        formData.policies.logging = file
        delete errors.policy_logging
        break
      case 'assessment-policy':
        formData.riskAssessment.policyFile = file
        delete errors.assessment_policy
        break
      case 'assessment-report':
        formData.riskAssessment.reportFile = file
        break
      case 'monitoring':
        formData.emergency.monitoringFile = file
        delete errors.monitoring_file
        break
      case 'emergency':
        formData.emergency.emergencyFile = file
        delete errors.emergency_file
        break
      default:
        // 数据处理阶段制度文件
        if (type in formData.policies) {
          formData.policies[type as keyof typeof formData.policies] = file
          delete errors[`policy_${type}`]
        }
        break
    }
  }
}

const removeFile = (type: string) => {
  switch (type) {
    case 'organization':
      formData.organization.uploadedFile = null
      if (organizationFileInput.value) {
        organizationFileInput.value.value = ''
      }
      break
    case 'classification':
      formData.classification.policyFile = null
      if (classificationFileInput.value) {
        classificationFileInput.value.value = ''
      }
      break
    case 'catalog':
      formData.classification.catalogFile = null
      if (catalogFileInput.value) {
        catalogFileInput.value.value = ''
      }
      break
    case 'logging':
      formData.policies.logging = null
      if (loggingFileInput.value) {
        loggingFileInput.value.value = ''
      }
      break
    case 'assessment-policy':
      formData.riskAssessment.policyFile = null
      if (assessmentPolicyFileInput.value) {
        assessmentPolicyFileInput.value.value = ''
      }
      break
    case 'assessment-report':
      formData.riskAssessment.reportFile = null
      if (assessmentReportFileInput.value) {
        assessmentReportFileInput.value.value = ''
      }
      break
    case 'monitoring':
      formData.emergency.monitoringFile = null
      if (monitoringFileInput.value) {
        monitoringFileInput.value.value = ''
      }
      break
    case 'emergency':
      formData.emergency.emergencyFile = null
      if (emergencyFileInput.value) {
        emergencyFileInput.value.value = ''
      }
      break
    default:
      // 数据处理阶段制度文件
      if (type in formData.policies) {
        formData.policies[type as keyof typeof formData.policies] = null
        if (policyFileInputs.value[type]) {
          policyFileInputs.value[type]!.value = ''
        }
      }
      break
  }
}

const getErrorKey = (type: string) => {
  switch (type) {
    case 'organization':
      return 'organization_file'
    case 'classification':
      return 'classification_policy'
    case 'catalog':
      return 'classification_catalog'
    case 'logging':
      return 'policy_logging'
    case 'assessment-policy':
      return 'assessment_policy'
    case 'assessment-report':
      return 'assessment_report'
    case 'monitoring':
      return 'monitoring_file'
    case 'emergency':
      return 'emergency_file'
    default:
      return `policy_${type}`
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表单验证（原型演示版本 - 校验已禁用）
const validateForm = () => {
  // 清空之前的错误
  Object.keys(errors).forEach((key) => {
    delete errors[key]
  })

  // 原型演示模式：直接返回true，跳过所有校验
  console.log('原型演示模式：数据安全防控措施制度表单校验已禁用，直接通过')
  return true

  /* 原始校验逻辑（原型完成后可恢复）
  let isValid = true

  // 组织架构验证
  if (!formData.organization.department.trim()) {
    errors.organization_department = '请输入数据安全负责部门'
    isValid = false
  }

  if (!formData.organization.manager.trim()) {
    errors.organization_manager = '请输入负责人姓名'
    isValid = false
  }

  if (!formData.organization.position.trim()) {
    errors.organization_position = '请输入负责人职位'
    isValid = false
  }

  if (!formData.organization.uploadedFile) {
    errors.organization_file = '请上传组织架构管理制度文件'
    isValid = false
  }

  // 数据分类分级验证
  if (!formData.classification.policyFile) {
    errors.classification_policy = '请上传分类分级管理制度文件'
    isValid = false
  }

  // 制度体系验证
  dataStages.forEach((stage) => {
    if (!formData.policies[stage.key as keyof typeof formData.policies]) {
      errors[`policy_${stage.key}`] = `请上传${stage.name}制度文件`
      isValid = false
    }
  })

  if (!formData.policies.logging) {
    errors.policy_logging = '请上传日志管理制度文件'
    isValid = false
  }

  // 风险评估验证
  if (!formData.riskAssessment.selfAssessment && !formData.riskAssessment.thirdParty) {
    errors.assessment_method = '请至少选择一种评估方式'
    isValid = false
  }

  if (formData.riskAssessment.thirdParty) {
    if (!formData.riskAssessment.instituteName.trim()) {
      errors.institute_name = '请输入第三方机构名称'
      isValid = false
    }

    if (!formData.riskAssessment.instituteCode.trim()) {
      errors.institute_code = '请输入机构信用代码'
      isValid = false
    } else if (!/^[A-Z0-9]{18}$/i.test(formData.riskAssessment.instituteCode)) {
      errors.institute_code = '信用代码格式不正确'
      isValid = false
    }

    if (!formData.riskAssessment.instituteQualification.trim()) {
      errors.institute_qualification = '请输入机构资质信息'
      isValid = false
    }
  }

  if (!formData.riskAssessment.policyFile) {
    errors.assessment_policy = '请上传评估制度文件'
    isValid = false
  }

  // 监测预警与应急管理验证
  if (!formData.emergency.monitoringFile) {
    errors.monitoring_file = '请上传监测预警机制文件'
    isValid = false
  }

  if (!formData.emergency.emergencyFile) {
    errors.emergency_file = '请上传应急预案及管理制度文件'
    isValid = false
  }

  return isValid
  */
}

// 上一步
const handlePrevious = () => {
  router.push('/corp/filing/form/qualification')
}

// 保存（暂存）
const handleSave = () => {
  console.log('保存数据安全防控措施制度信息:', formData)

  // 保存到本地存储
  localStorage.setItem(
    'enterprise_security_policy',
    JSON.stringify({
      formData: {
        ...formData,
        // 文件信息只保存文件名
        organization: {
          ...formData.organization,
          uploadedFileName: formData.organization.uploadedFile?.name,
        },
        classification: {
          policyFileName: formData.classification.policyFile?.name,
          catalogFileName: formData.classification.catalogFile?.name,
        },
        policies: Object.fromEntries(
          Object.entries(formData.policies).map(([key, file]) => [
            key,
            file ? { name: file.name } : null,
          ]),
        ),
        riskAssessment: {
          ...formData.riskAssessment,
          policyFileName: formData.riskAssessment.policyFile?.name,
          reportFileName: formData.riskAssessment.reportFile?.name,
        },
        emergency: {
          monitoringFileName: formData.emergency.monitoringFile?.name,
          emergencyFileName: formData.emergency.emergencyFile?.name,
        },
      },
    }),
  )

  saveDialogOpen.value = true
}

// 下一步
const handleNext = () => {
  if (validateForm()) {
    handleSave()
    // 跳转到下一步（数据安全防控措施-技术）
    router.push('/corp/filing/form/security-tech')
  }
}

// 表单提交
const handleSubmit = () => {
  // 阻止默认提交行为，由handleNext处理
}

// 页面加载时尝试恢复数据
const loadSavedData = () => {
  const saved = localStorage.getItem('enterprise_security_policy')
  if (saved) {
    try {
      const data = JSON.parse(saved)
      // 恢复非文件数据
      if (data.formData) {
        Object.assign(formData.organization, {
          department: data.formData.organization?.department || '',
          manager: data.formData.organization?.manager || '',
          position: data.formData.organization?.position || '',
        })

        Object.assign(formData.riskAssessment, {
          selfAssessment: data.formData.riskAssessment?.selfAssessment || false,
          thirdParty: data.formData.riskAssessment?.thirdParty || false,
          instituteName: data.formData.riskAssessment?.instituteName || '',
          instituteCode: data.formData.riskAssessment?.instituteCode || '',
          instituteQualification: data.formData.riskAssessment?.instituteQualification || '',
        })
      }
    } catch (e) {
      console.warn('Failed to load saved data:', e)
    }
  }
}

// 组件挂载时加载保存的数据
onMounted(() => {
  loadSavedData()
})
</script>

<style scoped>
/* 自定义样式 */
.border-red-500 {
  border-color: rgb(239 68 68);
}
</style>
