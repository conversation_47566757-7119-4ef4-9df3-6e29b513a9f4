<template>
  <div class="bg-muted/50 px-4 py-3 rounded-t-md border border-b-0">
    <form @submit.prevent="handleSearch" class="w-full">
      <div class="space-y-3">
        <!-- 动态渲染筛选控件 -->
        <template v-for="field in filterFields" :key="field.key">
          <!-- 文本输入框 -->
          <div v-if="field.type === 'input'" class="w-full">
            <Label :for="field.key" class="text-sm font-medium mb-1 block">
              {{ field.label }}
            </Label>
            <Input
              :id="field.key"
              v-model="filters[field.key]"
              :placeholder="field.placeholder"
              class="w-full h-8 text-sm"
            />
          </div>

          <!-- 下拉选择框 -->
          <div v-else-if="field.type === 'select'" class="w-full">
            <Label :for="field.key" class="text-sm font-medium mb-1 block">
              {{ field.label }}
            </Label>
            <Select v-model="filters[field.key]">
              <SelectTrigger :id="field.key" class="w-full h-8 text-sm">
                <SelectValue :placeholder="field.placeholder" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="option in field.options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 日期范围选择器 -->
          <div v-else-if="field.type === 'dateRange'" class="w-full">
            <Label :for="field.key" class="text-sm font-medium mb-1 block">
              {{ field.label }}
            </Label>
            <DatePickerWithRange
              :id="field.key"
              v-model="filters[field.key]"
              :placeholder="field.placeholder"
              class="w-full h-8 text-sm"
            />
          </div>
        </template>

        <!-- 操作按钮区域 -->
        <div class="flex items-center gap-2 pt-2">
          <Button type="submit" size="sm" class="h-8 px-3 flex items-center gap-1 flex-1">
            <Search class="w-3 h-3" />
            <span class="text-sm">查询</span>
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            @click="handleReset"
            class="h-8 px-3 flex items-center gap-1 flex-1"
          >
            <RotateCcw class="w-3 h-3" />
            <span class="text-sm">重置</span>
          </Button>
          <Button
            v-if="showExport"
            type="button"
            variant="outline"
            size="sm"
            @click="handleExport"
            class="h-8 px-3 flex items-center gap-1 flex-1"
          >
            <Download class="w-3 h-3" />
            <span class="text-sm">导出</span>
          </Button>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Search, RotateCcw, Download } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import DatePickerWithRange from '@/components/ui/date-picker/DatePickerWithRange.vue'

// 字段类型定义
export interface FilterField {
  key: string
  label: string
  type: 'input' | 'select' | 'dateRange'
  placeholder: string
  options?: { label: string; value: string }[] // select类型时必需
  defaultValue?: any
}

// 组件属性
interface Props {
  filterFields: FilterField[]
  showExport?: boolean
  initialValues?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  showExport: false,
  initialValues: () => ({}),
})

// 事件定义
interface Emits {
  search: [filters: Record<string, any>]
  reset: []
  export: []
}

const emit = defineEmits<Emits>()

// 响应式数据
const filters = reactive<Record<string, any>>({})

// 初始化筛选器值
const initializeFilters = () => {
  props.filterFields.forEach((field) => {
    filters[field.key] =
      props.initialValues[field.key] || field.defaultValue || getDefaultValue(field.type)
  })
}

// 获取字段类型的默认值
const getDefaultValue = (type: string) => {
  switch (type) {
    case 'input':
      return ''
    case 'select':
      return 'ALL'
    case 'dateRange':
      return null
    default:
      return ''
  }
}

// 处理搜索
const handleSearch = () => {
  emit('search', { ...filters })
}

// 处理重置
const handleReset = () => {
  initializeFilters()
  emit('reset')
  // 重置后立即触发搜索
  emit('search', { ...filters })
}

// 处理导出
const handleExport = () => {
  emit('export')
}

// 监听props变化，重新初始化
watch(
  () => props.filterFields,
  () => {
    initializeFilters()
  },
  { immediate: true },
)

watch(
  () => props.initialValues,
  (newValues) => {
    Object.assign(filters, newValues)
  },
  { deep: true },
)

// 暴露方法给父组件
defineExpose({
  getFilters: () => ({ ...filters }),
  setFilters: (newFilters: Record<string, any>) => {
    Object.assign(filters, newFilters)
  },
  resetFilters: handleReset,
})
</script>