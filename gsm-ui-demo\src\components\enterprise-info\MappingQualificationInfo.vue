<template>
  <div class="space-y-6">
    <!-- 资质概览 -->
    <Card :class="disableCardVariants ? undefined : 'card-secondary'">
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Award class="h-5 w-5" />
            <span>{{ mode === 'audit' ? '测绘资质审核' : '测绘资质概览' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <Badge :variant="hasValidQualifications ? 'default' : 'destructive'">
              {{ hasValidQualifications ? '资质有效' : '资质异常' }}
            </Badge>
            <Badge v-if="mode === 'audit'" variant="outline">
              审核模式
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ qualifications.length }}</div>
            <div class="text-sm text-blue-600">资质总数</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ validQualifications }}</div>
            <div class="text-sm text-green-600">有效资质</div>
          </div>
          <div class="text-center p-4 bg-red-50 rounded-lg">
            <div class="text-2xl font-bold text-red-600">{{ expiredQualifications }}</div>
            <div class="text-sm text-red-600">过期资质</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 资质列表 - 审核模式表格视图 -->
    <Card v-if="mode === 'audit'" :class="disableCardVariants ? undefined : 'card-primary'">
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <FileText class="h-5 w-5" />
            <span>资质审核列表</span>
          </div>
          <Button v-if="hasAuditChanges" @click="saveAuditResults" class="flex items-center space-x-2">
            <Save class="w-4 h-4" />
            保存审核意见
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="table-elevated rounded-md border">
          <Table class="data-table">
            <TableHeader>
              <TableRow class="data-table-header">
                <TableHead class="data-table-cell">序号</TableHead>
                <TableHead class="data-table-cell">企业名称</TableHead>
                <TableHead class="data-table-cell">资质类别</TableHead>
                <TableHead class="data-table-cell">资质等级</TableHead>
                <TableHead class="data-table-cell">证书编号</TableHead>
                <TableHead class="data-table-cell">资质有效期</TableHead>
                <TableHead class="data-table-cell">资质状态</TableHead>
                <TableHead class="data-table-cell">审核意见</TableHead>
                <TableHead class="data-table-cell">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="(qualification, index) in qualifications" :key="index" class="data-table-row">
                <TableCell class="data-table-cell">{{ index + 1 }}</TableCell>
                <TableCell class="data-table-cell">{{ enterprise.name }}</TableCell>
                <TableCell class="data-table-cell">{{ qualification.category }}</TableCell>
                <TableCell class="data-table-cell">
                  <Badge variant="outline">{{ qualification.level }}</Badge>
                </TableCell>
                <TableCell class="data-table-cell">
                  <code class="text-sm bg-muted px-2 py-1 rounded">{{ qualification.certificateNumber }}</code>
                </TableCell>
                <TableCell class="data-table-cell">
                  <div :class="isExpiringSoon(qualification.validUntil) ? 'text-red-600 font-medium' : ''">
                    {{ formatDate(qualification.validUntil) }}
                    <span v-if="isExpiringSoon(qualification.validUntil)" class="text-xs block">
                      (即将过期)
                    </span>
                  </div>
                </TableCell>
                <TableCell class="data-table-cell">
                  <Badge :variant="qualification.status === '有效' ? 'default' : 'destructive'">
                    {{ qualification.status }}
                  </Badge>
                </TableCell>
                <TableCell class="data-table-cell">
                  <div class="space-y-2 min-w-64">
                    <Select v-model="auditResults[index].decision">
                      <SelectTrigger class="w-full">
                        <SelectValue placeholder="请选择审核结果" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="approve">通过</SelectItem>
                        <SelectItem value="reject">驳回</SelectItem>
                        <SelectItem value="conditional">有条件通过</SelectItem>
                      </SelectContent>
                    </Select>
                    <Textarea 
                      v-model="auditResults[index].comments"
                      placeholder="请输入审核意见..."
                      class="w-full text-sm"
                      rows="2"
                    />
                  </div>
                </TableCell>
                <TableCell class="data-table-cell">
                  <div class="flex space-x-1">
                    <Button size="sm" variant="outline" @click="viewCertificate(qualification)">
                      <Eye class="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" @click="downloadCertificate(qualification)">
                      <Download class="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>

    <!-- 资质列表 - 查看模式卡片视图 -->
    <Card v-else :class="disableCardVariants ? undefined : 'card-primary'">
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <FileText class="h-5 w-5" />
          <span>资质详细信息</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="(qualification, index) in qualifications"
            :key="index"
            class="border rounded-lg p-4 space-y-4"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <Badge :variant="qualification.status === '有效' ? 'default' : 'destructive'">
                  {{ qualification.status }}
                </Badge>
                <h4 class="font-semibold">{{ qualification.category }}</h4>
                <Badge variant="outline">{{ qualification.level }}</Badge>
              </div>
              <div class="flex items-center space-x-2">
                <Button size="sm" variant="outline" @click="viewCertificate(qualification)">
                  <Eye class="w-4 h-4 mr-2" />
                  查看证书
                </Button>
                <Button size="sm" variant="outline" @click="downloadCertificate(qualification)">
                  <Download class="w-4 h-4 mr-2" />
                  下载
                </Button>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="space-y-2">
                <Label class="text-sm font-medium">证书编号</Label>
                <div class="text-sm font-mono bg-muted p-2 rounded">{{ qualification.certificateNumber }}</div>
              </div>
              
              <div class="space-y-2">
                <Label class="text-sm font-medium">发证机关</Label>
                <div class="text-sm">{{ qualification.issuingAuthority }}</div>
              </div>
              
              <div class="space-y-2">
                <Label class="text-sm font-medium">发证日期</Label>
                <div class="text-sm">{{ formatDate(qualification.issueDate) }}</div>
              </div>
              
              <div class="space-y-2">
                <Label class="text-sm font-medium">有效期至</Label>
                <div class="text-sm" :class="isExpiringSoon(qualification.validUntil) ? 'text-red-600 font-medium' : ''">
                  {{ formatDate(qualification.validUntil) }}
                  <span v-if="isExpiringSoon(qualification.validUntil)" class="text-xs">
                    (即将过期)
                  </span>
                </div>
              </div>
              
              <div class="space-y-2">
                <Label class="text-sm font-medium">业务范围</Label>
                <div class="text-sm">
                  <div v-for="scope in qualification.businessScope" :key="scope" class="flex items-center space-x-1">
                    <Check class="w-3 h-3 text-green-500" />
                    <span>{{ scope }}</span>
                  </div>
                </div>
              </div>
              
              <div class="space-y-2">
                <Label class="text-sm font-medium">作业限制</Label>
                <div class="text-sm text-muted-foreground">
                  {{ qualification.restrictions || '无特殊限制' }}
                </div>
              </div>
            </div>

            <!-- 资质详细说明 -->
            <div v-if="qualification.description" class="space-y-2">
              <Label class="text-sm font-medium">资质说明</Label>
              <div class="text-sm bg-muted p-3 rounded leading-relaxed">
                {{ qualification.description }}
              </div>
            </div>

            <!-- 续期提醒 -->
            <div v-if="isExpiringSoon(qualification.validUntil)" class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div class="flex items-start space-x-2">
                <AlertTriangle class="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <div class="font-medium text-yellow-800">续期提醒</div>
                  <div class="text-sm text-yellow-700 mt-1">
                    该资质将在 {{ getDaysUntilExpiration(qualification.validUntil) }} 天后过期，请及时办理续期手续。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 特殊情况说明 -->
    <Card v-if="specialNotes" class="card-subtle">
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <MessageSquare class="h-5 w-5" />
          <span>特殊情况说明</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm leading-relaxed bg-blue-50 p-4 rounded-lg">
          {{ specialNotes }}
        </div>
      </CardContent>
    </Card>

    <!-- 资质审核记录 -->
    <Card class="card-subtle">
      <CardHeader>
        <CardTitle class="flex items-center space-x-2">
          <History class="h-5 w-5" />
          <span>审核记录</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-3">
          <div
            v-for="(record, index) in auditRecords"
            :key="index"
            class="flex items-center justify-between p-3 border rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <Badge :variant="record.result === '通过' ? 'default' : 'destructive'">
                {{ record.result }}
              </Badge>
              <div>
                <div class="font-medium">{{ record.category }}</div>
                <div class="text-sm text-muted-foreground">{{ record.auditDate }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm">审核人：{{ record.auditor }}</div>
              <div class="text-xs text-muted-foreground">{{ record.department }}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { 
  Award, 
  FileText, 
  Eye, 
  Download, 
  Check, 
  AlertTriangle, 
  MessageSquare, 
  History,
  Save
} from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { 
  Table, 
  TableBody, 
  TableHead, 
  TableHeader, 
  TableRow, 
  TableCell 
} from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'

interface Enterprise {
  id: string
  name: string
  type: string
  creditCode: string
  registrationStatus: '注册中' | '通过' | '未通过' | '待审核'
  registrationTime: string
  contactPerson: string
  contactPhone: string
  address: string
  vehicleCount: number
  riskLevel: '低' | '中' | '高'
}

// 企业测绘资质信息数据接口
interface SurveyingQualInfo {
  id: number // 资质记录ID
  enterprise_id: string // 所属企业ID
  qualification_level: string // 资质等级 (甲级、乙级)
  certificate_number: string // 证书编号 (唯一)
  valid_from: string // 有效期起
  valid_to: string // 有效期止
  issuing_authority: string // 发证机关
  create_time: string // 创建时间
  
  // 扩展字段用于UI显示
  category?: string // 资质类别
  status?: '有效' | '过期' // 计算得出的状态
  business_scope?: string[] // 业务范围
  restrictions?: string // 作业限制
  description?: string // 资质说明
}

// 保持原有接口以兼容现有代码
interface Qualification extends SurveyingQualInfo {
  category: string
  level: string
  certificateNumber: string
  issuingAuthority: string
  issueDate: string
  validUntil: string
  status: '有效' | '过期'
  businessScope: string[]
  restrictions?: string
  description?: string
}

interface AuditRecord {
  category: string
  result: '通过' | '未通过'
  auditDate: string
  auditor: string
  department: string
  comments?: string
}

interface AuditResult {
  decision: 'approve' | 'reject' | 'conditional' | ''
  comments: string
}

interface Props {
  enterprise: Enterprise
  mode?: 'view' | 'audit'
  disableCardVariants?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'view',
  disableCardVariants: false,
})

// 审核结果数据
const auditResults = ref<AuditResult[]>([])

// 初始化审核结果数组
watch(() => props.mode, (newMode) => {
  if (newMode === 'audit') {
    auditResults.value = qualifications.value.map(() => ({
      decision: '',
      comments: ''
    }))
  }
}, { immediate: true })

// 模拟测绘资质数据 - 企业资质信息结构
const qualifications = computed((): Qualification[] => [
  {
    id: 1,
    enterprise_id: props.enterprise.id,
    qualification_level: '甲级',
    certificate_number: 'A1110001234567',
    valid_from: '2022-06-15',
    valid_to: '2025-06-14',
    issuing_authority: '国家测绘地理信息局',
    create_time: '2022-06-15T10:30:00',
    
    // 兼容字段
    category: '互联网地图服务',
    level: '甲级',
    certificateNumber: 'A1110001234567',
    issuingAuthority: '国家测绘地理信息局',
    issueDate: '2022-06-15',
    validUntil: '2025-06-14',
    status: '有效',
    businessScope: [
      '地理位置定位服务',
      '导航电子地图制作',
      '互联网地图服务发布'
    ],
    business_scope: [
      '地理位置定位服务',
      '导航电子地图制作',
      '互联网地图服务发布'
    ],
    restrictions: '限于智能网联汽车应用场景',
    description: '该资质允许企业在智能网联汽车领域提供互联网地图服务，包括实时导航、位置定位等功能。'
  },
  {
    id: 2,
    enterprise_id: props.enterprise.id,
    qualification_level: '乙级',
    certificate_number: 'B1110009876543',
    valid_from: '2023-03-20',
    valid_to: '2024-03-19',
    issuing_authority: '北京市规划和自然资源委员会',
    create_time: '2023-03-20T14:15:00',
    
    // 兼容字段
    category: '导航电子地图制作',
    level: '乙级',
    certificateNumber: 'B1110009876543',
    issuingAuthority: '北京市规划和自然资源委员会',
    issueDate: '2023-03-20',
    validUntil: '2024-03-19',
    status: '过期',
    businessScope: [
      '车载导航电子地图制作',
      '道路交通信息采集',
      '地图数据更新维护'
    ],
    business_scope: [
      '车载导航电子地图制作',
      '道路交通信息采集',
      '地图数据更新维护'
    ],
    description: '该资质已过期，需要及时办理续期手续以确保业务正常开展。'
  },
  {
    id: 3,
    enterprise_id: props.enterprise.id,
    qualification_level: '甲级',
    certificate_number: 'A1110002468135',
    valid_from: '2023-09-10',
    valid_to: '2026-09-09',
    issuing_authority: '国家测绘地理信息局',
    create_time: '2023-09-10T09:00:00',
    
    // 兼容字段
    category: '地理信息系统工程',
    level: '甲级',
    certificateNumber: 'A1110002468135',
    issuingAuthority: '国家测绘地理信息局',
    issueDate: '2023-09-10',
    validUntil: '2026-09-09',
    status: '有效',
    businessScope: [
      '地理信息系统设计',
      '地理信息数据库建设',
      '空间分析服务'
    ],
    business_scope: [
      '地理信息系统设计',
      '地理信息数据库建设',
      '空间分析服务'
    ]
  }
])

const auditRecords = computed((): AuditRecord[] => [
  {
    category: '互联网地图服务',
    result: '通过',
    auditDate: '2024-01-15',
    auditor: '张审核员',
    department: '北京市测绘地理信息局'
  },
  {
    category: '导航电子地图制作',
    result: '未通过',
    auditDate: '2024-03-20',
    auditor: '李审核员',
    department: '北京市规划和自然资源委员会',
    comments: '资质已过期，需要重新申请'
  },
  {
    category: '地理信息系统工程',
    result: '通过',
    auditDate: '2023-12-05',
    auditor: '王审核员',
    department: '国家测绘地理信息局'
  }
])

const specialNotes = computed(() => {
  return '企业具备完整的测绘资质体系，在智能网联汽车地图服务领域具有专业优势。其中导航电子地图制作资质已过期，建议尽快办理续期手续以确保业务连续性。'
})

// 计算属性
const validQualifications = computed(() => 
  qualifications.value.filter(q => q.status === '有效').length
)

const expiredQualifications = computed(() => 
  qualifications.value.filter(q => q.status === '过期').length
)

const hasValidQualifications = computed(() => validQualifications.value > 0)

// 审核相关计算属性
const hasAuditChanges = computed(() => {
  if (props.mode !== 'audit') return false
  return auditResults.value.some(result => result.decision !== '' || result.comments.trim() !== '')
})

// 方法
const saveAuditResults = () => {
  console.log('保存审核结果:', auditResults.value)
  // 这里实现保存审核结果的逻辑
  // 可以发送到后端API
}
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const isExpiringSoon = (validUntil: string) => {
  const today = new Date()
  const expiryDate = new Date(validUntil)
  const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
  return daysUntilExpiry <= 90 && daysUntilExpiry > 0
}

const getDaysUntilExpiration = (validUntil: string) => {
  const today = new Date()
  const expiryDate = new Date(validUntil)
  return Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
}

const viewCertificate = (qualification: Qualification) => {
  console.log('查看证书:', qualification.certificateNumber)
  // 这里实现查看证书的逻辑
}

const downloadCertificate = (qualification: Qualification) => {
  console.log('下载证书:', qualification.certificateNumber)
  // 这里实现下载证书的逻辑
}
</script>