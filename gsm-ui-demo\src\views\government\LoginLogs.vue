<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          查询和管理用户登录日志，支持安全审计、异常检测和风险分析
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="handleSecurityAnalysis" variant="outline" class="flex items-center gap-2">
          <ShieldCheck class="w-4 h-4" />
          安全分析
        </Button>
        <Button @click="handleExportReport" variant="outline" class="flex items-center gap-2">
          <FileText class="w-4 h-4" />
          安全报告
        </Button>
        <Button @click="handleRiskAlert" variant="outline" class="flex items-center gap-2">
          <Bell class="w-4 h-4" />
          风险预警
        </Button>
      </div>
    </div>

    <!-- 安全统计面板 -->
    <div class="grid grid-cols-1 lg:grid-cols-7 gap-4">
      <!-- 统计卡片 -->
      <div class="lg:col-span-5">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-base font-bold text-foreground">今日登录</p>
                  <p class="text-2xl font-bold">{{ stats.todayLogins }}</p>
                  <p class="text-xs text-green-600 flex items-center gap-1 mt-1">
                    <TrendingUp class="w-3 h-3" />
                    +{{ stats.todayGrowth }}%
                  </p>
                </div>
                <Calendar class="w-8 h-8 text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-base font-bold text-foreground">在线用户</p>
                  <p class="text-2xl font-bold text-green-600">{{ stats.onlineUsers }}</p>
                  <p class="text-xs text-muted-foreground mt-1">活跃会话</p>
                </div>
                <Users class="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-base font-bold text-foreground">失败登录</p>
                  <p class="text-2xl font-bold text-red-600">{{ stats.failedLogins }}</p>
                  <p class="text-xs text-red-600 flex items-center gap-1 mt-1">
                    <AlertTriangle class="w-3 h-3" />
                    需关注
                  </p>
                </div>
                <XCircle class="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-base font-bold text-foreground">异常IP</p>
                  <p class="text-2xl font-bold text-orange-600">{{ stats.suspiciousIPs }}</p>
                  <p class="text-xs text-orange-600 flex items-center gap-1 mt-1">
                    <Shield class="w-3 h-3" />
                    已拦截
                  </p>
                </div>
                <Globe class="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-base font-bold text-foreground">上链存证</p>
                  <p class="text-2xl font-bold text-blue-600">{{ stats.onChainLogs }}</p>
                  <p class="text-xs text-blue-600 flex items-center gap-1 mt-1">
                    <Database class="w-3 h-3" />
                    安全存证
                  </p>
                </div>
                <ShieldCheck class="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- 实时监控面板 -->
      <Card class="lg:col-span-2">
        <CardHeader class="pb-2">
          <CardTitle class="text-base flex items-center gap-2">
            <Activity class="w-4 h-4" />
            实时登录状态
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-3">
          <div class="flex items-center justify-between text-sm">
            <span class="text-muted-foreground">活跃会话</span>
            <Badge variant="default">{{ stats.onlineUsers }}</Badge>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-muted-foreground">政府用户</span>
            <Badge variant="secondary">{{ stats.govUsers }}</Badge>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-muted-foreground">企业用户</span>
            <Badge variant="outline">{{ stats.entUsers }}</Badge>
          </div>
          <Separator />
          <div class="flex items-center justify-between text-sm">
            <span class="text-red-600">登录异常</span>
            <Badge variant="destructive">{{ stats.loginAnomalies }}</Badge>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-orange-600">风险IP</span>
            <Badge variant="destructive">{{ stats.riskIPs }}</Badge>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 登录日志列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>登录日志</span>
          <div class="flex items-center gap-2">
            <Badge variant="outline">共 {{ filteredLogs.length }} 条记录</Badge>
            <Button
              variant="outline"
              size="sm"
              @click="refreshLogs"
              :disabled="isRefreshing"
              class="flex items-center gap-1"
            >
              <RotateCcw :class="`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`" />
              刷新
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent class="p-0">
        <!-- 筛选条件 - 紧贴表格 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[60px]">序号</TableHead>
                <TableHead>登录时间</TableHead>
                <TableHead>用户信息</TableHead>
                <TableHead>登录状态</TableHead>
                <TableHead>IP地址</TableHead>
                <TableHead>位置信息</TableHead>
                <TableHead>设备信息</TableHead>
                <TableHead>风险等级</TableHead>
                <TableHead>会话时长</TableHead>
                <TableHead class="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedLogs.length === 0">
                <TableCell :colspan="10" class="h-24 text-center text-muted-foreground">
                  暂无登录日志数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(log, index) in pagedLogs"
                :key="log.id"
                :class="`hover:bg-muted/40 ${getRiskRowStyle(log.riskLevel)}`"
              >
                <TableCell class="font-mono text-xs">{{
                  (currentPage - 1) * pageSize + index + 1
                }}</TableCell>
                <TableCell class="whitespace-nowrap text-sm">
                  <div>{{ log.loginTime }}</div>
                  <div class="text-xs text-muted-foreground">{{ getTimeAgo(log.loginTime) }}</div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <Avatar class="w-6 h-6">
                      <AvatarFallback class="text-xs">
                        <User
                          v-if="
                            !getUserInitials(log.userName) || getUserInitials(log.userName) === 'U'
                          "
                          class="w-3 h-3"
                        />
                        <span v-else>{{ getUserInitials(log.userName) }}</span>
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div class="font-medium text-sm">{{ log.userName }}</div>
                      <div class="flex items-center gap-1">
                        <Badge :variant="getUserTypeVariant(log.userType)" class="text-xs">
                          {{ log.userType }}
                        </Badge>
                        <component
                          :is="log.userType.includes('政府') ? Shield : Building2"
                          class="w-3 h-3 text-muted-foreground"
                        />
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <Badge :variant="getStatusVariant(log.status)">
                      {{ log.status }}
                    </Badge>
                    <component
                      :is="getStatusIcon(log.status)"
                      :class="`w-3 h-3 ${getStatusIconColor(log.status)}`"
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <div class="font-mono text-xs">
                    <div class="flex items-center gap-1">
                      <Globe class="w-3 h-3 text-muted-foreground" />
                      {{ log.ipAddress }}
                    </div>
                    <div class="text-muted-foreground mt-1">
                      {{ getIPType(log.ipAddress) }}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="text-xs">
                    <div class="flex items-center gap-1">
                      <MapPin class="w-3 h-3 text-muted-foreground" />
                      {{ log.location?.city || '未知' }}
                    </div>
                    <div class="text-muted-foreground">
                      {{ log.location?.country || '未知地区' }}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="text-xs">
                    <div class="flex items-center gap-1">
                      <Monitor class="w-3 h-3 text-muted-foreground" />
                      {{ log.deviceInfo?.os || '未知系统' }}
                    </div>
                    <div class="text-muted-foreground">
                      {{ log.deviceInfo?.browser || '未知浏览器' }}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getRiskLevelVariant(log.riskLevel)">
                    {{ log.riskLevel }}
                  </Badge>
                  <div v-if="log.riskReasons && log.riskReasons.length > 0" class="mt-1">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertTriangle class="w-3 h-3 text-orange-500 cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent side="left" class="max-w-sm">
                          <div class="space-y-1">
                            <p class="font-medium">风险因素:</p>
                            <ul class="text-xs space-y-1">
                              <li v-for="reason in log.riskReasons" :key="reason">
                                • {{ reason }}
                              </li>
                            </ul>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableCell>
                <TableCell>
                  <div class="text-xs">
                    <div v-if="log.logoutTime">
                      {{ calculateSessionDuration(log.loginTime, log.logoutTime) }}
                    </div>
                    <div v-else class="flex items-center gap-1 text-green-600">
                      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      在线中
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" class="w-48">
                      <DropdownMenuItem @click="handleViewDetail(log.id)">
                        <Eye class="w-4 h-4 mr-2" />
                        详细信息
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleViewSessions(log.userId)">
                        <Clock class="w-4 h-4 mr-2" />
                        会话历史
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleViewIPHistory(log.ipAddress)">
                        <Globe class="w-4 h-4 mr-2" />
                        IP历史
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleSecurityCheck(log.id)">
                        <ShieldCheck class="w-4 h-4 mr-2" />
                        安全检查
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="log.riskLevel !== '正常'"
                        @click="handleRiskAnalysis(log.id)"
                        class="text-orange-600"
                      >
                        <AlertTriangle class="w-4 h-4 mr-2" />
                        风险分析
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="!log.isOnChain"
                        @click="handleUploadChain(log.id)"
                        class="text-blue-600"
                      >
                        <Database class="w-4 h-4 mr-2" />
                        上链存证
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="log.status === '成功' && !log.logoutTime"
                        @click="handleForceLogout(log.sessionId)"
                        class="text-red-600"
                      >
                        <LogOut class="w-4 h-4 mr-2" />
                        强制下线
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredLogs.length) }} 条， 共
            {{ filteredLogs.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 登录详情抽屉 -->
    <Sheet :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <SheetContent
        side="right"
        class="w-[66vw] min-w-[640px] max-w-[1100px] max-h-[100vh] overflow-y-auto"
      >
        <SheetHeader>
          <SheetTitle>登录详情</SheetTitle>
          <SheetDescription> 查看完整的登录信息、安全分析和风险评估结果 </SheetDescription>
        </SheetHeader>

        <div v-if="currentLog" class="space-y-6 py-4">
          <Tabs default-value="basic" class="w-full">
            <TabsList class="grid w-full grid-cols-5">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="security">安全分析</TabsTrigger>
              <TabsTrigger value="device">设备信息</TabsTrigger>
              <TabsTrigger value="location">位置信息</TabsTrigger>
              <TabsTrigger value="blockchain">区块链存证</TabsTrigger>
            </TabsList>

            <!-- 基本信息 -->
            <TabsContent value="basic" class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">登录时间</Label>
                  <p class="text-sm">{{ currentLog.loginTime }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">登录状态</Label>
                  <Badge :variant="getStatusVariant(currentLog.status)">{{
                    currentLog.status
                  }}</Badge>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">用户名称</Label>
                  <p class="text-sm">{{ currentLog.userName }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">用户类型</Label>
                  <Badge :variant="getUserTypeVariant(currentLog.userType)">{{
                    currentLog.userType
                  }}</Badge>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">会话ID</Label>
                  <p class="text-sm font-mono">{{ currentLog.sessionId }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">会话时长</Label>
                  <p class="text-sm">
                    {{
                      currentLog.logoutTime
                        ? calculateSessionDuration(currentLog.loginTime, currentLog.logoutTime)
                        : '当前在线'
                    }}
                  </p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">登出时间</Label>
                  <p class="text-sm">{{ currentLog.logoutTime || '尚未登出' }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">风险等级</Label>
                  <Badge :variant="getRiskLevelVariant(currentLog.riskLevel)">{{
                    currentLog.riskLevel
                  }}</Badge>
                </div>
              </div>
            </TabsContent>

            <!-- 安全分析 -->
            <TabsContent value="security" class="space-y-4">
              <div class="space-y-4">
                <div class="p-4 border rounded-md">
                  <h4 class="font-medium mb-3 flex items-center gap-2">
                    <ShieldCheck class="w-4 h-4 text-blue-500" />
                    安全评估结果
                  </h4>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground"
                        >IP信誉评级</Label
                      >
                      <div class="flex items-center gap-2 mt-1">
                        <Badge :variant="getIPReputationVariant(currentLog.ipReputation)">
                          {{ currentLog.ipReputation }}
                        </Badge>
                        <component
                          :is="getIPReputationIcon(currentLog.ipReputation)"
                          class="w-4 h-4"
                        />
                      </div>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">登录频次</Label>
                      <p class="text-sm">过去24小时内登录 {{ currentLog.recentLoginCount }} 次</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">设备指纹</Label>
                      <p class="text-sm font-mono">{{ currentLog.deviceFingerprint }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">认证方式</Label>
                      <Badge variant="outline">{{ currentLog.authMethod }}</Badge>
                    </div>
                  </div>
                </div>

                <div
                  v-if="currentLog.riskReasons && currentLog.riskReasons.length > 0"
                  class="p-4 border rounded-md bg-orange-50 dark:bg-orange-900/20"
                >
                  <h4 class="font-medium mb-3 flex items-center gap-2 text-orange-600">
                    <AlertTriangle class="w-4 h-4" />
                    风险因素分析
                  </h4>
                  <div class="space-y-2">
                    <div
                      v-for="(reason, index) in currentLog.riskReasons"
                      :key="index"
                      class="flex items-start gap-2"
                    >
                      <Badge variant="destructive" class="text-xs">{{ index + 1 }}</Badge>
                      <span class="text-sm">{{ reason }}</span>
                    </div>
                  </div>
                </div>

                <div
                  v-if="currentLog.securityEvents && currentLog.securityEvents.length > 0"
                  class="p-4 border rounded-md"
                >
                  <h4 class="font-medium mb-3 flex items-center gap-2">
                    <Activity class="w-4 h-4 text-red-500" />
                    相关安全事件
                  </h4>
                  <div class="space-y-2">
                    <div
                      v-for="event in currentLog.securityEvents"
                      :key="event.id"
                      class="flex items-center justify-between p-2 bg-muted rounded"
                    >
                      <div>
                        <div class="text-base font-semibold">{{ event.type }}</div>
                        <div class="text-xs text-muted-foreground">{{ event.description }}</div>
                      </div>
                      <div class="text-xs text-muted-foreground">{{ event.timestamp }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 设备信息 -->
            <TabsContent value="device" class="space-y-4">
              <div class="space-y-4">
                <div class="p-4 border rounded-md">
                  <h4 class="font-medium mb-3 flex items-center gap-2">
                    <Monitor class="w-4 h-4 text-blue-500" />
                    设备详细信息
                  </h4>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">操作系统</Label>
                      <p class="text-sm">{{ currentLog.deviceInfo?.os || '未知' }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">浏览器</Label>
                      <p class="text-sm">{{ currentLog.deviceInfo?.browser || '未知' }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">设备类型</Label>
                      <p class="text-sm">{{ currentLog.deviceInfo?.deviceType || '未知' }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground"
                        >屏幕分辨率</Label
                      >
                      <p class="text-sm">{{ currentLog.deviceInfo?.screenResolution || '未知' }}</p>
                    </div>
                    <div class="col-span-2">
                      <Label class="text-base font-semibold text-muted-foreground"
                        >User Agent</Label
                      >
                      <p class="text-xs font-mono bg-muted p-2 rounded mt-1 break-all">
                        {{ currentLog.deviceInfo?.userAgent || '未知' }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 位置信息 -->
            <TabsContent value="location" class="space-y-4">
              <div class="space-y-4">
                <div class="p-4 border rounded-md">
                  <h4 class="font-medium mb-3 flex items-center gap-2">
                    <MapPin class="w-4 h-4 text-green-500" />
                    地理位置信息
                  </h4>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">IP地址</Label>
                      <p class="text-sm font-mono">{{ currentLog.ipAddress }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">IP类型</Label>
                      <Badge variant="outline">{{ getIPType(currentLog.ipAddress) }}</Badge>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">国家</Label>
                      <p class="text-sm">{{ currentLog.location?.country || '未知' }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">城市</Label>
                      <p class="text-sm">{{ currentLog.location?.city || '未知' }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">ISP</Label>
                      <p class="text-sm">{{ currentLog.location?.isp || '未知' }}</p>
                    </div>
                    <div>
                      <Label class="text-base font-semibold text-muted-foreground">时区</Label>
                      <p class="text-sm">{{ currentLog.location?.timezone || '未知' }}</p>
                    </div>
                  </div>

                  <!-- 位置地图占位 -->
                  <div
                    class="mt-4 p-8 border-2 border-dashed border-muted rounded-md text-center text-muted-foreground"
                  >
                    <MapPin class="w-8 h-8 mx-auto mb-2" />
                    <p>地图显示位置信息</p>
                    <p class="text-xs">{{ currentLog.location?.coordinates || '坐标未知' }}</p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <!-- 区块链存证 -->
            <TabsContent value="blockchain" class="space-y-4">
              <div
                v-if="currentLog.isOnChain"
                class="p-4 border rounded-md bg-green-50 dark:bg-green-900/20"
              >
                <div class="flex items-center gap-2 mb-3">
                  <Database class="w-4 h-4 text-green-600" />
                  <span class="font-medium text-green-600">区块链存证信息</span>
                  <Badge variant="default" class="ml-auto">已上链</Badge>
                </div>
                <div class="space-y-3">
                  <div>
                    <Label class="text-base font-semibold text-muted-foreground">区块高度</Label>
                    <p class="text-sm font-mono">
                      {{ currentLog.blockchainInfo?.blockHeight || '12,847,392' }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-base font-semibold text-muted-foreground">区块哈希</Label>
                    <p class="text-xs font-mono bg-muted p-2 rounded">
                      {{
                        currentLog.blockchainInfo?.blockHash ||
                        '0x1234567890abcdef1234567890abcdef12345678901234567890abcdef123456'
                      }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-base font-semibold text-muted-foreground">交易哈希</Label>
                    <p class="text-xs font-mono bg-muted p-2 rounded">
                      {{
                        currentLog.blockchainInfo?.txHash ||
                        '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab'
                      }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-base font-semibold text-muted-foreground">上链时间</Label>
                    <p class="text-sm">
                      {{ currentLog.blockchainInfo?.timestamp || '2025-08-25 14:32:15' }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-base font-semibold text-muted-foreground">Gas费用</Label>
                    <p class="text-sm">{{ currentLog.blockchainInfo?.gasFee || '0.00234' }} ETH</p>
                  </div>
                  <div>
                    <Label class="text-base font-semibold text-muted-foreground">确认次数</Label>
                    <p class="text-sm">
                      {{ currentLog.blockchainInfo?.confirmations || '1,247' }} 次确认
                    </p>
                  </div>
                </div>
              </div>
              <div v-else class="p-4 border-2 border-dashed border-muted rounded-md text-center">
                <Database class="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p class="text-muted-foreground">该日志尚未上链存证</p>
                <Button @click="handleUploadChain(currentLog.id)" class="mt-2" size="sm">
                  立即上链存证
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showDetailDialog = false">关闭</Button>
          <Button
            v-if="currentLog && !currentLog.isOnChain"
            @click="handleUploadChain(currentLog.id)"
          >
            <Database class="w-4 h-4 mr-2" />
            上链存证
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>

    <!-- 会话历史对话框 -->
    <Sheet :open="showSessionDialog" @update:open="showSessionDialog = $event">
      <SheetContent side="right" class="z-[60] w-[50vw] min-w-[520px] max-w-[900px]">
        <SheetHeader>
          <SheetTitle>会话历史</SheetTitle>
          <SheetDescription> 查看用户的历史登录会话记录和行为模式分析 </SheetDescription>
        </SheetHeader>

        <div class="py-4">
          <div class="text-sm text-muted-foreground text-center py-8">
            用户历史会话记录将在此处显示
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showSessionDialog = false">关闭</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>

    <!-- IP历史对话框 -->
    <Sheet :open="showIPDialog" @update:open="showIPDialog = $event">
      <SheetContent side="right" class="z-[60] w-[50vw] min-w-[520px] max-w-[900px]">
        <SheetHeader>
          <SheetTitle>IP历史记录</SheetTitle>
          <SheetDescription> 查看该IP地址的历史访问记录和风险评估信息 </SheetDescription>
        </SheetHeader>

        <div class="py-4">
          <div class="text-sm text-muted-foreground text-center py-8">
            IP历史访问记录将在此处显示
          </div>
        </div>

        <SheetFooter>
          <Button variant="outline" @click="showIPDialog = false">关闭</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Activity,
  AlertTriangle,
  Bell,
  Building2,
  Calendar,
  CheckCircle,
  Clock,
  Database,
  Eye,
  FileText,
  Globe,
  LogOut,
  MapPin,
  Monitor,
  MoreHorizontal,
  RotateCcw,
  Shield,
  ShieldCheck,
  TrendingUp,
  User,
  Users,
  XCircle,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

type LoginStatus = '成功' | '失败' | '异常'
type UserType = '政府管理员' | '政府监管员' | '企业管理员' | '企业操作员'
type RiskLevel = '正常' | '低风险' | '中风险' | '高风险'
type IPReputation = '可信' | '一般' | '可疑' | '恶意'

interface DeviceInfo {
  os: string
  browser: string
  deviceType: string
  screenResolution: string
  userAgent: string
}

interface LocationInfo {
  country: string
  city: string
  isp: string
  timezone: string
  coordinates: string
}

interface SecurityEvent {
  id: number
  type: string
  description: string
  timestamp: string
}

interface BlockchainInfo {
  blockHeight: number
  blockHash: string
  txHash: string
  timestamp: string
  gasFee: string
  confirmations: number
}

interface LoginLog {
  id: number
  userId: string
  userName: string
  userType: UserType
  loginTime: string
  logoutTime?: string
  status: LoginStatus
  ipAddress: string
  location?: LocationInfo
  deviceInfo?: DeviceInfo
  sessionId: string
  riskLevel: RiskLevel
  riskReasons?: string[]
  ipReputation: IPReputation
  recentLoginCount: number
  deviceFingerprint: string
  authMethod: string
  securityEvents?: SecurityEvent[]
  isOnChain: boolean
  blockchainInfo?: BlockchainInfo
}

// 统计数据
const stats = ref({
  todayLogins: 1487,
  todayGrowth: 12.5,
  onlineUsers: 234,
  failedLogins: 23,
  suspiciousIPs: 8,
  onChainLogs: 12847,
  govUsers: 156,
  entUsers: 78,
  loginAnomalies: 5,
  riskIPs: 3,
})

// 筛选条件
const filters = ref({
  userName: '',
  userType: 'ALL' as 'ALL' | UserType,
  status: 'ALL' as 'ALL' | LoginStatus,
  riskLevel: 'ALL' as 'ALL' | RiskLevel,
  ipAddress: '',
  timeRange: null as [Date, Date] | null,
  isOnChain: 'ALL' as 'ALL' | 'true' | 'false',
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'userName',
    label: '用户名称',
    type: 'input',
    placeholder: '请输入用户名',
  },
  {
    key: 'userType',
    label: '用户类型',
    type: 'select',
    placeholder: '请选择用户类型',
    options: [
      { label: '全部类型', value: 'ALL' },
      { label: '政府管理员', value: '政府管理员' },
      { label: '政府监管员', value: '政府监管员' },
      { label: '企业管理员', value: '企业管理员' },
      { label: '企业操作员', value: '企业操作员' },
    ],
  },
  {
    key: 'status',
    label: '登录状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '成功', value: '成功' },
      { label: '失败', value: '失败' },
      { label: '异常', value: '异常' },
    ],
  },
  {
    key: 'riskLevel',
    label: '风险等级',
    type: 'select',
    placeholder: '请选择等级',
    options: [
      { label: '全部等级', value: 'ALL' },
      { label: '正常', value: '正常' },
      { label: '低风险', value: '低风险' },
      { label: '中风险', value: '中风险' },
      { label: '高风险', value: '高风险' },
    ],
  },
  {
    key: 'ipAddress',
    label: 'IP地址',
    type: 'input',
    placeholder: '请输入IP地址',
  },
  {
    key: 'timeRange',
    label: '登录时间',
    type: 'dateRange',
    placeholder: '请选择时间范围',
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(12)
const isRefreshing = ref(false)

// 对话框状态
const showDetailDialog = ref(false)
const showSessionDialog = ref(false)
const showIPDialog = ref(false)
const currentLog = ref<LoginLog | null>(null)

// Mock 数据
const logs = ref<LoginLog[]>([
  {
    id: 1,
    userId: 'U-2025-001',
    userName: '李管理员',
    userType: '政府管理员',
    loginTime: '2025-08-25 14:30:25',
    logoutTime: '2025-08-25 16:45:12',
    status: '成功',
    ipAddress: '*************',
    location: {
      country: '中国',
      city: '北京',
      isp: '中国电信',
      timezone: 'Asia/Shanghai',
      coordinates: '39.9042, 116.4074',
    },
    deviceInfo: {
      os: 'Windows 11',
      browser: 'Chrome 127.0',
      deviceType: 'Desktop',
      screenResolution: '1920x1080',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    },
    sessionId: 'sess_2025082514302501',
    riskLevel: '正常',
    ipReputation: '可信',
    recentLoginCount: 3,
    deviceFingerprint: 'fp_abc123def456',
    authMethod: '用户名密码',
    isOnChain: true,
    blockchainInfo: {
      blockHeight: 12847392,
      blockHash: '0x1234567890abcdef1234567890abcdef12345678901234567890abcdef123456',
      txHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
      timestamp: '2025-08-25 14:32:15',
      gasFee: '0.00234',
      confirmations: 1247,
    },
  },
  {
    id: 2,
    userId: 'U-2025-003',
    userName: '张总经理',
    userType: '企业管理员',
    loginTime: '2025-08-25 14:25:10',
    status: '成功',
    ipAddress: '*************',
    location: {
      country: '中国',
      city: '上海',
      isp: '中国联通',
      timezone: 'Asia/Shanghai',
      coordinates: '31.2304, 121.4737',
    },
    deviceInfo: {
      os: 'macOS 14.0',
      browser: 'Safari 17.0',
      deviceType: 'Desktop',
      screenResolution: '2560x1440',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
    },
    sessionId: 'sess_2025082514251003',
    riskLevel: '正常',
    ipReputation: '可信',
    recentLoginCount: 1,
    deviceFingerprint: 'fp_xyz789uvw012',
    authMethod: '用户名密码',
    isOnChain: true,
  },
  {
    id: 3,
    userId: 'U-2025-002',
    userName: '王监管员',
    userType: '政府监管员',
    loginTime: '2025-08-25 14:20:33',
    status: '成功',
    ipAddress: '**********',
    location: {
      country: '中国',
      city: '北京',
      isp: '政府专网',
      timezone: 'Asia/Shanghai',
      coordinates: '39.9042, 116.4074',
    },
    deviceInfo: {
      os: 'Windows 10',
      browser: 'Edge 127.0',
      deviceType: 'Desktop',
      screenResolution: '1366x768',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    },
    sessionId: 'sess_2025082514203302',
    riskLevel: '正常',
    ipReputation: '可信',
    recentLoginCount: 5,
    deviceFingerprint: 'fp_def456ghi789',
    authMethod: '用户名密码',
    isOnChain: false,
  },
  {
    id: 4,
    userId: 'U-2025-004',
    userName: '刘操作员',
    userType: '企业操作员',
    loginTime: '2025-08-25 14:15:47',
    status: '失败',
    ipAddress: '*************',
    location: {
      country: '中国',
      city: '上海',
      isp: '中国联通',
      timezone: 'Asia/Shanghai',
      coordinates: '31.2304, 121.4737',
    },
    deviceInfo: {
      os: 'Android 13',
      browser: 'Chrome Mobile 127.0',
      deviceType: 'Mobile',
      screenResolution: '1080x2400',
      userAgent: 'Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36',
    },
    sessionId: 'sess_2025082514154704',
    riskLevel: '低风险',
    riskReasons: ['密码错误超过3次'],
    ipReputation: '一般',
    recentLoginCount: 8,
    deviceFingerprint: 'fp_ghi789jkl012',
    authMethod: '用户名密码',
    isOnChain: false,
  },
  {
    id: 5,
    userId: 'U-2025-099',
    userName: '未知用户',
    userType: '企业操作员',
    loginTime: '2025-08-25 13:58:42',
    status: '异常',
    ipAddress: '**************',
    location: {
      country: '俄罗斯',
      city: '莫斯科',
      isp: '未知ISP',
      timezone: 'Europe/Moscow',
      coordinates: '55.7558, 37.6176',
    },
    deviceInfo: {
      os: 'Linux',
      browser: '未知浏览器',
      deviceType: 'Desktop',
      screenResolution: '未知',
      userAgent: 'Bot/Unknown',
    },
    sessionId: 'sess_2025082513584299',
    riskLevel: '高风险',
    riskReasons: [
      '来源IP位于高风险国家',
      '使用异常User Agent',
      '登录时间异常（非工作时间）',
      '设备指纹不匹配',
    ],
    ipReputation: '恶意',
    recentLoginCount: 15,
    deviceFingerprint: 'fp_malicious001',
    authMethod: '暴力破解尝试',
    securityEvents: [
      {
        id: 1,
        type: '暴力破解',
        description: '在5分钟内尝试登录50次',
        timestamp: '2025-08-25 13:55:00',
      },
      {
        id: 2,
        type: 'IP黑名单',
        description: 'IP地址已被加入黑名单',
        timestamp: '2025-08-25 13:59:00',
      },
    ],
    isOnChain: true,
  },
])

// 计算属性
const filteredLogs = computed(() => {
  return logs.value.filter((log) => {
    if (filters.value.userName && !log.userName.includes(filters.value.userName)) {
      return false
    }
    if (filters.value.userType !== 'ALL' && log.userType !== filters.value.userType) {
      return false
    }
    if (filters.value.status !== 'ALL' && log.status !== filters.value.status) {
      return false
    }
    if (filters.value.riskLevel !== 'ALL' && log.riskLevel !== filters.value.riskLevel) {
      return false
    }
    if (filters.value.ipAddress && !log.ipAddress.includes(filters.value.ipAddress)) {
      return false
    }
    if (filters.value.timeRange && filters.value.timeRange[0] && filters.value.timeRange[1]) {
      const startDate = new Date(
        filters.value.timeRange[0].getFullYear(),
        filters.value.timeRange[0].getMonth(),
        filters.value.timeRange[0].getDate(),
      )
      const endDate = new Date(
        filters.value.timeRange[1].getFullYear(),
        filters.value.timeRange[1].getMonth(),
        filters.value.timeRange[1].getDate(),
        23,
        59,
        59,
      )
      const loginDate = new Date(log.loginTime.replace(/-/g, '/'))
      if (loginDate < startDate || loginDate > endDate) return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredLogs.value.length / pageSize.value)),
)

const pagedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredLogs.value.slice(start, start + pageSize.value)
})

// 辅助函数
const getUserInitials = (userName: string) => {
  return userName.slice(0, 2).toUpperCase()
}

const getTimeAgo = (time: string) => {
  const now = new Date()
  const loginTime = new Date(time.replace(/-/g, '/'))
  const diffMs = now.getTime() - loginTime.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))

  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`
  return `${Math.floor(diffMins / 1440)}天前`
}

const calculateSessionDuration = (loginTime: string, logoutTime: string) => {
  const login = new Date(loginTime.replace(/-/g, '/'))
  const logout = new Date(logoutTime.replace(/-/g, '/'))
  const diffMs = logout.getTime() - login.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))

  if (diffMins < 60) return `${diffMins}分钟`
  const hours = Math.floor(diffMins / 60)
  const mins = diffMins % 60
  return `${hours}小时${mins}分钟`
}

const getIPType = (ip: string) => {
  if (ip.startsWith('192.168.') || ip.startsWith('10.')) return '内网IP'
  if (ip.startsWith('127.')) return '本地IP'
  return '公网IP'
}

// 事件处理
const handleSearch = (searchFilters?: Record<string, any>) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    userName: '',
    userType: 'ALL',
    status: 'ALL',
    riskLevel: 'ALL',
    ipAddress: '',
    timeRange: null,
    isOnChain: 'ALL',
  }
  currentPage.value = 1
}

const refreshLogs = () => {
  isRefreshing.value = true
  setTimeout(() => {
    isRefreshing.value = false
  }, 1000)
}

const exportData = () => {
  const headers = [
    '序号',
    '登录时间',
    '用户名',
    '用户类型',
    '登录状态',
    'IP地址',
    '位置',
    '风险等级',
    '会话时长',
  ]
  const rows = filteredLogs.value.map((log, index) => [
    (index + 1).toString(),
    log.loginTime,
    log.userName,
    log.userType,
    log.status,
    log.ipAddress,
    `${log.location?.city || '未知'}, ${log.location?.country || '未知'}`,
    log.riskLevel,
    log.logoutTime ? calculateSessionDuration(log.loginTime, log.logoutTime) : '在线中',
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `登录日志_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleSecurityAnalysis = () => {
  console.log('开始安全分析')
}

const handleExportReport = () => {
  console.log('导出安全报告')
}

const handleRiskAlert = () => {
  console.log('配置风险预警')
}

const handleViewDetail = (id: number) => {
  const log = logs.value.find((l) => l.id === id)
  if (log) {
    currentLog.value = log
    showDetailDialog.value = true
  }
}

const handleViewSessions = (userId: string) => {
  console.log('查看用户会话历史:', userId)
  showSessionDialog.value = true
}

const handleViewIPHistory = (ipAddress: string) => {
  console.log('查看IP历史:', ipAddress)
  showIPDialog.value = true
}

const handleSecurityCheck = (id: number) => {
  console.log('执行安全检查:', id)
}

const handleRiskAnalysis = (id: number) => {
  console.log('执行风险分析:', id)
}

const handleUploadChain = (id: number) => {
  const log = logs.value.find((l) => l.id === id)
  if (log) {
    log.isOnChain = true
    console.log('上链存证:', id)
  }
  showDetailDialog.value = false
}

const handleForceLogout = (sessionId: string) => {
  console.log('强制下线会话:', sessionId)
}

// 样式函数
const getUserTypeVariant = (type: UserType) => {
  return type.includes('政府') ? 'default' : 'outline'
}

const getStatusVariant = (status: LoginStatus) => {
  switch (status) {
    case '成功':
      return 'default'
    case '失败':
      return 'secondary'
    case '异常':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getStatusIcon = (status: LoginStatus) => {
  switch (status) {
    case '成功':
      return CheckCircle
    case '失败':
      return XCircle
    case '异常':
      return AlertTriangle
    default:
      return Clock
  }
}

const getStatusIconColor = (status: LoginStatus) => {
  switch (status) {
    case '成功':
      return 'text-green-500'
    case '失败':
      return 'text-red-500'
    case '异常':
      return 'text-orange-500'
    default:
      return 'text-gray-500'
  }
}

const getRiskLevelVariant = (level: RiskLevel) => {
  switch (level) {
    case '正常':
      return 'default'
    case '低风险':
      return 'secondary'
    case '中风险':
      return 'destructive'
    case '高风险':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getRiskRowStyle = (level: RiskLevel) => {
  switch (level) {
    case '高风险':
      return 'border-l-4 border-l-red-500 bg-red-50 dark:bg-red-900/10'
    case '中风险':
      return 'border-l-4 border-l-orange-500 bg-orange-50 dark:bg-orange-900/10'
    case '低风险':
      return 'border-l-4 border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/10'
    default:
      return ''
  }
}

const getIPReputationVariant = (reputation: IPReputation) => {
  switch (reputation) {
    case '可信':
      return 'default'
    case '一般':
      return 'secondary'
    case '可疑':
      return 'destructive'
    case '恶意':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getIPReputationIcon = (reputation: IPReputation) => {
  switch (reputation) {
    case '可信':
      return CheckCircle
    case '一般':
      return Shield
    case '可疑':
      return AlertTriangle
    case '恶意':
      return XCircle
    default:
      return Globe
  }
}
</script>
