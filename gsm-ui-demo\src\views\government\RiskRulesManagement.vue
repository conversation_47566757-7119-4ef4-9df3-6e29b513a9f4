<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理系统风险检测规则，支持规则创建、编辑、启用/禁用和规则引擎配置
        </p>
      </div>
      <Button @click="handleCreateRule" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        新增规则
      </Button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">规则总数</p>
              <p class="text-2xl font-bold">{{ stats.total }}</p>
            </div>
            <Shield class="w-8 h-8 text-primary" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">启用规则</p>
              <p class="text-2xl font-bold text-green-600">{{ stats.active }}</p>
            </div>
            <CheckCircle class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-bold text-foreground">高风险规则</p>
              <p class="text-2xl font-bold text-red-600">{{ stats.highRisk }}</p>
            </div>
            <AlertTriangle class="w-8 h-8 text-red-500" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-base font-semibold text-muted-foreground">触发次数</p>
              <p class="text-2xl font-bold text-blue-600">{{ stats.triggered }}</p>
            </div>
            <Activity class="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 规则列表 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>规则列表</span>
          <Badge variant="outline"> 共 {{ filteredRules.length }} 条规则 </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 筛选条件 -->
        <CompactFilterForm
          :filter-fields="filterFields"
          :show-export="true"
          :initial-values="filters"
          @search="handleSearch"
          @reset="resetFilters"
          @export="exportData"
        />
        <div class="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-[80px]">序号</TableHead>
                <TableHead>规则名称</TableHead>
                <TableHead>规则类别</TableHead>
                <TableHead>风险等级</TableHead>
                <TableHead>适用范围</TableHead>
                <TableHead>触发次数</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead class="w-[140px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="pagedRules.length === 0">
                <TableCell :colspan="9" class="h-24 text-center text-muted-foreground">
                  暂无规则数据
                </TableCell>
              </TableRow>
              <TableRow
                v-for="(rule, index) in pagedRules"
                :key="rule.id"
                class="hover:bg-muted/40"
              >
                <TableCell>{{ (currentPage - 1) * pageSize + index + 1 }}</TableCell>
                <TableCell>
                  <div class="flex items-center gap-2">
                    <component :is="getRuleIcon(rule.category)" class="w-4 h-4 text-primary" />
                    <div>
                      <div class="font-medium">{{ rule.name }}</div>
                      <div class="text-xs text-muted-foreground">ID: {{ rule.id }}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="getCategoryVariant(rule.category)">{{ rule.category }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getRiskLevelVariant(rule.riskLevel)">{{ rule.riskLevel }}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" class="text-xs">{{ rule.scope }}</Badge>
                </TableCell>
                <TableCell>
                  <div class="flex items-center gap-1">
                    <span class="font-medium">{{ rule.triggerCount }}</span>
                    <Activity class="w-3 h-3 text-muted-foreground" />
                  </div>
                </TableCell>
                <TableCell>
                  <Badge :variant="rule.isActive ? 'default' : 'destructive'">
                    {{ rule.isActive ? '启用' : '禁用' }}
                  </Badge>
                </TableCell>
                <TableCell class="whitespace-nowrap text-sm">{{ rule.updatedAt }}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="handleView(rule.id)">
                        <Eye class="w-4 h-4 mr-2" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleEdit(rule.id)">
                        <Edit class="w-4 h-4 mr-2" />
                        编辑规则
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleEditEngine(rule.id)">
                        <Settings class="w-4 h-4 mr-2" />
                        规则引擎
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="handleTest(rule.id)">
                        <Play class="w-4 h-4 mr-2" />
                        测试规则
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDuplicate(rule.id)">
                        <Copy class="w-4 h-4 mr-2" />
                        复制规则
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        v-if="rule.isActive"
                        @click="handleDisable(rule.id)"
                        class="text-orange-600"
                      >
                        <XCircle class="w-4 h-4 mr-2" />
                        禁用规则
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        v-if="!rule.isActive"
                        @click="handleEnable(rule.id)"
                        class="text-green-600"
                      >
                        <CheckCircle class="w-4 h-4 mr-2" />
                        启用规则
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem @click="handleDelete(rule.id)" class="text-red-600">
                        <Trash2 class="w-4 h-4 mr-2" />
                        删除规则
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4 px-4 pb-4">
          <div class="text-sm text-muted-foreground">
            显示第 {{ (currentPage - 1) * pageSize + 1 }} -
            {{ Math.min(currentPage * pageSize, filteredRules.length) }} 条， 共
            {{ filteredRules.length }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              上一页
            </Button>
            <span class="text-sm"> {{ currentPage }} / {{ totalPages }} </span>
            <Button
              variant="outline"
              size="sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 规则详情对话框 -->
    <Dialog :open="showDetailDialog" @update:open="showDetailDialog = $event">
      <DialogContent class="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>规则详情 - {{ currentRule?.name }}</DialogTitle>
          <DialogDescription> 查看规则详细信息、配置参数和执行逻辑 </DialogDescription>
        </DialogHeader>

        <div v-if="currentRule" class="space-y-6 py-4">
          <Tabs default-value="basic" class="w-full">
            <TabsList class="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="conditions">触发条件</TabsTrigger>
              <TabsTrigger value="actions">执行动作</TabsTrigger>
              <TabsTrigger value="logs">执行日志</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">规则名称</Label>
                  <p class="text-sm">{{ currentRule.name }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">规则类别</Label>
                  <Badge :variant="getCategoryVariant(currentRule.category)">{{
                    currentRule.category
                  }}</Badge>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">风险等级</Label>
                  <Badge :variant="getRiskLevelVariant(currentRule.riskLevel)">{{
                    currentRule.riskLevel
                  }}</Badge>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">适用范围</Label>
                  <p class="text-sm">{{ currentRule.scope }}</p>
                </div>
                <div class="col-span-2">
                  <Label class="text-base font-semibold text-muted-foreground">规则描述</Label>
                  <p class="text-sm">{{ currentRule.description }}</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">触发次数</Label>
                  <p class="text-sm">{{ currentRule.triggerCount }} 次</p>
                </div>
                <div>
                  <Label class="text-base font-semibold text-muted-foreground">最后触发</Label>
                  <p class="text-sm">{{ currentRule.lastTriggered || '从未触发' }}</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="conditions" class="space-y-4">
              <div class="p-4 border rounded-md">
                <h4 class="font-medium mb-2">触发条件配置</h4>
                <div class="text-sm text-muted-foreground">
                  此处将显示规则的具体触发条件和参数配置
                </div>
                <div class="mt-4 p-3 bg-muted rounded font-mono text-xs">
                  // 示例条件<br />
                  if (dataType === "location" && accuracy &lt; 10) {<br />
                  &nbsp;&nbsp;triggerAlert("位置数据精度不足");<br />
                  }
                </div>
              </div>
            </TabsContent>

            <TabsContent value="actions" class="space-y-4">
              <div class="p-4 border rounded-md">
                <h4 class="font-medium mb-2">执行动作配置</h4>
                <div class="text-sm text-muted-foreground">此处将显示规则触发后的执行动作</div>
                <div class="mt-4 space-y-2">
                  <Badge variant="outline">记录风险日志</Badge>
                  <Badge variant="outline">发送告警通知</Badge>
                  <Badge variant="outline">暂停数据处理</Badge>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="logs" class="space-y-4">
              <div class="text-sm text-muted-foreground">最近执行日志将在此处显示</div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showDetailDialog = false">关闭</Button>
          <Button @click="handleEdit(currentRule?.id)">编辑规则</Button>
          <Button @click="handleEditEngine(currentRule?.id)" variant="secondary">
            <Settings class="w-4 h-4 mr-2" />
            规则引擎
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 规则测试对话框 -->
    <Dialog :open="showTestDialog" @update:open="showTestDialog = $event">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>规则测试</DialogTitle>
          <DialogDescription> 测试规则 "{{ currentRule?.name }}" 的执行效果 </DialogDescription>
        </DialogHeader>

        <div class="space-y-4 py-4">
          <div>
            <Label for="test-data">测试数据</Label>
            <Textarea
              id="test-data"
              v-model="testData"
              placeholder="请输入测试数据（JSON格式）"
              rows="6"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showTestDialog = false">取消</Button>
          <Button @click="runTest">
            <Play class="w-4 h-4 mr-2" />
            运行测试
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Copy,
  Edit,
  Eye,
  MoreHorizontal,
  Play,
  Plus,
  Settings,
  Shield,
  Trash2,
  XCircle,
  Database,
} from 'lucide-vue-next'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import CompactFilterForm, { type FilterField } from '@/components/ui/filter/CompactFilterForm.vue'

// 路由
const router = useRouter()

type RiskLevel = '高风险' | '中风险' | '低风险'
type RuleCategory = '数据采集' | '数据传输' | '数据处理' | '访问控制' | '系统安全'
type RuleScope = '车端' | '云端' | '全局'

interface RiskRule {
  id: number
  name: string
  category: RuleCategory
  riskLevel: RiskLevel
  scope: RuleScope
  description: string
  triggerCount: number
  lastTriggered?: string
  isActive: boolean
  updatedAt: string
}

// 新增：检索筛选类型定义，供 handleSearch 使用
type SearchFilters = Partial<{
  name: string
  category: 'ALL' | RuleCategory
  riskLevel: 'ALL' | RiskLevel
  scope: 'ALL' | RuleScope
  isActive: 'ALL' | 'true' | 'false'
}>

// 统计数据
const stats = ref({
  total: 28,
  active: 24,
  highRisk: 8,
  triggered: 1247,
})

// 筛选条件
const filters = ref({
  name: '',
  category: 'ALL' as 'ALL' | RuleCategory,
  riskLevel: 'ALL' as 'ALL' | RiskLevel,
  scope: 'ALL' as 'ALL' | RuleScope,
  isActive: 'ALL' as 'ALL' | 'true' | 'false',
})

// 筛选表单配置
const filterFields: FilterField[] = [
  {
    key: 'name',
    label: '规则名称',
    type: 'input',
    placeholder: '请输入规则名称',
  },
  {
    key: 'category',
    label: '规则类别',
    type: 'select',
    placeholder: '请选择类别',
    options: [
      { label: '全部类别', value: 'ALL' },
      { label: '数据采集', value: '数据采集' },
      { label: '数据传输', value: '数据传输' },
      { label: '数据处理', value: '数据处理' },
      { label: '访问控制', value: '访问控制' },
      { label: '系统安全', value: '系统安全' },
    ],
  },
  {
    key: 'riskLevel',
    label: '风险等级',
    type: 'select',
    placeholder: '请选择等级',
    options: [
      { label: '全部等级', value: 'ALL' },
      { label: '高风险', value: '高风险' },
      { label: '中风险', value: '中风险' },
      { label: '低风险', value: '低风险' },
    ],
  },
  {
    key: 'scope',
    label: '适用范围',
    type: 'select',
    placeholder: '请选择范围',
    options: [
      { label: '全部范围', value: 'ALL' },
      { label: '车端', value: '车端' },
      { label: '云端', value: '云端' },
      { label: '全局', value: '全局' },
    ],
  },
  {
    key: 'isActive',
    label: '规则状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '全部状态', value: 'ALL' },
      { label: '启用', value: 'true' },
      { label: '禁用', value: 'false' },
    ],
  },
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const showDetailDialog = ref(false)
const showTestDialog = ref(false)
const currentRule = ref<RiskRule | null>(null)
const testData = ref('')

// Mock 数据
const rules = ref<RiskRule[]>([
  {
    id: 1,
    name: '位置数据精度异常检测',
    category: '数据采集',
    riskLevel: '高风险',
    scope: '车端',
    description: '检测车辆位置数据精度是否符合要求，防止低精度数据导致的安全风险',
    triggerCount: 156,
    lastTriggered: '2025-08-25 14:30:00',
    isActive: true,
    updatedAt: '2025-08-20 10:00:00',
  },
  {
    id: 2,
    name: '数据传输加密检查',
    category: '数据传输',
    riskLevel: '高风险',
    scope: '全局',
    description: '验证数据传输过程中是否使用了安全的加密协议',
    triggerCount: 89,
    lastTriggered: '2025-08-25 12:15:00',
    isActive: true,
    updatedAt: '2025-08-18 16:30:00',
  },
  {
    id: 3,
    name: '敏感区域访问控制',
    category: '访问控制',
    riskLevel: '高风险',
    scope: '车端',
    description: '检测车辆是否进入敏感或禁止区域，触发相应的安全措施',
    triggerCount: 234,
    lastTriggered: '2025-08-25 09:45:00',
    isActive: true,
    updatedAt: '2025-08-15 14:00:00',
  },
  {
    id: 4,
    name: '数据处理频率异常',
    category: '数据处理',
    riskLevel: '中风险',
    scope: '云端',
    description: '监测数据处理频率是否超出正常范围，防止系统过载',
    triggerCount: 67,
    lastTriggered: '2025-08-24 18:20:00',
    isActive: true,
    updatedAt: '2025-08-10 11:45:00',
  },
  {
    id: 5,
    name: '用户权限越权检测',
    category: '系统安全',
    riskLevel: '高风险',
    scope: '全局',
    description: '检测用户是否存在权限越权访问行为',
    triggerCount: 23,
    lastTriggered: '2025-08-23 15:10:00',
    isActive: true,
    updatedAt: '2025-08-05 09:00:00',
  },
  {
    id: 6,
    name: '数据完整性校验',
    category: '数据处理',
    riskLevel: '中风险',
    scope: '云端',
    description: '验证接收到的数据是否完整，检测数据传输过程中的丢失',
    triggerCount: 45,
    lastTriggered: '2025-08-22 11:30:00',
    isActive: true,
    updatedAt: '2025-07-30 16:15:00',
  },
  {
    id: 7,
    name: '异常登录行为检测',
    category: '系统安全',
    riskLevel: '中风险',
    scope: '全局',
    description: '检测异常的登录模式，如多地点同时登录等可疑行为',
    triggerCount: 12,
    lastTriggered: '2025-08-20 14:25:00',
    isActive: false,
    updatedAt: '2025-07-25 13:40:00',
  },
])

// 计算属性
const filteredRules = computed(() => {
  return rules.value.filter((rule) => {
    if (filters.value.name && !rule.name.includes(filters.value.name)) {
      return false
    }
    if (filters.value.category !== 'ALL' && rule.category !== filters.value.category) {
      return false
    }
    if (filters.value.riskLevel !== 'ALL' && rule.riskLevel !== filters.value.riskLevel) {
      return false
    }
    if (filters.value.scope !== 'ALL' && rule.scope !== filters.value.scope) {
      return false
    }
    if (filters.value.isActive !== 'ALL') {
      const isActive = filters.value.isActive === 'true'
      if (rule.isActive !== isActive) return false
    }
    return true
  })
})

const totalPages = computed(() =>
  Math.max(1, Math.ceil(filteredRules.value.length / pageSize.value)),
)

const pagedRules = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  return filteredRules.value.slice(start, start + pageSize.value)
})

// 事件处理
const handleSearch = (searchFilters?: SearchFilters) => {
  if (searchFilters) {
    Object.assign(filters.value, searchFilters)
  }
  currentPage.value = 1
}

const resetFilters = () => {
  filters.value = {
    name: '',
    category: 'ALL',
    riskLevel: 'ALL',
    scope: 'ALL',
    isActive: 'ALL',
  }
  currentPage.value = 1
}

const exportData = () => {
  const headers = [
    '序号',
    '规则名称',
    '规则类别',
    '风险等级',
    '适用范围',
    '触发次数',
    '状态',
    '更新时间',
  ]
  const rows = filteredRules.value.map((rule, index) => [
    (index + 1).toString(),
    rule.name,
    rule.category,
    rule.riskLevel,
    rule.scope,
    rule.triggerCount.toString(),
    rule.isActive ? '启用' : '禁用',
    rule.updatedAt,
  ])
  const content = [headers, ...rows].map((arr) => arr.join(',')).join('\n')
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `风险规则管理_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  URL.revokeObjectURL(url)
}

const handleCreateRule = () => {
  router.push('/gov/system/rules/risk/edit')
}

const handleView = (id: number) => {
  const rule = rules.value.find((r) => r.id === id)
  if (rule) {
    currentRule.value = rule
    showDetailDialog.value = true
  }
}

const handleEdit = (id?: number) => {
  router.push(`/gov/system/rules/risk/edit/${id}`)
  showDetailDialog.value = false
}

const handleEditEngine = (id?: number) => {
  console.log('编辑规则引擎:', id)
  showDetailDialog.value = false
}

const handleTest = (id: number) => {
  const rule = rules.value.find((r) => r.id === id)
  if (rule) {
    currentRule.value = rule
    testData.value =
      '{\n  "dataType": "location",\n  "accuracy": 5,\n  "timestamp": "2025-08-25T14:30:00Z"\n}'
    showTestDialog.value = true
  }
}

const runTest = () => {
  console.log('运行规则测试:', testData.value)
  showTestDialog.value = false
}

const handleDuplicate = (id: number) => {
  console.log('复制规则:', id)
}

const handleEnable = (id: number) => {
  const rule = rules.value.find((r) => r.id === id)
  if (rule) {
    rule.isActive = true
  }
}

const handleDisable = (id: number) => {
  const rule = rules.value.find((r) => r.id === id)
  if (rule) {
    rule.isActive = false
  }
}

const handleDelete = (id: number) => {
  const index = rules.value.findIndex((r) => r.id === id)
  if (index > -1) {
    rules.value.splice(index, 1)
  }
}

// 样式函数
import type { BadgeVariants } from '@/components/ui/badge'
const getCategoryVariant = (category: RuleCategory): NonNullable<BadgeVariants['variant']> => {
  const variants: Record<RuleCategory, NonNullable<BadgeVariants['variant']>> = {
    数据采集: 'default',
    数据传输: 'secondary',
    数据处理: 'outline',
    访问控制: 'outline',
    系统安全: 'destructive',
  }
  return variants[category] ?? 'outline'
}

const getRiskLevelVariant = (level: RiskLevel) => {
  switch (level) {
    case '高风险':
      return 'destructive'
    case '中风险':
      return 'secondary'
    case '低风险':
      return 'outline'
    default:
      return 'outline'
  }
}

const getRuleIcon = (category: RuleCategory) => {
  const icons = {
    数据采集: Database,
    数据传输: Activity,
    数据处理: Settings,
    访问控制: Shield,
    系统安全: AlertTriangle,
  }
  return icons[category] || Shield
}
</script>
