# 开发规范和原则

## 核心原则

### 1. 严格使用官方组件
- **永远不要创建简化版**
- **严格使用 shadcn-vue.com 官方的方法**
- **避免模仿、简化、等绕过问题的办法**

### 2. 直面问题根源
- **遇到环境适配或依赖等问题时，应该直面问题根源**
- **系统性修复问题**
- **为今后开发扫清障碍**
- **避免技术债务**

### 3. 官方组件使用规范
- 只使用 `npx shadcn-vue@latest add [component]` 添加组件
- 只使用 `npx shadcn-vue@latest add [Block]` 添加 blocks
- 严格遵循官方文档和示例
- 不自创、不模仿、不自定义任何组件和样式

### 4. 问题解决方法
- ✅ 诊断根本原因
- ✅ 系统性修复配置
- ✅ 验证解决方案
- ❌ 创建简化版本
- ❌ 绕过问题
- ❌ 临时解决方案

## 技术栈规范

### UI 组件库
- **shadcn-vue 官方组件**：严格使用官方 CLI 添加
- **Radix Vue**：作为底层组件库
- **Tailwind CSS**：使用官方配置和主题
- **Lucide Vue Next**：官方图标库

### 主题系统
- 使用官方主题配色方案
- 通过 components.json 配置
- CSS 变量严格按照官方规范

## 项目结构规范

```
src/
├── components/
│   ├── ui/              # shadcn-vue 官方组件
│   ├── AppSidebar.vue   # 官方 blocks
│   └── Nav*.vue         # 官方导航组件
├── lib/
│   └── utils.ts         # 官方工具函数
└── assets/
    └── index.css        # 官方主题 CSS
```

## 开发流程

1. **需求分析** → 确定需要的官方组件
2. **组件添加** → 使用官方 CLI 添加
3. **配置验证** → 确保配置正确
4. **功能实现** → 基于官方组件开发
5. **测试验证** → 确保功能正常

## 禁止行为

- ❌ 手动创建 UI 组件
- ❌ 复制粘贴组件代码
- ❌ 修改官方组件源码
- ❌ 创建简化版本
- ❌ 绕过配置问题
- ❌ 使用非官方组件库

## 质量保证

- 除特别生命外，所有组件必须来自官方 shadcn-vue
- 所有配置必须符合官方规范
- 所有问题必须从根源解决
- 代码质量和可维护性优先

---

**记住：质量胜过速度，官方胜过自制，系统性解决胜过临时方案**
