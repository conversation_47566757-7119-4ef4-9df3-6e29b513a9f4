<template>
  <section class="gsm-external-module stats-section">
    <div class="external-container">
      <div class="section-header">
        <h2 class="section-title">平台运营数据</h2>
        <p class="section-subtitle">实时展示平台运营效果，全面反映智能网联汽车数据安全监管成效</p>
      </div>
      <div class="stats-grid">
        <div v-for="stat in operationalStats" :key="stat.id" class="stat-card">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-title">{{ stat.title }}</div>
          <div class="stat-description">{{ stat.description }}</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 运营统计数据
const operationalStats = [
  {
    id: 'policy-implementation',
    value: '100%',
    title: '政策落实覆盖率',
    description: '国家数据安全政策法规全面落实执行',
  },
  {
    id: 'regulatory-coverage',
    value: '31省市',
    title: '监管覆盖范围',
    description: '全国省级行政区域监管体系全覆盖',
  },
  {
    id: 'vehicle-monitoring',
    value: '500,000+',
    title: '监管车辆数量',
    description: '智能网联汽车实时安全监控管理',
  },
  {
    id: 'enterprise-integration',
    value: '1,200+',
    title: '接入企业数量',
    description: '汽车制造与科技企业接入平台',
  },
  {
    id: 'compliance-rate',
    value: '99.8%',
    title: '合规达标率',
    description: '企业数据处理活动合规性检查通过率',
  },
  {
    id: 'emergency-response',
    value: '7×24小时',
    title: '应急响应时效',
    description: '全天候安全事件监控和应急处置',
  },
]
</script>

<style scoped>
/* 统计数据区域 */
.stats-section {
  padding: 6rem 0;
  background: var(--external-background-color);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--external-text-color);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--external-primary-color) 0%,
    var(--external-primary-hover) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--external-text-color-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.stat-card {
  background: var(--external-card-bg);
  border: 1px solid var(--external-border-color);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--external-primary-color), var(--external-primary-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 128, 204, 0.15);
  border-color: var(--external-primary-color);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--external-primary-color);
  margin-bottom: 1rem;
  line-height: 1;
  text-shadow: 0 2px 8px rgba(0, 128, 204, 0.3);
}

.stat-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--external-text-color);
  margin-bottom: 0.75rem;
}

.stat-description {
  font-size: 0.875rem;
  color: var(--external-text-color-regular);
  line-height: 1.5;
}

/* Light theme specific adjustments */
.gsm-external-module[data-theme='light'] .stats-section {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  position: relative;
}

.gsm-external-module[data-theme='light'] .stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid-light-stats" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="%23e2e8f0" stroke-width="0.5" opacity="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid-light-stats)"/></svg>');
  opacity: 0.6;
  z-index: 0;
}

.gsm-external-module[data-theme='light'] .external-container {
  position: relative;
  z-index: 1;
}

.gsm-external-module[data-theme='light'] .section-title {
  color: #1e293b;
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.gsm-external-module[data-theme='light'] .section-subtitle {
  color: #64748b;
}

.gsm-external-module[data-theme='light'] .stat-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(203, 213, 225, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.gsm-external-module[data-theme='light'] .stat-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 40px rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(255, 255, 255, 0.95);
}

.gsm-external-module[data-theme='light'] .stat-value {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.gsm-external-module[data-theme='light'] .stat-title {
  color: #1e293b;
}

.gsm-external-module[data-theme='light'] .stat-description {
  color: #475569;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }

  .stat-card {
    padding: 2.5rem;
  }

  .stat-value {
    font-size: 3rem;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-section {
    padding: 4rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-value {
    font-size: 2rem;
  }

  .stat-title {
    font-size: 1rem;
  }

  .stat-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1.25rem;
  }
}
</style>
