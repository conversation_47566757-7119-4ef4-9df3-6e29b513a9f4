<template>
  <header class="header">
    <nav class="nav-container">
      <div class="nav-brand">
        <RouterLink
          :to="{ name: 'Landing' }"
          class="brand-flex brand-link"
          aria-label="返回首页"
          title="返回首页"
        >
          <AppLogo
            size="medium"
            alt="地理信息安全监测平台"
            className="brand-logo hover:scale-105 transition-transform duration-300"
            :forceTheme="'dark'"
          />
          <div>
            <h1 class="brand-title">地理信息安全监测平台</h1>
            <p class="brand-subtitle">{{ roleLabel }}</p>
          </div>
        </RouterLink>
      </div>

      <nav class="primary-nav">
        <button
          v-for="item in menus"
          :key="item.to"
          type="button"
          class="nav-btn"
          :class="{ active: isActive(item) }"
          @click="go(item.to)"
        >
          {{ item.label }}
        </button>
      </nav>

      <div class="nav-actions">
        <ThemeToggle />
        <TopUserMenu />
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ThemeToggle from '@/components/ThemeToggle.vue'
import TopUserMenu from '@/components/layout/TopUserMenu.vue'
import AppLogo from '@/components/layout/AppLogo.vue'
import { getPrimaryMenu, type Role } from '@/config/primary-menu'

const route = useRoute()
const router = useRouter()

const role = computed<Role>(() => {
  const p = route.path
  if (p.startsWith('/corp')) return 'enterprise'
  if (p.startsWith('/gov') || p.startsWith('/admin')) return 'government'
  const storage = localStorage.getItem('user_type')
  return storage === 'enterprise' ? 'enterprise' : 'government'
})

const roleLabel = computed(() =>
  role.value === 'government' ? '政府端管理系统' : '企业端服务系统',
)

const menus = computed(() => getPrimaryMenu(role.value))

function isActive(item: { to: string; exact?: boolean }) {
  if (item.exact) return route.path === item.to
  return route.path.startsWith(item.to)
}

function go(to: string) {
  if (to !== route.fullPath) router.push(to)
}
</script>

<style scoped>
/* 字体已在全局 fonts.css 中定义 */

/* 政府端 header 改为深色底色 + 左浅右深渐变覆盖 */
.header {
  position: sticky;
  top: 0;
  z-index: 9999; /* 最高z-index */
  background: #010a1b; /* 参考色：深色底 */
  border-bottom: 1px solid rgba(51, 65, 85, 0.4);
  width: 100%;
  height: 80px; /* 统一高度 */
  box-shadow: 0 2px 16px rgba(15, 23, 42, 0.1);
  overflow: hidden; /* 隐藏伪元素溢出 */
}

/* 渐变覆盖层：左侧亮蓝过渡到右侧透明，露出深色底，达到左浅右深效果 */
.header::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, #2b65c3, hsla(0, 0%, 100%, 0) 60%);
  pointer-events: none;
  z-index: 0; /* 置于内容之下 */
}

.nav-container {
  position: relative; /* 提升内容层级，盖住覆盖层 */
  z-index: 1;
  display: flex;
  justify-content: space-between; /* 恢复原来的布局，工具按钮在右侧 */
  align-items: center;
  padding: 0.5rem 1rem;
  width: 100%;
  box-sizing: border-box;
  gap: 0.5rem;
  height: 100%; /* 垂直居中对齐 */
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 240px; /* 减少宽度，让菜单更靠近左端 */
}

.sidebar-trigger {
  display: inline-flex;
  align-items: center;
}

.brand-flex {
  display: inline-flex;
  align-items: center;
  gap: 0.2rem;
  color: #e5e7eb;
  text-decoration: none;
}

.brand-title {
  font-family:
    'AlimamaFangYuanTiVF',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-size: 1.67rem; /* 加大1/3 */
  font-weight: 900; /* 强烈加粗 */
  font-style: italic; /* 斜体 */
  line-height: 1.5rem;
  margin: 4px 0 0 0;
  color: #ffffff;
}

.brand-subtitle {
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0.9;
  margin: 3px 0 0 0;
  color: #ffffff;
}

.primary-nav {
  display: flex;
  gap: 1rem; /* 减少间距，让菜单更紧凑 */
}

.nav-btn {
  color: #e5e7eb;
  background: transparent;
  border: none; /* 移除完整边框 */
  padding: 0.625rem 2.25rem; /* 加大按钮尺寸 */
  border-radius: 0.75rem; /* 稍微加大圆角 */
  font-size: 1.4rem; /* 调整为更合适的字体尺寸 */
  font-weight: 500; /* 增加字体权重 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 44px; /* 确保触摸友好 */
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(43, 101, 195, 0.1), rgba(43, 101, 195, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.nav-btn:hover {
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(43, 101, 195, 0.15);
}

.nav-btn:hover::before {
  opacity: 1;
}

.nav-btn.active {
  color: #ffffff;
  font-weight: 600;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(43, 101, 195, 0.3);
}

.nav-btn.active::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(43, 101, 195, 0.25), rgba(43, 101, 195, 0.15));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-actions {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* nav-actions 区域的按钮统一尺寸与幽灵按钮风格（深色背景 + 浅蓝边框），避免反白效果 */
.nav-actions :deep(button),
.nav-actions :deep(.user-trigger) {
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  background: #010a1b !important; /* 与 header 背景一致 */
  border: 1px solid rgba(43, 101, 195, 0.6) !important; /* 浅蓝描边 */
  color: #cfe2ff !important;
  box-shadow: none !important;
  line-height: 1;
}

.nav-actions :deep(button:hover),
.nav-actions :deep(.user-trigger:hover) {
  background: rgba(43, 101, 195, 0.12) !important;
  border-color: rgba(43, 101, 195, 0.9) !important;
  color: #e6f0ff !important;
}

.nav-actions :deep(button:active),
.nav-actions :deep(.user-trigger:active) {
  background: rgba(43, 101, 195, 0.18) !important;
}

.nav-actions :deep(button:focus-visible),
.nav-actions :deep(.user-trigger:focus-visible) {
  outline: none;
  box-shadow: 0 0 0 2px rgba(43, 101, 195, 0.45) !important;
}

/* 响应式设计优化 */
@media (max-width: 1024px) {
  .primary-nav {
    gap: 0.125rem; /* 小屏幕下减少间距 */
    margin-left: 0.5rem; /* 小屏幕下适度靠近品牌标题 */
  }

  .nav-btn {
    padding: 0.5rem 1rem; /* 小屏幕下稍微减少内边距 */
    font-size: 1.2rem; /* 小屏幕下调整字体大小 */
    min-height: 40px; /* 小屏幕下调整最小高度 */
  }
}

@media (max-width: 768px) {
  .nav-brand {
    min-width: 200px; /* 小屏幕下进一步减少品牌区域宽度 */
  }

  .primary-nav {
    gap: 0.1rem; /* 超小屏幕下最小间距 */
    margin-left: 0.3rem; /* 超小屏幕下适度靠近品牌标题 */
  }

  .nav-btn {
    padding: 0.4rem 0.8rem; /* 超小屏幕下最小内边距 */
    font-size: 1rem; /* 超小屏幕下调整字体大小 */
    min-height: 36px; /* 超小屏幕下最小高度 */
  }
}

/* 深色主题兼容性 */
@media (prefers-color-scheme: dark) {
  .nav-btn {
    color: #f1f5f9; /* 在深色主题下使用更亮的文字颜色 */
  }

  .nav-btn:hover {
    color: #ffffff;
  }

  .nav-btn.active {
    color: #ffffff;
  }
}
</style>
