<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">
          {{ isEditMode ? '编辑' : '新建' }}风险规则
        </h1>
        <p class="text-muted-foreground">
          {{ isEditMode ? '修改现有风险检测规则的配置和逻辑' : '创建新的风险检测规则和引擎逻辑' }}
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="handlePreview" variant="outline" class="flex items-center gap-2">
          <Eye class="w-4 h-4" />
          预览规则
        </Button>
        <Button @click="handleTest" variant="outline" class="flex items-center gap-2">
          <Play class="w-4 h-4" />
          测试规则
        </Button>
        <Button @click="handleSave" class="flex items-center gap-2">
          <Save class="w-4 h-4" />
          保存规则
        </Button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：基本信息和配置 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 基本信息 -->
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <Label>规则名称 *</Label>
                <Input v-model="ruleForm.name" placeholder="请输入规则名称" />
              </div>
              <div>
                <Label>规则类别</Label>
                <Select v-model="ruleForm.category">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="数据采集">数据采集</SelectItem>
                    <SelectItem value="数据传输">数据传输</SelectItem>
                    <SelectItem value="数据处理">数据处理</SelectItem>
                    <SelectItem value="访问控制">访问控制</SelectItem>
                    <SelectItem value="系统安全">系统安全</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>风险等级</Label>
                <Select v-model="ruleForm.riskLevel">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="低风险">低风险</SelectItem>
                    <SelectItem value="中风险">中风险</SelectItem>
                    <SelectItem value="高风险">高风险</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>适用范围</Label>
                <Select v-model="ruleForm.scope">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="车端">车端</SelectItem>
                    <SelectItem value="云端">云端</SelectItem>
                    <SelectItem value="全局">全局</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label>规则描述</Label>
              <Textarea
                v-model="ruleForm.description"
                placeholder="请详细描述规则的用途和检测逻辑"
                rows="3"
              />
            </div>
            <div class="flex items-center justify-between">
              <Label>启用规则</Label>
              <Switch v-model:checked="ruleForm.isActive" />
            </div>
          </CardContent>
        </Card>

        <!-- 触发条件配置 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Settings class="w-5 h-5" />
              触发条件配置
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-4">
              <div
                v-for="(condition, index) in ruleForm.conditions"
                :key="index"
                class="p-4 border rounded-lg"
              >
                <div class="flex items-center justify-between mb-3">
                  <h4 class="font-medium">条件 {{ index + 1 }}</h4>
                  <Button
                    @click="removeCondition(index)"
                    variant="ghost"
                    size="sm"
                    :disabled="ruleForm.conditions.length === 1"
                  >
                    <X class="w-4 h-4" />
                  </Button>
                </div>

                <div class="grid grid-cols-3 gap-3">
                  <div>
                    <Label class="text-sm">数据字段</Label>
                    <Select v-model="condition.field">
                      <SelectTrigger>
                        <SelectValue placeholder="选择字段" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="location.accuracy">位置精度</SelectItem>
                        <SelectItem value="location.speed">行驶速度</SelectItem>
                        <SelectItem value="data.frequency">数据频率</SelectItem>
                        <SelectItem value="user.loginCount">登录次数</SelectItem>
                        <SelectItem value="system.cpuUsage">CPU使用率</SelectItem>
                        <SelectItem value="network.bandwidth">网络带宽</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label class="text-sm">比较操作</Label>
                    <Select v-model="condition.operator">
                      <SelectTrigger>
                        <SelectValue placeholder="选择操作" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value=">">>（大于）</SelectItem>
                        <SelectItem value="<"><（小于）</SelectItem>
                        <SelectItem value=">=">=（大于等于）</SelectItem>
                        <SelectItem value="<=">=（小于等于）</SelectItem>
                        <SelectItem value="==">=（等于）</SelectItem>
                        <SelectItem value="!=">=（不等于）</SelectItem>
                        <SelectItem value="contains">包含</SelectItem>
                        <SelectItem value="not_contains">不包含</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label class="text-sm">阈值</Label>
                    <Input v-model="condition.value" placeholder="输入阈值" />
                  </div>
                </div>

                <div class="mt-3 grid grid-cols-2 gap-3">
                  <div>
                    <Label class="text-sm">逻辑关系</Label>
                    <Select v-model="condition.logic">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="AND">AND（且）</SelectItem>
                        <SelectItem value="OR">OR（或）</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label class="text-sm">时间窗口（分钟）</Label>
                    <Input v-model.number="condition.timeWindow" type="number" min="1" max="1440" />
                  </div>
                </div>
              </div>
            </div>

            <Button @click="addCondition" variant="outline" class="w-full">
              <Plus class="w-4 h-4 mr-2" />
              添加条件
            </Button>
          </CardContent>
        </Card>

        <!-- 执行动作配置 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Zap class="w-5 h-5" />
              执行动作配置
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-3">
              <div
                v-for="action in availableActions"
                :key="action.key"
                class="flex items-center justify-between p-3 border rounded-lg"
              >
                <div class="flex items-center gap-3">
                  <Checkbox
                    :id="action.key"
                    :checked="ruleForm.actions.includes(action.key)"
                    @update:checked="toggleAction(action.key, $event)"
                  />
                  <div>
                    <Label :for="action.key" class="font-medium">{{ action.label }}</Label>
                    <p class="text-sm text-muted-foreground">{{ action.description }}</p>
                  </div>
                </div>
                <component :is="action.icon" class="w-5 h-5 text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 高级配置 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Sliders class="w-5 h-5" />
              高级配置
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <Label>优先级</Label>
                <Select v-model="ruleForm.priority">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="低">低</SelectItem>
                    <SelectItem value="中">中</SelectItem>
                    <SelectItem value="高">高</SelectItem>
                    <SelectItem value="紧急">紧急</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>执行频率（秒）</Label>
                <Input v-model.number="ruleForm.frequency" type="number" min="1" max="3600" />
              </div>
              <div>
                <Label>重试次数</Label>
                <Input v-model.number="ruleForm.retryCount" type="number" min="0" max="10" />
              </div>
              <div>
                <Label>超时时间（秒）</Label>
                <Input v-model.number="ruleForm.timeout" type="number" min="1" max="300" />
              </div>
            </div>

            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <Label>启用日志记录</Label>
                <Switch v-model:checked="ruleForm.enableLogging" />
              </div>
              <div class="flex items-center justify-between">
                <Label>启用告警通知</Label>
                <Switch v-model:checked="ruleForm.enableAlert" />
              </div>
              <div class="flex items-center justify-between">
                <Label>自动禁用（触发次数过多）</Label>
                <Switch v-model:checked="ruleForm.autoDisable" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧：规则预览和测试 -->
      <div class="space-y-6">
        <!-- 规则预览 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-lg font-semibold">规则预览</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3 text-sm">
              <div>
                <span class="font-medium text-muted-foreground">规则名称:</span>
                <p class="mt-1">{{ ruleForm.name || '未命名规则' }}</p>
              </div>
              <div>
                <span class="font-medium text-muted-foreground">触发条件:</span>
                <div class="mt-1 space-y-1">
                  <div
                    v-for="(condition, index) in ruleForm.conditions"
                    :key="index"
                    class="text-xs bg-muted p-2 rounded"
                  >
                    {{ getConditionText(condition) }}
                    <span
                      v-if="index < ruleForm.conditions.length - 1"
                      class="text-blue-600 font-medium ml-1"
                    >
                      {{ condition.logic }}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <span class="font-medium text-muted-foreground">执行动作:</span>
                <div class="mt-1 space-y-1">
                  <Badge
                    v-for="actionKey in ruleForm.actions"
                    :key="actionKey"
                    variant="outline"
                    class="text-xs mr-1 mb-1"
                  >
                    {{ getActionLabel(actionKey) }}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 规则测试 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-lg font-semibold">规则测试</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <Label>测试数据（JSON）</Label>
              <Textarea
                v-model="testData"
                placeholder="输入测试数据..."
                rows="6"
                class="font-mono text-sm"
              />
            </div>
            <Button @click="runTest" class="w-full">
              <Play class="w-4 h-4 mr-2" />
              运行测试
            </Button>

            <!-- 测试结果 -->
            <div v-if="testResult" class="mt-4">
              <Label class="text-base font-semibold text-muted-foreground">测试结果</Label>
              <div
                :class="`mt-2 p-3 rounded border ${testResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`"
              >
                <div class="flex items-center gap-2">
                  <component
                    :is="testResult.success ? CheckCircle : XCircle"
                    :class="`w-4 h-4 ${testResult.success ? 'text-green-600' : 'text-red-600'}`"
                  />
                  <span
                    :class="`text-base font-semibold ${testResult.success ? 'text-green-600' : 'text-red-600'}`"
                  >
                    {{ testResult.success ? '规则匹配成功' : '规则匹配失败' }}
                  </span>
                </div>
                <p class="text-sm mt-1 text-muted-foreground">{{ testResult.message }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 历史版本 -->
        <Card v-if="isEditMode">
          <CardHeader>
            <CardTitle class="text-lg font-semibold">历史版本</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <div
                v-for="version in ruleVersions"
                :key="version.id"
                class="flex items-center justify-between p-2 border rounded"
              >
                <div>
                  <div class="text-base font-semibold">v{{ version.version }}</div>
                  <div class="text-xs text-muted-foreground">{{ version.updatedAt }}</div>
                </div>
                <Button @click="restoreVersion(version.id)" variant="ghost" size="sm">
                  <RotateCcw class="w-3 h-3" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 测试结果对话框 -->
    <Dialog :open="showTestDialog" @update:open="showTestDialog = $event">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>规则测试结果</DialogTitle>
        </DialogHeader>

        <div class="py-4 space-y-4">
          <div class="p-4 border rounded-lg">
            <h4 class="font-medium mb-2">执行摘要</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-muted-foreground">测试时间:</span>
                <span>{{ new Date().toLocaleString() }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-muted-foreground">执行耗时:</span>
                <span>{{ (Math.random() * 100) | 0 }}ms</span>
              </div>
              <div class="flex justify-between">
                <span class="text-muted-foreground">匹配结果:</span>
                <Badge :variant="testResult?.success ? 'default' : 'destructive'">
                  {{ testResult?.success ? '成功' : '失败' }}
                </Badge>
              </div>
            </div>
          </div>

          <div class="p-4 border rounded-lg">
            <h4 class="font-medium mb-2">条件评估</h4>
            <div class="space-y-2">
              <div v-for="(condition, index) in ruleForm.conditions" :key="index" class="text-sm">
                <div class="flex items-center gap-2">
                  <CheckCircle class="w-4 h-4 text-green-500" />
                  <span>条件{{ index + 1 }}: {{ getConditionText(condition) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showTestDialog = false">关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  AlertTriangle,
  Bell,
  CheckCircle,
  Database,
  Eye,
  Play,
  Plus,
  RotateCcw,
  Save,
  Settings,
  Shield,
  Sliders,
  X,
  XCircle,
  Zap,
} from 'lucide-vue-next'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface RuleCondition {
  field: string
  operator: string
  value: string
  logic: string
  timeWindow: number
}

interface RuleForm {
  name: string
  category: string
  riskLevel: string
  scope: string
  description: string
  isActive: boolean
  conditions: RuleCondition[]
  actions: string[]
  priority: string
  frequency: number
  retryCount: number
  timeout: number
  enableLogging: boolean
  enableAlert: boolean
  autoDisable: boolean
}

interface TestResult {
  success: boolean
  message: string
}

interface RuleVersion {
  id: string
  version: string
  updatedAt: string
}

// 路由和状态
const route = useRoute()
const router = useRouter()
const ruleId = route.params.id as string
const isEditMode = computed(() => !!ruleId)

// 表单数据
const ruleForm = ref<RuleForm>({
  name: '',
  category: '数据采集',
  riskLevel: '中风险',
  scope: '全局',
  description: '',
  isActive: true,
  conditions: [
    {
      field: 'location.accuracy',
      operator: '<',
      value: '10',
      logic: 'AND',
      timeWindow: 5,
    },
  ],
  actions: ['log', 'alert'],
  priority: '中',
  frequency: 60,
  retryCount: 3,
  timeout: 30,
  enableLogging: true,
  enableAlert: true,
  autoDisable: false,
})

// 测试相关
const testData = ref(`{
  "location": {
    "accuracy": 5,
    "speed": 80,
    "latitude": 39.9042,
    "longitude": 116.4074
  },
  "timestamp": "2025-08-25T14:30:00Z",
  "vehicleId": "V-2025-001"
}`)
const testResult = ref<TestResult | null>(null)
const showTestDialog = ref(false)

// 可用动作
const availableActions = [
  {
    key: 'log',
    label: '记录日志',
    description: '将事件记录到系统日志中',
    icon: Database,
  },
  {
    key: 'alert',
    label: '发送告警',
    description: '向管理员发送告警通知',
    icon: Bell,
  },
  {
    key: 'block',
    label: '阻断操作',
    description: '阻止当前操作继续执行',
    icon: Shield,
  },
  {
    key: 'suspend',
    label: '暂停服务',
    description: '暂停相关服务或功能',
    icon: AlertTriangle,
  },
]

// 历史版本
const ruleVersions = ref<RuleVersion[]>([
  {
    id: 'v1',
    version: '1.0',
    updatedAt: '2025-08-20 10:30:00',
  },
  {
    id: 'v2',
    version: '1.1',
    updatedAt: '2025-08-22 14:15:00',
  },
])

// 辅助函数
const getConditionText = (condition: RuleCondition) => {
  const fieldNames: Record<string, string> = {
    'location.accuracy': '位置精度',
    'location.speed': '行驶速度',
    'data.frequency': '数据频率',
    'user.loginCount': '登录次数',
    'system.cpuUsage': 'CPU使用率',
    'network.bandwidth': '网络带宽',
  }

  const operatorNames: Record<string, string> = {
    '>': '大于',
    '<': '小于',
    '>=': '大于等于',
    '<=': '小于等于',
    '==': '等于',
    '!=': '不等于',
    contains: '包含',
    not_contains: '不包含',
  }

  return `${fieldNames[condition.field] || condition.field} ${operatorNames[condition.operator] || condition.operator} ${condition.value}`
}

const getActionLabel = (actionKey: string) => {
  const action = availableActions.find((a) => a.key === actionKey)
  return action?.label || actionKey
}

// 事件处理
const addCondition = () => {
  ruleForm.value.conditions.push({
    field: 'location.accuracy',
    operator: '<',
    value: '',
    logic: 'AND',
    timeWindow: 5,
  })
}

const removeCondition = (index: number) => {
  if (ruleForm.value.conditions.length > 1) {
    ruleForm.value.conditions.splice(index, 1)
  }
}

const toggleAction = (actionKey: string, checked: boolean) => {
  if (checked) {
    if (!ruleForm.value.actions.includes(actionKey)) {
      ruleForm.value.actions.push(actionKey)
    }
  } else {
    const index = ruleForm.value.actions.indexOf(actionKey)
    if (index > -1) {
      ruleForm.value.actions.splice(index, 1)
    }
  }
}

const handlePreview = () => {
  console.log('预览规则:', ruleForm.value)
}

const handleTest = () => {
  runTest()
  showTestDialog.value = true
}

const runTest = () => {
  try {
    // 模拟规则测试
    const data = JSON.parse(testData.value)
    let success = true
    let message = '所有条件都满足，规则匹配成功'

    // 简单的测试逻辑
    for (const condition of ruleForm.value.conditions) {
      const fieldPath = condition.field.split('.')
      let value = data
      for (const path of fieldPath) {
        value = value?.[path]
      }

      if (value === undefined) {
        success = false
        message = `字段 ${condition.field} 不存在于测试数据中`
        break
      }

      const conditionValue = parseFloat(condition.value)
      const dataValue = parseFloat(value)

      switch (condition.operator) {
        case '>':
          if (!(dataValue > conditionValue)) {
            success = false
            message = `条件不满足: ${dataValue} 不大于 ${conditionValue}`
          }
          break
        case '<':
          if (!(dataValue < conditionValue)) {
            success = false
            message = `条件不满足: ${dataValue} 不小于 ${conditionValue}`
          }
          break
        // 其他操作符...
      }

      if (!success) break
    }

    testResult.value = { success, message }
  } catch (error) {
    testResult.value = {
      success: false,
      message: '测试数据格式错误，请输入有效的JSON',
    }
  }
}

const handleSave = () => {
  // 保存规则
  console.log('保存规则:', ruleForm.value)

  // 返回规则列表页面
  router.push('/gov/system/rules/risk')
}

const restoreVersion = (versionId: string) => {
  console.log('恢复版本:', versionId)
}

// 初始化
onMounted(() => {
  if (isEditMode.value) {
    // 加载现有规则数据
    ruleForm.value.name = `位置数据精度异常检测_${ruleId}`
    ruleForm.value.description = '检测车辆位置数据精度是否符合要求，防止低精度数据导致的安全风险'
  }
})
</script>
