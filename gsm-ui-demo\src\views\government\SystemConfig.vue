<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">{{ $route.meta.title }}</h1>
        <p class="text-muted-foreground">
          管理系统参数配置，包括安全策略、业务规则、接口配置和系统性能参数
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button @click="handleBackup" variant="outline" class="flex items-center gap-2">
          <Download class="w-4 h-4" />
          备份配置
        </Button>
        <Button @click="handleRestore" variant="outline" class="flex items-center gap-2">
          <Upload class="w-4 h-4" />
          恢复配置
        </Button>
        <Button @click="handleSaveAll" class="flex items-center gap-2">
          <Save class="w-4 h-4" />
          保存配置
        </Button>
      </div>
    </div>

    <!-- 配置状态提示 -->
    <Alert
      v-if="hasUnsavedChanges"
      variant="default"
      class="border-orange-200 bg-orange-50 dark:bg-orange-900/20"
    >
      <AlertCircle class="h-4 w-4 text-orange-600" />
      <AlertTitle class="text-orange-600">配置变更提醒</AlertTitle>
      <AlertDescription class="text-orange-600">
        当前配置存在未保存的更改，请记得及时保存以防止数据丢失
      </AlertDescription>
    </Alert>

    <!-- 配置分类导航 -->
    <Card>
      <CardHeader>
        <CardTitle>配置分类</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-2">
          <Button
            v-for="category in configCategories"
            :key="category.key"
            :variant="activeCategory === category.key ? 'default' : 'outline'"
            size="sm"
            @click="activeCategory = category.key"
            class="flex items-center gap-2"
          >
            <component :is="category.icon" class="w-4 h-4" />
            {{ category.label }}
            <Badge v-if="getCategoryConfigCount(category.key)" variant="secondary" class="ml-1">
              {{ getCategoryConfigCount(category.key) }}
            </Badge>
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 配置内容 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：配置列表 -->
      <Card class="lg:col-span-1">
        <CardHeader>
          <CardTitle class="flex items-center justify-between">
            <span>{{ getCurrentCategoryLabel() }}配置</span>
            <Badge variant="outline">{{ filteredConfigs.length }}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-2">
            <div class="relative">
              <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input v-model="searchQuery" placeholder="搜索配置项..." class="pl-8" />
            </div>

            <ScrollArea class="h-96">
              <div class="space-y-1">
                <Button
                  v-for="config in filteredConfigs"
                  :key="config.key"
                  :variant="selectedConfig?.key === config.key ? 'default' : 'ghost'"
                  size="sm"
                  class="w-full justify-start text-left"
                  @click="selectConfig(config)"
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center gap-2 min-w-0">
                      <component :is="getConfigIcon(config)" class="w-4 h-4 flex-shrink-0" />
                      <div class="min-w-0">
                        <div class="font-medium truncate">{{ config.name }}</div>
                        <div class="text-xs text-muted-foreground truncate">
                          {{ config.description }}
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center gap-1 flex-shrink-0 ml-2">
                      <Badge
                        v-if="hasConfigChanged(config.key)"
                        variant="destructive"
                        class="w-2 h-2 p-0 rounded-full"
                      />
                      <Badge :variant="config.enabled ? 'default' : 'secondary'" class="text-xs">
                        {{ config.enabled ? '启用' : '禁用' }}
                      </Badge>
                    </div>
                  </div>
                </Button>
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      </Card>

      <!-- 右侧：配置详情 -->
      <Card class="lg:col-span-2">
        <CardHeader>
          <CardTitle v-if="selectedConfig" class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <component :is="getConfigIcon(selectedConfig)" class="w-5 h-5" />
              {{ selectedConfig.name }}
            </div>
            <div class="flex items-center gap-2">
              <Switch
                :checked="selectedConfig.enabled"
                @update:checked="updateConfigEnabled($event)"
              />
              <Label class="text-sm">{{ selectedConfig.enabled ? '启用' : '禁用' }}</Label>
            </div>
          </CardTitle>
          <CardDescription v-if="selectedConfig">
            {{ selectedConfig.description }}
            <span v-if="selectedConfig.requiresRestart" class="text-orange-600">
              • 修改后需要重启系统生效
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent v-if="selectedConfig">
          <div class="space-y-6">
            <!-- 基本信息 -->
            <div class="grid grid-cols-2 gap-4 p-4 border rounded-lg">
              <div>
                <Label class="text-base font-semibold text-muted-foreground">配置键</Label>
                <p class="text-sm font-mono">{{ selectedConfig.key }}</p>
              </div>
              <div>
                <Label class="text-base font-semibold text-muted-foreground">配置类型</Label>
                <Badge variant="outline">{{ getConfigTypeLabel(selectedConfig.type) }}</Badge>
              </div>
              <div>
                <Label class="text-base font-semibold text-muted-foreground">最后修改</Label>
                <p class="text-sm">{{ selectedConfig.lastModified || '从未修改' }}</p>
              </div>
              <div>
                <Label class="text-base font-semibold text-muted-foreground">修改人</Label>
                <p class="text-sm">{{ selectedConfig.modifiedBy || '-' }}</p>
              </div>
            </div>

            <!-- 配置值编辑 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <Label class="text-base font-medium">配置值</Label>
                <Button
                  v-if="hasConfigChanged(selectedConfig.key)"
                  @click="resetConfigValue"
                  variant="outline"
                  size="sm"
                >
                  <RotateCcw class="w-4 h-4 mr-2" />
                  重置
                </Button>
              </div>

              <!-- 字符串类型 -->
              <div v-if="selectedConfig.type === 'string'">
                <Input
                  :model-value="getConfigValue(selectedConfig.key) as string"
                  @update:model-value="updateConfigValue(selectedConfig.key, $event as any)"
                  :placeholder="selectedConfig.defaultValue as string"
                />
              </div>

              <!-- 数字类型 -->
              <div v-else-if="selectedConfig.type === 'number'">
                <Input
                  :model-value="getConfigValue(selectedConfig.key) as number"
                  @update:model-value="updateConfigValue(selectedConfig.key, Number($event))"
                  type="number"
                  :placeholder="String(selectedConfig.defaultValue)"
                  :min="selectedConfig.min"
                  :max="selectedConfig.max"
                />
                <div
                  v-if="selectedConfig.min !== undefined || selectedConfig.max !== undefined"
                  class="text-sm text-muted-foreground mt-1"
                >
                  范围: {{ selectedConfig.min ?? '-∞' }} ~ {{ selectedConfig.max ?? '+∞' }}
                </div>
              </div>

              <!-- 布尔类型 -->
              <div
                v-else-if="selectedConfig.type === 'boolean'"
                class="flex items-center space-x-2"
              >
                <Switch
                  :checked="getConfigValue(selectedConfig.key) as boolean"
                  @update:checked="updateConfigValue(selectedConfig.key, $event as any)"
                />
                <Label>{{ getConfigValue(selectedConfig.key) ? '启用' : '禁用' }}</Label>
              </div>

              <!-- 选择类型 -->
              <div v-else-if="selectedConfig.type === 'select' && selectedConfig.options">
                <Select
                  :model-value="getConfigValue(selectedConfig.key) as string"
                  @update:model-value="updateConfigValue(selectedConfig.key, $event as any)"
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="option in selectedConfig.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- JSON类型 -->
              <div v-else-if="selectedConfig.type === 'json'">
                <Textarea
                  :model-value="JSON.stringify(getConfigValue(selectedConfig.key), null, 2)"
                  @update:model-value="
                    updateConfigValue(selectedConfig.key, JSON.parse(($event as any) || '{}'))
                  "
                  rows="8"
                  class="font-mono text-sm"
                  placeholder="请输入有效的JSON格式"
                />
              </div>

              <!-- 数组类型 -->
              <div v-else-if="selectedConfig.type === 'array'">
                <div class="space-y-2">
                  <div
                    v-for="(item, index) in getConfigValue(selectedConfig.key) as string[]"
                    :key="index"
                    class="flex items-center gap-2"
                  >
                    <Input
                      :model-value="item"
                      @update:model-value="
                        updateArrayItem(selectedConfig.key, index, $event as string)
                      "
                      class="flex-1"
                    />
                    <Button
                      @click="removeArrayItem(selectedConfig.key, index)"
                      variant="outline"
                      size="sm"
                    >
                      <Trash2 class="w-4 h-4" />
                    </Button>
                  </div>
                  <Button
                    @click="addArrayItem(selectedConfig.key)"
                    variant="outline"
                    size="sm"
                    class="w-full"
                  >
                    <Plus class="w-4 h-4 mr-2" />
                    添加项
                  </Button>
                </div>
              </div>
            </div>

            <!-- 验证错误 -->
            <Alert v-if="configErrors[selectedConfig.key]" variant="destructive">
              <AlertCircle class="h-4 w-4" />
              <AlertDescription>
                {{ configErrors[selectedConfig.key] }}
              </AlertDescription>
            </Alert>

            <!-- 配置说明 -->
            <div v-if="selectedConfig.help" class="p-4 bg-muted rounded-lg">
              <h4 class="font-medium mb-2">配置说明</h4>
              <p class="text-sm text-muted-foreground whitespace-pre-line">
                {{ selectedConfig.help }}
              </p>
            </div>

            <!-- 相关配置 -->
            <div v-if="selectedConfig.related && selectedConfig.related.length" class="space-y-2">
              <h4 class="font-medium">相关配置</h4>
              <div class="flex flex-wrap gap-2">
                <Button
                  v-for="relatedKey in selectedConfig.related"
                  :key="relatedKey"
                  @click="selectConfigByKey(relatedKey)"
                  variant="outline"
                  size="sm"
                >
                  {{ getConfigByKey(relatedKey)?.name }}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>

        <!-- 空状态 -->
        <CardContent v-else>
          <div class="text-center py-12 text-muted-foreground">
            <Settings class="w-12 h-12 mx-auto mb-4" />
            <p>请从左侧选择要配置的参数</p>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 恢复配置对话框 -->
    <Dialog :open="showRestoreDialog" @update:open="showRestoreDialog = $event">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>恢复配置</DialogTitle>
          <DialogDescription> 选择要恢复的配置文件，这将覆盖当前所有配置设置 </DialogDescription>
        </DialogHeader>

        <div class="space-y-4 py-4">
          <div>
            <Label for="config-file">配置文件</Label>
            <Input id="config-file" type="file" accept=".json" @change="handleFileSelect" />
          </div>

          <Alert variant="destructive">
            <AlertTriangle class="h-4 w-4" />
            <AlertDescription> 恢复配置将覆盖所有当前设置，请确认备份重要配置 </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showRestoreDialog = false">取消</Button>
          <Button @click="confirmRestore" variant="destructive">确认恢复</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  AlertCircle,
  AlertTriangle,
  Database,
  Download,
  Globe,
  Lock,
  Monitor,
  Plus,
  RotateCcw,
  Save,
  Search,
  Settings,
  Shield,
  Timer,
  Trash2,
  Upload,
  Zap,
} from 'lucide-vue-next'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

type ConfigType = 'string' | 'number' | 'boolean' | 'select' | 'json' | 'array'
type ConfigValue = string | number | boolean | object | Array<any>

interface ConfigOption {
  label: string
  value: string
}

interface SystemConfig {
  key: string
  name: string
  description: string
  category: string
  type: ConfigType
  defaultValue: ConfigValue
  currentValue?: ConfigValue
  enabled: boolean
  requiresRestart?: boolean
  min?: number
  max?: number
  options?: ConfigOption[]
  help?: string
  related?: string[]
  lastModified?: string
  modifiedBy?: string
}

interface ConfigCategory {
  key: string
  label: string
  icon: any
}

// 配置分类
const configCategories: ConfigCategory[] = [
  { key: 'security', label: '安全策略', icon: Shield },
  { key: 'database', label: '数据库配置', icon: Database },
  { key: 'api', label: '接口配置', icon: Globe },
  { key: 'performance', label: '性能优化', icon: Zap },
  { key: 'monitoring', label: '监控告警', icon: Monitor },
  { key: 'auth', label: '认证授权', icon: Lock },
  { key: 'schedule', label: '任务调度', icon: Timer },
]

// 状态管理
const activeCategory = ref('security')
const searchQuery = ref('')
const selectedConfig = ref<SystemConfig | null>(null)
const configValues = ref<Record<string, ConfigValue>>({})
const configErrors = ref<Record<string, string>>({})
const hasUnsavedChanges = ref(false)
const showRestoreDialog = ref(false)

// 系统配置数据
const systemConfigs = ref<SystemConfig[]>([
  // 安全策略
  {
    key: 'security.session_timeout',
    name: '会话超时时间',
    description: '用户会话空闲超时时间（分钟）',
    category: 'security',
    type: 'number',
    defaultValue: 30,
    currentValue: 30,
    enabled: true,
    requiresRestart: false,
    min: 5,
    max: 1440,
    help: '设置用户登录会话的空闲超时时间，超时后将自动登出。建议设置为30-120分钟。',
    related: ['security.max_login_attempts', 'auth.token_expiry'],
  },
  {
    key: 'security.max_login_attempts',
    name: '最大登录尝试次数',
    description: '用户连续登录失败的最大次数',
    category: 'security',
    type: 'number',
    defaultValue: 5,
    currentValue: 5,
    enabled: true,
    min: 3,
    max: 10,
    help: '用户连续登录失败达到此次数后，账户将被临时锁定。建议设置为3-5次。',
  },
  {
    key: 'security.password_policy',
    name: '密码策略',
    description: '用户密码复杂度要求',
    category: 'security',
    type: 'json',
    defaultValue: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
    },
    currentValue: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
    },
    enabled: true,
    help: '定义用户密码的复杂度要求，包括最小长度、字符类型等。',
  },
  {
    key: 'security.allowed_origins',
    name: '允许的来源域名',
    description: 'CORS跨域请求允许的域名列表',
    category: 'security',
    type: 'array',
    defaultValue: ['https://geoai.gov.cn', 'https://www.geoai.gov.cn'],
    currentValue: ['https://geoai.gov.cn', 'https://www.geoai.gov.cn'],
    enabled: true,
    requiresRestart: true,
    help: '配置允许跨域访问的域名列表，用于API接口的CORS策略。',
  },

  // 数据库配置
  {
    key: 'database.connection_pool_size',
    name: '数据库连接池大小',
    description: '数据库连接池的最大连接数',
    category: 'database',
    type: 'number',
    defaultValue: 20,
    currentValue: 20,
    enabled: true,
    requiresRestart: true,
    min: 5,
    max: 100,
    help: '设置数据库连接池的最大连接数，影响系统的并发性能。',
  },
  {
    key: 'database.query_timeout',
    name: '查询超时时间',
    description: '数据库查询超时时间（秒）',
    category: 'database',
    type: 'number',
    defaultValue: 30,
    currentValue: 30,
    enabled: true,
    min: 10,
    max: 300,
    help: '设置数据库查询的超时时间，超时的查询将被中断。',
  },
  {
    key: 'database.backup_enabled',
    name: '自动备份',
    description: '是否启用数据库自动备份',
    category: 'database',
    type: 'boolean',
    defaultValue: true,
    currentValue: true,
    enabled: true,
    help: '启用后系统将按计划自动备份数据库。',
  },

  // 接口配置
  {
    key: 'api.rate_limit',
    name: 'API请求频率限制',
    description: '单个IP每分钟最大请求数',
    category: 'api',
    type: 'number',
    defaultValue: 100,
    currentValue: 100,
    enabled: true,
    min: 10,
    max: 1000,
    help: '限制单个IP地址每分钟的API请求次数，防止滥用。',
  },
  {
    key: 'api.response_cache_ttl',
    name: '响应缓存时间',
    description: 'API响应缓存的生存时间（秒）',
    category: 'api',
    type: 'number',
    defaultValue: 300,
    currentValue: 300,
    enabled: true,
    min: 60,
    max: 3600,
    help: '设置API响应结果的缓存时间，提高响应速度。',
  },
  {
    key: 'api.log_level',
    name: 'API日志级别',
    description: 'API请求日志的记录级别',
    category: 'api',
    type: 'select',
    defaultValue: 'INFO',
    currentValue: 'INFO',
    enabled: true,
    options: [
      { label: 'ERROR - 仅错误', value: 'ERROR' },
      { label: 'WARN - 警告及以上', value: 'WARN' },
      { label: 'INFO - 信息及以上', value: 'INFO' },
      { label: 'DEBUG - 调试信息', value: 'DEBUG' },
    ],
    help: '设置API请求日志的详细程度，DEBUG级别会记录最详细的信息。',
  },

  // 性能优化
  {
    key: 'performance.enable_gzip',
    name: '启用Gzip压缩',
    description: '是否启用HTTP响应的Gzip压缩',
    category: 'performance',
    type: 'boolean',
    defaultValue: true,
    currentValue: true,
    enabled: true,
    help: '启用Gzip压缩可以减少网络传输数据量，提高页面加载速度。',
  },
  {
    key: 'performance.cache_static_assets',
    name: '静态资源缓存时间',
    description: '静态资源的浏览器缓存时间（小时）',
    category: 'performance',
    type: 'number',
    defaultValue: 24,
    currentValue: 24,
    enabled: true,
    min: 1,
    max: 168,
    help: '设置静态资源（CSS、JS、图片等）在浏览器中的缓存时间。',
  },

  // 监控告警
  {
    key: 'monitoring.health_check_interval',
    name: '健康检查间隔',
    description: '系统健康检查的时间间隔（秒）',
    category: 'monitoring',
    type: 'number',
    defaultValue: 30,
    currentValue: 30,
    enabled: true,
    min: 10,
    max: 300,
    help: '设置系统健康检查的执行频率。',
  },
  {
    key: 'monitoring.alert_email',
    name: '告警邮箱',
    description: '系统异常告警的接收邮箱',
    category: 'monitoring',
    type: 'string',
    defaultValue: '<EMAIL>',
    currentValue: '<EMAIL>',
    enabled: true,
    help: '配置接收系统告警邮件的邮箱地址。',
  },

  // 认证授权
  {
    key: 'auth.token_expiry',
    name: 'Token过期时间',
    description: 'JWT Token的过期时间（小时）',
    category: 'auth',
    type: 'number',
    defaultValue: 24,
    currentValue: 24,
    enabled: true,
    min: 1,
    max: 168,
    help: '设置JWT Token的有效期，过期后用户需要重新登录。',
  },
  {
    key: 'auth.enable_2fa',
    name: '启用双因子认证',
    description: '是否启用双因子身份认证',
    category: 'auth',
    type: 'boolean',
    defaultValue: false,
    currentValue: false,
    enabled: false,
    help: '启用双因子认证可以提高账户安全性，但会增加登录复杂度。',
  },

  // 任务调度
  {
    key: 'schedule.backup_time',
    name: '备份执行时间',
    description: '每日自动备份的执行时间',
    category: 'schedule',
    type: 'string',
    defaultValue: '02:00',
    currentValue: '02:00',
    enabled: true,
    help: '设置每日自动备份任务的执行时间，格式为HH:MM。',
  },
])

// 计算属性
const filteredConfigs = computed(() => {
  let configs = systemConfigs.value.filter((config) => config.category === activeCategory.value)

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    configs = configs.filter(
      (config) =>
        config.name.toLowerCase().includes(query) ||
        config.description.toLowerCase().includes(query) ||
        config.key.toLowerCase().includes(query),
    )
  }

  return configs
})

// 辅助函数
const getCurrentCategoryLabel = () => {
  return configCategories.find((cat) => cat.key === activeCategory.value)?.label || '未知'
}

const getCategoryConfigCount = (categoryKey: string) => {
  return systemConfigs.value.filter((config) => config.category === categoryKey).length
}

const getConfigIcon = (config: SystemConfig) => {
  const iconMap: Record<string, any> = {
    string: Settings,
    number: Settings,
    boolean: Settings,
    select: Settings,
    json: Settings,
    array: Settings,
  }
  return iconMap[config.type] || Settings
}

const getConfigTypeLabel = (type: ConfigType) => {
  const labels: Record<ConfigType, string> = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值',
    select: '选择',
    json: 'JSON对象',
    array: '数组',
  }
  return labels[type]
}

const selectConfig = (config: SystemConfig) => {
  selectedConfig.value = config
  // 初始化配置值
  if (!(config.key in configValues.value)) {
    configValues.value[config.key] = config.currentValue ?? config.defaultValue
  }
}

const selectConfigByKey = (key: string) => {
  const config = systemConfigs.value.find((c) => c.key === key)
  if (config) {
    selectConfig(config)
  }
}

const getConfigByKey = (key: string) => {
  return systemConfigs.value.find((c) => c.key === key)
}

const getConfigValue = (key: string) => {
  return (
    configValues.value[key] ??
    selectedConfig.value?.currentValue ??
    selectedConfig.value?.defaultValue
  )
}

const updateConfigValue = (key: string, value: ConfigValue) => {
  configValues.value[key] = value
  hasUnsavedChanges.value = true

  // 清除错误
  delete configErrors.value[key]

  // 简单验证
  const config = systemConfigs.value.find((c) => c.key === key)
  if (config) {
    if (config.type === 'number' && typeof value === 'number') {
      if (config.min !== undefined && value < config.min) {
        configErrors.value[key] = `值不能小于 ${config.min}`
      }
      if (config.max !== undefined && value > config.max) {
        configErrors.value[key] = `值不能大于 ${config.max}`
      }
    }
  }
}

const updateConfigEnabled = (enabled: boolean) => {
  if (selectedConfig.value) {
    selectedConfig.value.enabled = enabled
    hasUnsavedChanges.value = true
  }
}

const hasConfigChanged = (key: string) => {
  const config = systemConfigs.value.find((c) => c.key === key)
  if (!config) return false

  const currentValue = configValues.value[key]
  const originalValue = config.currentValue ?? config.defaultValue

  return (
    currentValue !== undefined && JSON.stringify(currentValue) !== JSON.stringify(originalValue)
  )
}

const resetConfigValue = () => {
  if (selectedConfig.value) {
    const key = selectedConfig.value.key
    configValues.value[key] = selectedConfig.value.currentValue ?? selectedConfig.value.defaultValue
    delete configErrors.value[key]
    hasUnsavedChanges.value = true
  }
}

const updateArrayItem = (key: string, index: number, value: string) => {
  const array = [...(getConfigValue(key) as string[])]
  array[index] = value
  updateConfigValue(key, array)
}

const addArrayItem = (key: string) => {
  const array = [...(getConfigValue(key) as string[]), '']
  updateConfigValue(key, array)
}

const removeArrayItem = (key: string, index: number) => {
  const array = [...(getConfigValue(key) as string[])]
  array.splice(index, 1)
  updateConfigValue(key, array)
}

// 事件处理
const handleSaveAll = () => {
  // 应用所有配置更改
  Object.keys(configValues.value).forEach((key) => {
    const config = systemConfigs.value.find((c) => c.key === key)
    if (config) {
      config.currentValue = configValues.value[key]
      config.lastModified = new Date().toLocaleString('zh-CN')
      config.modifiedBy = '系统管理员'
    }
  })

  // 清空临时更改
  configValues.value = {}
  hasUnsavedChanges.value = false
  configErrors.value = {}

  console.log('所有配置已保存')
}

const handleBackup = () => {
  const configData = {
    timestamp: new Date().toISOString(),
    configs: systemConfigs.value.map((config) => ({
      key: config.key,
      value: config.currentValue ?? config.defaultValue,
      enabled: config.enabled,
    })),
  }

  const blob = new Blob([JSON.stringify(configData, null, 2)], {
    type: 'application/json;charset=utf-8;',
  })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `系统配置备份_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
  link.click()
  URL.revokeObjectURL(url)
}

const handleRestore = () => {
  showRestoreDialog.value = true
}

const handleFileSelect = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const configData = JSON.parse(e.target?.result as string)
        // 这里可以验证配置数据格式
        console.log('配置文件已读取:', configData)
      } catch (error) {
        configErrors.value['restore'] = '配置文件格式错误'
      }
    }
    reader.readAsText(file)
  }
}

const confirmRestore = () => {
  // 实际的恢复逻辑
  showRestoreDialog.value = false
  console.log('配置已恢复')
}

// 初始化
watch(
  () => activeCategory.value,
  () => {
    selectedConfig.value = null
  },
  { immediate: true },
)
</script>
