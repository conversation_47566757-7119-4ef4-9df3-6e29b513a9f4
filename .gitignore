# ================================
# 时空数据安全监测平台 - Git 忽略配置
# ================================

# ================================
# 项目管理和 AI 配置文件 - 不提交
# ================================
.claude/
.vscode/
.augment/
.claudeagents/
.claudecommands/
.data/
.bmad*

# Claude Code Manager
claude-code-manager/

# ================================
# Node.js 和包管理器
# ================================
node_modules/
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.tsbuildinfo

# ================================
# 构建输出和分发文件
# ================================
dist/
dist
dist-ssr
build/
out/

# ================================
# 日志文件
# ================================
*.log
logs/
logs

# ================================
# 环境配置和本地文件
# ================================
.env
.env.local
.env.*.local
*.local
# ================================
# 秘钥证书
# ================================
*.key

# ================================
# 系统文件
# ================================
.DS_Store
Thumbs.db
desktop.ini

# ================================
# 编辑器和 IDE 配置
# ================================
# VS Code
.vscode/*
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
.idea
*.iml
*.ipr
*.iws

# 其他编辑器
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ================================
# 测试和覆盖率报告
# ================================
coverage/
*.lcov
.nyc_output/
test-results/
playwright-report/

# Cypress
/cypress/videos/
/cypress/screenshots/

# ================================
# 临时文件和缓存
# ================================
*.tmp
*.temp
.cache/
.temp/

# ================================
# 备份文件
# ================================
*.backup
*-backup/

# ================================
# 项目特定文件
# ================================
# 原型文件（可选择性提交）
地理信息安全监测平台2025.7.31-Axure原型/




# ================================
# 安全：敏感目录与证书/密钥类型全量忽略（避免误提交）
# ================================
# 目录约定
secrets/
secret/
keys/
**/cloud-keys/

# 常见证书/密钥文件
*.pem
*.p12
*.pfx
*.cer
*.crt
*.der
*.p7b
*.p7c
*.jks
*.keystore
*.p8
*.asc

# Dump 等敏感输出
*.stackdump
# Ignore packaged zip artifacts from CI
*.zip
!docs/*.zip
!**/*.source.zip
